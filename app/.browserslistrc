# This file is used by the build system to adjust CSS and JS output to support the specified browsers below.
# For additional information regarding the format and rule options, please see:
# https://github.com/browserslist/browserslist#queries

# For the full list of supported browsers by the Angular framework, please see:
# https://angular.io/guide/browser-support

# You can see what browsers were selected by your queries by running:
#   npx browserslist

# Modern browsers only – safe for Angular 20 and Capacitor apps
Chrome >= 107
ChromeAndroid >= 107
Firefox >= 104
Edge >= 107
Safari >= 16
iOS >= 16
not IE 11
