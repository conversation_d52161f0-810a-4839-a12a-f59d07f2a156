// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-apple-sign-in')
    implementation project(':capacitor-community-firebase-analytics')
    implementation project(':capacitor-app')
    implementation project(':capacitor-app-launcher')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-device')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-push-notifications')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-native-settings')
    implementation "com.android.billingclient:billing:7.1.1"
}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
