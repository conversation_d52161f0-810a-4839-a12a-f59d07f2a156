import { CapacitorConfig } from "@capacitor/cli";
import { ISplashConfig } from "ISplashConfig";
import { environment as Env } from "./src/environments/environment";

let splashConfig = <ISplashConfig>{
  ...Env.SPLASH_CONFIG,
  ...{
    launchAutoHide: false,

    // launchShowDuration: 10000,
    // useDialog: true,
    // "androidSplashResourceName": "splash",
    // "layoutName": "launch_screen",
    // "androidSpinnerStyle": "large",
    // "iosSpinnerStyle": "large",
    androidScaleType: "CENTER_CROP",
    splashFullScreen: false,
    splashImmersive: false,
  },
};

const config: CapacitorConfig = {
  appId: Env.APP_STORE_ID,
  appName: Env.APP_NAME,
  bundledWebRuntime: false,
  webDir: "www/browser",
  loggingBehavior: Env.IS_DEBUG ? "debug" : "production",
  plugins: {
    PushNotifications: {
      presentationOptions: ["badge", "sound", "alert"],
    },
    SplashScreen: splashConfig,
  },
  server: {
    androidScheme: "http",
    hostname: Env.APP_WEBSITE,
  },
};

export default config;
