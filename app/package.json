{"name": "dvip-mobile-v2", "version": "1.0.0", "author": "OnlineConnections inc.", "homepage": "https://onlineconnectionsinc.com", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test --code-coverage", "show-coverage": "open-cli coverage/index.html", "lint": "ng lint", "e2e": "ng e2e", "ionic-e2e:develop": "TS_NODE_PROJECT=e2e/tsconfig.json  wdio e2e/config/wdio.web.config.ts --watch", "ionic-e2e:run:web": "TS_NODE_PROJECT=e2e/tsconfig.json  wdio e2e/config/wdio.web.config.ts", "ionic-e2e:run:ios": "TS_NODE_PROJECT=e2e/tsconfig.json  wdio e2e/config/wdio.ios.config.ts", "ionic-e2e:run:android": "TS_NODE_PROJECT=e2e/tsconfig.json  wdio e2e/config/wdio.android.config.ts", "ionic-e2e:screenshots": "TS_NODE_PROJECT=e2e/tsconfig.json  wdio e2e/config/wdio.screenshot.config.ts --suite=screenshots", "prepare": "cd .. && husky app/.husky", "translation_upload": "node ./../tools/translation_helper.js -o=upload -e=production", "translation_download": "node ./../tools/translation_helper.js -o=download -e=production", "rand": "ionic build && npx cap run android", "randl": "ionic cap run android --livereload --external", "rios": "ionic build && npx cap run ios", "riosl": "ionic cap run ios --livereload --external", "oand": "ionic build && npx cap sync && npx cap copy && ionic cap run android --livereload --external --consolelogs", "oios": "ionic build && npx cap sync && npx cap copy && ionic cap run ios --livereload --external --consolelogs", "fb_hash_dev": "node ./../tools/utils.js fbkey --env development", "mpm": "mpm", "c": "mpm qcheckout", "cp": "mpm qcheckout -e .production", "chversion": "node ../tools/version_bump.js", "pinfo": "node ../tools/build_helper.js build info", "android_release": "node ../tools/build_helper.js build android", "android_release_current_distribute": "node ../tools/build_helper.js build android -d 1 -u 1", "android_release_current_upload": "node ../tools/build_helper.js build android -u 1", "android_release_all": "node ../tools/build_helper.js build android -b 1", "android_release_all_upload": "node ../tools/build_helper.js build android -b 1 -u 1", "android_release_all_firebase_only": "node ../tools/build_helper.js build android -b 1 -d 1", "android_release_all_distribute": "node ../tools/build_helper.js build android -b 1 -d 1 -u 1", "ios_release": "node ../tools/build_helper.js build ios", "ios_release_current_distribute": "node ../tools/build_helper.js build ios -d 1 -u 1", "ios_release_all": "node ../tools/build_helper.js build ios -b 1", "ios_release_all_distribute": "node ../tools/build_helper.js build ios -b 1 -u 1", "firebase_signature": "node ../tools/utils.js firebase_signature", "deeplinking": "node ../tools/utils.js deeplinking", "sync_all": "npm i && npx cap sync && npx cap copy", "qb": "ionic build && npx cap copy", "release_notes": "node ../tools/list_release_notes.js", "install_apks": "echo bundletool install-apks --apks=/absolut/path/to/file.apks", "ls:prettier": "prettier --write --ignore-unknown", "build_prod": "ionic build --prod --aot --minifyjs --minifycss --optimizejs"}, "private": true, "dependencies": {"@angular-devkit/build-angular": "^20.2.1", "@angular/cdk": "^20.2.1", "@angular/common": "^20.2.2", "@angular/core": "^20.2.2", "@angular/forms": "^20.2.2", "@angular/platform-browser": "^20.2.2", "@angular/platform-browser-dynamic": "^20.2.2", "@angular/router": "^20.2.2", "@auth0/angular-jwt": "^5.2.0", "@capacitor-community/apple-sign-in": "^7.0.1", "@capacitor-community/firebase-analytics": "^7.0.1", "@capacitor/android": "^7.4.3", "@capacitor/app": "^7.0.2", "@capacitor/app-launcher": "^7.0.2", "@capacitor/browser": "^7.0.2", "@capacitor/camera": "^7.0.2", "@capacitor/core": "^7.4.3", "@capacitor/device": "^7.0.2", "@capacitor/ios": "^7.4.3", "@capacitor/keyboard": "^7.0.2", "@capacitor/push-notifications": "^7.0.2", "@capacitor/splash-screen": "^7.0.2", "@ionic/angular": "^8.7.3", "@ionic/angular-toolkit": "^12.3.0", "@ionic/core": "^8.7.3", "@ionic/lab": "^3.2.15", "@ionic/storage": "^4.0.0", "@ionic/storage-angular": "^4.0.0", "@lottiefiles/lottie-player": "^2.0.12", "@ngx-translate/core": "^17.0.0", "@ngx-translate/http-loader": "^17.0.0", "angular-google-tag-manager": "^1.11.0", "capacitor-ios-autofill-save-password": "^5.0.0", "cordova-plugin-purchase": "^13.12.1", "date-fns": "^4.1.0", "faye": "^1.4.1", "jquery": "^3.7.1", "jquery-countdown": "^2.2.0", "lodash": "^4.17.21", "moment": "^2.30.1", "ngx-image-cropper": "^9.1.5", "swiper": "^11.2.10", "ts-md5": "^2.0.1", "tslib": "^2.8.1", "yup": "^1.7.0", "zone.js": "^0.15.1"}, "devDependencies": {"@angular-eslint/eslint-plugin": "^20.2.0", "@angular-eslint/eslint-plugin-template": "^20.2.0", "@angular-eslint/template-parser": "^20.2.0", "@angular/cli": "^20.2.1", "@angular/compiler": "^20.2.2", "@angular/compiler-cli": "^20.2.2", "@angular/language-service": "^20.2.2", "@babel/core": "^7.28.3", "@capacitor/cli": "^7.4.3", "@types/jasmine": "^5.1.9", "@types/jasminewd2": "^2.0.13", "@types/node": "^24.3.0", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "@wdio/protocols": "^9.16.2", "blurhash": "^2.0.5", "capacitor-native-settings": "^7.0.2", "devtools": "^8.42.0", "eslint": "^9.34.0", "husky": "^9.1.7", "imagemagick": "^0.1.3", "jasmine-core": "^5.9.0", "jasmine-spec-reporter": "^7.0.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "open-cli": "^8.0.0", "prettier": "^3.6.2", "rxjs": "^7.8.2", "ts-node": "^10.9.2", "typescript": "5.9.2"}, "description": "DatingVIP Mobile v2 project"}