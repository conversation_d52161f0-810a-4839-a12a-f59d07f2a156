<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title></title>
    <base href="/" />
    <!--
    <meta name="color-scheme" content="light dark" />
    -->
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />
    <link rel="icon" type="image/png" href="assets/icon/favicon.png" />
    <!-- add to homescreen for ios -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <script type="text/javascript">
      // this is a fix for Faye lib for comet not working with newer angular
      // ref: https://github.com/angular/angular-cli/issues/9827#issuecomment-386154063
      window.global = window;
    </script>
  </head>
  <body>
    <app-root></app-root>
    <!-- animated splash -->
    <div id="global_animated_splash">
      <lottie-player
        class="lottie-player"
        id="lottie_player_el"
        autoplay
        loop
        src="/assets/json/splash-animation.json"
      ></lottie-player>
    </div>
    <!-- end of animated splash -->
  </body>
</html>
