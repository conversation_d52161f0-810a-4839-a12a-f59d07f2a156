import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { TabsPage } from "./tabs.page";

const routes: Routes = [
  {
    path: "",
    component: TabsPage,
    children: [
      {
        path: "browse",
        loadChildren: () =>
          import("../browse/browse.module").then((m) => m.BrowsePageModule),
      },
      {
        path: "search",
        loadChildren: () =>
          import("../search/search.module").then((m) => m.SearchPageModule),
      },
      {
        path: "meet",
        loadChildren: () =>
          import("../meet/meet.module").then((m) => m.MeetPageModule),
      },
      {
        path: "my-profile",
        loadChildren: () =>
          import("../my-profile/my-profile.module").then(
            (m) => m.MyProfilePageModule
          ),
      },
      {
        path: "",
        redirectTo: "/app/tabs/meet",
        pathMatch: "full",
      },
    ],
  },
  {
    path: "",
    redirectTo: "/app/tabs/meet",
    pathMatch: "full",
  },
];

import { TranslateModule } from "@ngx-translate/core";

@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TabsPageRoutingModule {}
