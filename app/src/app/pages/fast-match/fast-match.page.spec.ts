import { ComponentFixture, TestBed, waitForAsync } from "@angular/core/testing";
import { IonicModule } from "@ionic/angular";

import { FastMatchPage } from "./fast-match.page";
import { StorageService } from "../../services/storage.service";
import { LoaderService } from "../../services/loader.service";

describe("FastMatchPage", () => {
  let component: FastMatchPage;
  let fixture: ComponentFixture<FastMatchPage>;
  let loaderSpy: LoaderService;
  let storageSpy: StorageService;

  beforeEach(
    waitForAsync(() => {
      loaderSpy = jasmine.createSpyObj("LoaderService", [
        "storeLoaderStatus",
        "isLoading",
      ]);
      storageSpy = jasmine.createSpyObj("StorageService", ["get", "set"]);

      TestBed.configureTestingModule({
        declarations: [FastMatchPage],
        imports: [IonicModule.forRoot()],
        providers: [
          { provide: LoaderService, useValue: loaderSpy },
          { provide: StorageService, useValue: storageSpy },
        ],
      }).compileComponents();

      fixture = TestBed.createComponent(FastMatchPage);
      component = fixture.componentInstance;
      fixture.detectChanges();
    })
  );

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
