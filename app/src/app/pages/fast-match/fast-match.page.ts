import {
  <PERSON><PERSON><PERSON>w<PERSON>nit,
  <PERSON>mponent,
  ElementRef,
  NgZone,
  QueryList,
  ViewChild,
  ViewChildren,
} from "@angular/core";
import { G<PERSON><PERSON>, GestureController } from "@ionic/angular";
import { LoaderService } from "../../services/loader.service";
import { ProfileService } from "../../services/profile.service";
import { createAnimation, Animation } from "@ionic/core";
import {
  ProfileSuggestionsService,
  SuggestedItem,
} from "src/app/services/profile-suggestions.service";
import { Subscription, firstValueFrom, Observable } from "rxjs";
import { CardBaseComponent } from "src/app/components/card-base/card-base.component";
import { ProfileBase } from "src/app/schemas/profile-base";
import { Debug } from "src/app/helpers/debug";
import { Router } from "@angular/router";
import { StateService } from "src/app/services/state.service";
import { AclService } from "../../services/acl.service";
import { UnreadCountService } from "src/app/services/unread-count.service";
import { ApiService } from "src/app/services/api/api.service";
import { AccountService } from "../../services/account.service";
import { Browser } from "@capacitor/browser";

@Component({
  selector: "app-fast-match",
  templateUrl: "./fast-match.page.html",
  styleUrls: ["./fast-match.page.scss"],
  standalone: false,
})
export class FastMatchPage implements AfterViewInit {
  public cardRef: ElementRef;

  @ViewChildren("cardDiv")
  public cardChildren: QueryList<CardBaseComponent>;

  @ViewChild("status")
  public statusElement: ElementRef;

  public get wasNetworkError() {
    return this.apiService.wasNetworkError;
  }

  public ngAfterViewInit() {
    this.cardRef = this.cardChildren.first.cardRef;

    this.initGestureAndAnimations();
  }

  public gotoMatchFilter() {
    this.router.navigateByUrl("/app/match-options-menu");
  }

  public gotoBrowseProfile() {
    this.router.navigateByUrl("/app/tabs/browse");
  }

  private get windowWidth(): number {
    return window.innerWidth;
  }

  private get windowHeight(): number {
    return window.innerWidth;
  }

  /**
   * Current profile.
   */
  private ciSub: Subscription;
  private ciProfile: ProfileBase.type;

  /**
   * Card state.
   */
  public cardState:
    | "like"
    | "pass"
    | "superlike"
    | "later"
    | "dismiss"
    | "visit"
    | "" = "";
  private previousCardState:
    | "like"
    | "pass"
    | "superlike"
    | "later"
    | "dismiss"
    | "visit"
    | "" = "";

  constructor(
    public loaderService: LoaderService,
    public profileService: ProfileService,
    private gestureCtrl: GestureController,
    public profileSuggestions: ProfileSuggestionsService,
    private debug: Debug,
    private ngZone: NgZone,
    private router: Router,
    private stateService: StateService,
    public acl: AclService,
    public unreadCountService: UnreadCountService,
    public apiService: ApiService,
    public accountService: AccountService,
  ) {
    this.profileSuggestions.items$.subscribe({
      next: (data: SuggestedItem) => {
        this.profileSuggestionsObserver(data);
      },
    });
  }

  public get ci(): Observable<ProfileBase.type> {
    return this.profileSuggestions.currentItem$;
  }

  public get ni(): Observable<ProfileBase.type> {
    return this.profileSuggestions.nextItem$;
  }

  public get ps(): Observable<SuggestedItem> {
    return this.profileSuggestions.items$;
  }

  private profileSuggestionsObserver(data: SuggestedItem) {}

  private _cardStyle = null;

  private restoreAnimation: Animation;
  private swipeRightAnimation: Animation;
  private swipeLeftAnimation: Animation;
  private swipeUpAnimation: Animation;
  private superlikeAnimation: Animation;

  private swipeGestureY: Gesture;
  private swipeGestureX: Gesture;

  private ANIM_SPEED = 300;

  private async initGestureAndAnimations() {
    this._cardStyle = this.cardRef.nativeElement.style;

    this.restoreAnimation = createAnimation("backToCenter")
      .addElement(this.cardRef.nativeElement)
      .duration(100)
      .to("transform", "translate(0px,0px)");

    this.superlikeAnimation = createAnimation("superlike")
      .addElement(this.cardRef.nativeElement)
      .duration(this.ANIM_SPEED)
      .easing("ease-out")
      .to("opacity", "0")
      .to("transform", "scale(1.5)");

    this.swipeLeftAnimation = createAnimation("swipeLeft")
      .addElement(this.cardRef.nativeElement)
      .duration(this.ANIM_SPEED)
      .to("transform", `translate(${-this.windowWidth}px,0px)`);

    this.swipeRightAnimation = createAnimation("swipeRight")
      .addElement(this.cardRef.nativeElement)
      .duration(this.ANIM_SPEED)
      .to("transform", `translate(${+this.windowWidth}px,0px)`);

    this.swipeUpAnimation = createAnimation("swipeUp")
      .addElement(this.cardRef.nativeElement)
      .duration(this.ANIM_SPEED)
      .to("transform", `translate(0px,${-this.windowHeight * 1.6}px)`);

    this.swipeGestureX = await this.gestureCtrl.create({
      el: this.cardRef.nativeElement,
      threshold: 15,
      disableScroll: true,
      gestureName: "xswipe",
      onStart: this._gStartX.bind(this),
      onEnd: this._gStopX.bind(this),
      onMove: this._gMoveX.bind(this),
      notCaptured: (e) => {
        this.debug.l("x not captured", e);
      },
      passive: true,
      gesturePriority: 10,
      direction: "x",
    });

    this.swipeGestureY = await this.gestureCtrl.create({
      el: this.cardRef.nativeElement,
      threshold: 20,
      disableScroll: true,
      gestureName: "yswipe",
      onStart: this._gStartY.bind(this),
      onEnd: this._gStopY.bind(this),
      onMove: this._gMoveY.bind(this),
      notCaptured: (e) => {
        this.debug.l("y not captured", e);
      },
      passive: true,
      direction: "y",
    });

    this.swipeGestureX.enable(true);
    this.swipeGestureY.enable(true);
  }

  public openProfile(profile) {
    // store it in state so we don't have to re-fetch it
    this.stateService.set("profile", profile.username, profile);

    // open profile
    this.router.navigate(["/app/profile", profile.username]);
  }

  public openGallery(profile) {
    // store it in state so we don't have to re-fetch it
    this.stateService.set("profile", profile.username, profile);

    if (!this.accountService.isPremium()) {
      // open profile
      return this.router.navigate(["/app/profile", profile.username]);
    }

    // open url
    this.router.navigate(["/app/photo-gallery", profile.username]);
  }

  private _gStartX(event) {
    this.debug.l("startx", event);
  }

  private actionHappened(
    name: "left" | "right" | "up" | "superlike" | "visit" | "dismiss" | void,
  ) {
    if (name) {
      this.displaySwipeStatus(name);

      this.ciSub = this.ci.subscribe({
        next: (profile) => {
          this.ciProfile = profile;
          if (name === "right" && profile.profile_type === "ads") {
            name = "visit";
          }
          if (
            (name === "left" || name === "up") &&
            profile.profile_type === "ads"
          ) {
            name = "dismiss";
          }
        },
      });
      this.ciSub.unsubscribe();
    }

    switch (name) {
      case "superlike":
        this.superlikeAnimation.play().then(async () => {
          await this.aclAction("superlike").catch((e) =>
            this.debug.le("ACL superlike error", e),
          );

          this.profileSuggestions.next().then(() => {
            this._cardStyle.transform = "scale(1)";
            this._cardStyle.opacity = "0";
            this._restoreCard();
          });
        });

        break;

      case "right":
        this.swipeRightAnimation.play().then(async () => {
          await this.aclAction("like").catch((e) =>
            this.debug.le("ACL like error", e),
          );

          this.swipedRight().then(() => {
            this._cardStyle.opacity = "0";
            this._restoreCard();
          });
        });

        break;

      case "left":
        this.swipeLeftAnimation.play().then(async () => {
          await this.aclAction("pass").catch((e) =>
            this.debug.le("ACL pass error", e),
          );

          this.swipedLeft().then(() => {
            this._cardStyle.opacity = "0";
            this._restoreCard();
          });
        });

        break;

      case "up":
        this.swipeUpAnimation.play().then(async () => {
          await this.aclAction("skip").catch((e) =>
            this.debug.le("ACL skip error", e),
          );

          this.swipedUp().then(() => {
            this._cardStyle.opacity = "0";
            this._restoreCard();
          });
        });

        break;

      case "visit":
        this.swipeRightAnimation.play().then(async () => {
          this.swipedRight().then(async () => {
            this._cardStyle.opacity = "0";
            this._restoreCard();
            await Browser.open({ url: this.ciProfile["redirect"] });
          });
        });

        break;

      case "dismiss":
        this.swipeLeftAnimation.play().then(async () => {
          this.swipedLeft().then(() => {
            this._cardStyle.opacity = "0";
            this._restoreCard();
          });
        });

        break;

      default:
        this.restoreAnimation.play().then(() => {
          this._cardStyle.opacity = "1";
          this._restoreCard(0);
        });

        break;
    }
  }

  /**
   * Extract ACL functionality.
   *
   * @params {action} string
   * @returns Promise<void>
   */
  private aclAction(action: string): Promise<void> {
    return this.acl
      .init(action)
      .then((res) => {
        if (!res) this.profileSuggestions.undo();

        this[action]();
      })
      .catch((err) => this.profileSuggestions.undo());
  }

  private _restoreCard(timeout = 10, doTranslate = true) {
    setTimeout(() => {
      this.restoreAnimation.stop();

      this._cardStyle.transform = "none";
      this._cardStyle.opacity = "1";
    }, timeout);

    this.swipeLeftAnimation.stop();
    this.swipeRightAnimation.stop();
    this.swipeUpAnimation.stop();
    this.superlikeAnimation.stop();

    this.cardState = "";

    this.determineSwipeDirection();
  }

  private determineSwipeDirection(
    event = null,
  ): "left" | "right" | "up" | void {
    let res = null;

    if (event) {
      if (Math.abs(event.startX - event.currentX) > this.windowWidth / 4) {
        // was horizontal action
        res = event.deltaX < 0 ? "left" : "right";
      } else if (event.startY - event.currentY > this.windowHeight / 5) {
        res = "up";
      }
    }

    this.displaySwipeStatus(res);

    return res;
  }

  private _gStopX(event) {
    this.debug.l("stop", event);

    if (!this.determineSwipeDirection(event)) {
      this.actionHappened();
    } else {
      this.actionHappened(this.determineSwipeDirection(event));
    }
  }

  private _gMoveX(event) {
    this.determineSwipeDirection(event);

    this._cardStyle.transform = `translateX(${
      event.deltaX
    }px) rotate3d(0,0,-1,${-(event.deltaX / this.windowWidth) * 10}deg)`;
    this._cardStyle.opacity = `${
      1.2 - Math.abs(event.deltaX / this.windowWidth)
    }`;

    // fix for ios auto transition
    this._cardStyle.transitionDuration = "0s";
  }

  private _gStartY(event) {
    this.debug.l("starty", event);
  }

  private _gStopY(event) {
    this.debug.l("stop", event);

    if (!this.determineSwipeDirection(event)) {
      this.actionHappened();
    } else {
      this.actionHappened("up");
    }
  }

  private _gMoveY(event) {
    this.determineSwipeDirection(event);

    if (event.deltaY < 0) {
      this._cardStyle.transform = `translateY(${event.deltaY}px)`;
      this._cardStyle.opacity = `${
        1 - Math.abs(event.deltaY / this.windowHeight)
      }`;
    }

    // fix for ios auto transition
    this._cardStyle.transitionDuration = "0s";
  }

  // @todo use these to implement actions

  private displaySwipeStatus(
    direction:
      | "left"
      | "right"
      | "up"
      | "superlike"
      | "dismiss"
      | "visit"
      | "" = "",
  ) {
    /*
    let _c = {
      left: "red",
      right: "green",
      up: "blue",
    };
    // demo only - feedback about the event
    this._cardStyle.backgroundColor = _c[direction] || "white";
    */

    switch (direction) {
      case "right":
        this.cardState = "like";
        break;
      case "left":
        this.cardState = "pass";
        break;
      case "up":
        this.cardState = "later";
        break;
      case "superlike":
        this.cardState = "superlike";
        break;
      case "dismiss":
        this.cardState = "dismiss";
        break;
      case "visit":
        this.cardState = "visit";
        break;
      default:
        this.cardState = "";
    }

    // if the value changed, we force the component to re-compile
    if (this.previousCardState != this.cardState) {
      this.ngZone.run(() => {
        this.previousCardState = this.cardState;
      });
    }
  }

  private swipedLeft(): Promise<any> {
    return this.profileSuggestions.next();
    /*
    return new Promise((resolve) => {
      // test
      setTimeout(() => {
        resolve(null);
      }, 500);
    });
    */
  }

  private swipedRight(): Promise<any> {
    return this.profileSuggestions.next();
    /*
    return new Promise((resolve) => {
      // test
      setTimeout(() => {
        resolve(null);
      }, 500);
    });
    */
  }

  private swipedUp(): Promise<any> {
    return this.profileSuggestions.next();
  }

  // @todo add actual action to the stuff
  // you know call like/dislike/ etc functions
  // this is only for testing, and so someone can work on the templates properly

  // bind like and dislike
  public likePressed(profile) {
    this.debug.l("likepressed", profile);
    this.actionHappened("right");
  }

  public dislikePressed(profile) {
    this.debug.l("dislike", profile);
    this.actionHappened("left");
  }

  public undoPressed(profile) {
    this.acl.init("undo");
    this.profileSuggestions.undo();
  }

  public superlikePressed(profile) {
    this.debug.l("superlike", profile);
    this.actionHappened("superlike");
  }

  /**
   * Superlike profile.
   */
  private superlike(): void {
    firstValueFrom(
      this.profileService.superLike(
        this.profileSuggestions.currentProfile.username,
      ),
    ).catch((e) => {
      this.debug.le("profile superlike error", e);
    });
  }

  /**
   * Like profile.
   */
  private like(): void {
    firstValueFrom(
      this.profileService.like(this.profileSuggestions.currentProfile.username),
    ).catch((e) => {
      this.debug.le("profile like error", e);
    });
  }

  /**
   * Pass profile.
   */
  private pass(): void {
    firstValueFrom(
      this.profileService.pass(this.profileSuggestions.currentProfile.username),
    ).catch((e) => {
      this.debug.le("profile pass error", e);
    });
  }

  /**
   * Skip profile.
   */
  private skip(): void {
    firstValueFrom(
      this.profileService.skip(this.profileSuggestions.currentProfile.username),
    ).catch((e) => {
      this.debug.le("profile skip error", e);
    });
  }

  /**
   * Visit ads.
   */
  public visit() {
    this.debug.l("visit ads", this.ciProfile);
    this.actionHappened("visit");
  }

  /**
   * Dismiss ads.
   */
  public dismiss() {
    this.debug.l("dismiss ads", this.ciProfile);
    this.actionHappened("dismiss");
  }
}
