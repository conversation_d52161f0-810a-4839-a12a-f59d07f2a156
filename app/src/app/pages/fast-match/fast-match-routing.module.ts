import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { FastMatchPage } from "./fast-match.page";

const routes: Routes = [
  {
    path: "",
    component: FastMatchPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class FastMatchPageRoutingModule {}
