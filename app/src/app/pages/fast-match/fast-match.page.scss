ion-header {
  ion-title {
    ion-img {
      padding-top: 5px;
      padding-bottom: 5px;
    }
  }
}

ion-content {
 --background: var(--ion-color-silver);
}

.logoSvg {
  height: 36px;
}

.statusMessage {
  margin-top: 15vh;

  ion-icon {
    font-size: 7rem;
    opacity: .3;
  }

  h6 {
    color: var(--ion-color-medium);
  }
}

.nt-indicator {
  pointer-events: none;
  background-color: var(--toolbar-nt-indicator);
  width: 12px;
  height: 12px;
  border: 2px solid var(--toolbar-nt-indicator-border);
  border-radius: 50%;
  display: block;
  position: absolute;
  right: -5px;
  top: 0;

  &.for-menu {
    right: 10px;
    top: 15px;
  }
}

.ios .nt-indicator.for-menu {
  top:9px;
  right: 5px;
}

ion-card.loadingMatch {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  margin: 0;
  position: absolute;
  display: flex;
  flex-direction: column;

  ion-img {
    flex-grow: 1;
  }

  .skeleton-image {
    flex-grow: 1;

    ion-skeleton-text {
      height: 100%;
      margin: 0;
    }
  }

  ion-card-header {
    position: absolute;
    width: 100%;
    bottom: 0;
    top: auto;

    ion-skeleton-text {
      margin-left: auto;
      margin-right: auto;
    }

    ion-col.user-action-buttons-wrapper {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      margin-bottom: 8px; 

      ion-fab-button {
        margin: 8px;
        width: 74px;
        height: 74px;
        opacity: .5;

        ion-icon {
          font-size: 40px;
        }
      }
    }
  }
}

.no-profile-ico {
  font-size: 84px;
}

ion-icon.action-sheet-icon.sc-ion-action-sheet-md {
  margin: 10px !important;
}

