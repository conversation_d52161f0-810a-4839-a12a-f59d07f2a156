import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { FastMatchPageRoutingModule } from "./fast-match-routing.module";

import { FastMatchPage } from "./fast-match.page";

import { TranslateModule } from "@ngx-translate/core";
import { CardBaseComponentModule } from "src/app/components/card-base/card-base.component.module";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    FastMatchPageRoutingModule,
    CardBaseComponentModule,
  ],
  declarations: [FastMatchPage],
})
export class FastMatchPageModule {}
