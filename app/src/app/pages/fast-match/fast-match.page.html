<ion-content slot="fixed">
  <!-- loading container { -->
  <ion-card
    class="loadingMatch"
    [class.hidden]="(this.profileSuggestions.items$ | async)?.status != 'loading'"
  >
    <div class="skeleton-image">
      <ion-skeleton-text animated></ion-skeleton-text>
    </div>
    <ion-card-header class="action-buttons ion-no-padding ion-margin-bottom">
      <ion-grid class="ion-no-padding">
        <ion-row class="ion-text-center">
          <ion-col class="user-action-buttons-wrapper">
            <!--
            <ion-fab-button size="small" color="white">
              <ion-icon name="arrow-undo"></ion-icon>
            </ion-fab-button>
          -->
            <ion-fab-button color="white">
              <ion-icon name="close"></ion-icon>
            </ion-fab-button>
            <ion-fab-button class="superlike" color="white">
              <ion-icon name="star"></ion-icon>
            </ion-fab-button>
            <ion-fab-button color="white">
              <ion-icon name="heart"></ion-icon>
            </ion-fab-button>
            <!--
            <ion-fab-button size="small" color="white">
              <ion-icon name="ellipsis-horizontal"></ion-icon>
            </ion-fab-button>
          -->
          </ion-col>
        </ion-row>
        <ion-row class="ion-text-center">
          <ion-col>
            <ion-skeleton-text
              class="ion-margin-bottom"
              animated
              style="width: 65%"
            ></ion-skeleton-text>
            <ion-skeleton-text
              class="ion-margin-bottom"
              animated
              style="width: 40%"
            ></ion-skeleton-text>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card-header>
  </ion-card>
  <!-- } loading container -->

  <!-- error container { -->
  <ion-grid
    class="statusMessage ion-text-center"
    [class.hidden]="(this.profileSuggestions.items$ | async)?.status != 'error' && !this.wasNetworkError"
  >
    <ion-row>
      <ion-col>
        <ion-icon color="primary" name="alert-circle-outline"></ion-icon>
        <h3>{{ 'error_loading_fast_match' | translate }}</h3>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-button color="primary" (click)="this.profileSuggestions.refresh()"
          >{{ 'retry' | translate }}</ion-button
        >
      </ion-col>
    </ion-row>
  </ion-grid>
  <!-- } error container -->

  <!-- no more matches container { -->
  <ion-grid
    class="statusMessage ion-padding-horizontal ion-text-center"
    [class.hidden]="(this.profileSuggestions.items$ | async)?.status != 'no_more_results'"
  >
    <ion-row>
      <ion-col>
        <div class="no-profile-ico">😴</div>
        <h2>{{ 'no_more_matches' | translate }}</h2>
        <h6>{{ 'no_more_matches_subtext' | translate }}</h6>
      </ion-col>
    </ion-row>
    <ion-row class="ion-justify-content-center">
      <ion-col size="9" size-sm="5">
        <ion-button
          color="primary"
          class="ion-margin-vertical"
          expand="block"
          (click)="gotoMatchFilter()"
          >{{ 'filter_matches' | translate }}</ion-button
        >
        <ion-button color="primary" expand="block" (click)="gotoBrowseProfile()"
          >{{ 'browse_profiles' | translate }}</ion-button
        >
      </ion-col>
    </ion-row>
  </ion-grid>
  <!-- } no more matches container -->

  <!-- card container { -->
  <card-base #cardDivNext [profile]="(ni | async)" [offset]="4"></card-base>
  <card-base
    #cardDiv
    (onUndo)="undoPressed($event)"
    (onLike)="likePressed($event)"
    (onDislike)="dislikePressed($event)"
    (onSuperlike)="superlikePressed($event)"
    (onOpenProfile)="openProfile($event)"
    (onOpenGallery)="openGallery($event)"
    (onVisit)="visit()"
    (onDismiss)="dismiss()"
    [profile]="(ci | async)"
    [state]="this.cardState"
  ></card-base>
</ion-content>
