import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { TranslateModule } from "@ngx-translate/core";
import { PipesModule } from "src/app/pipes/pipes.module";

import { BillingHistoryPageRoutingModule } from "./billing-history-routing.module";
import { BillingHistoryPage } from "./billing-history.page";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    PipesModule,
    BillingHistoryPageRoutingModule,
  ],
  declarations: [BillingHistoryPage],
})
export class BillingHistoryPageModule {}
