<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ "title_billing_history" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content padding class="ion-margin">
  <ion-grid class="ion-margin ion-no-padding">
    <ion-row class="ion-no-padding">
      <ion-col class="ion-no-padding">
        <ion-text>{{ "billing_history_subtitle" | translate }}</ion-text>
      </ion-col>
    </ion-row>
  </ion-grid>

  <ion-grid
    class="table-view ion-margin"
    [class.hidden]="this.billingHistoryService.inProgress || this.billingHistoryService?.items?.length === 0"
    >
    <ion-row>
      <ion-col size="2" class="bg-color"
        >{{ "order_date" | translate }}</ion-col
        >
        <ion-col size="3" class="bg-color">{{ "status" | translate }}</ion-col>
        <ion-col size="2" class="bg-color">{{ "card_used" | translate }}</ion-col>
        <ion-col size="2" class="bg-color">{{ "price" | translate }}</ion-col>
        <ion-col size="3" class="bg-color"
          >{{ "subscription_type" | translate }}</ion-col
          >
        </ion-row>
        @for (item of this.billingHistoryService.items; track item) {
          <ion-row>
            <ion-col size="2">{{ item.ts * 1000 | date: 'shortDate' }}</ion-col>
            <ion-col size="3">{{ item | transactionType | translate }}</ion-col>
            <ion-col size="2">
              @if (item.cctype) {
                {{ item.cctype }}
              } @else {
                {{ item.processor_name }}
              }
            </ion-col>
            <ion-col size="2">
              @if (item.type == 'refund' || item.type == 'chargeback') {
                <span>
                  -
                </span>
              }
              {{ item.amount }} {{ item.currency }}
            </ion-col>
            <ion-col size="3">
              @if ( item.status == 'failed' ) {
                <span>-</span>
              }
              @if ( item.is_rebill) {
                @for (bitem of item.items; track bitem) {
                  <div>
                    {{ bitem.rebill_title }}<br />
                  </div>
                }
              } @else {
                @for (bitem of item.items; track bitem) {
                  <div>
                    {{ bitem.svc_title }}<br />
                  </div>
                }
              }
            </ion-col>
          </ion-row>
        }
      </ion-grid>

      <ion-item
        [class.hidden]="!this.billingHistoryService.inProgress"
        class="ion-text-center"
        >
        <ion-spinner name="crescent"></ion-spinner> {{ "loading_content" | translate
        }}
      </ion-item>

      <ion-grid
        class="ion-text-center"
        [class.hidden]="this.billingHistoryService.inProgress || this.billingHistoryService?.items?.length > 0"
        >
        <ion-row>
          <ion-col>
            <p>{{ 'no_billing_history' | translate }}</p>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-content>
