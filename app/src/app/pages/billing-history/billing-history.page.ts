import { Component, OnInit } from "@angular/core";
import { BillingHistoryService } from "src/app/services/billing-history.service";
import { BillingHistoryItem } from "src/app/schemas/billing-history";
@Component({
  selector: "app-billing-history",
  templateUrl: "./billing-history.page.html",
  styleUrls: ["./billing-history.page.scss"],
  standalone: false,
})
export class BillingHistoryPage implements OnInit {
  constructor(public billingHistoryService: BillingHistoryService) {}

  private _items;

  get items() {
    return this._items;
  }

  ngOnInit() {
    this.billingHistoryService.get();
  }
}
