ion-content {
  background-color: black;
   swiper-container {
    background-color: black;
    width: 100%;
    height: 100%;


    .swiper-slide {
      overflow: hidden;
    }
  }
  .full-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    z-index: 999; /* Ensures it stays on top of other content */
    opacity: 1; /* Start fully opaque */
    transition: opacity 1s ease-in-out; /* Smooth fade-out over 1 second */
  }
}

ion-header {
  ion-toolbar {
    --background: black;
    --border-color: transparent;
  }
  &::after {
    background: transparent;
  }
}

.statusMessage {
  margin-top: 10vh;

  ion-icon {
    font-size: 7rem;
    opacity: .3;
  }
}

ion-footer {
  background-image: var(--gl-gradient-bottom-to-top);
  padding-top: 40px;
  position: absolute;
  bottom: 0;
  left: 0;

  &:before {
    height: 0;
  }

  ion-button.liked-photo {

    ion-icon, span {
      color: #2B5DB2;
    }
  }
}
