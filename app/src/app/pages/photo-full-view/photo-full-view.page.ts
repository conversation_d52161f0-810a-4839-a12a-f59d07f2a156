import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hild,
  ElementRef,
} from "@angular/core";

import { ProfileUser } from "../../schemas/profile-user";
import { ActivatedRoute, Router } from "@angular/router";
import { StateService } from "src/app/services/state.service";
import _ from "lodash";
import { ProfileService } from "src/app/services/profile.service";
import { PhotoService } from "../../services/photo.service";
import { GalleryImage } from "../../schemas/gallery-image";
import { environment as Env } from "src/environments/environment";
import { firstValueFrom } from "rxjs";
import { AccountService } from "../../services/account.service";
import { Swiper } from "swiper";
import { Utils } from "src/app/helpers/utils";

@Component({
  selector: "app-photo-full-view",
  templateUrl: "./photo-full-view.page.html",
  styleUrls: ["./photo-full-view.page.scss"],
  standalone: false,
})
export class PhotoFullViewPage implements OnDestroy {
  @ViewChild("swiper") swiperRef: ElementRef | undefined;
  @ViewChild("fullOverlay", { static: true }) fullOverlay!: ElementRef;
  swiper: Swiper;

  public ionViewDidEnter(): void {
    Object.assign(this.swiperRef, this.swiperConfig);

    this.swiper = this.swiperRef?.nativeElement.swiper;
    this.swiper.on("slideChange", (event) => {
      this.updateImageIndex(event);
    });

    this.slideToIndex();
  }

  public swiperConfig: any = {
    zoom: {
      minRatio: 1,
      maxRatio: 3,
    },
    effect: "fade", // fade | cube | coverflow | flip | cards | creative
    fadeEffect: {
      crossFade: true,
    },
  };

  /**
   * UserProfile.
   */
  public profile: ProfileUser.type;

  /**
   * Photos.
   */
  public photos: GalleryImage.type[] = [];

  public selectedPhoto: any;

  public image: any;

  public pageType: "profile" | "photo_list";
  private username: string = "";
  private photoIndex: number;

  public openComments: boolean = false;
  public doEnableSwipe: boolean = true;
  public autoZoom: boolean = true;

  // @todo add comment section
  public toggleCommentsSection: boolean = false;

  /**
   * View did enter.
   */
  public didEnter: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private stateService: StateService,
    private profileService: ProfileService,
    private ngZone: NgZone,
    public photoService: PhotoService,
    public router: Router,
    public accountService: AccountService,
  ) {}

  init() {
    this.pageType =
      this.route.snapshot.params?.type == "profile" ? "profile" : "photo_list";

    this.username = this.route.snapshot.params?.username;

    if (this.photoIndex === undefined) {
      this.photoIndex = parseInt(this.route.snapshot.params?.index) || 0;
    }

    // do we want to open comments? ( add ?comments=true to end of url)
    this.openComments = this.route.snapshot.queryParams?.showComments == "1";

    // do we want to show only one image
    this.doEnableSwipe = this.route.snapshot.queryParams?.disableSwipe != "1";

    // do we want auto zoom?
    if (
      this.route.snapshot.queryParams?.disableAutoZoom == "1" ||
      Env?.GALLERY_DISABLE_AUTO_ZOOM
    ) {
      this.autoZoom = false;
    }
  }

  public async updateImageIndex(event) {
    this.photoIndex = event.realIndex ?? event[0].realIndex;
    await this.updateSelectedPhoto();
  }

  public async updateSelectedPhoto() {
    this.selectedPhoto = this.photos[this.photoIndex];

    this.ngZone.run(() => {});
  }

  public handlePinchZoomEvents(event) {
    if (event[1] < 1 && this.swiper.zoom.enabled) {
      this.swiper.zoom.disable();
      event[3].firstElementChild.style.transform = "translate3d(0px, 0px, 0px)";
      event[2].style.transform = "translate3d(0px, 0px, 0px) scale(1.1)";
      setTimeout(() => {
        this.swiper.zoom.enable();
      }, 3000);
    }

    let status: boolean = event[1] > 1;

    if (this.doEnableSwipe) {
      this.lockSwipes(status);
    }

    if (!status) {
      if (this.doEnableSwipe) {
        this.lockSwipes(this.toggleCommentsSection);
      }
    }
  }

  /**
   * Lock swiper.
   */
  private lockSwipes(status: boolean = true): void {
    this.swiper.allowSlideNext = !status;
    this.swiper.allowSlidePrev = !status;
    this.swiper.allowTouchMove = !status;
  }

  public hasData() {
    return !_.isEmpty(this.photos);
  }

  public isLoading() {
    return (
      !this.hasData() &&
      !this.wasError() &&
      this.profileService.isGetInProgress(this.username)
    );
  }

  public wasError() {
    return this.profileService.wasGetError(this.username);
  }

  public getPhotos() {
    return this.pageType == "profile" ? this.profile.photos : this.photos;
  }

  private getProfile(): Promise<any> {
    if (!_.isEmpty(this.profile)) {
      return Promise.resolve({ data: this.profile });
    } else {
      return firstValueFrom(this.profileService.get(this.username));
    }
  }

  public onSwiperInit(e) {
    // initialized before we have object, use with caution
  }

  ionViewWillEnter() {
    this.init();

    this.profile = this.stateService.get("profile", this.username);
    this.photos = this.stateService.get("pictureCollection", this.username);

    if (this.pageType == "profile") {
      // if it's a profile gallery, then we set it up that way
      this.getProfile().then((data) => {
        this.profile = data.data;
        this.setInitialState();
      });
    } else {
      this.setInitialState();
    }

    let photoComments = this.stateService.get("photo", "comments");

    if (photoComments) {
      this.profile.photos.forEach((photo) => {
        if (
          photo.image_id === photoComments.image_id &&
          photo.gallery_id === photoComments.gallery_id
        ) {
          photo.comments = photoComments.comments ?? photo.comments;
        }
      });
    }
  }

  ngOnDestroy() {
    this.stateService.delete("profile", this.username);
    this.stateService.delete("pictureCollection", this.username);
    this.stateService.delete("photo", "comments");
  }

  setInitialState() {
    this.didEnter = true;

    // Show only main_photo for free user.
    this.photos = this.getPhotos();

    if (
      !this.accountService.isPremium() &&
      this.accountService.account.user_id != this.profile.user_id &&
      this.pageType != "photo_list"
    ) {
      this.photos = _.filter(this.getPhotos(), (photo) => photo.main_photo);
    }

    this.updateSelectedPhoto();
  }

  private hideOverlay() {
    if (this.fullOverlay) {
      this.fullOverlay.nativeElement.style.opacity = "0";

      // Remove from view after fade-out completes
      setTimeout(() => {
        this.fullOverlay.nativeElement.style.display = "none";
      }, 300); // Same duration as the CSS transition (1 second)
    }
  }

  private slideToIndex() {
    if (this.photoIndex > 0) {
      Utils.retryUntil(
        () => this.photoIndex != this.swiper.activeIndex,
        () => {
          this.swiper.slideTo(this.photoIndex, 0);
          this.hideOverlay();
        },
        10,
        500,
      );
    } else {
      this.hideOverlay();
    }
  }

  /**
   * Render position dots based on pages object
   */
  public getDots(showNumber: boolean = false): string {
    let dots = "";

    // we don't need this if we have 1 photo
    if (this.photos.length <= 1 || !this.doEnableSwipe) {
      return "";
    }

    // show dots if we have multiple pages
    for (let i: number = 0; i < this.photos.length; i++) {
      dots += this.photoIndex === i ? "&#9679; " : "&#9675; ";
    }

    return dots;
  }

  /**
   * Like/unlike photo.
   *
   * @param photo
   */
  public likePhoto(photo: GalleryImage.type) {
    let command: string = photo.liked ? "unlike" : "like";
    this.photoService[command]({
      gallery_id: photo.gallery_id,
      image_id: photo.image_id,
      owner_id: this.profile.user_id,
    }).subscribe({
      next: (res) => {
        photo.liked = !photo.liked;
        photo.liked ? photo.likes++ : photo.likes--;
      },
    });
  }

  /**
   * Open photo comments page.
   *
   * @param photo
   */
  public async openPhotoComments(photo: GalleryImage.type) {
    this.stateService.set("photo", "comments", {
      gallery_id: photo.gallery_id,
      image_id: photo.image_id,
      owner_id: this.profile.user_id,
    });
    await this.router.navigate([
      "/app/photo-comments",
      this.profile.username,
      photo.gallery_id,
      photo.image_id,
    ]);
  }
}
