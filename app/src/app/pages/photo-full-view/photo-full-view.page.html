<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        text=""
        color="white"
        icon="chevron-back-sharp"
      ></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content fullscreen class="ion-no-padding" scrollbar-y="false">
  <div #fullOverlay class="full-overlay"></div>
  <swiper-container
    #swiper
    [config]="swiperConfig"
    (swiperzoomchange)="handlePinchZoomEvents($event)"
    [zoom]="true"
    (swiperafterinit)="onSwiperInit($event)"
    >
    @for (photo of photos; track photo; let i = $index) {
      <swiper-slide
        class="swiper-slide"
        >
        <div class="swiper-zoom-container">
          <img [src]="photo.image_url" />
        </div>
      </swiper-slide>
    }
  </swiper-container>

  <div
    [class.hidden]="!isLoading() || wasError()"
    style="text-align: center; padding-top: 40%"
    >
    <ion-spinner name="crescent"></ion-spinner>
  </div>

  <!-- error container { -->
  <ion-grid class="statusMessage ion-text-center" [class.hidden]="!wasError()">
    <ion-row>
      <ion-col>
        <ion-icon color="primary" name="sad"></ion-icon>
        <h3>{{ 'error_loading_gallery' | translate }}</h3>
      </ion-col>
    </ion-row>
  </ion-grid>
  <!-- } error container -->
</ion-content>

@if (pageType=='profile' && selectedPhoto) {
  <ion-footer class="ion-text-left">
    <ion-button
      size="large"
      fill="clear"
      color="white"
      (click)="likePhoto(selectedPhoto)"
      [class.liked-photo]="selectedPhoto.liked"
      >
      <ion-icon slot="start" name="thumbs-up"></ion-icon>
      <span color="white">{{ selectedPhoto.likes }}</span>
    </ion-button>
    <ion-button
      size="large"
      fill="clear"
      color="white"
      (click)="openPhotoComments(selectedPhoto)"
      >
      <ion-icon slot="start" name="chatbox"></ion-icon>
      <span color="white">{{ selectedPhoto.comments }}</span>
    </ion-button>
  </ion-footer>
}
