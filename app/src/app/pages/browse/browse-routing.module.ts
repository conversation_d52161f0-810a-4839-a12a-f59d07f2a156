import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { BrowsePage } from "./browse.page";

const routes: Routes = [
  {
    path: "",
    component: BrowsePage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BrowsePageRoutingModule {}
