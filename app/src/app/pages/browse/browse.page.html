<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
      <span
        [class.hidden]="!unreadCountService.hasInbox"
        class="nt-indicator for-menu"
      ></span>
    </ion-buttons>
    <ion-title> {{ 'title_browse' | translate }} </ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" [routerLink]="'/app/inbox'"
        ><ion-icon name="mail"></ion-icon>
        <span [class.hidden]="!this.unreadCountService.hasInbox" class="nt-indicator">
        </span>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <app-push-notifications-popup
    [(doShow)]="this.pushNotificationService.showNotificationPopup"
    (onSuccess)="this.pushNotificationService.onDialogSuccess()"
    (onCancel)="this.pushNotificationService.onDialogCancel()"
  ></app-push-notifications-popup>

  <app-happy-hour></app-happy-hour>

  <ion-refresher slot="fixed" (ionRefresh)="this.listPaginator.refresh($event)">
    <ion-refresher-content refreshingSpinner="crescent"></ion-refresher-content>
  </ion-refresher>

  <app-introduction-popup
    [doShow]="reminderIntroduction.introduction.doShow"
    [isValentines]="reminderIntroduction.isValentines"
  ></app-introduction-popup>
  <!-- filter selector { -->
  <ion-list lines="none">
    <ion-item class="distance-sort">
      <ion-select
        label="{{'browse_by' | translate }}"
        value="nearby"
        (ionChange)="listPaginator.changeFilter({command: $event.detail.value})"
        interface="popover"
        [interfaceOptions]="{'cssClass': 'distance-popover'}"
        class="ion-no-padding"
        >
        <ion-select-option value="nearby"
          >{{ 'near_me' | translate }}</ion-select-option
          >
          <ion-select-option value="all"
            >{{ 'all' | translate}}</ion-select-option
            >
            <ion-select-option value="new"
              >{{ 'new' | translate }}</ion-select-option
              >
              <ion-select-option value="activity"
                >{{ 'recent_activity' | translate }}</ion-select-option
                >
              </ion-select>
            </ion-item>
          </ion-list>
          <!-- } filter selector -->

          <!-- empty container { -->
          <ion-grid
            class="statusMessage ion-text-center"
            [class.hidden]="!this.listPaginator.isEmpty()"
            >
            <ion-row>
              <ion-col>
                <ion-icon color="primary" name="heart-dislike-outline"></ion-icon>
                <h2>{{ 'no_featured_users_title' | translate }}</h2>
                <h6>{{ 'no_featured_users_description' | translate }}</h6>
              </ion-col>
            </ion-row>
          </ion-grid>
          <!-- } empty container -->

          <!-- loading container { -->
          @if (profileService.listBrowse.list$ | async; as listBrowse) {
            <ion-grid
              [class.hidden]="this.profileService.listBrowse.wasError()"
              class="browse-profiles-wrapper"
              >
              <ion-row>
                @for (profile of listBrowse; track profile; let i = $index) {
                  @if (showAdAtPosition(i) > -1) {
                    <ion-col size="6" size-md="3" >
                      <ion-card class="ion-no-margin active"
                        href="{{ this.ads[showAdAtPosition(i)].redirect }}">
                        <div class="distance">{{ 'browse_ad' | translate }}</div>
                        <div class="img-placeholder">
                          <ion-img
                            [src]="this.ads[showAdAtPosition(i)].image"
                            style="pointer-events: none"
                          ></ion-img>
                        </div>
                        <ion-card-header class="ad">
                          <ion-card-title color="dark">
                            <span class="status-indicator ads-indicator"></span>
                            {{ this.ads[showAdAtPosition(i)].title }}
                          </ion-card-title>
                          <ion-text>
                            @if (profile.city) {
                              <span>{{ this.ads[showAdAtPosition(i)].description }}</span>
                            }
                          </ion-text>
                        </ion-card-header>
                      </ion-card>
                    </ion-col>
                  }
                  <ion-col
                    size="6"
                    size-md="3"
                    [attr.item-start]="i"
                    [class.hidden]="profile?.do_hide"
                    >
                    <ion-card
                      [ngClass]="profile.is_active ? 'active' : 'inactive'"
                      class="ion-no-margin browse-profile"
                      [routerLink]="[ '/app/profile', profile.username ]"
                      >
                      <div class="distance">{{ profile.distance | distance }}</div>
                      <div class="img-placeholder">
                        <ion-img
                          [src]="profile.avatar_url"
                          style="pointer-events: none"
                        ></ion-img>
                      </div>
                      <ion-card-header>
                        <ion-card-title color="dark">
                          <span class="status-indicator"></span>
                          {{ (profile.first_name || profile.username) | slice:0:14 }}, {{
                          profile.age }}
                        </ion-card-title>
                        <ion-text>
                          @if (profile.city) {
                            <span>{{ profile.city }},</span>
                            } {{
                            profile.country === 'US' ? profile.state : profile.country }}
                          </ion-text>
                        </ion-card-header>
                      </ion-card>
                    </ion-col>
                  }
                </ion-row>
              </ion-grid>
            }
            <!-- } loading container -->

            <ion-infinite-scroll
              [class.hidden]="this.listPaginator.didReachEnd()"
              threshold="100px"
              id="infinite-scroll"
              (ionInfinite)="this.listPaginator.next($event)"
              >
              <ion-infinite-scroll-content loadingSpinner="crescent">
                <!-- sceleton content before loading { -->
                <ion-grid class="ion-text-start" [class.hidden]="listPaginator.didLoad()">
                  @for (_r of [].constructor(3); track _r) {
                    <ion-row>
                      @for (_c of [].constructor(4); track _c) {
                        <ion-col size="6" size-md="3">
                          <ion-card class="ion-no-margin">
                            <ion-skeleton-text
                              style="width: 20%"
                              class="ion-float-left ion-margin"
                              animated
                            ></ion-skeleton-text>
                            <ion-thumbnail class="img-placeholder">
                              <ion-skeleton-text></ion-skeleton-text>
                            </ion-thumbnail>
                            <ion-card-header class="skeleton">
                              <ion-card-title>
                                <ion-skeleton-text
                                  animated
                                  style="width: 40%"
                                  class="ion-float-left"
                                ></ion-skeleton-text>
                                <span class="status-indicator"></span>
                              </ion-card-title>
                              <ion-text>
                                <ion-skeleton-text
                                  animated
                                  style="width: 60%"
                                ></ion-skeleton-text>
                              </ion-text>
                            </ion-card-header>
                          </ion-card>
                        </ion-col>
                      }
                    </ion-row>
                  }
                </ion-grid>
                <!-- } end sceleton content -->
              </ion-infinite-scroll-content>
            </ion-infinite-scroll>

            <!-- error container { -->
            <ion-grid
              class="statusMessage ion-text-center"
              [class.hidden]="!this.profileService.listBrowse.wasError()"
              >
              <ion-row>
                <ion-col>
                  <ion-icon color="primary" name="alert-circle-outline"></ion-icon>
                  <h3>{{ 'error_list' | translate }}</h3>
                </ion-col>
              </ion-row>
              <ion-row>
                <ion-col>
                  <ion-button color="primary" (click)="this.listPaginator.refresh()"
                    >{{ 'refresh' | translate }}</ion-button
                    >
                  </ion-col>
                </ion-row>
              </ion-grid>
              <!-- } error container -->

              <div
                style="text-align: center; display: none"
                [class.hidden]="!this.listPaginator.didReachEnd()"
                >
                ¯\_(ツ)_/¯
              </div>
            </ion-content>
