import { Component, OnInit } from "@angular/core";
import { ProfileService } from "../../services/profile.service";
import { ListPaginator } from "src/app/services/lists/list-paginator";
import { Debug } from "src/app/helpers/debug";
import { PaginatorHelperService } from "src/app/services/paginator-helper.service";
import { UnreadCountService } from "src/app/services/unread-count.service";
import { Router } from "@angular/router";
import { PushNotificationService } from "src/app/services/push-notification.service";
import { ReminderIntroductionService } from "../../services/reminder-introduction.service";
import { AdsService } from "src/app/services/ads.service";
import { waitForAsync } from "@angular/core/testing";
import { AccountService } from "src/app/services/account.service";
import { Subscription } from "rxjs";

@Component({
  selector: "app-browse",
  templateUrl: "./browse.page.html",
  styleUrls: ["./browse.page.scss"],
  standalone: false,
})
export class BrowsePage {
  public listPaginator: ListPaginator;
  public ads = [];
  public profile_ads_place = 11;

  /**
   * List subscription.
   */
  private listSub;

  constructor(
    public profileService: ProfileService,
    private paginatorHelper: PaginatorHelperService,
    public unreadCountService: UnreadCountService,
    public router: Router,
    public pushNotificationService: PushNotificationService,
    public reminderIntroduction: ReminderIntroductionService,
    public adsService: AdsService,
    private accountService: AccountService,
  ) {
    this.listPaginator = this.paginatorHelper.browsePaginator;
  }

  ionViewWillEnter() {
    // Wait until the list loads and after that fetch ads.
    this.listSub = this.listPaginator.didLoad$.subscribe({
      next: (res: boolean) => {
        if (res) {
          this.ads = this.adsService.adDataArr;
          this.listSub.unsubscribe();
        }
      },
    });
  }

  public showAdAtPosition(position: number): number {
    if (
      position / this.profile_ads_place > this.ads.length ||
      position % this.profile_ads_place != 0 ||
      !position
    ) {
      return -1;
    }
    let index: number;
    let indexS: string;
    indexS = (position / this.profile_ads_place).toFixed(0);
    index = parseInt(indexS) - 1;
    return index;
  }
}
