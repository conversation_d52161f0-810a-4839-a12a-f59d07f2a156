import { ComponentFixture, TestBed, waitForAsync } from "@angular/core/testing";
import { IonicModule } from "@ionic/angular";

import { BrowsePage } from "./browse.page";
import { LoaderService } from "../../services/loader.service";

describe("BrowsePage", () => {
  let component: BrowsePage;
  let fixture: ComponentFixture<BrowsePage>;
  let loaderSpy: LoaderService;

  beforeEach(
    waitForAsync(() => {
      loaderSpy = jasmine.createSpyObj("LoaderService", [
        "storeLoaderStatus",
        "isLoading",
      ]);

      TestBed.configureTestingModule({
        declarations: [BrowsePage],
        imports: [IonicModule.forRoot()],
        providers: [{ provide: LoaderService, useValue: loaderSpy }],
      }).compileComponents();

      fixture = TestBed.createComponent(BrowsePage);
      component = fixture.componentInstance;
      fixture.detectChanges();
    })
  );

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
