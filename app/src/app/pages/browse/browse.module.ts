import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { BrowsePageRoutingModule } from "./browse-routing.module";
import { BrowsePage } from "./browse.page";

import { TranslateModule } from "@ngx-translate/core";
import { PipesModule } from "src/app/pipes/pipes.module";
import { HappyHourComponentModule } from "../../components/happy-hour/happy-hour.component.module";
import { PushNotificationsPopupComponentModule } from "../../components/push-notifications-popup/push-notifications-popup.component.module";
import { IntroductionPopupComponentModule } from "../../components/introduction-popup/introduction-popup.component.module";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    BrowsePageRoutingModule,
    PipesModule,
    HappyHourComponentModule,
    PushNotificationsPopupComponentModule,
    IntroductionPopupComponentModule,
  ],
  declarations: [BrowsePage],
})
export class BrowsePageModule {}
