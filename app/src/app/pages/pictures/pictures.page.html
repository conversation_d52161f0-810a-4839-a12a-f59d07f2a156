<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        text=""
        icon="chevron-back-sharp"
        (click)="resetSelection()"
      ></ion-back-button>
    </ion-buttons>
    <ion-title>{{ "title_pictures" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-fab vertical="top" horizontal="end" slot="fixed" class="add-attachment">
  <ion-fab-button (click)="displayUploadMenu()">
    <ion-icon name="add"></ion-icon>
  </ion-fab-button>
</ion-fab>

<ion-content>
  <ion-grid class="my-profile-photos">
    <ion-row>
      @for (img of images; track img) {
        <ion-col size="6" size-md="3">
          <ion-img
            [src]="img.thumb_url"
            class="user-signup-img"
            (click)="toggleImgSelect(img)"
            [class.selected]="img['selected']"
            [class.delete]="img['delete']"
            [id]="img.image_id"
            longPress
            (onLongPressEnd)="active(img)"
          ></ion-img>
        </ion-col>
      }
    </ion-row>
  </ion-grid>
</ion-content>

<ion-footer>
  <ion-toolbar>
    <ion-button
      expand="block"
      class="ion-margin-horizontal"
      (click)="navCtrl.back()"
      >{{'set_attachments' | translate}}</ion-button
      >
    </ion-toolbar>
  </ion-footer>
