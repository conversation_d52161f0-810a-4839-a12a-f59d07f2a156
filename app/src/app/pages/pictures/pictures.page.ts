import { Component, OnInit } from "@angular/core";
import { GalleryImage } from "../../schemas/gallery-image";
import { Debug } from "../../helpers/debug";
import { CameraSource } from "@capacitor/camera";
import { CameraService } from "../../services/camera.service";
import { ChatMessageService } from "../../services/chat-message.service";
import {
  ActionSheetController,
  ActionSheetOptions,
  NavController,
} from "@ionic/angular";
import { TranslateService } from "@ngx-translate/core";
import { MiscService } from "../../services/misc.service";
import { PictureService } from "../../services/picture.service";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { StateService } from "../../services/state.service";
import { AccountService } from "../../services/account.service";
import { PictureUploadHelperService } from "../../picture-upload-helper.service";
import _ from "lodash";
import { ApiService } from "../../services/api/api.service";
import { firstValueFrom } from "rxjs";

@Component({
  selector: "app-pictures",
  templateUrl: "./pictures.page.html",
  styleUrls: ["./pictures.page.scss"],
  standalone: false,
})
export class PicturesPage implements OnInit {
  /**
   * Images.
   */
  public images: GalleryImage.type[] = [];

  /**
   * Photo data (base64).
   *
   * @type {string}
   */
  protected photo: any = "";

  /**
   * Processing image upload.
   *
   * @type {boolean}
   */
  protected processingPhoto: boolean = false;

  /**
   * Is action sheet active?
   *
   * @type {boolean}
   */
  protected isActionSheetCtrlActive: boolean = false;

  private openByDefault: string = "";
  private preSelect: GalleryImage.type[] = [];
  public isLoading: boolean = false;

  constructor(
    public dbg: Debug,
    public camera: CameraService,
    public messageService: ChatMessageService,
    public navCtrl: NavController,
    public translate: TranslateService,
    public actionSheetCtrl: ActionSheetController,
    public misc: MiscService,
    public pictureService: PictureService,
    public dlg: DialogHelperService,
    public stateService: StateService,
    public accountService: AccountService,
    public pictureUploadHelper: PictureUploadHelperService,
    public api: ApiService,
  ) {}

  async ngOnInit() {
    await this.dlg.showLoading();

    this.messageService
      .getAttachableImages()
      .then((data) => {
        this.images = data ?? [];
        this.images.map((img) => (img["selected"] = false));

        // pre-select images
        if (this.messageService.selectedAttachImages.length) {
          this.setSelection();
        }
      })
      .catch((error) => this.dbg.le("get attachable images error", error))
      .then(() => this.dlg.hideLoading());
  }

  /**
   * when view did enter
   */
  public async ionViewDidEnter() {
    switch (this.openByDefault) {
      case "image":
        this.uploadPhoto(CameraSource.Photos);
        break;
      case "camera":
        this.uploadPhoto(CameraSource.Camera);
        break;
    }
  }

  /**
   * set selected images if any
   */
  private setSelection(): void {
    let preSelect: GalleryImage.type[] =
      this.messageService.selectedAttachImages;
    if (preSelect.length) {
      let ids = preSelect.map(({ image_id }) => "" + image_id);

      this.images.forEach((img) => {
        if (ids.indexOf("" + img.image_id) >= 0) {
          img["selected"] = true;
        }
      });
    }
  }

  /**
   * Reset selection.
   */
  public resetSelection(): void {
    this.images.forEach((img) => (img["selected"] = false));
  }

  /**
   * Handle long-press action.
   *
   * @param {GalleryImage} img
   */
  public async active(img: GalleryImage.type) {
    img["delete"] = true;

    let config: ActionSheetOptions = {
      header: this.translate.instant("delete_photo"),
      buttons: [
        {
          text: this.translate.instant("yes"),
          handler: () => this.deleteImg(img),
        },
        {
          text: this.translate.instant("no"),
          role: "cancel",
          handler: () => {
            this.isActionSheetCtrlActive = false;
            img["delete"] = false;
          },
        },
      ],
    };

    if (!this.isActionSheetCtrlActive) {
      let ac = await this.actionSheetCtrl.create(config);

      ac.present().then(() => {
        this.isActionSheetCtrlActive = true;
      });
    }
  }

  /**
   * Toggle image select.
   *
   * @param {GalleryImage} img
   * @return {PicturesPage}
   */
  public toggleImgSelect(img: GalleryImage.type) {
    img["selected"] = !img["selected"];

    return this;
  }

  /**
   * Upload Photo function
   *
   * @param {CameraSource.Photos} sourceType
   */
  protected uploadPhoto(sourceType: CameraSource.Photos | CameraSource.Camera) {
    this.camera
      .getPicture(sourceType)
      .then((data) => {
        this.photo = data.base64String;

        return this.savePhoto();
      })
      .catch((e) => this.dbg.le("Photo upload failed", e));
  }

  /**
   * Save photo.
   *
   * @return {Promise<never | ApiResponses.GalleryImageResponse>}
   */
  private savePhoto() {
    this.processingPhoto = true;

    return firstValueFrom(
      this.pictureService.uploadAttachment({
        image_data: this.photo,
      }),
    )
      .then(({ data }) => {
        // select uploaded photo by default
        data.photo["selected"] = true;

        if (!_.some(this.images, data.photo)) {
          this.images.push(data.photo);
        }

        this.processingPhoto = false;
      })
      .catch((e) => {
        this.dlg.showToastBaseOnErrorResponse(e);
        this.processingPhoto = false;
      });
  }

  /**
   * Delete image function
   *
   * @param imageId
   */
  protected deleteImg(img: GalleryImage.type) {
    this.processingPhoto = true;
    this.toggleImgSelect(img);

    firstValueFrom(
      this.pictureService.deleteAttachment({
        image_id: img.image_id,
      }),
    )
      .then(() => {
        this.images = this.images.filter((val) => {
          return val.image_id != img.image_id;
        });

        this.processingPhoto = false;
        this.isActionSheetCtrlActive = false;
      })
      .catch((errors) => {
        this.dlg.showToastBaseOnErrorResponse(errors);
        this.processingPhoto = false;
      });
  }

  /**
   * Upload photo.
   */
  public displayUploadMenu() {
    this.pictureUploadHelper.displayUploadMenu(async (data) => {
      await this.dlg.showLoading();
      this.photo = data;
      await this.savePhoto();
      await this.dlg.hideLoading();
    });
  }

  /**
   * Is command in progress?
   *
   * @param cmd string
   */
  public isInProgress(cmd: string) {
    return !!this.api.isInEventList({ command: cmd });
  }
}
