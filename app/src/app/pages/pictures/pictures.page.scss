.add-attachment {
  margin-top: constant(safe-area-inset-top) !important;
  margin-top: env(safe-area-inset-top) !important;
}

.my-profile-photos {
  .user-signup-img {
    border-radius: 8px;
    overflow: hidden;

    &.selected {
      border: 5px solid var(--ion-color-success);
    }

    &.delete {
      border: 5px solid var(--ion-color-danger);
    }
  }

  .upload-button {
    background-color: #e6e6e6;
    border-radius: 20px;
    height: 155px;
    width: 155px;
    padding: 0;

    .button-inner {
      font-size: 5em;
    }
  }
}
