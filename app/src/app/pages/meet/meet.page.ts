import { Component, OnInit } from "@angular/core";
import { UnreadCountService } from "../../services/unread-count.service";
import { NavigationEnd, Router } from "@angular/router";

@Component({
  selector: "app-meet",
  templateUrl: "./meet.page.html",
  styleUrls: ["./meet.page.scss"],
  standalone: false,
})
export class MeetPage implements OnInit {
  public title: string = "";

  constructor(
    public unreadCountService: UnreadCountService,
    public router: Router,
  ) {}

  ngOnInit() {
    this.router.events.subscribe((event: NavigationEnd) => {
      if (event instanceof NavigationEnd) {
        this.title = "title-" + this.router.url.split("/").pop();
      }
    });
  }
}
