<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
      <span
        [class.hidden]="!this.unreadCountService.hasInbox"
        class="nt-indicator for-menu"
      >
      </span>
    </ion-buttons>
    <ion-title> {{ title | translate }} </ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" [routerLink]="'/app/inbox'"
        ><ion-icon name="mail"></ion-icon>
        <span [class.hidden]="!this.unreadCountService.hasInbox" class="nt-indicator">
        </span>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-tabs>
    <ion-tab-bar
      style="padding: 0px !important"
      slot="top"
      class="tab-nav ion-tabs-top"
    >
      <ion-tab-button tab="fast-match">
        {{'tabs_meet' | translate}}
      </ion-tab-button>
      <ion-tab-button tab="liked-us">
        {{'tabs_likes_me' | translate}}
      </ion-tab-button>
      <ion-tab-button tab="matches">
        {{'tabs_mutual' | translate}}
      </ion-tab-button>
      <ion-tab-button tab="we-liked">
        {{'tabs_i_like' | translate}}
      </ion-tab-button>
    </ion-tab-bar>
  </ion-tabs>
</ion-content>
