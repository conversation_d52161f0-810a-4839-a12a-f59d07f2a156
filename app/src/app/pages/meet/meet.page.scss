.nt-indicator {
  pointer-events: none;
  background-color: var(--toolbar-nt-indicator);
  width: 12px;
  height: 12px;
  border: 2px solid var(--toolbar-nt-indicator-border);
  border-radius: 50%;
  display: block;
  position: absolute;
  right: -5px;
  top: 0;

  &.for-menu {
    right: 10px;
    top: 15px;
  }
}

.ios .nt-indicator.for-menu {
  top:9px;
  right: 5px;
}

.tab-nav {
  --background: #FFF;
  --background-focused: #FFF;
  border: 0;

  ion-tab-button {
    --padding-start: 8px;
    --padding-end: 8px;
    font-size: 13px;
    font-weight: 600;
    position: relative;
    &:after {
      background-color: var(--ion-color-silver-shade);
      width: 100%;
      height: 1px;
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }

  .tab-selected {
    &:after {
      background-color: var(--ion-color-primary);
      height: 3px;
    }
  }
}