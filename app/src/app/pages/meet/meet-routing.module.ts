import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { MeetPage } from "./meet.page";

const routes: Routes = [
  {
    path: "",
    component: MeetPage,
    children: [
      {
        path: "fast-match",
        loadChildren: () =>
          import("../fast-match/fast-match.module").then(
            (m) => m.FastMatchPageModule
          ),
      },
      {
        path: "liked-us",
        loadChildren: () =>
          import("../liked-us/liked-us.module").then(
            (m) => m.LikedUsPageModule
          ),
      },
      {
        path: "matches",
        loadChildren: () =>
          import("../matches/matches.module").then((m) => m.MatchesPageModule),
      },
      {
        path: "we-liked",
        loadChildren: () =>
          import("../we-liked/we-liked.module").then(
            (m) => m.WeLikedPageModule
          ),
      },
      {
        path: "",
        redirectTo: "/app/tabs/meet/fast-match",
        pathMatch: "full",
      },
    ],
  },
  {
    path: "",
    redirectTo: "/app/tabs/meet/fast-match",
    pathMatch: "full",
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MeetPageRoutingModule {}
