import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { GoPremiumPage } from "./go-premium.page";

const routes: Routes = [
  {
    path: "",
    component: GoPremiumPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class GoPremiumPageRoutingModule {}
