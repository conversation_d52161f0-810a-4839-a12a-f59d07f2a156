import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  On<PERSON>nit,
  ViewChild,
  ElementRef,
} from "@angular/core";
import { DeviceInfoService } from "src/app/services/device-info.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { PurchaseService } from "src/app/services/purchase.service";
import { environment as Env } from "../../../../environments/environment";
import { StateService } from "../../../services/state.service";
import { ProfileUser } from "../../../schemas/profile-user";
import { TranslateService } from "@ngx-translate/core";
import { IapItem } from "src/app/services/iap-item.interface";
import { NavController, Platform } from "@ionic/angular";
import { RefresherService } from "../../../services/refresher.service";

@Component({
  selector: "app-go-premium",
  templateUrl: "./go-premium.page.html",
  styleUrls: ["./go-premium.page.scss"],
  standalone: false,
})
export class GoPremiumPage implements OnInit, OnDestroy {
  //@ViewChild("ionSlides", { static: false }) Slide: ElementRef | undefined;

  public sendMessageProfile: ProfileUser.type;
  public profileFullSizeLimit: ProfileUser.type;

  private time;
  public displayTime;

  constructor(
    public purchaseService: PurchaseService,
    private navController: NavController,
    private dialog: DialogHelperService,
    private deviceInfo: DeviceInfoService,
    private platform: Platform,
    private stateService: StateService,
    private translateService: TranslateService,
    private refresherService: RefresherService,
  ) {}

  private sub = null;

  ngOnDestroy() {
    this.stateService.delete("sendMessageProfile", null);
    this.stateService.delete("profileFullSizeLimit", null);

    // reset dialog type
    this.purchaseService.setDialogType("");
  }

  async ionViewWillEnter() {
    this.sendMessageProfile = this.stateService.get("sendMessageProfile", null);
    this.profileFullSizeLimit = this.stateService.get(
      "profileFullSizeLimit",
      null,
    );
  }

  ionViewDidEnter() {
    // disable back button for the view
    this.sub = this.platform.backButton.subscribeWithPriority(999, () => {
      this.close();
    });
  }

  get dialogType() {
    return this.purchaseService.dialogType;
  }

  ionViewWillLeave() {
    // enable when exiting
    this.sub.unsubscribe();

    // reset dialog type
    this.purchaseService.setDialogType("");
  }

  public inProgress() {
    return this.purchaseService.inProgress();
  }

  get env() {
    return Env;
  }

  public data: IapItem[];

  public selectedItem: IapItem;

  public selectProduct(item: IapItem) {
    this.selectedItem = item;
  }

  public isIos() {
    return this.deviceInfo.platform == "ios";
  }

  ngOnInit() {
    if (this.purchaseService.subscriptions.length == 0) {
      // initialize mock if page opened directly for design ( for example )
      this.dialog.showToast(
        "",
        "DEV_ONLY: Dialog was not initialized properly, using mock data!",
        "error",
      );
      this.purchaseService.init_MOCK();
    }

    // center item as default
    this.selectProduct(
      this.purchaseService.subscriptions[1] ||
        this.purchaseService.subscriptions[0],
    );

    this.data = this.purchaseService.subscriptions;

    if (this.dialogType === "like_limit_reached") {
      this.setTime();
      this.timerTick();
    }
  }

  public upgradeClicked() {
    this.purchaseService.buySubscription(this.selectedItem.store_id);
    this.refresherService.refreshEnd$.subscribe(() => this.navController.pop());
  }

  public close() {
    this.navController.pop().then(() => {
      this.purchaseService.executeCloseCallback();
    });
  }

  /**
   * Set time.
   */
  private setTime() {
    let d = new Date();
    let h = d.getHours();
    let m = d.getMinutes();
    let s = d.getSeconds();
    let secondsUntilEndOfDate = 24 * 60 * 60 - h * 60 * 60 - m * 60 - s;
    this.time = secondsUntilEndOfDate;
  }

  /**
   * Set seconds as digital clock.
   */
  private secondsAsDigitalClock(): void {
    let hours = Math.floor(this.time / 60 / 60);
    let minutes = Math.floor(this.time / 60) - hours * 60;
    let seconds = this.time % 60;
    let hoursString = "";
    let minutesString = "";
    let secondsString = "";
    hoursString = hours < 10 ? "0" + hours : hours.toString();
    hoursString = hoursString == "00" ? "" : hoursString + ":";
    minutesString = minutes < 10 ? "0" + minutes : minutes.toString();
    secondsString = seconds < 10 ? "0" + seconds : seconds.toString();
    this.displayTime = hoursString + minutesString + ":" + secondsString;
  }

  /**
   * Time tick.
   */
  private timerTick(): void {
    setTimeout(() => {
      this.time--;

      this.secondsAsDigitalClock();

      if (this.time > 0) this.timerTick();
    }, 1000);
  }
}
