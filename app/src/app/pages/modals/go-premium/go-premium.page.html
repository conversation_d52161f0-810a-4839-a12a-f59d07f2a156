<ion-content>
  <ion-grid class="ion-text-center ion-no-padding">
    <ion-row class="ion-text-center try-premium-title-wrapper">
      <ion-col>
        <ion-icon
          (click)="close()"
          size="large"
          name="close-outline"
          class="ion-hide-lg-up ion-padding-top ion-padding-start ion-float-left close-outline"
        ></ion-icon>
        <ion-text>
          <h1 class="try-premium-title">
            {{ 'title_go_premium' | translate }}
          </h1>
        </ion-text>
      </ion-col>
    </ion-row>

    <ion-row class="features-list">
      <ion-col class="ion-padding-horizontal">
        <swiper-container
          pagination="false"
          class="ion-flex-direction-row"
          #ionSlides
          >
          <!-- FREE LIKES -->
          @if (dialogType == 'like_limit_reached') {
            <swiper-slide>
              <ion-row>
                <ion-col>
                  <ion-icon
                    src="assets/icons/likes-limits-pin.svg"
                    color="danger"
                    size="large"
                    class="custom-icon"
                  ></ion-icon>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <h4 class="features-list--color">
                      {{ "slide_premium_unlock_title_limit_likes" | translate }}
                    </h4>
                  </ion-text>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <p class="p--size features-list--color"><strong>
                      {{ "slide_premium_unlock_message_1_limit_likes" | translate
                      }}
                    </strong></p>
                    <h1 class="features-list--color ion-no-margin"><strong>{{ displayTime }}</strong></h1>
                    <p class="p--size features-list--color ion-padding-top">
                      {{ "slide_premium_unlock_message_2_limit_likes" | translate
                      }}
                    </p>
                  </ion-text>
                </ion-col>
              </ion-row>
            </swiper-slide>
          }

          <!-- COMMENTS LIMIT -->
          @if (dialogType == 'photo_comments_limit') {
            <swiper-slide>
              <ion-row>
                <ion-col>
                  <ion-icon
                    src="assets/icons/ico-unlock-features.svg"
                    color="danger"
                    size="large"
                    class="custom-icon"
                  ></ion-icon>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <h4 class="features-list--color">
                      {{ "slide_premium_photo_comments_limit_title" | translate }}
                    </h4>
                  </ion-text>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <p class="p--size features-list--color">
                      {{ "slide_premium_photo_comments_limit_message" | translate
                      }}
                    </p>
                  </ion-text>
                </ion-col>
              </ion-row>
            </swiper-slide>
          }

          <!-- UNLOCK ALL FEATURES -->
          @if (!dialogType) {
            <swiper-slide>
              <ion-row>
                <ion-col>
                  <ion-icon
                    src="assets/icons/ico-unlock-features.svg"
                    color="danger"
                    size="large"
                    class="custom-icon"
                  ></ion-icon>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <h4 class="features-list--color">
                      {{ "slide_premium_unlock_title" | translate }}
                    </h4>
                  </ion-text>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <p class="p--size features-list--color">
                      {{ "slide_premium_unlock_message" | translate }}
                    </p>
                  </ion-text>
                </ion-col>
              </ion-row>
            </swiper-slide>
          }

          <!-- FLIRTS LIMIT -->
          @if (dialogType == 'flirt_limit') {
            <swiper-slide>
              <ion-row>
                <ion-col>
                  <ion-icon
                    src="assets/icons/ico-flirt.svg"
                    color="danger"
                    size="large"
                    class="custom-icon"
                  ></ion-icon>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <h4 class="features-list--color">
                      {{ "slide_premium_unlock_title_flirt" | translate }}
                    </h4>
                  </ion-text>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <p class="p--size features-list--color">
                      {{ "slide_premium_unlock_message_flirt" | translate }}
                    </p>
                  </ion-text>
                </ion-col>
              </ion-row>
            </swiper-slide>
          }

          <!-- MESSAGES LIMIT -->
          @if (dialogType == 'messages_limit') {
            <swiper-slide>
              <ion-row>
                <ion-thumbnail>
                  <ion-img
                    [src]="sendMessageProfile?.avatar_url || '/assets/img/avatar.svg'"
                    class="user-thumbnail"
                  ></ion-img>
                </ion-thumbnail>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <h4 class="features-list--color">
                      {{ "slide_premium_title_messages_2" | translate }}
                    </h4>
                  </ion-text>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <p
                      class="p--size features-list--color"
                      [class.ion-no-margin]="sendMessageProfile"
                      >
                      {{ "slide_premium_subtitle_messages_2" | translate }}
                    </p>
                  </ion-text>
                </ion-col>
              </ion-row>
              <ion-row [class.hidden]="!sendMessageProfile">
                <ion-col>
                  <ion-text>
                    <p class="p--size features-list--color">
                      {{ sendMessageProfile?.first_name ||
                      sendMessageProfile?.username }}
                    </p>
                  </ion-text>
                </ion-col>
              </ion-row>
            </swiper-slide>
          }

          <!-- UPGRADE TO SEND MESSAGE SLIDE -->
          @if (dialogType == 'upgrade_to_read') {
            <swiper-slide>
              <ion-row>
                <ion-thumbnail>
                  <ion-img
                    [src]="sendMessageProfile?.avatar_url || '/assets/img/avatar.svg'"
                    class="user-thumbnail"
                  ></ion-img>
                </ion-thumbnail>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <h4
                      class="features-list--color"
                      [class.hidden]="sendMessageProfile?.gender_id!=2"
                      >
                      {{ "slide_premium_want_to_read_message_female" | translate
                      }}
                    </h4>
                    <h4
                      class="features-list--color"
                      [class.hidden]="sendMessageProfile?.gender_id!=1"
                      >
                      {{ "slide_premium_want_to_read_message_male" | translate }}
                    </h4>
                    <h4
                      class="features-list--color"
                      [class.hidden]="(sendMessageProfile?.gender_id==1 || sendMessageProfile?.gender_id==2)"
                      >
                      {{ "slide_premium_want_to_read_message_none" | translate }}
                    </h4>
                  </ion-text>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <p
                      class="p--size features-list--color"
                      [class.ion-no-margin]="sendMessageProfile"
                      >
                      {{ "slide_premium_want_to_read_message_desc_1" | translate:
                      { 'first_name': (sendMessageProfile?.first_name ||
                      sendMessageProfile?.username || 'this user') } }}<br />
                      {{ "slide_premium_want_to_read_message_desc_2" | translate:
                      { 'first_name': (sendMessageProfile?.first_name ||
                      sendMessageProfile?.username || 'this user') } }}
                    </p>
                  </ion-text>
                </ion-col>
              </ion-row>
            </swiper-slide>
          }

          <!-- PHOTO FULL SIZE LIMIT -->
          @if (dialogType == 'photo_full_size_limit') {
            <swiper-slide>
              <ion-row>
                <ion-thumbnail>
                  <ion-img
                    [src]="profileFullSizeLimit?.avatar_url || '/assets/img/avatar.svg'"
                    class="user-thumbnail"
                  ></ion-img>
                </ion-thumbnail>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <h4
                      class="features-list--color"
                      [class.hidden]="profileFullSizeLimit?.gender_id!=2"
                      [innerHTML]="'slide_premium_want_to_see_photo_female' | translate"
                    ></h4>
                    <h4
                      class="features-list--color"
                      [class.hidden]="profileFullSizeLimit?.gender_id!=1"
                      [innerHTML]="'slide_premium_want_to_see_photo_male' | translate"
                    ></h4>
                    <h4
                      class="features-list--color"
                      [class.hidden]="(profileFullSizeLimit?.gender_id==1 || profileFullSizeLimit?.gender_id==2)"
                      [innerHTML]="'slide_premium_want_to_see_photo_none' | translate"
                    ></h4>
                  </ion-text>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <p
                      class="p--size features-list--color"
                      [class.ion-no-margin]="profileFullSizeLimit"
                      >
                      {{ "slide_premium_want_to_see_photo_desc_1" | translate: {
                      'first_name': (profileFullSizeLimit?.first_name ||
                      profileFullSizeLimit?.username || 'this user') } }}<br />
                      {{ "slide_premium_want_to_read_message_desc_2" | translate:
                      { 'first_name': (profileFullSizeLimit?.first_name ||
                      profileFullSizeLimit?.username || 'this user') } }}
                    </p>
                  </ion-text>
                </ion-col>
              </ion-row>
            </swiper-slide>
          }
        </swiper-container>
      </ion-col>
    </ion-row>

    <ion-row class="product-items ion-padding">
      @for (item of data; track item; let i = $index) {
        <ion-col
          size="4"
          (click)="selectProduct(item)"
          [ngClass]="{'selected': item.id == selectedItem.id}"
          class="item"
          >
          <div class="item-inner">
            <div [class.hidden]="i == 0 || i == 2" class="best-label">
              <ion-text> {{ 'popular' | translate }} </ion-text>
            </div>
            <ion-text>
              <h2>{{ item.ribbonText }}</h2>
            </ion-text>
            <ion-text class="period-length">{{ item.titleText }}</ion-text>
            <div>
              <ion-text>
                <p class="price">{{ item.price }}</p>
              </ion-text>
              <ion-text>
                <p class="period-unit">{{ item.periodText }}</p>
              </ion-text>
            </div>
            <div
              [class.hidden]="item.savingsInPercentage == null || item.savingsInPercentage <= 0"
              class="savings"
              >
              <ion-text>
                {{ 'premium_save' | translate }} {{ item.savingsInPercentage }}%
              </ion-text>
            </div>
          </div>
        </ion-col>
      }
    </ion-row>

    <ion-row class="ion-padding-horizontal">
      <ion-col>
        <ion-button
          [disabled]="this.inProgress() || !this.selectedItem.canPurchase"
          expand="block"
          color="primary"
          (click)="upgradeClicked()"
          >{{ "upgrade_now" | translate }}
          <ion-spinner
            name="crescent"
            [class.hidden]="!this.inProgress()"
          ></ion-spinner>
        </ion-button>
      </ion-col>
    </ion-row>

    <ion-row class="ion-padding-horizontal">
      <ion-col>
        <ion-text>
          <p class="billed">{{ selectedItem.description }}</p>
        </ion-text>
      </ion-col>
    </ion-row>

    @if (isIos()) {
      <ion-row class="ion-padding-horizontal">
        <ion-col class="legal" padding-horizontal>
          <ion-text>
            {{ 'iap_info_text_1' | translate }}
            <a class="terms-link" routerLink="/page/{{env.PAGE_NAME_TOS}}"
              >{{ 'terms_and_conditions' | translate }}</a
              >
              {{ 'and' | translate }}
              <a
                class="privacy-policy-link"
                routerLink="/page/{{env.PAGE_NAME_PRIVACY_POLICY}}"
                >{{ 'privacy_policy' | translate }}</a
                >
              </ion-text>
            </ion-col>
          </ion-row>
        }

        <ion-row class="ion-padding-horizontal ion-padding-top">
          <ion-col class="legal" padding-horizontal>
            <ion-text><!--{{ 'iap_info_text_2' | translate }}--></ion-text>
          </ion-col>
        </ion-row>
      </ion-grid>

      <!--<pre [innerHTML]="data | json"></pre>-->
    </ion-content>
