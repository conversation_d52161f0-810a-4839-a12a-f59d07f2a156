import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { GoPremiumPageRoutingModule } from "./go-premium-routing.module";
import { GoPremiumPage } from "./go-premium.page";
import { TranslateModule } from "@ngx-translate/core";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    GoPremiumPageRoutingModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [GoPremiumPage],
})
export class GoPremiumPageModule {}
