.try-premium-title-wrapper {
  padding-top: constant(safe-area-inset-top) !important;
  padding-top: env(safe-area-inset-top) !important;

  .try-premium-title {
    margin-left: 40px;
    margin-right: 40px;
  }
}

.features-list {
  background-color: var(--ion-color-primary);
  padding: 24px 0;

  &--color {
    color: var(--ion-color-white);
  }

  .swiper-container {
    padding-bottom: 0;

    swiper-slide {
      flex-direction: column;
    }
  }

  swiper-slide {
    ion-thumbnail {
      --size: 84px;
      --border-radius: 50%;
      margin: 6px;
    }

    ion-icon {
      background-color: var(--ion-color-white);
      border-radius: 50%;
      padding: 14px;
      font-size: 3.5rem;

      &.custom-icon {
        width: 42px;
        height: 42px;
        padding: 20px;
      }
    }

    h4 {
      text-transform: uppercase;
      font-weight: bold;
      letter-spacing: 0;
    }
  }
}

.max-width--300 {
  max-width: 300px;
  @media screen and (min-width:480px) {
    max-width: 360px;
  }
}

.p--size {
  font-size: 14px;
  margin: 2px auto 0;
}

.swiper-pagination {
  bottom: 0 !important;
}

.product-items {
  padding: 32px 16px;
  margin: 0 -4px;

  .item-inner {
    height: 100%;
    border: 2px solid transparent;
    border-radius: 6px;
    box-shadow: 0 0 0 1px var(--ion-color-light-shade);
    margin: 0 4px;
    padding: 0 10px 8px;
  }

  .selected {
    .item-inner {
      border: 2px solid var(--ion-color-primary);
      box-shadow: none;
    }
  }

  .best-label,
  .savings {
    background-color: var(--ion-color-primary);
    color: var(--ion-color-white);
    height: 16px;
    border-radius: 3px;
    font-size: 10px;
    text-transform: uppercase;
    white-space: nowrap;
    padding: 2px 8px;
    position: absolute;
    left: 50%;
    top: -8px;
    transform: translateX(-50%);
  }
  .savings {
    background-color: var(--ion-color-success);
    top: auto;
    bottom: -8px;
  }

  .period-length {
    font-size: 14px;
    font-weight: 700;
  }

  .price {
    color: var(--ion-color-primary);
    font-weight: 700;
    margin: 8px 0 2px;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  .period-unit {
    font-size: 12px;
    margin-top: 0;
  }

}

.billed {
  font-size: 14px;
}

.legal {
  font-size: 12px;
  line-height: 1.4;
  margin-top: 0;
  text-align: justify;
}
