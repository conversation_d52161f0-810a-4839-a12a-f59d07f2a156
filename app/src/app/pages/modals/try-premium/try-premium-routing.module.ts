import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { TryPremiumPage } from "./try-premium.page";

const routes: Routes = [
  {
    path: "",
    component: TryPremiumPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TryPremiumPageRoutingModule {}
