.try-premium-title-wrapper {
  padding-top: constant(safe-area-inset-top) !important;
  padding-top: env(safe-area-inset-top) !important;
}

ul {
  margin: 0;
  padding: 0;
  li {
    list-style-type: none;
  }
}

.features-list {
  h6 {
    margin: 0;
    font-weight: bold;
  }

  p {
    margin: 0;
  }

  ion-row {
    padding-top: 12px;
    padding-bottom: 12px;
  }
}

.fixed-col {
  flex: 0 0 80px;
}

.list-ico {
  background-color: var(--ion-color-primary);
  color: var(--ion-color-white);
  border-radius: 50%;
  padding: 8px;
}

.legal {
  font-size: 12px;
  line-height: 1.4;
  margin-top: 0;
  text-align: justify;
}
