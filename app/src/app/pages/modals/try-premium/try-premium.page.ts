import { Component, OnInit } from "@angular/core";
import { NavController, Platform } from "@ionic/angular";
import { TME_welcome_offer_click } from "src/app/classes/gtm-event.class";
import { AnalyticsService } from "src/app/services/analytics.service";
import { DeviceInfoService } from "src/app/services/device-info.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { PurchaseService } from "src/app/services/purchase.service";
import { environment as Env } from "../../../../environments/environment";
import { IapItem } from "src/app/services/iap-item.interface";
import { RefresherService } from "../../../services/refresher.service";

@Component({
  selector: "app-try-premium",
  templateUrl: "./try-premium.page.html",
  styleUrls: ["./try-premium.page.scss"],
  standalone: false,
})
export class TryPremiumPage implements OnInit {
  /**
   * Product entry.
   *
   * @type {ProductEntry[]}
   */
  public data: IapItem;

  get env() {
    return Env;
  }

  public isIos() {
    return this.deviceInfo.platform == "ios";
  }

  constructor(
    public purchaseService: PurchaseService,
    private deviceInfo: DeviceInfoService,
    private navController: NavController,
    private dialog: DialogHelperService,
    private platform: Platform,
    private analytics: AnalyticsService,
    private refresherService: RefresherService,
  ) {}

  private sub = null;

  ionViewDidEnter() {
    // disable back button for the view
    this.sub = this.platform.backButton.subscribeWithPriority(999, () => {
      this.close();
    });
  }

  ionViewWillLeave() {
    // enable when exiting
    this.sub.unsubscribe();
  }

  public inProgress() {
    return this.purchaseService.inProgress();
  }

  /**
   * Upgrade button text.
   *
   * @type {string}
   */
  public upgradeNowButtonText: string = "upgrade_now_free_trial";

  ngOnInit() {
    if (this.purchaseService.subscriptions.length == 0) {
      // initialize mock if page opened directly for design ( for example )
      this.dialog.showToast(
        "",
        "DEV_ONLY: Dialog was not initialized properly, using mock data!",
        "error",
      );

      this.purchaseService.init_MOCK();
    }

    // center item as default - if we have set a campaign item, use that else use the offer
    this.data = this.purchaseService.isCampaign
      ? this.purchaseService.selectedCampaign
      : this.purchaseService.offers[0];
  }

  public buyOffer(id) {
    this.analytics.logEvent("welcome_offer_click", <TME_welcome_offer_click>{
      offer_type: "Free trial",
    });

    this.purchaseService.buySubscription(id);
    this.refresherService.refreshEnd$.subscribe(() => this.navController.pop());
  }

  public close() {
    this.navController.pop().then(() => {
      this.purchaseService.executeCloseCallback();
    });
  }
}
