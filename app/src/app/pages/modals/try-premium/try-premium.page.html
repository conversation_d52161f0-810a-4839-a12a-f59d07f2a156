<ion-content>
  <ion-grid>
    <ion-row class="ion-text-center try-premium-title-wrapper">
      <ion-col>
        <ion-text>
          <h1 class="try-premium-title">
            {{ 'try_premium_title' | translate }}
            <br />
            @if (data.hasFreeTrial) {
              <span
                >{{ 'try_premium_text' | translate }}</span
                >
              }
            </h1>
          </ion-text>
        </ion-col>
      </ion-row>

      <ion-row
        class="features-list ion-align-items-center ion-justify-content-center"
        >
        <ion-col size-md="6" class="ion-padding">
          <ion-grid class="ion-no-padding">
            <ion-row class="ion-align-items-center ion-padding-vertical">
              <ion-col class="fixed-col ion-text-center ion-no-padding">
                <ion-icon name="close" size="large" class="list-ico"></ion-icon>
              </ion-col>
              <ion-col>
                <ion-text>
                  <h6>{{ 'try_premium_title_1' | translate }}</h6>
                </ion-text>
                <ion-text>
                  <p [innerHTML]="'try_premium_text_1_br' | translate"></p>
                </ion-text>
              </ion-col>
            </ion-row>

            @if (data.hasFreeTrial) {
              <ion-row
                class="ion-align-items-center ion-padding-vertical"
                >
                <ion-col class="fixed-col ion-text-center ion-no-padding">
                  <ion-icon
                    src="assets/icons/free.svg"
                    size="large"
                    class="list-ico"
                  ></ion-icon>
                </ion-col>
                <ion-col>
                  <ion-text>
                    <h6>{{ 'try_premium_title_2' | translate }}</h6>
                  </ion-text>
                  <ion-text>
                    <p [innerHTML]="'try_premium_text_2' | translate"></p>
                  </ion-text>
                </ion-col>
              </ion-row>
            }

            <ion-row class="ion-align-items-center ion-padding-vertical">
              <ion-col class="fixed-col ion-text-center ion-no-padding">
                <ion-icon
                  name="chatbubbles"
                  size="large"
                  class="list-ico"
                ></ion-icon>
              </ion-col>
              <ion-col>
                <ion-text>
                  <h6>{{ 'try_premium_title_3' | translate }}</h6>
                </ion-text>
              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-col>
      </ion-row>

      <ion-row class="ion-justify-content-center">
        <ion-col size-md="6" class="ion-padding-horizontal ion-text-center">
          <ion-text>
            <p [innerHTML]="data.trialPriceText"></p>
          </ion-text>
        </ion-col>
      </ion-row>

      <ion-row class="ion-justify-content-center">
        <ion-col size-md="6" class="ion-padding-horizontal">
          <ion-button
            [disabled]="this.inProgress() || !data?.canPurchase"
            (click)="this.buyOffer(data.store_id)"
            expand="block"
            >{{ data.trialCallToAction | translate}}
            <ion-spinner
              name="crescent"
              [class.hidden]="!this.inProgress()"
            ></ion-spinner>
          </ion-button>
        </ion-col>
      </ion-row>

      <ion-row class="ion-justify-content-center">
        <ion-col size-md="6" class="ion-no-padding">
          <ion-button (click)="close()" expand="block" fill="clear">
            {{ 'not_right_now' | translate }}
          </ion-button>
        </ion-col>
      </ion-row>

      <ion-row class="ion-justify-content-center">
        <ion-col size-md="6" class="ion-padding-horizontal ion-text-center">
          <ion-text>
            <p>{{ data.description }}</p>
          </ion-text>
        </ion-col>
      </ion-row>

      @if (isIos()) {
        <ion-row
          class="ion-padding-horizontal ion-justify-content-center"
          >
          <ion-col size-md="6" class="legal">
            <ion-text>
              {{ 'iap_info_text_1' | translate }}
              <a class="terms-link" routerLink="/page/{{env.PAGE_NAME_TOS}}"
                >{{ 'terms_and_conditions' | translate }}</a
                >
                {{ 'and' | translate }}
                <a
                  class="privacy-policy-link"
                  routerLink="/page/{{env.PAGE_NAME_PRIVACY_POLICY}}"
                  >{{ 'privacy_policy' | translate }}</a
                  >
                </ion-text>
              </ion-col>
            </ion-row>
          }

          <ion-row
            class="ion-padding-horizontal ion-padding-top ion-justify-content-center"
            >
            <ion-col size-md="6" class="legal">
              <ion-text><!--{{ 'iap_info_text_2' | translate }}--></ion-text>
            </ion-col>
          </ion-row>
        </ion-grid>

        <!--
        <pre [innerHTML]="data | json"></pre>
        -->
      </ion-content>
