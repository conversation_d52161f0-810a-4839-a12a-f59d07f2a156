import { ComponentFixture, TestBed, waitForAsync } from "@angular/core/testing";
import { IonicModule } from "@ionic/angular";

import { GoPremiumDiscounted24hPage } from "./go-premium-discounted-24h.page";

describe("GoPremiumDiscounted24hPage", () => {
  let component: GoPremiumDiscounted24hPage;
  let fixture: ComponentFixture<GoPremiumDiscounted24hPage>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [GoPremiumDiscounted24hPage],
      imports: [IonicModule.forRoot()],
    }).compileComponents();

    fixture = TestBed.createComponent(GoPremiumDiscounted24hPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
