import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { GoPremiumDiscounted24hPageRoutingModule } from "./go-premium-discounted-24h-routing.module";

import { GoPremiumDiscounted24hPage } from "./go-premium-discounted-24h.page";

import { TranslateModule } from "@ngx-translate/core";
import { CountdownUpgradeComponentModule } from "../../../components/countdown-upgrade/countdown-upgrade.component.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    GoPremiumDiscounted24hPageRoutingModule,
    CountdownUpgradeComponentModule,
  ],
  declarations: [GoPremiumDiscounted24hPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class GoPremiumDiscounted24hPageModule {}
