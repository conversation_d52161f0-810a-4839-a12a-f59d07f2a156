import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild } from "@angular/core";
import { AccountService } from "src/app/services/account.service";
import { NavController, Platform } from "@ionic/angular";
import { DeviceInfoService } from "src/app/services/device-info.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { PurchaseService } from "src/app/services/purchase.service";
import { IapItem } from "src/app/services/iap-item.interface";
import { environment as Env } from "../../../../environments/environment";
import { StateService } from "../../../services/state.service";
import { ProfileUser } from "../../../schemas/profile-user";
import { RefresherService } from "../../../services/refresher.service";
import { CountdownController } from "../shared/countdown";
import * as _ from "lodash";
import { Router } from "@angular/router";

@Component({
  selector: "app-go-premium-discounted-24h",
  templateUrl: "./go-premium-discounted-24h.page.html",
  styleUrls: ["./go-premium-discounted-24h.page.scss"],
  standalone: false,
})
export class GoPremiumDiscounted24hPage
  extends CountdownController
  implements OnInit, OnDestroy
{
  public sendMessageProfile: ProfileUser.type;
  public profileFullSizeLimit: ProfileUser.type;

  public dataIap: IapItem[];

  public selectedItem: IapItem;

  constructor(
    public navController: NavController,
    public platform: Platform,
    public stateService: StateService,
    public accountService: AccountService,
    public purchaseService: PurchaseService,
    private dialog: DialogHelperService,
    private deviceInfo: DeviceInfoService,
    private refresherService: RefresherService,
    private router: Router,
  ) {
    super(navController, platform, stateService, accountService);
  }

  ngOnDestroy() {
    // reset dialog type
    this.purchaseService.setDialogType("");
  }

  get dialogType() {
    return this.purchaseService.dialogType;
  }

  ionViewWillLeave() {
    super.ionViewWillLeave();
    // reset dialog type
    this.purchaseService.setDialogType("");
  }

  public inProgress() {
    return this.purchaseService.inProgress();
  }

  get env() {
    return Env;
  }

  public selectProduct(item: IapItem) {
    this.selectedItem = item;
  }

  public isIos() {
    return this.deviceInfo.platform == "ios";
  }

  ngOnInit() {
    if (this.accountService.account.is_premium) {
      //premum user is alredy upgraded
      this.router.navigate(["app/tabs/browse"]);
      return;
    }

    if (this.purchaseService.checkIfOffer24hIsActive() == false) {
      //offer is not active - go to upgrade
      this.purchaseService.openUpgradeDialog((error) => {
        this.dialog.showToastBaseOnErrorResponse(error, null, false);
      });
      return;
    }
    if (this.purchaseService.discounts.length == 0) {
      // initialize mock if page opened directly for design ( for example )
      this.dialog.showToast(
        "",
        "DEV_ONLY: Dialog was not initialized properly, using mock data!",
        "error",
      );
      this.purchaseService.init_MOCK();
    }

    // center item as default
    this.selectProduct(
      this.purchaseService.discounts[1] || this.purchaseService.discounts[0],
    );

    this.dataIap = this.purchaseService.discounts;

    let ts: any = this.accountService.account.registration_ts;

    let sec = Math.floor(_.now() / 1000 - parseInt(ts));

    this.stateService.set("countdown", this.accountService.account.username, {
      type: "swipe",
      renewal: Math.floor(Date.now() / 1000) + (24 * 60 * 60 - sec),
    });
    super.ngOnInit();
  }

  public upgradeClicked() {
    this.purchaseService.buySubscription(this.selectedItem.store_id);
    this.refresherService.refreshEnd$.subscribe(() => this.navController.pop());
  }

  public close() {
    this.navController.pop().then(() => {
      this.purchaseService.executeCloseCallback();
    });
  }
}
