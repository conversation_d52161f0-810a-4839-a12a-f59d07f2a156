import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { GoPremiumDiscounted24hPage } from "./go-premium-discounted-24h.page";

const routes: Routes = [
  {
    path: "",
    component: GoPremiumDiscounted24hPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class GoPremiumDiscounted24hPageRoutingModule {}
