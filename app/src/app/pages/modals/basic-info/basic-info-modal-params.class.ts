/**
 * param list for basic dialog
 */
export class BasicInfoModalParams {
  public title: string = "";
  public topText: string = "";
  public bottomText: string = "";
  public buttonText: string = "";
  public buttonVisible: boolean = true;
  public footerButtonText: string = "";
  public footerButtonVisible: boolean = true;
  public footerButtonCallback: Function = null;
  public footerButton2Text: string = "";
  public footerButton2Visible: boolean = false;
  public footerButton2Callback: Function = null;
  public iconName?: string = "";
  public imageUrl?: string = "";
  public buttonCallback: Function = null;
}
