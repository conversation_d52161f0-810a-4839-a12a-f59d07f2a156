<ion-content>
  <ion-grid class="ion-text-center ion-no-padding">
    <ion-row class="ion-text-center try-premium-title-wrapper">
      <ion-col>
        <ion-icon
          (click)="close()"
          size="large"
          name="close-outline"
          class="ion-hide-lg-up ion-padding-top ion-padding-start ion-float-left"
        ></ion-icon>
        <ion-text>
          <h1 class="try-premium-title">
            {{ 'title_go_premium_downloaded' | translate }}
          </h1>
        </ion-text>
      </ion-col>
    </ion-row>

    <ion-row class="features-list">
      <ion-col class="ion-padding-horizontal">
        <swiper-container
          pagination="false"
          class="ion-flex-direction-row"
          #ionSlides
          >
          <swiper-slide>
            <ion-row>
              <ion-col>
                <ion-text>
                  <h2 class="features-list--color ion-margin-vertical">
                    {{ "slide_go_premium_discounted_congratulation" | translate }}
                  </h2>
                </ion-text>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col class="ion-margin-bottom">
                <ion-text>
                  <p
                    class="p--size features-list--color ion-no-margin"
                    [class.ion-no-margin]="sendMessageProfile"
                    >
                    {{ "slide_go_premium_downloaded_desc_1" | translate }}
                    <b class="p--size ion-padding-top"
                      >{{ "slide_go_premium_discounted_desc_2" | translate }}</b
                      >
                    </p>
                  </ion-text>
                </ion-col>
              </ion-row>
            </swiper-slide>
          </swiper-container>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col class="ion-padding-horizontal">
          <p>
            <ion-text class="ion-padding-top">{{ 'choose_your_plan_header' | translate }}</ion-text>
          </p>
          <p>
            <ion-text class="ion-padding-top">{{ 'choose_your_plan' | translate }}:</ion-text>
          </p>
        </ion-col>
      </ion-row>

      <ion-row class="product-items ion-padding">
        @for (item of data; track item; let i = $index) {
          <ion-col
            (click)="selectProduct(item)"
            [ngClass]="{'selected': item.id == selectedItem.id}"
            size-md="3"
            >
            <div class="item-inner">
              <div [class.hidden]="i == 0 || i == 2" class="best-label">
                <ion-text> {{ 'best_value' | translate }} </ion-text>
              </div>
              <ion-text>
                <h2>{{ item.ribbonText }}</h2>
              </ion-text>
              <ion-text class="period-length">{{ item.titleText }}</ion-text>
              <div>
                <ion-text>
                  <p class="price">{{ item.price }}</p>
                </ion-text>
                <ion-text>
                  <p class="period-unit">{{ item.periodText }}</p>
                </ion-text>
              </div>
              <div
                [class.hidden]="item.savingsInPercentage == null || item.savingsInPercentage <= 0"
                class="savings"
                >
                <ion-text>
                  {{ 'premium_save' | translate }} {{ item.savingsInPercentage }}%
                </ion-text>
              </div>
            </div>
          </ion-col>
        }
      </ion-row>

      <ion-row class="ion-padding-horizontal ion-justify-content-center">
        <ion-col size-md="6">
          <ion-button
            [disabled]="this.inProgress() || !this.selectedItem.canPurchase"
            expand="block"
            color="primary"
            (click)="upgradeClicked()"
            >{{ "upgrade_now" | translate }}
            <ion-spinner
              name="crescent"
              [class.hidden]="!this.inProgress()"
            ></ion-spinner>
          </ion-button>
        </ion-col>
      </ion-row>

      <ion-row class="ion-padding-horizontal ion-justify-content-center">
        <ion-col size-md="10">
          <ion-text>
            <p class="billed">{{ selectedItem.description }}</p>
          </ion-text>
        </ion-col>
      </ion-row>

      @if (isIos()) {
        <ion-row
          class="ion-padding-horizontal ion-justify-content-center"
          >
          <ion-col class="legal ion-padding-horizontal" size-md="10">
            <ion-text>
              {{ 'iap_info_text_1' | translate }}
              <a class="terms-link" routerLink="/page/{{env.PAGE_NAME_TOS}}"
                >{{ 'terms_and_conditions' | translate }}</a
                >
                {{ 'and' | translate }}
                <a
                  class="privacy-policy-link"
                  routerLink="/page/{{env.PAGE_NAME_PRIVACY_POLICY}}"
                  >{{ 'privacy_policy' | translate }}</a
                  >
                </ion-text>
              </ion-col>
            </ion-row>
          }

          <ion-row class="ion-padding-horizontal ion-padding-top">
            <ion-col class="legal" padding-horizontal>
              <ion-text><!--{{ 'iap_info_text_2' | translate }}--></ion-text>
            </ion-col>
          </ion-row>
        </ion-grid>

        <!--<pre [innerHTML]="data | json"></pre>-->
      </ion-content>
