.try-premium-title-wrapper {
  padding-top: constant(safe-area-inset-top) !important;
  padding-top: env(safe-area-inset-top) !important;

  .try-premium-title {
    margin: 1.25rem 40px;
    font-size: 1.25rem;
  }
}

.features-list {
  background-color: var(--ion-color-primary);
  padding: .5rem 0;

  &--color {
    color: var(--ion-color-white);
    font-size: 1.2rem;
    line-height: 1.5rem;
  }

  .swiper-container {
    padding-bottom: 0;

    ion-slide {
      flex-direction: column;
    }
  }

  swiper-slide {
    ion-thumbnail {
      --size: 84px;
      --border-radius: 50%;
      margin: 6px;
    }

    ion-icon {
      background-color: var(--ion-color-white);
      border-radius: 50%;
      padding: 14px;
      font-size: 3.5rem;

      &.custom-icon {
        width: 42px;
        height: 42px;
        padding: 20px;
      }
    }

    h4 {
      text-transform: uppercase;
      font-weight: bold;
      letter-spacing: 0;
    }
  }
}

.max-width--300 {
  max-width: 300px;
  @media screen and (min-width:480px) {
    max-width: 360px;
  }
}

.p--size {
  font-size: 1rem;
  line-height: 1.4rem;

  b {
    display: inline-block;
    width: 100%;
  }
}

.swiper-pagination {
  bottom: 0 !important;
}

.product-items {
  padding: 0 16px 20px;
  margin: 0 -4px;
  text-align: center;
  align-items: center;
  display: flex;
  justify-content: center;
  height: 100%;
  flex-wrap: wrap;

  .item-inner {
    height: 100%;
    border: 2px solid transparent;
    border-radius: 6px;
    box-shadow: 0 0 0 1px var(--ion-color-light-shade);
    margin: 0 .4rem;
    padding: 0 10px 8px;

    h2 {
      font-size: 2rem;
    }
  }

  .selected {
    .item-inner {
      border: 2px solid var(--ion-color-primary);
      box-shadow: none;
    }
  }

  .best-label,
  .savings {
    background-color: var(--ion-color-success);
    color: var(--ion-color-white);
    height: 16px;
    border-radius: 3px;
    font-size: 11px;
    white-space: nowrap;
    padding: 2px 8px;
    position: absolute;
    left: 50%;
    bottom: -8px;
    transform: translateX(-50%);
    font-weight: bold;
  }
  .savings {
    background-color: var(--ion-color-success);
    top: auto;
    bottom: -8px;
  }

  .period-length {
    font-size: 14px;
    font-weight: 700;
  }

  .price {
    color: var(--ion-color-primary);
    font-weight: 700;
    margin: 8px 0 2px;
  }

  .period-unit {
    font-size: 12px;
    margin-top: 0;
  }

}

.billed {
  font-size: 14px;
}

.legal {
  font-size: 12px;
  line-height: 1.4;
  margin-top: 0;
  text-align: justify;
}
