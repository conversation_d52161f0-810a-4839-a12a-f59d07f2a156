import { Component, OnInit } from "@angular/core";
import { ProfileUser } from "../../../schemas/profile-user";
import { PurchaseService } from "../../../services/purchase.service";
import { NavController, Platform } from "@ionic/angular";
import { DialogHelperService } from "../../../services/dialog-helper.service";
import { DeviceInfoService } from "../../../services/device-info.service";
import { StateService } from "../../../services/state.service";
import { TranslateService } from "@ngx-translate/core";
import { environment as Env } from "../../../../environments/environment";
import { IapItem } from "../../../services/iap-item.interface";
import { RefresherService } from "../../../services/refresher.service";

@Component({
  selector: "app-go-premium-downloaded",
  templateUrl: "./go-premium-downloaded.page.html",
  styleUrls: ["./go-premium-downloaded.page.scss"],
  standalone: false,
})
export class GoPremiumDownloadedPage implements OnInit {
  public sendMessageProfile: ProfileUser.type;
  public profileFullSizeLimit: ProfileUser.type;

  constructor(
    public purchaseService: PurchaseService,
    private navController: NavController,
    private dialog: DialogHelperService,
    private deviceInfo: DeviceInfoService,
    private platform: Platform,
    private stateService: StateService,
    private translateService: TranslateService,
    private refresherService: RefresherService,
  ) {}

  private sub = null;

  ngOnDestroy() {
    // reset dialog type
    this.purchaseService.setDialogType("");
  }

  ionViewDidEnter() {
    // disable back button for the view
    this.sub = this.platform.backButton.subscribeWithPriority(999, () => {
      this.close();
    });
  }

  get dialogType() {
    return this.purchaseService.dialogType;
  }

  ionViewWillLeave() {
    // enable when exiting
    this.sub.unsubscribe();

    // reset dialog type
    this.purchaseService.setDialogType("");
  }

  public inProgress() {
    return this.purchaseService.inProgress();
  }

  get env() {
    return Env;
  }

  public data: IapItem[];

  public selectedItem: IapItem;

  public selectProduct(item: IapItem) {
    this.selectedItem = item;
  }

  public isIos() {
    return this.deviceInfo.platform == "ios";
  }

  ngOnInit() {
    if (this.purchaseService.discounts.length == 0) {
      // initialize mock if page opened directly for design ( for example )
      this.dialog.showToast(
        "",
        "DEV_ONLY: Dialog was not initialized properly, using mock data!",
        "error",
      );
      this.purchaseService.init_MOCK();
    }

    // center item as default
    this.selectProduct(
      this.purchaseService.discounts[1] || this.purchaseService.discounts[0],
    );

    this.data = this.purchaseService.discounts;
  }

  public upgradeClicked() {
    this.purchaseService.buySubscription(this.selectedItem.store_id);
    this.refresherService.refreshEnd$.subscribe(() => this.navController.pop());
  }

  public close() {
    this.navController.pop().then(() => {
      this.purchaseService.executeCloseCallback();
    });
  }
}
