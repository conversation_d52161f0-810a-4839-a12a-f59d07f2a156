import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { GoPremiumDownloadedPageRoutingModule } from "./go-premium-downloaded-routing.module";
import { GoPremiumDownloadedPage } from "./go-premium-downloaded.page";
import { TranslateModule } from "@ngx-translate/core";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    GoPremiumDownloadedPageRoutingModule,
  ],
  declarations: [GoPremiumDownloadedPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class GoPremiumDownloadedPageModule {}
