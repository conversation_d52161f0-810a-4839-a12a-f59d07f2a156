<ion-content>
  <ion-grid class="ion-text-center h100">
    <ion-row class="ion-justify-content-center ion-align-items-center h100">
      <ion-col>
        <ion-grid>
          <ion-row class="ion-justify-content-center ion-padding-top">
            <ion-col size-sm="8">
              <ion-text>
                <h1 class="ion-no-margin">
                  <!-- on old app it was sufix {{'get_more_upgrade_' + suffix | translate }} -->
                  {{ 'get_more_upgrade_super_likes' | translate }}
                </h1>
              </ion-text>
            </ion-col>
          </ion-row>

          <ion-row class="ion-justify-content-center">
            <ion-col size-sm="8">
              <ion-text>
                <h3 class="ion-no-margin">
                  <!-- on old app it was sufix {{ 'get_more_' + suffix | translate }} -->
                  {{ 'get_more_super_likes' | translate }}
                </h3>
              </ion-text>
            </ion-col>
          </ion-row>

          <ion-row class="ion-justify-content-center ion-margin-top">
            <ion-col size-sm="5">
              <ion-button class="ion-margin-vertical" expand="block">
                {{ "upgrade_vip_now" | translate }}
              </ion-button>
            </ion-col>
          </ion-row>

          <ion-row class="ion-justify-content-center">
            <ion-col size-sm="8">
              <ion-text>
                <!-- on old app it was sufix {{ 'more_now_' + suffix | translate }} -->
                {{ 'more_now_super_likes' | translate }}
              </ion-text>
            </ion-col>
          </ion-row>

          <ion-row class="ion-justify-content-center ion-margin-top">
            <ion-col size-sm="5">
              <ion-button
                (click)="close()"
                class="ion-margin-vertical"
                expand="block"
                fill="clear"
              >
                {{ "upgrade_dialog_btn_later" | translate }}
              </ion-button>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
