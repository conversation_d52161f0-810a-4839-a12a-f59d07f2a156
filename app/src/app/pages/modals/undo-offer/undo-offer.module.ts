import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { UndoOfferPageRoutingModule } from "./undo-offer-routing.module";

import { UndoOfferPage } from "./undo-offer.page";
import { TranslateModule } from "@ngx-translate/core";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    UndoOfferPageRoutingModule,
  ],
  declarations: [UndoOfferPage],
})
export class UndoOfferPageModule {}
