.offer-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

ion-chip {
  height: 160px;
  width: 160px;
  border-radius: 50%;

  ion-icon {
    font-size: 6rem;
    margin: auto;
  }
}

ion-grid {
  ion-row {
    ion-col {
      h4 {
        line-height: 1.5;
      }
    }
  }
}

.dialog-image {
  padding: 32px 0;
}

ion-button {
  &.button-clear {
    text-decoration: underline;
  }
}

.dialogImage {
  border-radius: 10px;
  max-height: 15vh;
  max-width: 100%;
  object-fit: contain;
}

.info-shortText {
  font-size: 2.4rem;
  font-weight: bold;
}

.info-text {
  font-size: 1rem;
}

.info-box-bg-opaque {
  .info-shortText {
    font-size: 3vh;
    font-weight: bold;
    margin: 0;
  }
}