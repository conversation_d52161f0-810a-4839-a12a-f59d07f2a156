import { Component, OnInit } from "@angular/core";
import { StateService } from "../../../services/state.service";
import { Debug } from "../../../helpers/debug";
import { NavController } from "@ionic/angular";
import { BasicInfoModalParams } from "../basic-info/basic-info-modal-params.class";
import { timer } from "rxjs";

@Component({
  selector: "app-offer",
  templateUrl: "./offer.page.html",
  styleUrls: ["./offer.page.scss"],
  standalone: false,
})
export class OfferPage implements OnInit {
  public data: BasicInfoModalParams;

  constructor(
    public dbg: Debug,
    public navController: NavController,
    public stateService: StateService,
  ) {}

  ngOnInit() {
    this.data = this.stateService.get("offerDialogData", "");
  }

  public buttonPressed() {
    if (!this.data.buttonCallback) {
      return this.dbg.le("offers - no buttonCallback param");
    }

    this.data.buttonCallback();
  }

  public footerButtonPressed() {
    if (!this.data.footerButtonCallback) {
      return this.dbg.le("offers - no footerButtonCallback param");
    }

    this.data.footerButtonCallback();
    this.navController.pop();
  }

  public footerButton2Pressed() {
    if (!this.data.footerButton2Callback) {
      return this.dbg.le("offers - no footerButton2Callback param");
    }

    this.data.footerButton2Callback();
    this.navController.pop();
  }
}
