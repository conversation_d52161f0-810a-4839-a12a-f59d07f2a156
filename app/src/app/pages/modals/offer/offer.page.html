@if (data) {
  <ion-content class="ion-text-center">
    <div class="offer-wrapper ion-padding">
      <div class="offer-inner">
        <h1 class="info-title">{{data.title | translate}}</h1>
        @if (data.iconName) {
          <div class="icon-background fElement">
            <ion-icon name="{{data.iconName}}" class="info-icon"></ion-icon>
          </div>
        }
        <div class="dialog-image">
          <ion-thumbnail style="width: 100%; height: auto">
            <ion-img style="pointer-events: none" [src]="data.imageUrl"></ion-img>
          </ion-thumbnail>
        </div>
        <ion-row class="ion-padding-top">
          <ion-col>
            <div class="info-box info-box-bg-opaque">
              <p class="info-shortText">{{data.topText | translate}}</p>
              <p class="info-text">{{data.bottomText | translate}}</p>
            </div>
          </ion-col>
        </ion-row>
        <ion-row [class.hidden]="!data.buttonVisible">
          <ion-col class="ion-no-padding">
            <ion-button [class.hidden]="!data.buttonVisible" (click)="buttonPressed()"
              >{{data.buttonText | translate}}</ion-button
              >
            </ion-col>
          </ion-row>
          <ion-row [class.hidden]="!data.footerButtonVisible">
            <ion-col class="ion-no-padding">
              <ion-button
                class="skipButton"
                [class.shadow]="false"
                (click)="footerButtonPressed()"
                >{{ data.footerButtonText | translate }}</ion-button
                >
              </ion-col>
            </ion-row>
            <ion-row [class.hidden]="!data.footerButton2Visible">
              <ion-col class="ion-no-padding">
                <ion-button
                  class="skipButton"
                  fill="clear"
                  [class.shadow]="false"
                  (click)="footerButton2Pressed()"
                  >{{ data.footerButton2Text | translate }}</ion-button
                  >
                </ion-col>
              </ion-row>
            </div>
          </div>
        </ion-content>
      }
