import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { FreeCountdownPageRoutingModule } from "./free-countdown-routing.module";

import { FreeCountdownPage } from "./free-countdown.page";
import { TranslateModule } from "@ngx-translate/core";
import { CountdownUpgradeComponentModule } from "../../../components/countdown-upgrade/countdown-upgrade.component.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    FreeCountdownPageRoutingModule,
    CountdownUpgradeComponentModule,
  ],
  declarations: [FreeCountdownPage],
})
export class FreeCountdownPageModule {}
