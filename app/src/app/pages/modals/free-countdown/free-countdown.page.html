<ion-content>
  <ion-grid class="ion-no-padding ion-text-center">
    <ion-row class="ion-justify-content-center features-list">
      <ion-col size="12" size-sm="9">
        <ion-text color="light">
          <h1 class="features-list--color ion-padding-horizontal">
            {{ 'cdf_title_' + data.type | translate }}
          </h1>
        </ion-text>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center">
      <ion-col size="12" class="ion-padding">
        <ion-chip [disabled]="true" class="ion-no-padding">
          <ion-icon
            name="star"
            class="ion-no-margin"
            color="primary"
          ></ion-icon>
        </ion-chip>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center ion-padding-horizontal">
      <ion-col size="12" size-sm="9">
        <ion-text>
          <h4>{{ 'cdf_counter_title_' + data.type | translate }}</h4>
        </ion-text>
      </ion-col>
    </ion-row>

    <app-countdown-upgrade [renewal]="data.renewal"></app-countdown-upgrade>

    <ion-row class="ion-justify-content-center ion-padding-top">
      <ion-col>
        <!-- there is different type of icons depend on type to show see old app -->
        <ion-icon color="danger" name="star"></ion-icon>
        <ion-icon color="danger" name="star" size="large"></ion-icon>
        <ion-icon color="danger" name="star"></ion-icon>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center">
      <ion-col size-sm="6">
        <ion-card class="ion-padding-top">
          <ion-card-header>
            <ion-card-subtitle>
              {{'cdf_action_line1_' + data.type | translate }}
            </ion-card-subtitle>
            <ion-card-title>
              {{'cdf_action_line2_' + data.type | translate}}
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-grid class="ion-no-padding">
              <ion-row>
                <ion-col size="12">
                  <ion-text
                    >{{'cdf_action_line3_' + data.type | translate }}</ion-text
                  >
                </ion-col>
                <ion-col size="12">
                  <ion-text
                    >{{'cdf_action_line4_' + data.type | translate }}</ion-text
                  >
                </ion-col>
              </ion-row>
              <ion-row>
                <ion-col>
                  <ion-button
                    class="ion-margin-vertical"
                    expand="block"
                    (click)="openUpgradeDialog()"
                  >
                    {{ "upgrade_now" | translate }}
                  </ion-button>
                </ion-col>
              </ion-row>
            </ion-grid>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center">
      <ion-col size-sm="5">
        <ion-button (click)="close()" expand="block" fill="clear">
          {{ "not_now" | translate }}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
