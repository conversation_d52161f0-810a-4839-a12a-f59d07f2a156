.features-list {
  background-color: var(--ion-color-primary);
  padding-top: constant(safe-area-inset-top) !important;
  padding-top: env(safe-area-inset-top) !important;
  padding-bottom: calc(40px + 16px);
}
ion-chip {
  height: 80px;
  width: 80px;
  border-radius: 50%;
  margin-top: calc(-40px - 16px);
  --background: rgba(255, 255, 255, 1);
  opacity: 1;

  ion-icon {
    font-size: 3.5rem;
    margin: auto;
  }
}
.countdown-timer {
  display: flex;
}
.cbCounterNumber {
  font-weight: bold;
  font-size: 2.5rem;
}
.cbCounterNumberFooter {
  font-weight: bold;
}
ion-card {
  margin-top: 0;
  margin-bottom: 0;
  box-shadow: none;
  ion-card-subtitle {
    text-transform: uppercase;
    font-size: 1.2em;
  }
  ion-card-title {
    text-transform: uppercase;
    font-size: 2em;
  }
  ion-card-content {
    padding-bottom: 0;
  }
}

