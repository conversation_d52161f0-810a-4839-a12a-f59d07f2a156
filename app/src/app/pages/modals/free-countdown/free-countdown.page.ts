import { Component, OnInit } from "@angular/core";
import { CountdownController } from "../shared/countdown";
import { NavController, Platform } from "@ionic/angular";
import { StateService } from "../../../services/state.service";
import { AccountService } from "../../../services/account.service";
import { PurchaseService } from "../../../services/purchase.service";
import { DialogHelperService } from "../../../services/dialog-helper.service";

@Component({
  selector: "app-free-countdown",
  templateUrl: "./free-countdown.page.html",
  styleUrls: ["./free-countdown.page.scss"],
  standalone: false,
})
export class FreeCountdownPage extends CountdownController implements OnInit {
  constructor(
    public navController: NavController,
    public platform: Platform,
    public stateService: StateService,
    public accountService: AccountService,
    public purchaseService: PurchaseService,
    public dialog: DialogHelperService,
  ) {
    super(navController, platform, stateService, accountService);
  }

  public openUpgradeDialog(): void {
    this.purchaseService.openUpgradeDialog((error) => {
      this.dialog.showToastBaseOnErrorResponse(error, null, false);
    });
  }
}
