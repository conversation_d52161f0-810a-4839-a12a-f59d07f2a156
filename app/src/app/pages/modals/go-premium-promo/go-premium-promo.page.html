<ion-content>
  <ion-grid class="ion-text-center ion-no-padding">
    <ion-row class="ion-text-center go-premium-title-wrapper">
      <ion-col>
        <ion-icon
          (click)="close()"
          size="large"
          name="close-outline"
          class="ion-hide-lg-up ion-padding-top ion-padding-start ion-float-left"
        ></ion-icon>
        <ion-text>
          <h1 class="go-premium-title">
            {{ 'title_go_premium_promo' | translate }}
          </h1>
        </ion-text>
      </ion-col>
    </ion-row>

    <ion-row
      class="benefits-list"
      [class.hidden]="dialogType!='default' || !dialogType"
      >
      <ion-col class="ion-padding-horizontal">
        <swiper-container pagination="false" class="ion-flex-direction-row">
          <swiper-slide>
            <ion-row class="benefits-images">
              <ion-col>
                <img
                  src="/assets/img/trial/trial-{{imageGender}}-1.png"
                  class="benefits-img first"
                  />
              </ion-col>
              <ion-col>
                <img
                  src="/assets/img/trial/trial-{{imageGender}}-2.png"
                  class="benefits-img second"
                  />
              </ion-col>
              <ion-col>
                <img
                  src="/assets/img/trial/trial-{{imageGender}}-3.png"
                  class="benefits-img third"
                  />
              </ion-col>
            </ion-row>
            <ion-row class="max-width--340">
              <ion-col>
                <ion-text>
                  <h4 class="benefits-list--color">
                    {{ "slide_premium_unlock_title" | translate }}
                  </h4>
                </ion-text>
              </ion-col>
            </ion-row>
            <ion-row class="max-width--300">
              <ion-col>
                <ion-text>
                  <p class="p--size benefits-list--color">
                    {{ "slide_premium_unlock_message" | translate }}
                  </p>
                </ion-text>
              </ion-col>
            </ion-row>
          </swiper-slide>
        </swiper-container>
      </ion-col>
    </ion-row>

    <ion-row [class.hidden]="dialogType!='welcome'">
      <ion-grid class="ion-text-center ion-padding-horizontal">
        <ion-row class="ion-justify-content-center">
          <ion-col size="12" size-sm="8">
            <h1>{{ "welcome_congrats" | translate }}</h1>
          </ion-col>

          <ion-col size="9" size-sm="6">
            <img class="info-photo" src="/assets/img/promo.png" />
          </ion-col>

          <ion-col class="ion-padding-vertical" size="12" size-sm="8">
            <ion-text
              [innerHTML]="'translate_welcome_info_page_offer_var' | translate : this.data"
            ></ion-text>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-row>

    <ion-row
      class="product-item-welcome ion-padding"
      [class.hidden]="dialogType!='welcome'"
      >
      <ion-col>
        <ion-item
          button
          [disabled]="this.inProgress() || !data?.canPurchase"
          color="primary"
          class="product-item-welcome-wrapper"
          lines="none"
          (click)="this.buyOffer(data.store_id)"
          detail="false"
          >
          <ion-label class="go-premium-label ion-text-wrap ion-text-center">
            <h2
              [innerHTML]="data?.promoPriceText"
              class="go-premium-heading"
            ></h2>
            <ion-text
              [innerHTML]="data?.promoSubscriptionDetails"
              class="go-premium-text"
            ></ion-text>
          </ion-label>
          <ion-spinner
            name="crescent"
            [class.hidden]="!this.inProgress()"
          ></ion-spinner>
        </ion-item>
      </ion-col>
    </ion-row>

    <ion-row
      class="product-items ion-padding"
      [class.hidden]="dialogType!='default' || !dialogType"
      >
      <ion-col>
        <ion-item
          button
          [disabled]="this.inProgress() || !data?.canPurchase"
          class="product-item-wrapper"
          lines="none"
          (click)="this.buyOffer(data.store_id)"
          detail="false"
          >
          <ion-label class="ion-text-wrap ion-text-center">
            <h2
              [innerHTML]="data?.promoPriceText"
              class="go-premium-heading"
            ></h2>
            <ion-text
              [innerHTML]="data?.promoSubscriptionDetails"
              class="go-premium-text"
            ></ion-text>
            <ion-text color="primary" class="go-premium-button"
              >{{ 'tap_here_to_upgrade' | translate }}</ion-text
              >
            </ion-label>
            <ion-spinner
              name="crescent"
              [class.hidden]="!this.inProgress()"
            ></ion-spinner>
          </ion-item>
        </ion-col>
      </ion-row>

      <ion-row class="go-premium-disclaimer ion-padding">
        <ion-col>
          <ion-text><p>{{ 'premium_promo_item_sub_disclaimer' | translate}}</p></ion-text>
          <ion-text><p>@if (isIos()) {
            {{ 'premium_promo_item_cancel_disclaimer_ios' | translate}}
          } @else {
            {{ 'premium_promo_item_cancel_disclaimer_android' | translate}}
          }</p></ion-text>
        </ion-col>
      </ion-row>


      @if (isIos()) {
        <ion-row class="ion-padding-horizontal">
          <ion-col class="legal" padding-horizontal>
            <ion-text>
              {{ 'iap_info_text_1' | translate }}
              <a class="terms-link" routerLink="/page/{{env.PAGE_NAME_TOS}}"
                >{{ 'terms_and_conditions' | translate }}</a
                >
                {{ 'and' | translate }}
                <a
                  class="privacy-policy-link"
                  routerLink="/page/{{env.PAGE_NAME_PRIVACY_POLICY}}"
                  >{{ 'privacy_policy' | translate }}</a
                  >
                </ion-text>
              </ion-col>
            </ion-row>
          }

          <ion-row class="ion-padding-horizontal ion-padding-top">
            <ion-col class="legal" padding-horizontal>
              <ion-text><!-- {{ 'iap_info_text_2' | translate }}--></ion-text>
            </ion-col>
          </ion-row>
        </ion-grid>

        <!--<pre [innerHTML]="data | json"></pre>-->
      </ion-content>
