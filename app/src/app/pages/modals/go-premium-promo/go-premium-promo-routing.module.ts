import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { GoPremiumPromoPage } from "./go-premium-promo.page";

const routes: Routes = [
  {
    path: "",
    component: GoPremiumPromoPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class GoPremiumPromoPageRoutingModule {}
