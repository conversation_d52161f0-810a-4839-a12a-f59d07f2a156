import { Component, OnInit } from "@angular/core";
import { NavController, Platform } from "@ionic/angular";
import { AccountService } from "src/app/services/account.service";
import { DeviceInfoService } from "src/app/services/device-info.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { PurchaseService } from "src/app/services/purchase.service";
import { environment as Env } from "../../../../environments/environment";
import { Utils } from "src/app/helpers/utils";
import { IapItem } from "src/app/services/iap-item.interface";
import { PromoDialogType } from "src/app/services/upgrade-dialog.type";
import { RefresherService } from "../../../services/refresher.service";

@Component({
  selector: "app-go-premium-promo",
  templateUrl: "./go-premium-promo.page.html",
  styleUrls: ["./go-premium-promo.page.scss"],
  standalone: false,
})
export class GoPremiumPromoPage implements OnInit {
  /**
   * Product entry.
   *
   * @type {ProductEntry[]}
   */
  public data: IapItem;

  get env() {
    return Env;
  }

  public isIos() {
    return this.deviceInfo.platform == "ios";
  }

  public get dialogType(): PromoDialogType {
    return this.purchaseService.promoDialogType;
  }

  constructor(
    public purchaseService: PurchaseService,
    private deviceInfo: DeviceInfoService,
    private navController: NavController,
    private dialog: DialogHelperService,
    private platform: Platform,
    public accountService: AccountService,
    private refresherService: RefresherService,
  ) {}

  public isGender() {
    let _gender = Utils.getGenderNameById(
      this.accountService.account.looking_id,
    );
    _gender = ["male", "female"].indexOf(_gender) < 0 ? "other" : _gender;
    return _gender;
  }

  imageGender = this.isGender();

  private sub = null;

  ionViewDidEnter() {
    // disable back button for the view
    this.sub = this.platform.backButton.subscribeWithPriority(999, () => {
      this.close();
    });
  }

  ionViewWillLeave() {
    // enable when exiting
    this.sub.unsubscribe();
  }

  public inProgress() {
    return this.purchaseService.inProgress();
  }

  /**
   * Upgrade button text.
   *
   * @type {string}
   */
  public upgradeNowButtonText: string = "upgrade_now_free_trial";

  ngOnInit() {
    if (this.purchaseService.subscriptions.length == 0) {
      // initialize mock if page opened directly for design ( for example )
      this.dialog.showToast(
        "",
        "DEV_ONLY: Dialog was not initialized properly, using mock data!",
        "error",
      );

      this.purchaseService.init_MOCK();
    }

    // center item as default - if we have set a campaign item, use that else use the offer
    this.data =
      this.purchaseService.isCampaign && !!this.purchaseService.selectedCampaign
        ? this.purchaseService.selectedCampaign
        : this.purchaseService.offers[0];
  }

  public buyOffer(id) {
    this.purchaseService.buySubscription(id);
    this.refresherService.refreshEnd$.subscribe(() => this.navController.pop());
  }

  public close() {
    this.navController.pop().then(() => {
      console.log("close callback execution");
      this.purchaseService.executeCloseCallback();
    });
  }
}
