.max-width--340 {
  max-width: 340px;
  @media screen and (min-width:480px) {
    max-width: 360px;
  }
}

.max-width--300 {
  max-width: 300px;
  @media screen and (min-width:480px) {
    max-width: 360px;
  }
}

.p--size {
  font-size: 14px;
  margin: 2px auto 0;
}

.go-premium-title-wrapper {
  padding-top: constant(safe-area-inset-top) !important;
  padding-top: env(safe-area-inset-top) !important;

  .go-premium-title {
    margin-left: 40px;
    margin-right: 40px;
  }
}

.benefits-list {
  background-color: var(--ion-color-primary);
  padding: 32px 0;

  &--color {
    color: var(--ion-color-white);
  }

  .swiper-container {
    padding-bottom: 0;

    swiper-slide {
      flex-direction: column;
    }
  }

  swiper-slide {
    .benefits-images {
      margin-bottom: 4px;
    }

    .benefits-img {
      border: 1px solid var(--ion-color-white);
      border-radius: 50%;
      position: relative;

      &.first {
        margin-right: -12px;
        z-index: 3;
      }

      &.second {
        z-index: 2;
      }

      &.third {
        margin-left: -12px;
        z-index: 1;
      }
    }

    h4 {
      text-transform: uppercase;
      font-weight: bold;
    }
  }
}

.swiper-pagination {
  bottom: 0 !important;
}

.product-items {
  margin-top: 16px;

  &-wrapper {
    padding: 16px;
    border: 2px dashed var(--ion-color-primary);
    border-radius: 8px;
  }

  .go-premium {
    &-heading {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 12px;
    }

    &-text {
      color: var(--ion-color-charcoal);
      display: block;
      font-size: 15px;
      margin-bottom: 16px;
    }

    &-button {
      width: 100%;
      background-color: var(--ion-color-base);
      color: #FFF;
      border-radius: 6px;
      display: inline-block;
      font-weight: 600;
      text-transform: uppercase;
      padding: 8px;
    }
  }
}

.product-item-welcome {
  margin: 8px auto 12px;
  padding-top: 0 !important;

  &-wrapper {
    border-radius: 8px;
  }

  .go-premium {
    &-label {
      margin-top: 15px;
      margin-bottom: 15px;
    }

    &-heading {
      font-size: 17px;
      margin-bottom: 10px;
      font-weight: 400;
    }


    &-text {
      color: var(--ion-color-white);
      display: block;
      line-height: 19px;
      font-size: 17px;
      font-weight: 400;
    }

    &-button {
      display: inline-block;
      font-weight: 600;
      text-transform: uppercase;
    }
  }
}

.go-premium-disclaimer {
  p {
    font-size: 14px;
    text-wrap: balance;
    &:first-child {
      margin-top: 0;
    }
  }
}
.billed {
  font-size: 14px;
}

.legal {
  font-size: 12px;
  line-height: 1.4;
  margin-top: 0;
  text-align: justify;
}
