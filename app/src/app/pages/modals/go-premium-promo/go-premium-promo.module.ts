import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { GoPremiumPromoPageRoutingModule } from "./go-premium-promo-routing.module";
import { GoPremiumPromoPage } from "./go-premium-promo.page";
import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    GoPremiumPromoPageRoutingModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [GoPremiumPromoPage],
})
export class GoPremiumPromoPageModule {}
