import { Component, OnInit } from "@angular/core";
import { NavController, Platform } from "@ionic/angular";
import { StateService } from "../../../services/state.service";
import { AccountService } from "../../../services/account.service";
import "jquery-countdown";

@Component({
  template: "",
  standalone: false,
})
export abstract class CountdownController {
  private sub = null;

  public data: { type: string; renewal: number } = {
    type: "swipes",
    renewal: 0,
  };

  constructor(
    public navController: NavController,
    public platform: Platform,
    public stateService: StateService,
    public accountService: AccountService,
  ) {
    // disable back button for the view
    this.sub = this.platform.backButton.subscribeWithPriority(999, () => {
      this.close();
    });
  }

  ionViewWillLeave() {
    this.data.type = "swipes";
    this.data.renewal = 0;
    // enable when exiting
    this.sub.unsubscribe();
  }

  ionViewDidEnter() {}

  public close() {
    this.navController.pop();
  }

  ngOnInit() {
    let data = this.stateService.get(
      "countdown",
      this.accountService.account.username,
    );
    if (!data) return;

    switch (data.type) {
      case "superlike":
        data.type = "super_likes";
        break;
      case "like":
      case "pass":
      case "skip":
        data.type = "swipes";
        break;
    }

    this.data = data;
  }
}
