import { Component, OnInit } from "@angular/core";
import { FlirtService } from "../../../services/flirt.service";
import * as _ from "lodash";
import { DialogHelperService } from "../../../services/dialog-helper.service";
import { ProfileService } from "../../../services/profile.service";
import { ActivatedRoute } from "@angular/router";
import { Debug } from "../../../helpers/debug";
import { NavController } from "@ionic/angular";
import { FlirtItem } from "../../../schemas/flirt";
import { DelayHelperService } from "src/app/services/delay-helper.service";
import { StateService } from "src/app/services/state.service";
import { firstValueFrom } from "rxjs";
import { AccountService } from "../../../services/account.service";
import { MiscService } from "../../../services/misc.service";
import { PurchaseService } from "src/app/services/purchase.service";

@Component({
  selector: "app-flirt",
  templateUrl: "./flirt.page.html",
  styleUrls: ["./flirt.page.scss"],
  standalone: false,
})
export class FlirtPage implements OnInit {
  /**
   * Flirt list.
   */
  public flirtList: any[] = [];

  /**
   * Flirt group id.
   */
  public flirtGroupSelected: number;

  /**
   * Active flirt group.
   */
  public flirtGroupActive: FlirtItem.type[];

  /**
   * Flirt id.
   */
  public flirtSelected: number;

  /**
   * Profile username.
   */
  public username: string;
  public accountData: Object;

  constructor(
    public flirtService: FlirtService,
    public dlg: DialogHelperService,
    public profileService: ProfileService,
    public route: ActivatedRoute,
    public dbg: Debug,
    public navCtrl: NavController,
    public delayHelper: DelayHelperService,
    public stateService: StateService,
    public accountService: AccountService,
    public miscService: MiscService,
    public purchaseService: PurchaseService,
  ) {}

  async ngOnInit() {
    this.username = this.route.snapshot.params?.username;
    this.accountData = this.stateService.get("profile", this.username);

    await this.dlg.showLoading();
    this.flirtService
      .fetchList()
      .then(() => this.getFlirts())
      .then(() => this.dlg.hideLoading());
  }

  /**
   * Get flirts.
   */
  public async getFlirts() {
    this.flirtService.flirtItems.list$.subscribe((data) => {
      this.flirtList = _.toArray(
        _.map(
          _.groupBy(data, (group) => group.category_id),
          (group) => {
            return group.map((item) => {
              item["selected"] = false;
              return item;
            });
          },
        ),
      );
    });
  }

  /**
   * Select flirt.
   */
  public select() {
    this.flirtGroupActive.forEach(
      (item) => (item["selected"] = item.flirt_id === this.flirtSelected),
    );
    this.hasSelectedFlirt();
  }

  /**
   * Send flirt.
   */
  public sendFlirt() {
    if (!this.flirtGroupSelected) return;

    this.flirtGroupActive.forEach(async (item) => {
      if (!item["selected"]) return;

      await this.dlg.showLoading("sending_flirt", "😘");

      firstValueFrom(this.flirtService.send(item.flirt_id, this.username))
        .then(() => this.dlg.showToast("", "flirt_sent", "success"))
        .catch(async (error) => {
          this.dbg.le("flirt error", error);

          //get fresh user data for rules popup
          await firstValueFrom(this.accountService.get()).catch((e) =>
            this.dbg.le("account get error", e),
          );

          if (
            error.error.errors[0].key.includes("error_force_") &&
            this.accountService.showRulesPopup()
          ) {
            this.profileService.doShowRulesPopup = true;
          } else {
            // display error if not intercepted to display something different
            if (!this.interceptError(error)) {
              this.dlg.showToastBaseOnErrorResponse(error);
            }
          }
        })
        .then(() => this.dlg.hideLoading());
    });
  }

  private interceptError(error): boolean {
    let error_key = this.miscService.getErrorKey(error, "");

    if (this.accountService.account.is_premium || !error_key) {
      // we do not intercept for premium users
      // becuase we cannot display premium dialogs for them
      // we just display the message
      return false;
    }
    // for free users

    // list of errors to display upgrade dialog to
    if (
      [
        "cant_send_messages_not_enough_coins",
        "cant_send_messages",
        "cant_send_more_flirt",
      ].indexOf(error_key) >= 0
    ) {
      // display upgrade dialog ( with flirt type )
      this.purchaseService.setDialogType("flirt_limit").openUpgradeDialog(
        () => {},
        () => {},
      );

      // intercept
      return true;
    }

    // error is not intercepted
    return false;
  }

  /**
   * Check if we have selected flirt?
   */
  public hasSelectedFlirt(): boolean {
    let status: boolean = false;
    if (!this.flirtGroupSelected) return false;
    this.flirtGroupActive.forEach((item) => {
      if (!item["selected"]) return;
      status = true;
    });
    return status;
  }

  /**
   * Select flirt group.
   *
   * @param group
   * @param index
   */
  public selectFlirtGroup(group, index) {
    this.flirtGroupSelected =
      this.flirtGroupSelected !== group[index].category_id
        ? group[index].category_id
        : null;
    this.flirtGroupActive = group;
  }
}
