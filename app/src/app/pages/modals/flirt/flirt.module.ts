import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { FlirtPageRoutingModule } from "./flirt-routing.module";

import { FlirtPage } from "./flirt.page";

import { TranslateModule } from "@ngx-translate/core";
import { RulesPopupComponentModule } from "../../../components/rules-popup/rules-popup.component.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    FlirtPageRoutingModule,
    RulesPopupComponentModule,
  ],
  declarations: [FlirtPage],
})
export class FlirtPageModule {}
