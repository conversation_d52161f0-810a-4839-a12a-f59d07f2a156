<ion-header class="ion-no-border">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="navCtrl.pop()">
        <ion-icon name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{ "title_send_flirt" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  @if (accountService.accountLoaded$ | async) {
    <app-rules-popup
      [profile]="accountService.account"
      [(doShow)]="profileService.doShowRulesPopup"
      [text_1]="'rules_popup_header_1'|translate"
      [text_2]="'rules_popup_header_2'|translate"
      [text_3]="'rules_popup_header_flirt_3'|translate"
    ></app-rules-popup>
  }

  <ion-grid class="ion-text-center">
    <ion-row class="ion-justify-content-center ion-margin-top">
      <ion-col size="12" class="ion-padding-top">
        <ion-icon class="main-icon" src="assets/icons/ico-flirt.svg"></ion-icon>
      </ion-col>
    </ion-row>

    @if (accountData) {
      <ion-row class="ion-justify-content-center">
        <ion-col size="12" size-sm="9">
          <ion-text class="ion-text-uppercase">
            <h4>{{ "send_flirt_subtitle" | translate }}</h4>
          </ion-text>
          <ion-text>
            <h4>
              {{ "send_flirt_text" | translate:{'first_name':
              (accountData['first_name'] || accountData['username']) } }}
            </h4>
          </ion-text>
        </ion-col>
      </ion-row>
    }

    <ion-row class="ion-justify-content-center">
      <ion-col size="12" size-sm="9">
        <ion-text>
          <h5>{{ "send_flirt_select" | translate }}</h5>
        </ion-text>
      </ion-col>
    </ion-row>
  </ion-grid>
  @if (flirtService.flirtItems.list$) {
    <ion-list
      class="list-container ion-padding-end"
      size="auto"
      >
      @for (flirtGroup of flirtList; track flirtGroup; let i = $index) {
        <ion-radio-group>
          <ion-list-header lines="inset" (click)="selectFlirtGroup(flirtGroup, i)">
            <ion-label
              ><strong>{{ flirtGroup[i].category_name }}</strong>
              <ion-icon
                class="ion-float-right"
                name="{{ flirtGroupSelected === flirtGroup[i].category_id ? 'chevron-down' : 'chevron-up' }}"
              ></ion-icon>
            </ion-label>
          </ion-list-header>
          @for (flirt of flirtGroup; track flirt) {
            <ion-item
              [class.hidden]="flirtGroupSelected !== flirtGroup[i].category_id"
              >
              <ion-radio
                aria-label=""
                mode="md"
                label-placement="end"
                justify="start"
                color="success"
                [value]="flirt.flirt_id"
                (click)="flirtSelected = flirt.flirt_id;select()"
                ><ion-text class="ion-text-wrap"
                >{{ flirt.flirt_text }}</ion-text
                ></ion-radio
                >
              </ion-item>
            }
          </ion-radio-group>
        }
      </ion-list>
    }

    <ion-footer>
      <ion-grid>
        <ion-row class="ion-justify-content-center ion-padding-horizonta">
          <ion-col size-sm="5">
            <ion-button
              expand="block"
              color="primary"
              (click)="sendFlirt()"
              [disabled]="!hasSelectedFlirt() || this.delayHelper.isActive"
              >
              {{ this.delayHelper.isActive ? this.delayHelper.delay :
              ('send_flirt' | translate) }}
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-footer>
  </ion-content>
