<ion-content>
  <ion-grid class="ion-text-center ion-no-padding countdown">
    <ion-row class="ion-justify-content-center features-list">
      <ion-col size="12" size-sm="9">
        <ion-text color="light">
          <h1 class="features-list--color ion-padding-horizontal">
            {{ 'cdf_title_' + data.type | translate }}
          </h1>
        </ion-text>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center">
      <ion-col size="12" class="ion-padding">
        <ion-chip [disabled]="true" class="ion-no-padding">
          <ion-icon
            name="star"
            class="ion-no-margin"
            color="warning"
          ></ion-icon>
        </ion-chip>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center ion-padding-horizontal">
      <ion-col size="12" size-sm="9">
        <ion-text>
          <h4>{{ 'cdf_counter_title_' + data.type | translate }}</h4>
        </ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="12" class="ion-justify-content-center countdown-timer">
        <div class="countdown-hours ion-text-center ion-padding-horizontal">
          <div class="cbCounterNumber" id="cdHours"></div>
          <div class="cbCounterNumberFooter">{{'hours' | translate}}</div>
        </div>
        <div class="countdown-minutes ion-text-center ion-padding-horizontal">
          <div class="cbCounterNumber" id="cdMins"></div>
          <div class="cbCounterNumberFooter">{{'minutes' | translate}}</div>
        </div>
        <div class="countdown-seconds ion-text-center ion-padding-horizontal">
          <div class="cbCounterNumber" id="cdSecs"></div>
          <div class="cbCounterNumberFooter">{{'seconds' | translate}}</div>
        </div>
      </ion-col>
    </ion-row>
    <ion-row class="ion-justify-content-center ion-padding">
      <ion-col size-sm="5">
        <ion-button
          (click)="close()"
          class="ion-margin-vertical"
          expand="block"
        >
          {{ "got_it_thanks" | translate }}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
