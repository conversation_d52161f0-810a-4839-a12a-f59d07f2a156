.features-list {
  background-color: var(--ion-color-primary);
  padding-bottom: calc(40px + 16px);
  padding-top: constant(safe-area-inset-top) !important;
  padding-top: env(safe-area-inset-top) !important;
}

ion-chip {
  height: 80px;
  width: 80px;
  border-radius: 50%;
  margin-top: calc(-40px - 16px);
  --background: var(--ion-color-light);
  opacity: 1;

  ion-icon {
    font-size: 3.5rem;
    margin: auto;
  }
}
.countdown-timer {
  display: flex;
}
.cbCounterNumber {
  font-weight: bold;
  font-size: 2.5rem;
}

.cbCounterNumberFooter {
  font-weight: bold;
}
