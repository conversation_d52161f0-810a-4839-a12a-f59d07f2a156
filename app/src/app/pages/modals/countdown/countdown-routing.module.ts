import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { CountdownPage } from "./countdown.page";

const routes: Routes = [
  {
    path: "",
    component: CountdownPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CountdownPageRoutingModule {}
