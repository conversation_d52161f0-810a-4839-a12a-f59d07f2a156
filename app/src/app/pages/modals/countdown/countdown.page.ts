import { Component, OnInit } from "@angular/core";
import { CountdownController } from "../shared/countdown";
import { NavController, Platform } from "@ionic/angular";
import { StateService } from "../../../services/state.service";
import { AccountService } from "../../../services/account.service";

@Component({
  selector: "app-countdown",
  templateUrl: "./countdown.page.html",
  styleUrls: ["./countdown.page.scss"],
  standalone: false,
})
export class CountdownPage extends CountdownController implements OnInit {
  constructor(
    public navController: NavController,
    public platform: Platform,
    public stateService: StateService,
    public accountService: AccountService,
  ) {
    super(navController, platform, stateService, accountService);
  }
}
