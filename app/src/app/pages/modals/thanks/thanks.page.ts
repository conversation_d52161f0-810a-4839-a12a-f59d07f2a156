import { Component, OnInit } from "@angular/core";
import { NavController, Platform } from "@ionic/angular";

@Component({
  selector: "app-thanks",
  templateUrl: "./thanks.page.html",
  styleUrls: ["./thanks.page.scss"],
  standalone: false,
})
export class ThanksPage implements OnInit {
  private sub = null;

  constructor(
    private navController: NavController,
    private platform: Platform,
  ) {
    // disable back button for the view
    this.sub = this.platform.backButton.subscribeWithPriority(999, () => {
      this.close();
    });
  }

  ionViewWillLeave() {
    // enable when exiting
    this.sub.unsubscribe();
  }

  public close() {
    this.navController.pop();
  }

  ngOnInit() {}
}
