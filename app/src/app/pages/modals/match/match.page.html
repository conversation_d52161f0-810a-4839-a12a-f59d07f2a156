<ion-content>
  <ion-grid class="ion-text-center">
    <ion-row class="ion-justify-content-center ion-margin-top">
      <ion-col size="12" class="ion-padding-top">
        <ion-icon name="heart"></ion-icon>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center">
      <ion-col size="12" size-sm="9">
        <ion-text>
          <h1>{{ "val_you_have_match" | translate }}</h1>
        </ion-text>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center">
      @if (data) {
        <ion-col size="12" class="ion-padding">
          <ion-avatar>
            <img [src]="accountService.account.avatar_url" />
          </ion-avatar>
          <ion-avatar>
            <img [src]="data.avatar_url" />
          </ion-avatar>
        </ion-col>
      }
      @if (!data) {
        <ion-col size="12" class="ion-padding">
          <ion-avatar>
            <ion-skeleton-text></ion-skeleton-text>
          </ion-avatar>
          <ion-avatar>
            <ion-skeleton-text></ion-skeleton-text>
          </ion-avatar>
        </ion-col>
      }
    </ion-row>

    <ion-row class="ion-justify-content-center">
      <ion-col class="ion-padding">
        <ion-text>
          @if (data) {
            <h4>
              {{ "val_you_and_liked" | translate:{'first_name': (data?.first_name
              || data?.username) } }}
            </h4>
          }
        </ion-text>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center ion-padding-horizontal">
      <ion-col size-sm="5">
        <ion-button expand="block" color="primary" (click)="sendMessage()">
          {{ "send_message" | translate }}
        </ion-button>
        <ion-button
          (click)="close()"
          class="ion-margin-vertical"
          expand="block"
          fill="clear"
          >
          {{ "do_it_later" | translate }}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
