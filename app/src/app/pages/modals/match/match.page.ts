import { Component, OnInit } from "@angular/core";
import { NavController, Platform } from "@ionic/angular";
import { ActivatedRoute, Router } from "@angular/router";
import { ProfileUser } from "../../../schemas/profile-user";
import { StateService } from "../../../services/state.service";
import _ from "lodash";
import { ProfileService } from "../../../services/profile.service";
import { AccountService } from "../../../services/account.service";

@Component({
  selector: "app-match",
  templateUrl: "./match.page.html",
  styleUrls: ["./match.page.scss"],
  standalone: false,
})
export class MatchPage implements OnInit {
  private sub = null;

  /**
   * Profile data.
   */
  public data: ProfileUser.type;

  /**
   * Profile username.
   */
  public username: string = "";

  constructor(
    private navController: NavController,
    private platform: Platform,
    private stateService: StateService,
    private route: ActivatedRoute,
    private profileService: ProfileService,
    public accountService: AccountService,
    private router: Router,
  ) {
    // disable back button for the view
    this.sub = this.platform.backButton.subscribeWithPriority(999, async () => {
      await this.close();
    });

    this.username = this.route.snapshot.params?.username;
  }
  public ionViewDidEnter() {
    this.data = this.stateService.get("match", this.username);

    if (_.isEmpty(this.data)) {
      this.profileService.get(this.username).subscribe({
        next: (data) => {
          // set data
          this.data = data.data;
        },
      });
    }
  }

  ionViewWillLeave() {
    // enable when exiting
    this.sub.unsubscribe();
  }

  public async close() {
    await this.navController.pop();
  }

  ngOnInit() {}

  public async sendMessage() {
    await this.close();
    this.router.navigate(["app/chat-messages", this.username]);
  }
}
