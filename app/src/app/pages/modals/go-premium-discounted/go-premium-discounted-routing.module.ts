import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { GoPremiumDiscountedPage } from "./go-premium-discounted.page";

const routes: Routes = [
  {
    path: "",
    component: GoPremiumDiscountedPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class GoPremiumDiscountedPageRoutingModule {}
