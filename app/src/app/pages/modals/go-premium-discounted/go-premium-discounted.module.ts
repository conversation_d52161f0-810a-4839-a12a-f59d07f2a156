import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { GoPremiumDiscountedPageRoutingModule } from "./go-premium-discounted-routing.module";

import { GoPremiumDiscountedPage } from "./go-premium-discounted.page";

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    GoPremiumDiscountedPageRoutingModule,
  ],
  declarations: [GoPremiumDiscountedPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class GoPremiumDiscountedPageModule {}
