import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild } from "@angular/core";
import { NavController, Platform } from "@ionic/angular";
import { DeviceInfoService } from "src/app/services/device-info.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { PurchaseService } from "src/app/services/purchase.service";
import { IapItem } from "src/app/services/iap-item.interface";
import { environment as Env } from "../../../../environments/environment";
import { StateService } from "../../../services/state.service";
import { ProfileUser } from "../../../schemas/profile-user";
import { TranslateService } from "@ngx-translate/core";
import { RefresherService } from "../../../services/refresher.service";

@Component({
  selector: "app-go-premium-discounted",
  templateUrl: "./go-premium-discounted.page.html",
  styleUrls: ["./go-premium-discounted.page.scss"],
  standalone: false,
})
export class GoPremiumDiscountedPage implements OnInit, OnDestroy {
  // @ViewChild(IonSlides, { static: false }) slides: IonSlides;

  public sendMessageProfile: ProfileUser.type;
  public profileFullSizeLimit: ProfileUser.type;

  constructor(
    public purchaseService: PurchaseService,
    private navController: NavController,
    private dialog: DialogHelperService,
    private deviceInfo: DeviceInfoService,
    private platform: Platform,
    private stateService: StateService,
    private translateService: TranslateService,
    private refresherService: RefresherService,
  ) {}

  private sub = null;

  ngOnDestroy() {
    // reset dialog type
    this.purchaseService.setDialogType("");
  }

  ionViewDidEnter() {
    // disable back button for the view
    this.sub = this.platform.backButton.subscribeWithPriority(999, () => {
      this.close();
    });
  }

  get dialogType() {
    return this.purchaseService.dialogType;
  }

  ionViewWillLeave() {
    // enable when exiting
    this.sub.unsubscribe();

    // reset dialog type
    this.purchaseService.setDialogType("");
  }

  public inProgress() {
    return this.purchaseService.inProgress();
  }

  get env() {
    return Env;
  }

  public data: IapItem[];

  public selectedItem: IapItem;

  public selectProduct(item: IapItem) {
    this.selectedItem = item;
  }

  public isIos() {
    return this.deviceInfo.platform == "ios";
  }

  ngOnInit() {
    if (this.purchaseService.discounts.length == 0) {
      // initialize mock if page opened directly for design ( for example )
      this.dialog.showToast(
        "",
        "DEV_ONLY: Dialog was not initialized properly, using mock data!",
        "error",
      );
      this.purchaseService.init_MOCK();
    }

    // center item as default
    this.selectProduct(
      this.purchaseService.discounts[1] || this.purchaseService.discounts[0],
    );

    this.data = this.purchaseService.discounts;
  }

  public upgradeClicked() {
    this.purchaseService.buySubscription(this.selectedItem.store_id);
    this.refresherService.refreshEnd$.subscribe(() => this.navController.pop());
  }

  public close() {
    this.navController.pop().then(() => {
      this.purchaseService.executeCloseCallback();
    });
  }
}
