import { ComponentFixture, TestBed, waitForAsync } from "@angular/core/testing";
import { IonicModule } from "@ionic/angular";

import { GoPremiumDiscountedPage } from "./go-premium-discounted.page";

describe("GoPremiumDiscountedPage", () => {
  let component: GoPremiumDiscountedPage;
  let fixture: ComponentFixture<GoPremiumDiscountedPage>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [GoPremiumDiscountedPage],
      imports: [IonicModule.forRoot()],
    }).compileComponents();

    fixture = TestBed.createComponent(GoPremiumDiscountedPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
