import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { ProfileTabsPage } from "./profile-tabs.page";

const routes: Routes = [
  {
    path: "",
    component: ProfileTabsPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ProfileTabsPageRoutingModule {}
