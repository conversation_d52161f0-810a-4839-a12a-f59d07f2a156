<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button
        text=""
        icon="chevron-back-sharp"
        [class.hidden]="this.doHideBackButton() || this.isLastPage()"
      ></ion-back-button>
    </ion-buttons>
    <ion-title>
      <img
        style="pointer-events: none"
        src="assets/img/app-logo.svg"
        class="logoSvg"
        />
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-progress-bar
    [value]="(1/welcomeWizard.pages.length)*welcomeWizard.getRealIndex()"
  ></ion-progress-bar>

  <ion-grid class="ion-text-center ion-padding-horizontal">
    <ion-row class="ion-justify-content-center">
      @if (pageData.title) {
        <ion-col size="12" size-sm="8">
          <h1>{{ replaceTags(pageData.title) }}</h1>
        </ion-col>
      }

      @if (pageData.photo) {
        <ion-col size="9" size-sm="6">
          <img class="info-photo" [src]="pageData.photo" />
        </ion-col>
      }

      @if (pageData.description) {
        <ion-col
          class="ion-padding-vertical"
          size="12"
          size-sm="8"
          >
          <ion-text>{{ replaceTags(pageData.description) }}</ion-text>
        </ion-col>
      }

      @if (pageData.note) {
        <ion-col size="12" size-sm="8">
          <ion-text><small>{{ replaceTags(pageData.note) }}</small></ion-text>
        </ion-col>
      }

      <ion-col size="12" size-sm="5">
        <ion-button
          class="ion-margin-bottom"
          expand="block"
          (click)="mainButtonClick()"
          [disabled]="isProcessing() || !canAdvance()"
          >{{ getButtonText() }}</ion-button
          >
          @if (pageData.button_no) {
            <a (click)="alternateButtonClick()">
              {{ getAlternateButtonText() }}
            </a>
          }
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-content>
