import { Component, OnInit } from "@angular/core";
import { WelcomeWizardPage } from "../welcome-wizard/welcome-wizard.page";
import { AccountService } from "../../services/account.service";
import { Platform } from "@ionic/angular";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { Debug } from "../../helpers/debug";
import { WelcomeWizardService } from "../../services/welcome-wizard.service";
import { TranslateService } from "@ngx-translate/core";
import { Router } from "@angular/router";
import { MiscService } from "../../services/misc.service";
import { WelcomeProfileService } from "../../services/welcome-profile.service";
import { QueueCommandService } from "src/app/services/queue/queue-command.service";
import { PurchaseService } from "src/app/services/purchase.service";
import { AnalyticsService } from "src/app/services/analytics.service";
import { TME_welcome_form_completed } from "src/app/classes/gtm-event.class";
import { UtilsService } from "src/app/services/utils.service";
@Component({
  selector: "app-wpg-info",
  templateUrl: "./wpg-info.page.html",
  styleUrls: ["./wpg-info.page.scss"],
  standalone: false,
})
export class WpgInfoPage extends WelcomeWizardPage implements OnInit {
  constructor(
    public welcomeProfileService: WelcomeProfileService,
    public ownProfile: AccountService,
    public misc: MiscService,
    public welcomeWizard: WelcomeWizardService,
    public debug: Debug,
    public dialog: DialogHelperService,
    public translate: TranslateService,
    public router: Router,
    public queueCommand: QueueCommandService,
    public purchaseService: PurchaseService,
    public analytics: AnalyticsService,
    public utils: UtilsService,
  ) {
    super(
      welcomeProfileService,
      welcomeWizard,
      debug,
      dialog,
      translate,
      router,
      misc,
      analytics,
      utils,
    );
  }

  ngOnInit() {}

  public canAdvance() {
    return true;
  }

  private _modifiedData = null;

  /**
   * We're overriding this method to add some extra data to the page data
   * @returns
   */
  public get modifiedPageData() {
    if (!this._modifiedData) {
      this._modifiedData = this.pageData;
      this._modifiedData["description"] = this.translate.instant(
        "translate_welcome_info_page_offer_1m",
      );
    }

    return this._modifiedData;
  }

  public async save(): Promise<any> {
    // @todo check if it's update and if it is, then set update page open for last page,
    // or better yet open it from here ... maybe
    // this should always be the last step ...

    // try it with skip button ...

    this.debug.l("info page data", this.pageData);

    this.analytics.logEvent(
      "welcome_form_completed",
      <TME_welcome_form_completed>{},
    );

    if (this.pageData.action == "upgrade") {
      let offerProductId = null; //this.purchaseService.getSpecialOffer();

      // this.dbg.log("offer product id", offerProductId);

      // see if we

      if (!this.ownProfile.isPremium() || !offerProductId) {
        // this.dbg.log("EVT! dialog ? :)");

        if (!this.misc.isNativePlatform()) {
          // this.dbg.log("EVT! dialog open :)");

          await this.dialog.showToast(
            "",
            "Upgrade page can only be opened on native device!",
            "error",
          );

          /*
          this.purchaseService.setOnCloseHandler(async ()=>{
            await this.welcomeWizard.closeWizard();
          });

          this.router.navigate(['/app/modals/try-premium'], { replaceUrl: true });
          */
        } else {
          this.welcomeWizard.closed();

          /*
          this.purchaseService.openTrialDialog(
            () => {
              this.welcomeWizard.closeWizard();
            },
            () => {
              this.welcomeWizard.closeWizard();
            }
          );
          */

          this.purchaseService.openDiscountedDialog(
            () => {
              this.welcomeWizard.closeWizard();
            },
            () => {
              this.welcomeWizard.closeWizard();
            },
          );

          /*
          this.purchaseService.setPromoDialogType("welcome").openCampaignDialog(
            "welcomeWizardOffer",
            () => {
              this.welcomeWizard.closeWizard();
            },
            () => {
              this.welcomeWizard.closeWizard();
            }
          );
          */

          /*
          alert(
            "@todo show upgrade page, and if that's cancelled, then execute the closed() function to continue queue"
          );
          */

          //await this.router.navigate(["app/modals/try-premium"], { replaceUrl: true });
          //this.router.navigate(["app/modals/try-premium"]);

          //this.welcomeWizard.onWizardClose(() => {
          //   // if user is not premium open offer
          //
          //   // this.purchaseService.openUpgrade(
          //   //   () => {},
          //   //   [offerProductId],
          //   //   true,
          //   //   () => {
          //   //     console.log("On close upgrade dialog!");
          //   //     this.events.publish(AppEvents.SyncQueueNext);
          //   //   },
          //   //   "welcomeWizardOffer"
          //   );
          //});
        }
      } else {
        // go on with queue
        // this.events.publish(AppEvents.SyncQueueNext);
        this.queueCommand.gotoNextItem();
      }
    }

    return Promise.resolve();
  }
}
