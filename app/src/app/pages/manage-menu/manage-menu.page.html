<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ "title_manage_account" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

@if (accountService.account) {
  <ion-content>
    <ion-list class="ion-padding-bottom">
      <ion-list-header color="light" class="ion-text-uppercase">
        {{ "account_info" | translate }}
      </ion-list-header>
      <ion-item (click)="usernameToast()">
        <ion-label position="fixed" class="ion-text-wrap"
          >{{ "username" | translate }}:</ion-label
          >
          <ion-text color="medium" class="ion-text-right" slot="end">
            {{ accountService.account.username }}
          </ion-text>
        </ion-item>
        <ion-item>
          <ion-input
            label="{{ 'email' | translate }}:"
            class="ion-text-right"
            placeholder="{{ accountService.account.email }}"
            [(ngModel)]="accountService.account.email"
          ></ion-input>
        </ion-item>
        <div [class.hidden]="!localChange" style="text-align: center">
          <small
            ><ion-icon name="information-circle"></ion-icon> {{
            "please_confirm_email" | translate }}</small
            >
          </div>
          <ion-item lines="none">
            <ion-button
              size="default"
              class="ion-margin-vertical"
              (click)="updateEmail()"
              [disabled]="isUpdatingEmail || ! isEmailChanged() || ! isValidEmail()"
              >{{ "update_email" | translate }}</ion-button
              >
            </ion-item>
          </ion-list>
          <ion-list class="ion-padding-bottom">
            <ion-list-header color="light" class="ion-text-uppercase">
              {{ "change_password" | translate }}
              <button
                ion-button
                clear
                type="button"
                item-right
                (click)="showHidePassword()"
                class="show-hide-password"
                >
                <ion-icon
                  [name]="passwordType === 'text' ? 'eye' : 'eye-off'"
                ></ion-icon>
              </button>
            </ion-list-header>
            <ion-item>
              <ion-input
                placeholder="{{ 'old_password' | translate }}"
                [(ngModel)]="password.old"
                [type]="passwordType"
                name="password_current"
              ></ion-input>
            </ion-item>
            <ion-item>
              <ion-input
                placeholder="{{ 'new_password' | translate }}"
                [(ngModel)]="password.new"
                [type]="passwordType"
                name="password_new"
              ></ion-input>
            </ion-item>
            <ion-item>
              <ion-input
                placeholder="{{ 'retype_new_password' | translate }}"
                [(ngModel)]="password.confirm"
                [type]="passwordType"
                name="password_new_confirm"
              ></ion-input>
            </ion-item>
            <ion-item lines="none">
              <ion-button
                size="default"
                class="ion-margin-vertical"
                (click)="updatePassword()"
                [disabled]="!canUpdatePassword() || isUpdatingPassword"
                >{{ "update_password" | translate }}</ion-button
                >
              </ion-item>
            </ion-list>
            <ion-list class="ion-padding-bottom">
              <ion-list-header color="light" class="ion-text-uppercase">
                {{ "account_options" | translate }}
              </ion-list-header>
              <!--
              <ion-item button [disabled]="loaderService.isLoading()" (click)="downloadAccJson()">
                <ion-label>{{ "download_json" | translate }}</ion-label>
                <ion-spinner *ngIf="loaderService.isLoading() || false"></ion-spinner>
              </ion-item>
              -->
              <ion-item button (click)="deactivateAccount()">
                <ion-label>{{ "deactivate_account" | translate }}</ion-label>
              </ion-item>
              <ion-item button (click)="deleteAccount()">
                <ion-label>{{ "delete_account" | translate }}</ion-label>
              </ion-item>
            </ion-list>
          </ion-content>
        }
