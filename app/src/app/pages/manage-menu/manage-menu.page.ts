import { Component, OnInit } from "@angular/core";
import { AccountService } from "../../services/account.service";
import { ApiParams } from "../../services/api/api.params";
import { Debug } from "../../helpers/debug";
import AccountUpdate = ApiParams.AccountUpdate;
import { DialogHelperService } from "../../services/dialog-helper.service";
import { Router } from "@angular/router";
import { StateService } from "src/app/services/state.service";
import { LoaderService } from "../../services/loader.service";

@Component({
  selector: "app-manage-menu",
  templateUrl: "./manage-menu.page.html",
  styleUrls: ["./manage-menu.page.scss"],
  standalone: false,
})
export class ManageMenuPage implements OnInit {
  public bckAccountData: string = null;

  /**
   * Account update params.
   */
  private accountUpdate: AccountUpdate.type | any = {};

  /**
   * Password.
   */
  public password: { old: string; new: string; confirm: string } = {
    old: "",
    new: "",
    confirm: "",
  };

  /**
   * Backup first name.
   */
  private fName: string = "";

  /**
   * Backup email.
   */
  private email: string = "";

  /**
   * Password input type. (show/hide password)
   */
  public passwordType: string = "password";

  /**
   * @type {boolean}
   */
  public isUpdatingEmail: boolean = false;

  /**
   * Email update error?
   */
  public emailUpdateError: boolean = false;

  /**
   * @type {boolean}
   */
  public isUpdatingPassword: boolean = false;

  constructor(
    public accountService: AccountService,
    public dbg: Debug,
    public dialog: DialogHelperService,
    public router: Router,
    public stateService: StateService,
    public loaderService: LoaderService,
  ) {}

  public localChange: boolean = false;

  ngOnInit() {
    if (this.stateService.get("manageAccount.updatedEmail", "", "")) {
      this.localChange = true;
      this.accountService.account.email = this.stateService.get(
        "manageAccount.updatedEmail",
        "",
        "",
      );
    }

    this.accountService.account.email;

    this.fName = JSON.stringify(this.accountService.account.first_name);
    this.email = JSON.stringify(this.accountService.account.email);
  }

  ionViewDidLeave() {
    if (this.emailUpdateError) {
      this.accountService.account.email = JSON.parse(this.email);
    }
  }

  /**
   * Username tooltip/toast.
   */
  public usernameToast() {
    this.dialog.showToast("", "username_change", "info");
  }

  /**
   * Update email.
   */
  public updateEmail(): void {
    this.emailUpdateError = false;
    if (!this.isEmailChanged()) return;

    this.isUpdatingEmail = true;
    this.accountUpdate.email = this.accountService.account.email;

    this.accountService
      .update(this.accountUpdate)
      .then((res) => {
        //this.accountService.account = res.data;

        this.stateService.set(
          "manageAccount.updatedEmail",
          "",
          this.accountService.account.email,
        );
        this.localChange = true;

        this.bckAccountData = JSON.stringify(res.data);
        if (!this.showMessage(res)) {
          this.localChange = false;
        }

        this.backupAndResetEmail();
      })
      .catch((error) => {
        this.dialog.showToastBaseOnErrorResponse(error);
        this.dbg.le("update email error", error);
        this.emailUpdateError = true;
      })
      .then(() => (this.isUpdatingEmail = false));
  }

  /**
   * Update password.
   */
  public updatePassword(): void {
    if (!this.canUpdatePassword()) return;

    this.isUpdatingPassword = true;
    this.accountUpdate.password_current = this.password.old;
    this.accountUpdate.password_new = this.password.new;
    this.accountUpdate.password_new_re = this.password.confirm;

    this.accountService
      .update(this.accountUpdate)
      .then((res) => {
        this.resetPassword();
        this.showMessage(res);
      })
      .catch((error) => {
        this.dialog.showToastBaseOnErrorResponse(error);
        this.dbg.le("password update error", error);
      })
      .then(() => (this.isUpdatingPassword = false));
  }

  /**
   * Toggle input type (password/text).
   */
  public showHidePassword() {
    this.passwordType = this.passwordType === "text" ? "password" : "text";
  }

  /**
   * Check if we can update password?
   *
   * @returns {boolean}
   */
  public canUpdatePassword(): boolean {
    return (
      this.password.new === this.password.confirm &&
      this.password.new.length > 5 &&
      this.password.confirm.length > 5 &&
      this.password.old.length > 5
    );
  }

  /**
   * Check if email is changed.
   *
   * @returns {boolean}
   */
  public isEmailChanged() {
    return JSON.parse(this.email) !== this.accountService.account.email;
  }

  /**
   * Is valid email?
   *
   * @return {boolean}
   */
  public isValidEmail() {
    return /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
      this.accountService.account.email,
    );
  }

  /**
   * Deactivate account.
   */
  public deactivateAccount(): void {
    this.router.navigate(["/app/deactivation"]);
  }

  /**
   * Delete account.
   */
  public deleteAccount(): void {
    this.router.navigate(["/app/delete-account"]);
  }

  /**
   * Show message.
   */
  private showMessage(data): boolean {
    if (!data.messages.length) return false;

    this.dialog.showToast("", data.messages[0].message);
    return true;
  }

  /**
   * Backup and reset email.
   */
  private backupAndResetFName(): void {
    this.fName = JSON.stringify(this.accountService.account.first_name);
    this.accountUpdate = {};
  }

  /**
   * Backup and reset email.
   */
  private backupAndResetEmail(): void {
    this.email = JSON.stringify(this.accountService.account.email);
    this.accountUpdate = {};
  }

  /**
   * Reset password.
   */
  private resetPassword(): void {
    this.password.old = "";
    this.password.new = "";
    this.password.confirm = "";
    this.accountUpdate = {};
  }

  public async downloadAccJson() {
    this.loaderService.status = true;
    this.accountService.downloadJson().subscribe((res) => {
      const blob = new Blob([JSON.stringify(res)], {
        type: "application/json",
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "account-data.json";
      a.click();
      window.URL.revokeObjectURL(url);
      this.loaderService.status = false;
    });
  }
}
