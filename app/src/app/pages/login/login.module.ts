import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { LoginPageRoutingModule } from "./login-routing.module";

import { LoginPage } from "./login.page";
import { TranslateModule } from "@ngx-translate/core";
import { FormErrorListComponentModule } from "src/app/components/form-error-list/form-error-list.component.module";
import AutoFocusModule from "src/app/auto-focus.module";
import TrimModule from "src/app/directives/trim.directive.module";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    LoginPageRoutingModule,
    TranslateModule.forChild(),
    FormErrorListComponentModule,
    AutoFocusModule,
    TrimModule,
  ],
  declarations: [LoginPage],
})
export class LoginPageModule {}
