<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ "title_sign_in" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row class="ion-justify-content-center">
      <ion-col size-md="6" class="ion-text-center">
        <h3>{{ "welcome_back" | translate }}</h3>
      </ion-col>
    </ion-row>
    <ion-row class="ion-justify-content-center ion-padding-horizontal">
      <ion-col size-md="6">
        <form [formGroup]="loginForm" (ngSubmit)="doLogin()">
          <ion-item class="ion-no-padding">
            <ion-input
              inputTrim
              appAutoFocus
              formControlName="usernameEmail"
              type="text"
              autocomplete="username"
              placeholder="{{ 'username_or_email' | translate }}"
              required
              [ngModel]="vUsername"
              #usernameInput
            ></ion-input>
          </ion-item>
          <ion-item class="ion-no-padding">
              <ion-input
                formControlName="password"
                type="password"
                autocomplete="password"
                placeholder="{{ 'password' | translate }}"
                required
                [ngModel]="vPassword"
                #passwordInput
              ><ion-input-password-toggle slot="end"></ion-input-password-toggle>
            </ion-input>
          </ion-item>
          <ion-item class="ion-no-padding ion-text-right" lines="none">
            <a
              [routerLink]="['/forgot-password']"
              class="ion-no-margin small-text"
              slot="end"
              >{{ "forgot_password" | translate }}</a
            >
          </ion-item>
          <app-form-error-list [errorList]="getErrors()"></app-form-error-list>
          <ion-button type="submit" expand="block" [disabled]="isLoading()">
            {{ "sign_in" | translate }}
            <ion-spinner name="crescent" [class.hidden]="!isLoading()"></ion-spinner>
          </ion-button>
        </form>
      </ion-col>
    </ion-row>

    <ion-row
      class="ion-padding-horizontal ion-no-padding ion-justify-content-center"
    >
      <ion-col class="ion-text-center" size-md="6" style="text-align: center">
        <hr
          [class.hidden]="!this.doShowAppleLogin()"
          class="horizontal-line"
          [attr.data-content]="'or' | translate"
        />
        <ion-button
          [class.hidden]="!this.doShowAppleLogin()"
          id="apple_signup_button"
          class="ion-margin-vertical"
          expand="block"
          color="dark"
          (click)="loginapple()"
        >
          <ion-icon slot="start" name="logo-apple"></ion-icon>
          <span>{{ 'continue_with_apple' | translate }}</span>
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
