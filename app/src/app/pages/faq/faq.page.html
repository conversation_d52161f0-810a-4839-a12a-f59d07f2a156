<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ "title_faq" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row class="ion-justify-content-center ion-margin-top">
      <ion-col size="12" size-sm="8">
        <ion-searchbar
          [(ngModel)]="filterQuery"
          [showCancelButton]="true"
          placeholder="{{'search_faq' | translate}}"
        ></ion-searchbar>
      </ion-col>
      <ion-col
        size="12"
        size-sm="8"
        class="ion-text-center ion-padding-vertical"
        >
        <ion-text>{{ "faq_subtitle" | translate }}</ion-text>
      </ion-col>
      <ion-col
        size="12"
        size-sm="8"
        class="ion-text-center ion-margin-top"
        [class.hidden]="!this.faqService.inProgress"
        >
        <ion-text
          ><ion-spinner name="crescent"></ion-spinner> {{ "loading_content" |
          translate }}</ion-text
          >
        </ion-col>
      </ion-row>

      @if (this.faqService.items) {
        <ion-row class="ion-justify-content-center">
          <ion-col size="12" size-sm="10" class="ion-text-center">
            @for (group of this.faqService.items; track group) {
              <div>
                <h2 [class.hidden]="(group.data | filter:filterQuery).length <= 0">
                  {{ group.key }}
                </h2>
                @for (itemf of group.data | filter:filterQuery; track itemf) {
                  <ion-accordion-group
                    >
                    <ion-accordion value="{{ itemf.help_id }}">
                      <ion-item slot="header">
                        <ion-label class="ion-text-wrap item-subject"
                          >{{ itemf.help_subject }}</ion-label
                          >
                        </ion-item>
                        <ion-list slot="content">
                          <ion-item lines="none">
                            <ion-label class="ion-text-wrap">
                              <app-external-content
                                [content]="itemf.help_answer"
                              ></app-external-content>
                            </ion-label>
                          </ion-item>
                        </ion-list>
                      </ion-accordion>
                    </ion-accordion-group>
                  }
                </div>
              }
            </ion-col>
          </ion-row>
        }
      </ion-grid>
    </ion-content>
