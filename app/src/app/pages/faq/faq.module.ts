import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { FaqPageRoutingModule } from "./faq-routing.module";
import { FaqPage } from "./faq.page";
import { TranslateModule } from "@ngx-translate/core";
import { ExternalContentComponentModule } from "src/app/components/external-content/external-content.component.module";
import { PipesModule } from "../../pipes/pipes.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    FaqPageRoutingModule,
    ExternalContentComponentModule,
    PipesModule,
  ],
  declarations: [FaqPage],
})
export class FaqPageModule {}
