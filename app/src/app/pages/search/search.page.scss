.nt-indicator {
  pointer-events: none;
  background-color: var(--toolbar-nt-indicator);
  width: 12px;
  height: 12px;
  border: 2px solid var(--toolbar-nt-indicator-border);
  border-radius: 50%;
  display: block;
  position: absolute;
  right: -5px;
  top: 0;

  &.for-menu {
    right: 10px;
    top: 15px;
  }
}

.ios .nt-indicator.for-menu {
  top:9px;
  right: 5px;
}

ion-item-group {
  margin-bottom: 16px;
}

ion-item {
  --border-width: 0;
}

ion-item-divider {
  --background: transparent;
  border: 0;

  ion-label {
    color: #000;
    font-size: 14px;
    font-weight: 600;
  }
}

ion-range {
  padding-left: 0;
  padding-right: 0;

  .range-label {
    max-width: fit-content;
  }
}

.md {
  ion-item-divider {
    ion-label {
      margin: 0;
    }
  }

  ion-checkbox {
    width: 100%;
    margin: 6px 8px 6px 4px;
  }

  .pv-checkboxes {
    ion-item {
      --min-height: 40px;
    }
    ion-checkbox {
      margin: 6px 8px 6px 4px;
    }
  }

  ion-label.age-from, ion-label.age-to {
    font-size: 14px;
    max-width: min-content;
  }
}
