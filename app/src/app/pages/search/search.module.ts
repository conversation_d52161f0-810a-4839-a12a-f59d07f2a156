import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { SearchPageRoutingModule } from "./search-routing.module";

import { SearchPage } from "./search.page";
import { TranslateModule } from "@ngx-translate/core";
import ZipModule from "src/app/directives/zip.directive.module ";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    SearchPageRoutingModule,
    ZipModule,
  ],
  declarations: [SearchPage],
})
export class SearchPageModule {}
