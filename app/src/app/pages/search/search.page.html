<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-menu-button menu="main"> </ion-menu-button>
      <span
        [class.hidden]="!this.unreadCountService.hasInbox"
        class="nt-indicator for-menu"
        >
      </span>
    </ion-buttons>
    <ion-title> {{ 'tabs_search' | translate }} </ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" [routerLink]="'/app/inbox'"
        ><ion-icon name="mail"></ion-icon>
        <span [class.hidden]="!this.unreadCountService.hasInbox" class="nt-indicator">
        </span>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  @if (accountService.account) {
    <ion-list class="ion-no-padding">
      <ion-item-group class="ion-margin-top">
        <ion-item-divider>
          <ion-label class="main-label ion-text-uppercase"
            >{{ 'looking_for' | translate }}</ion-label
            >
          </ion-item-divider>
          <ion-item lines="none" class="ion-text-capitalize">
            <ion-select
              interface="popover"
              placeholder="Select One"
              [(ngModel)]="selectedGender"
              >
              @for (gender of genders; track gender) {
                <ion-select-option
                  class="ion-text-capitalize"
                  [value]="gender.id | number"
                  >{{ gender.name | translate }}</ion-select-option
                  >
                }
              </ion-select>
            </ion-item>
          </ion-item-group>
          <ion-item-group>
            <ion-item-divider>
              <ion-label class="main-label">{{ 'age_from' | translate }}</ion-label>
            </ion-item-divider>
            <ion-item lines="none" class="text-field">
              <ion-range
                [pin]="searchOptions.showPin"
                [dualKnobs]="true"
                [min]="searchOptions.ageFrom"
                [max]="searchOptions.ageTo"
                [step]="searchOptions.stepAge"
                [(ngModel)]="selectedAges"
                color="secondary"
                (ionInput)="selectAges($event)"
                >
                <ion-label slot="start" class="age-from range-label"
                  >{{ selectedAges.lower }}</ion-label
                  >
                  <ion-label slot="end" class="age-to range-label"
                    >{{ selectedAges.upper }}</ion-label
                    >
                  </ion-range>
                </ion-item>
              </ion-item-group>
              <ion-item-group>
                <ion-item-divider>
                  <ion-label class="main-label">{{ "located_in" | translate }}</ion-label>
                </ion-item-divider>
                @if (locationService.location) {
                  <ion-item lines="none">
                    <ion-label
                      >{{
                      locationService.location.country_list[accountService.account.country]
                      }}</ion-label
                      >
                    </ion-item>
                  }
                </ion-item-group>
                <ion-item-group>
                  <ion-item-divider>
                    <ion-label class="main-label"
                      >{{ "zip_postal_code" | translate }}</ion-label
                      >
                    </ion-item-divider>
                    <ion-item lines="none">
                      <ion-input
                        inputZip
                        [placeholder]="'zip_code' | translate"
                        [value]="accountService.account.zip"
                        [(ngModel)]="zipCode"
                      ></ion-input>
                    </ion-item>
                  </ion-item-group>
                  <ion-item-group>
                    <ion-item-divider>
                      <ion-label class="main-label">{{ "distance" | translate }}</ion-label>
                    </ion-item-divider>
                    <ion-item lines="none">
                      <ion-select
                        interface="popover"
                        placeholder="Select One"
                        [(ngModel)]="selectedDistance"
                        >
                        @for (distance of availableDistances; track distance) {
                          <ion-select-option
                            [value]="distance"
                            >
                            @if (distance > 0) {
                              <span
                                >{{ distance }} {{ 'miles_distance' | translate }} / {{ distance *
                                1.6 }} {{ 'kilometer_distance' | translate }}</span
                                >
                              }
                              @if (distance <= 0) {
                                <span>{{ 'no_limit' | translate }}</span>
                              }
                            </ion-select-option>
                          }
                        </ion-select>
                      </ion-item>
                    </ion-item-group>
                    <ion-item-group class="pv-checkboxes">
                      <ion-item lines="none">
                        <ion-checkbox
                          label-placement="end"
                          justify="start"
                          [(ngModel)]="withPhotos"
                          >{{ 'only_with_photos' | translate }}</ion-checkbox
                          >
                        </ion-item>
                        <!--       <ion-item lines="none" *ngIf="accountService.account.is_premium">
                        <ion-label>Only with videos</ion-label>
                        <ion-checkbox slot="start" [(ngModel)]="withVideos"></ion-checkbox>
                      </ion-item>
                      -->
                    </ion-item-group>
                  </ion-list>
                }

                <ion-button expand="block" class="ion-margin-horizontal" (click)="find()">
                  {{ "find_match" | translate }}
                </ion-button>
              </ion-content>
