import { Component, OnInit } from "@angular/core";
import { UnreadCountService } from "../../services/unread-count.service";
import { environment as Env } from "../../../environments/environment";
import { AccountService } from "../../services/account.service";
import { LocationService } from "../../services/location.service";
import { SearchService } from "../../services/search.service";
import { Router } from "@angular/router";
import { StateService } from "../../services/state.service";
import { ApiParams } from "../../services/api/api.params";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { ZipValidator } from "src/app/helpers/zip-validator.class";
import { TranslateService } from "@ngx-translate/core";
import { AnalyticsService } from "src/app/services/analytics.service";
import { TME_search_filter } from "src/app/classes/gtm-event.class";
import _ from "lodash";
import { Utils } from "src/app/helpers/utils";

@Component({
  selector: "app-search",
  templateUrl: "./search.page.html",
  styleUrls: ["./search.page.scss"],
  standalone: false,
})
export class SearchPage implements OnInit {
  /**
   * Search options.
   */
  public searchOptions = {
    minDistance: 0,
    maxDistance: 250,
    stepDistance: 1,
    minDistanceLabel: "any", // translate
    stepAge: 1,
    showPin: false,
    anyDistance: false,
    anyAge: false,
    ageFrom: Env.APP_VALID_AGE,
    ageTo: 99,
  };

  /**
   * Genders list.
   */
  public genders = Env.APP_GENDERS;

  public selectedGender: string;

  /**
   * Age select.
   */
  public selectedAges: { lower: number; upper: number };

  /**
   * Available distances.
   *
   * @type {[number]}
   */
  public availableDistances: number[] = [10, 25, 50, 100, 200, 0];

  /**
   * Selected distance.
   */
  public selectedDistance: number = 100;

  /**
   * Country list.
   */
  public countryList: Object;

  public zipCode: string;
  public withPhotos: boolean = true;
  // public withVideos: boolean = false;

  public searchParams: ApiParams.SearchBasic.type;

  constructor(
    public unreadCountService: UnreadCountService,
    public accountService: AccountService,
    public locationService: LocationService,
    public searchService: SearchService,
    public router: Router,
    public stateService: StateService,
    private dlg: DialogHelperService,
    private translateService: TranslateService,
    private analytics: AnalyticsService,
  ) {}

  public selectAges(e) {
    this.selectedAges.lower = e.detail.value.lower;
    this.selectedAges.upper = e.detail.value.upper;
  }

  ngOnInit() {
    this.selectedAges = {
      lower: this.searchOptions.ageFrom,
      upper: this.searchOptions.ageTo,
    };

    this.locationService.locationHelper({ get_countries: 1 }).then(() => {
      setTimeout(() => {
        // Convert to string for ngModel
        this.selectedGender =
          this.accountService.account?.looking_id.toString() ??
          Env.APP_DEFAULT_GENDER_LOOKING_ID.toString();
      }, 1000);
    });

    this.zipCode = this.accountService?.account?.zip ?? "";
  }

  public isValid(): boolean {
    this.zipCode = this.zipCode.trim();

    if (
      this.zipCode.length > 0 &&
      !ZipValidator.validateZip(
        this.accountService.account.country,
        this.zipCode,
      )
    ) {
      this.dlg.showToast(
        "error",
        this.translateService.instant("zip_invalid_error", {
          value: ZipValidator.getFormatForCountry(
            this.accountService.account.country,
          ).join(" , "),
        }),
        "error",
      );

      return false;
    }

    return true;
  }

  public find(): void {
    if (!this.isValid()) {
      return;
    }

    this.router.navigate(["/app/search-results"]);
    this.stateService.set(
      "search_params",
      this.accountService.account.username,
      {
        gender: this.accountService.account.gender_id,
        looking: this.selectedGender,
        age_from: this.selectedAges.lower,
        age_to: this.selectedAges.upper,
        zip: this.zipCode,
        distance: this.selectedDistance,
        has_photo: +this.withPhotos,
        country: this.accountService.account.country,
        // has_video: +this.withVideos,
      },
    );

    // send analytics
    this.analytics.logEvent("search_filter", <TME_search_filter>{
      filter_age: this.selectedAges.lower + "-" + this.selectedAges.upper,
      filter_distance: this.selectedDistance,
      filter_looking_for: Utils.getGenderNameById(
        parseFloat(this.selectedGender),
      ),
      filter_photos: this.withPhotos ? "yes" : "no",
    });
  }
}
