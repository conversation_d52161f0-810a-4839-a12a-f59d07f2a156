.spinner-wrapper {
  margin-top: 16px;
}

ion-grid {
  width: 100%;
  padding-bottom: 0;
}

ion-infinite-scroll-content {

  ion-grid {
    padding-top: 1px;
  }
}

ion-list {
    border-radius: 8px;
    box-shadow: 0 6px 12px rgba(0,0,0,.08);
    margin: 10px 10px 0;
}

ion-item {
    --ripple-color: transparent;
}

ion-card {
    box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
    border-radius: 8px;
    background:var(--ion-color-primary-contrast);
    height: 100%;

    .username {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .status-indicator {
        background-color: transparent;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
    }

    &.active {
        .status-indicator {
            background-color: var(--ion-color-success);
        }
    }

    ion-skeleton-text + .status-indicator {
      margin-left: 5px;
    }

    .img-placeholder {
      position: relative;
      width: 100%;
      height: 0;
      padding-top: 100%;

      ion-img,
      ion-skeleton-text {
        position: absolute;
        width: 100%;
        left: 0;
        top: 0;
      }
    }
}

ion-card-header {
  color: var(--ion-color-white);
  padding: 12px;
  padding-top: 60px;
  position: absolute;
  bottom: 0;
  width: 100%
}

ion-card-header:not(.skeleton) {
  background-image: var(--gl-gradient-bottom-to-top-60);
}

ion-card-title {
  color: var(--ion-color-white);
  font-size: 14px;
  font-weight: 600;
}

ion-card-content {
  padding: 0 12px 12px;
}

ion-text {
  width: 100%;
  font-size: 12px;
  opacity: 0.8;
}

.distance {
  background-color: rgba(var(--ion-color-dark-rgb), 0.4);
  color: var(--ion-color-white);
  border-radius: 4px;
  font-size: 12px;
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  z-index: 4;

  &:empty {
    display: none;
  }
}

.statusMessage {
  margin-top: 10vh;

  ion-icon {
    font-size: 7rem;
    opacity: .3;
  }

  h6 {
    color: var(--ion-color-medium);
  }
}
