import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { MatchesPage } from "./matches.page";

const routes: Routes = [
  {
    path: "",
    component: MatchesPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MatchesPageRoutingModule {}
