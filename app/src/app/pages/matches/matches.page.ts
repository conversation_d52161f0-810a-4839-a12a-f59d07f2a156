import { Component, OnInit } from "@angular/core";
import { ListPaginator } from "src/app/services/lists/list-paginator";
import { LoaderService } from "src/app/services/loader.service";
import { PaginatorHelperService } from "src/app/services/paginator-helper.service";
import { ProfileService } from "../../services/profile.service";

@Component({
  selector: "app-matches",
  templateUrl: "./matches.page.html",
  styleUrls: ["./matches.page.scss"],
  standalone: false,
})
export class MatchesPage {
  public listPaginator: ListPaginator;

  constructor(
    public profileService: ProfileService,
    public loaderService: LoaderService,
    public paginatorHelper: PaginatorHelperService,
  ) {
    this.listPaginator = this.paginatorHelper.matchPaginator;
  }

  ionViewDidEnter() {
    if (!this.listPaginator.didLoad()) {
      this.listPaginator.next();
    }
  }
}
