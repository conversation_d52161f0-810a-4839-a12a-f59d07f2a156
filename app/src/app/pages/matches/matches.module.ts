import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { MatchesPageRoutingModule } from "./matches-routing.module";

import { MatchesPage } from "./matches.page";

import { TranslateModule } from "@ngx-translate/core";
import { PipesModule } from "src/app/pipes/pipes.module";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    MatchesPageRoutingModule,
    PipesModule,
  ],
  declarations: [MatchesPage],
})
export class MatchesPageModule {}
