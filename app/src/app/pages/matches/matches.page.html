<ion-content>
  <div [class.hidden]="!this.loaderService.isLoading()">
    <ion-grid>
      <ion-row class="ion-justify-content-center">
        <ion-col class="ion-align-self-center ion-text-center">
          <ion-spinner name="crescent"></ion-spinner>
        </ion-col>
      </ion-row>
      <ion-row class="ion-justify-content-center">
        <ion-col class="ion-align-self-center ion-text-center">
          {{ 'loading_wait' | translate }}
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>

  <!-- empty container { -->
  @if (profileService.listMatches.list$ | async; as listMatches) {
    <ion-grid
      class="statusMessage ion-text-center"
      [class.hidden]="this.loaderService.isLoading() || listMatches.length > 0"
      >
      <ion-row>
        <ion-col>
          <ion-icon color="primary" name="heart-dislike-outline"></ion-icon>
          <h2>{{ 'matches_empty_title' | translate }}</h2>
          <h6>{{ 'matches_empty_description' | translate }}</h6>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-button color="primary" [routerLink]="['/app/match-options-menu']">
            {{ 'matches_empty_button' | translate }}
          </ion-button>
        </ion-col>
      </ion-row>
    </ion-grid>
  }
  <!-- } empty container -->

  @if (profileService.listMatches.list$ | async; as listMatches) {
    <ion-grid
      [class.hidden]="this.loaderService.isLoading() || listMatches.length == 0"
      >
      <ion-row>
        @for (profile of listMatches; track profile) {
          <ion-col size="6" size-md="3">
            <ion-card
              [ngClass]="profile.is_active ? 'active' : 'inactive'"
              class="ion-no-margin"
              [routerLink]="[ '/app/profile', profile.username ]"
              >
              <div class="distance">{{ profile.distance | distance }}</div>
              <div class="img-placeholder">
                <ion-img
                  style="pointer-events: none"
                  [src]="profile.avatar_url"
                ></ion-img>
              </div>
              <ion-card-header>
                <ion-card-title color="dark">
                  {{ (profile.first_name || profile.username) | slice:0:14 }}, {{
                  profile.age }}
                  <span class="status-indicator"></span>
                </ion-card-title>
                <ion-text>
                  @if (profile.city) {
                    <span>{{ profile.city }},</span>
                    } {{
                    profile.country === 'US' ? profile.state : profile.country }}
                  </ion-text>
                </ion-card-header>
              </ion-card>
            </ion-col>
          }
        </ion-row>
        <ion-infinite-scroll
          [class.hidden]="this.listPaginator.didReachEnd()"
          threshold="100px"
          id="infinite-scroll"
          (ionInfinite)="this.listPaginator.next($event)"
          >
          <ion-infinite-scroll-content loadingSpinner="circular">
          </ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </ion-grid>
    }
  </ion-content>
