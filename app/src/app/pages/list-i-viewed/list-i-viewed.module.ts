import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { ListIViewedPageRoutingModule } from "./list-i-viewed-routing.module";
import { ListIViewedPage } from "./list-i-viewed.page";
import { TranslateModule } from "@ngx-translate/core";
import { PipesModule } from "src/app/pipes/pipes.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    ListIViewedPageRoutingModule,
    PipesModule,
  ],
  declarations: [ListIViewedPage],
})
export class ListIViewedPageModule {}
