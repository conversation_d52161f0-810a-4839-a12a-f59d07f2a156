import { Component } from "@angular/core";
import { Debug } from "src/app/helpers/debug";
import { ListPaginator } from "src/app/services/lists/list-paginator";
import { PaginatorHelperService } from "src/app/services/paginator-helper.service";
import { UnreadCountService } from "src/app/services/unread-count.service";
import { ProfileService } from "../../services/profile.service";
import { AccountService } from "../../services/account.service";

@Component({
  selector: "app-list-i-viewed",
  templateUrl: "./list-i-viewed.page.html",
  styleUrls: ["./list-i-viewed.page.scss"],
  standalone: false,
})
export class ListIViewedPage {
  public listPaginator: ListPaginator;

  constructor(
    public profileService: ProfileService,
    private paginatorHelper: PaginatorHelperService,
    private debug: Debug,
    public unreadCountService: UnreadCountService,
    public accountService: AccountService,
  ) {
    this.listPaginator = this.paginatorHelper.listIViewed;
  }
}
