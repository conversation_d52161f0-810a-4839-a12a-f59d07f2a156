import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { ListIViewedPage } from "./list-i-viewed.page";

const routes: Routes = [
  {
    path: "",
    component: ListIViewedPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ListIViewedPageRoutingModule {}
