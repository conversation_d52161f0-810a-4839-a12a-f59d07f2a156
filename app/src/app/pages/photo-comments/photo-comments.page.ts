import { Component, OnInit, ViewChild } from "@angular/core";
import { StateService } from "../../services/state.service";
import { PhotoService } from "../../services/photo.service";
import { PhotoComment, PhotoCommentBase } from "../../schemas/photo-comment";
import { ActivatedRoute, Router } from "@angular/router";
import { ApiParams } from "../../services/api/api.params";
import PhotoCommentGetParams = ApiParams.PhotoCommentGetParams;
import { ProfileService } from "../../services/profile.service";
import { IonTextarea, NavController } from "@ionic/angular";
import { AccountService } from "../../services/account.service";
import { catchError } from "rxjs/operators";
import { firstValueFrom, of, throwError } from "rxjs";
import PhotoCommentDeleteParams = ApiParams.PhotoCommentDeleteParams;
import { Debug } from "../../helpers/debug";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { DelayHelperService } from "src/app/services/delay-helper.service";
import { MiscService } from "../../services/misc.service";
import { PurchaseService } from "src/app/services/purchase.service";
import { UtilsService } from "src/app/services/utils.service";

@Component({
  selector: "app-photo-comments",
  templateUrl: "./photo-comments.page.html",
  styleUrls: ["./photo-comments.page.scss"],
  standalone: false,
})
export class PhotoCommentsPage implements OnInit {
  @ViewChild("comment", { static: false })
  comment: IonTextarea;

  public params: PhotoCommentGetParams.type = {
    gallery_id: null,
    image_id: null,
    owner_id: null,
  };
  public username: string;
  public comments: PhotoCommentBase.type;
  public newComment: string = "";
  private _doShowTextarea: boolean = true;
  public disableSendButton: boolean = true;
  public minCharForComment: number = 3;
  public wasError: boolean = false;
  public doShowDeleteCommentButton: boolean = false;

  constructor(
    public stateService: StateService,
    public photoService: PhotoService,
    public route: ActivatedRoute,
    public profileService: ProfileService,
    public accountService: AccountService,
    public navCtrl: NavController,
    public router: Router,
    public dbg: Debug,
    public dlg: DialogHelperService,
    public delayHelper: DelayHelperService,
    public miscService: MiscService,
    public purchaseService: PurchaseService,
    public utilsService: UtilsService,
  ) {}

  ionViewDidEnter() {
    this.doShowTextarea = true;
  }

  public openProfile(username) {
    if (!this.accountService.isOwnProfile(username)) {
      return this.router.navigate(["/app/profile", username]);
    }
  }

  /**
   * On comment update.
   */
  public onCommentChange() {
    if (!this.newComment.length) return (this.disableSendButton = true);

    setTimeout(() => {
      if (this.newComment.startsWith(" ") || this.newComment.startsWith("\n")) {
        this.newComment = this.newComment.trim();
      }
      this.disableSendButton = this.newComment.length < this.minCharForComment;
    }, 10);
  }

  public isLoading() {
    return this.photoService.isGetCommentsInProgress();
  }

  public isInProgress() {
    return (
      this.photoService.isAddCommentInProgress() ||
      this.photoService.isDeleteCommentInProgress()
    );
  }

  set doShowTextarea(val: boolean) {
    this._doShowTextarea = val;

    if (this._doShowTextarea) {
      this.comment.autofocus = true;
      this.comment.setFocus();
    }
  }

  get doShowTextarea(): boolean {
    return this._doShowTextarea;
  }

  async ngOnInit() {
    let data = this.stateService.get("photo", "comments");

    if (data) this.params = data;

    if (
      !this.params.owner_id ||
      !this.params.gallery_id ||
      !this.params.image_id
    ) {
      this.params.gallery_id = this.route.snapshot.params.galleryId;
      this.params.image_id = this.route.snapshot.params.imageId;
    }

    this.username = this.route.snapshot.params.username;

    if (!this.params.owner_id) {
      let profile = await firstValueFrom(
        this.profileService.get(this.username),
      );
      this.params.owner_id = profile.data.user_id;
      this.username = profile.data.username;
    }

    if (this.username) {
      this.doShowDeleteCommentButton =
        this.username === this.accountService.account.username;
    }

    this.get();
  }

  public get(event = null) {
    this.photoService.getComments(this.params).subscribe({
      next: (res) => {
        this.comments = res.data;
        if (event) {
          event.target.complete();
        }
      },
      error: async (err) => {
        this.wasError = true;
        if (event) {
          event.target.complete();
        }
        await this.dlg.showToast("", "error_loading_comments", "info");
      },
    });
  }

  public send() {
    let params = {
      owner_id: this.params.owner_id,
      image_id: this.params.image_id,
      gallery_id: this.params.gallery_id,
      content: this.newComment,
    };

    this.photoService.addComment(params).subscribe({
      next: (res) => {
        let data: PhotoComment.type = {
          content: this.newComment,
          username: this.accountService.account.username,
          avatar_url: this.accountService.account.avatar_url,
          comment_id: res.data.comment_id,
          datetime: res.data.datetime,
          first_name: this.accountService.account.first_name,
        };
        this.comments.items.unshift(data);
        this.comments.comments_count++;
        this.newComment = "";
        this.doShowTextarea = false;
        this.disableSendButton = true;
        this.stateService.set("photo", "comments", {
          gallery_id: this.params.gallery_id,
          image_id: this.params.image_id,
          owner_id: this.params.owner_id,
          comments: this.comments.comments_count,
        });
      },
      error: async (err) => {
        if (this.miscService.getErrorKey(err, "") === "comments_limit_err") {
          this.purchaseService
            .setDialogType("photo_comments_limit")
            .openUpgradeDialog(
              (error) => {
                this.dlg.showErrorAlert(
                  "purchase_error",
                  this.utilsService.figureOutTheErrorMessage(error),
                  this.utilsService.figureOutTheErrorKey(error),
                );
              },
              () => {},
              true,
            );
        } else {
          this.dbg.le("add comment error", err);
          await this.dlg.showToastBaseOnErrorResponse(err);
        }
      },
    });
  }

  public removeComment(comment) {
    let params: PhotoCommentDeleteParams.type = {
      comment_id: comment.comment_id,
      gallery_id: this.params.gallery_id,
      image_id: this.params.image_id,
      owner_id: this.accountService.account.user_id,
    };

    this.photoService.deleteComment(params).subscribe({
      next: (res) => {
        this.comments.items = this.comments.items.filter(
          (item) => item.comment_id !== comment.comment_id,
        );
        this.comments.comments_count--;
        this.stateService.set("photo", "comments", {
          gallery_id: this.params.gallery_id,
          image_id: this.params.image_id,
          owner_id: this.params.owner_id,
          comments: this.comments.comments_count,
        });
      },
      error: async (err) => {
        this.dbg.le("photo delete comment error", err);
        await this.dlg.showToast("", err.error.errors[0].error, "error");
      },
    });
  }
}
