<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ 'title_photo_comments' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content fullscreen>
  <ion-refresher slot="fixed" (ionRefresh)="this.get($event)">
    <ion-refresher-content refreshingSpinner="crescent"></ion-refresher-content>
  </ion-refresher>

  @if (comments) {
    <ion-list [class.hidden]="this.isLoading()">
      @for (comment of comments.items; track comment) {
        <ion-item
          [class.hidden]="!comments.items.length"
          >
          <ion-avatar
            (click)="openProfile(comment.username)"
            class="ion-margin-end"
            slot="start"
            >
            <img [src]="comment.avatar_url" alt="" />
          </ion-avatar>
          <ion-label>
            <h2>
              {{ comment.first_name || comment.username }}
              <small color="primary">{{ comment.datetime }}</small>
            </h2>
            <p [innerHtml]="comment.content" class="ion-text-wrap"></p>
            @if (doShowDeleteCommentButton) {
              <ion-button
                size="small"
                (click)="removeComment(comment)"
                [disabled]="photoService.isDeleteCommentInProgress()"
                >
                <ion-icon name="trash"></ion-icon>
              </ion-button>
            }
          </ion-label>
        </ion-item>
      }
      <!-- empty container { -->
      <ion-grid
        class="statusMessage ion-text-center"
        [class.hidden]="comments.items.length"
        >
        <ion-row>
          <ion-col>
            <ion-icon color="primary" name="reader-outline"></ion-icon>
            <h2>{{ 'no_comments_title' | translate }}</h2>
            <h6>{{ 'no_comments_text' | translate }}</h6>
          </ion-col>
        </ion-row>
      </ion-grid>
      <!-- } empty container -->
    </ion-list>
  }

  <ion-list [class.hidden]="!this.isLoading()">
    @for (x of ' '.repeat(15).split(''); track x) {
      <ion-item>
        <ion-avatar slot="start">
          <ion-skeleton-text></ion-skeleton-text>
        </ion-avatar>
        <ion-label>
          <h2>
            <ion-skeleton-text
              animated
              class="ion-float-left"
              style="width: 40%"
            ></ion-skeleton-text>
            <ion-skeleton-text
              animated
              class="ion-float-right"
              style="width: 20%"
            ></ion-skeleton-text>
          </h2>
          <p>
            <ion-skeleton-text animated style="width: 25%"></ion-skeleton-text>
          </p>
        </ion-label>
      </ion-item>
    }
  </ion-list>
</ion-content>

<ion-footer>
  <ion-toolbar color="white">
    <ion-item class="comment-input-section" lines="none">
      <ion-input
        placeholder="{{ 'enter_comment' | translate }}"
        [(ngModel)]="newComment"
        [class.hidden]="doShowTextarea"
        (ionFocus)="doShowTextarea = true"
      ></ion-input>
      <ion-textarea
        autoGrow
        placeholder="{{ 'start_typing' | translate }}"
        [(ngModel)]="newComment"
        [class.hidden]="!doShowTextarea"
        [disabled]="this.isInProgress() || this.isLoading()"
        rows="2"
        (ionBlur)="doShowTextarea = false"
        [debounce]="500"
        (ionInput)="onCommentChange()"
        autofocus
        #comment
        class="comment"
      ></ion-textarea>

      <ion-fab-button
        [class.hidden]="this.delayHelper.isActive"
        class="send-button"
        color="primary"
        [disabled]="disableSendButton || photoService.isAddCommentInProgress()"
        (click)="send()"
        slot="end"
        >
        <ion-icon [class.hidden]="this.isInProgress()" name="send"></ion-icon>
        <ion-spinner [class.hidden]="!this.isInProgress()"></ion-spinner>
      </ion-fab-button>

      <ion-fab-button
        [class.hidden]="!this.delayHelper.isActive"
        [disabled]="true"
        class="send-button"
        color="primary"
        >
        {{ this.delayHelper.delay }}
      </ion-fab-button>
    </ion-item>
  </ion-toolbar>
</ion-footer>
