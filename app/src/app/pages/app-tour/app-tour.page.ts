import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { Router } from "@angular/router";
import { NavController } from "@ionic/angular";
import { Swiper } from "swiper";

@Component({
  selector: "app-app-tour",
  templateUrl: "./app-tour.page.html",
  styleUrls: ["./app-tour.page.scss"],
  standalone: false,
})
export class AppTourPage implements OnInit {
  @ViewChild("swiper") swiperRef: ElementRef | undefined;

  /**
   * Button text.
   *
   * @type {string}
   */
  public nextButton: string = "app_tour_next";

  /**
   * swiper.
   */
  public isEnd: boolean = false;

  constructor(
    public router: Router,
    public navCtrl: NavController,
  ) {}

  ngOnInit() {}

  public async slideChanged() {
    let length = (await this.swiperRef?.nativeElement.swiper.slides.length) - 1;
    let index = await this.swiperRef?.nativeElement.swiper.activeIndex;
    this.isEnd = length === index;
    this.nextButton = this.isEnd ? "app_tour_finish" : "app_tour_next";
  }

  /**
   * Go to next slide.
   */
  public next() {
    if (this.isEnd) return this.finishAppTour();
    this.swiperRef?.nativeElement.swiper.slideNext();
  }

  /**
   * Finish app tour function
   */
  public finishAppTour() {
    this.navCtrl.back();
  }
}
