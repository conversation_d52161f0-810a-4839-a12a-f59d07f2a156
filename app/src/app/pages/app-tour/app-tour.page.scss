ion-header {
  ion-title {
    ion-img {
      padding-top: 5px;
      padding-bottom: 5px;
      &.md {
        height: 36px;
      }
      &.ios {
        height: 36px;
      }
    }
  }
}
ion-grid {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-top: 0;

  .first-row {
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: auto;

    ion-col {
      padding-top: 0;
    }

    swiper-container {
      height: 100%;

      swiper-slide {
        align-items: flex-start;

        img {
          max-height: 60%;
          width: auto;
        }

        h2 {
          font-weight: 400;
          line-height: 1.5;
        }
      }
    }
  }
  .second-row {
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: auto;

    ion-button {
      height: 46px;
      text-transform: none;
    }
  }
}


