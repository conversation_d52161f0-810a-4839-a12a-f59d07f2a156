<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title mode="ios">
      <img
        src="assets/img/app-logo.svg"
        style="pointer-events: none"
        class="logoSvg"
        />
    </ion-title>
    @if (!isEnd) {
      <ion-buttons slot="end">
        <ion-button (click)="finishAppTour()"
          >{{ 'app_tour_skip' | translate }}</ion-button
          >
        </ion-buttons>
      }
    </ion-toolbar>
  </ion-header>

  <ion-content>
    <ion-grid>
      <ion-row class="first-row">
        <ion-col class="ion-text-center">
          <swiper-container
            #swiper
            pagination="true"
            (swiperslidechangetransitionstart)="slideChanged()"
            >
            <swiper-slide>
              <div class="slide">
                <img src="assets/tour/app_tour_slide1.png" />
                <h3 [innerHTML]="'app_tour_slide_1' | translate"></h3>
              </div>
            </swiper-slide>

            <swiper-slide>
              <div class="slide">
                <img src="assets/tour/app_tour_slide2.png" />
                <h3 [innerHTML]="'app_tour_slide_2' | translate"></h3>
              </div>
            </swiper-slide>

            <swiper-slide>
              <div class="slide">
                <img src="assets/tour/app_tour_slide3.png" />
                <h3 [innerHTML]="'app_tour_slide_3' | translate"></h3>
              </div>
            </swiper-slide>

            <swiper-slide>
              <div class="slide">
                <img src="assets/tour/app_tour_slide4.png" />
                <h3 [innerHTML]="'app_tour_slide_4' | translate"></h3>
              </div>
            </swiper-slide>

            <swiper-slide>
              <div class="slide">
                <img src="assets/tour/app_tour_slide5.png" />
                <h3 [innerHTML]="'app_tour_slide_5' | translate"></h3>
              </div>
            </swiper-slide>

            <swiper-slide>
              <div class="slide">
                <img src="assets/tour/app_tour_slide6.png" />
                <h3 [innerHTML]="'app_tour_slide_6' | translate"></h3>
              </div>
            </swiper-slide>
          </swiper-container>
        </ion-col>
      </ion-row>

      <ion-row class="second-row ion-justify-content-center">
        <ion-col size-md="6" size-lg="5" size-xs="12">
          <ion-button expand="block" (click)="next()"
            >{{ nextButton | translate }}</ion-button
            >
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-content>
