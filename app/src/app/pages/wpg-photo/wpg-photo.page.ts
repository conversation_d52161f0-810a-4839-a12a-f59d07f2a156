import { Component, <PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { WelcomeWizardPage } from "../welcome-wizard/welcome-wizard.page";
import { WelcomeWizardService } from "../../services/welcome-wizard.service";
import { Debug } from "../../helpers/debug";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { TranslateService } from "@ngx-translate/core";
import { Router } from "@angular/router";
import { PictureService } from "../../services/picture.service";
import { ApiParams } from "../../services/api/api.params";
import { ActionSheetController } from "@ionic/angular";
import { CameraService } from "../../services/camera.service";
import { MiscService } from "../../services/misc.service";
import { environment as Env } from "../../../environments/environment";
import { AccountService } from "../../services/account.service";
import { CameraSource } from "@capacitor/camera";
import { GalleryImage } from "../../schemas/gallery-image";
import { WelcomeProfileService } from "../../services/welcome-profile.service";
import { PictureUploadHelperService } from "src/app/picture-upload-helper.service";
import { firstValueFrom } from "rxjs";
import { AnalyticsService } from "src/app/services/analytics.service";
import { UtilsService } from "src/app/services/utils.service";

@Component({
  selector: "app-wpg-photo",
  templateUrl: "./wpg-photo.page.html",
  styleUrls: ["./wpg-photo.page.scss"],
  standalone: false,
})
export class WpgPhotoPage extends WelcomeWizardPage implements OnInit {
  public _processingPhoto: boolean = false;

  public photo: GalleryImage.type;

  constructor(
    public welcomeProfileService: WelcomeProfileService,
    public welcomeWizard: WelcomeWizardService,
    public debug: Debug,
    public dialog: DialogHelperService,
    public translate: TranslateService,
    public router: Router,
    public pictureService: PictureService,
    public as: ActionSheetController,
    public camera: CameraService,
    public misc: MiscService,
    public ownProfileService: AccountService,
    public ngZone: NgZone,
    public pictureUploadHelper: PictureUploadHelperService,
    public analytics: AnalyticsService,
    public utils: UtilsService,
  ) {
    super(
      welcomeProfileService,
      welcomeWizard,
      debug,
      dialog,
      translate,
      router,
      misc,
      analytics,
      utils,
    );
  }

  ngOnInit() {}

  public doShowPhotos() {
    return (
      this.pictureService.isInProgress ||
      this.getPhotoCnt() > 0 ||
      this.isProcessing() ||
      this.pictureService.isPhotoUploading()
    );
  }

  /**
   * Get photo count
   */
  public getPhotoCnt() {
    if (!this.pageData.values) return 0;
    return this.pageData.values.length;
  }

  public async displayPhotoMenu(index) {
    if (this._processingPhoto) {
      return;
    }

    let aso = {
      title: this.translate.instant("photo_options"),
      buttons: [
        {
          text: this.translate.instant("delete"),
          role: "delete",
          handler: () => {
            let imgParam: ApiParams.PictureDelete.type = [
              {
                gallery_id: this.pageData.values[index].gallery_id,
                image_id: this.pageData.values[index].image_id,
              },
            ];

            firstValueFrom(this.pictureService.delete(imgParam))
              .then(() => {
                this.debug.l("photo deleted");
              })
              .catch((e) => {
                this.debug.le("photo delete failed", e);
              });

            this.pageData.values.splice(index, 1);
          },
        },
      ],
    };

    // add cancel

    aso.buttons.push({
      text: this.translate.instant("cancel"),
      role: "cancel",
      handler: () => {},
    });

    let actionSheet = await this.as.create(aso);

    return await actionSheet.present();
  }

  /**
   * Display Photo Menu
   */
  public async displayUploadMenu() {
    if (this._processingPhoto) {
      return;
    }

    return this.pictureUploadHelper.displayUploadMenu(
      this.uploadPhoto.bind(this),
    );
  }

  /**
   * Upload Photo function
   */
  public fetchPhoto(sourceType) {
    return this.pictureUploadHelper.fetchPhoto(
      sourceType,
      this.uploadPhoto.bind(this),
    );
  }

  /**
   * Upload photo.
   *
   * @param data
   * @param url
   */
  public uploadPhoto(data = null, url = null) {
    this.ngZone.run(() => {
      this._processingPhoto = true;
    });

    let picParams = {
      gallery_id: 0,
    };

    if (data) {
      picParams["image_data"] = data;
    }
    if (url) {
      picParams["image_url"] = url;
    }

    this.pictureService
      .upload(<ApiParams.PictureUpload.type>picParams)
      .subscribe({
        next: (uploadResult) => {
          this._processingPhoto = false;
          let photo = uploadResult.data.photo;
          photo["image_data"] = data
            ? this.pictureUploadHelper.getPictureData(data)
            : uploadResult.data.photo.image_url;
          // add to current list
          this.pushPhoto(photo);
        },
        error: (error) => {
          this._processingPhoto = false;
          this.debug.le("image_upload_faile", error);
          this.dialog.showToastBaseOnErrorResponse(error);
        },
      });
  }

  public pushPhoto(data) {
    this.pageData.values.push(data);
  }
}
