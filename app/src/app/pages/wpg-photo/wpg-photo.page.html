<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button
        text=""
        icon="chevron-back-sharp"
        [class.hidden]="this.doHideBackButton()"
      ></ion-back-button>
    </ion-buttons>
    <ion-title mode="ios">
      <img
        style="pointer-events: none"
        src="assets/img/app-logo.svg"
        class="logoSvg"
        />
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-progress-bar
    value="{{ (1/welcomeWizard.pages.length)*welcomeWizard.getRealIndex() }}"
  ></ion-progress-bar>

  <ion-grid class="ion-text-center ion-padding">
    <ion-row class="ion-justify-content-center">
      @if (this.pageData.title) {
        <ion-col size="12" size-sm="8">
          <h1>{{ this.replaceTags(this.pageData.title) }}</h1>
        </ion-col>
      }

      @if (this.pageData.description) {
        <ion-col size="12" size-sm="8">
          <ion-text>{{ this.replaceTags(this.pageData.description) }}</ion-text>
        </ion-col>
      }

      <ion-col size="12" size-sm="8">
        <!-- Thumb container - This part need to show when procesing and image is uploaded -->
        <ion-grid [class.hidden]="!this.doShowPhotos()">
          <ion-row class="photoRow">
            @for (photo of this.pageData.values; track photo; let i = $index) {
              <ion-col
                size="4"
                >
                <span (click)="displayPhotoMenu(i)">
                  <img style="pointer-events: none" [src]="photo.image_data" />
                </span>
              </ion-col>
            }
            @for (photo of ' '.repeat(3-getPhotoCnt()).split(''); track photo) {
              <ion-col
                size="4"
                >
                <ion-chip
                  class="ion-no-padding ion-no-margin"
                  (click)="displayUploadMenu()"
                  >
                  <ion-icon name="add-circle" class="ion-no-margin"></ion-icon>
                  <ion-label class="ion-align-self-center ion-padding-horizontal"
                    ><small
                    >{{ 'upload_your_photo' | translate }}</small
                    ></ion-label
                    >
                  </ion-chip>
                </ion-col>
              }
            </ion-row>
          </ion-grid>
          <!-- Thumb container ends! -->

          <!-- Main Part - need to be hiden when procesing and image is uploaded -->
          <ion-grid (click)="displayUploadMenu()" [class.hidden]="this.doShowPhotos()">
            <ion-row class="ion-justify-content-center">
              <ion-col size="12" size-sm="8">
                <ion-icon class="icon-upload" name="cloud-upload"></ion-icon>
                <ion-text>{{ 'upload_your_photo' | translate }}</ion-text>
                <ion-button
                  expand="block"
                  class="ion-margin-top"
                  [disabled]="this._processingPhoto"
                  >{{ 'browse' | translate }}</ion-button
                  >
                </ion-col>
              </ion-row>
            </ion-grid>
            @if (pictureService.isInProgress) {
              <ion-progress-bar
                class="ion-margin-top"
                value="{{ pictureService.percentage/100 }}"
              ></ion-progress-bar>
            }
            <!-- Main Part ends! -->
          </ion-col>

          @if (this.pageData.note) {
            <ion-col size="12" size-sm="8">
              <h4>{{ 'photo_page_note_title' | translate }}</h4>
              <ion-text
                ><small
                >{{ 'photo_page_note_description' | translate }}</small
                ></ion-text
                >
              </ion-col>
            }
          </ion-row>
        </ion-grid>
      </ion-content>

      <ion-footer>
        <ion-toolbar class="ion-text-center">
          <ion-grid class="ion-margin-vertical">
            <ion-row class="ion-justify-content-center">
              <ion-col size="12" size-sm="6">
                <!-- Footer button need to be disabled and then enabled when first image is uploaded -->
                <ion-button
                  class="ion-margin-bottom"
                  expand="block"
                  (click)="mainButtonClick()"
                  [disabled]="this.isProcessing() || this._processingPhoto || getPhotoCnt()==0"
                  >{{ getButtonText() }}</ion-button
                  >
                  <!-- This part need to be hiden when first image is uploaded -->
                  @if (this.pageData.button_no) {
                    <a
                      [class.hidden]="this.pageData.values.length>0 || this.isProcessing() || this._processingPhoto"
                      (click)="alternateButtonClick()"
                      class="later"
                      >
                      {{ getAlternateButtonText() }}
                    </a>
                  }
                </ion-col>
              </ion-row>
            </ion-grid>
          </ion-toolbar>
        </ion-footer>
