ion-header {
  ion-title {

    ion-img {
      padding-top: 5px;
      padding-bottom: 5px;
    }
  }
}

.logoSvg {
  height: 36px;
}

ion-icon {

	&.icon-upload {
		display: block;
		font-size: 4rem;
		margin: auto;
	}
}

.photoRow {
  min-height: 31vw;

  ion-col {

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

ion-chip {
  flex-wrap: wrap;
  align-content: center;
  height: 100%;
  width: 100%;
  border-radius: 0;

  ion-icon {
    width: 100%;
    font-size: 2rem;
    margin-bottom: 5px;
  }

  ion-label {
    width: 100%;
  }
}

ion-button {
  height: 46px;
  text-transform: none;
}

.later {
  color: var(--ion-color-dark);
  font-size: 14px;
}
