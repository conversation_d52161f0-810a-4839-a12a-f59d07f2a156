<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ this.title }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <!-- Skeleton text { -->
  @if (this.content.length==0 && !this.wasError) {
    <div>
      <ion-grid>
        <ion-row>
          <ion-col>
            <ion-skeleton-text
              style="width: 45%"
              class="ion-margin-vertical"
              animated
            ></ion-skeleton-text>
            @for (_c of [].constructor(15); track _c) {
              <ion-skeleton-text
                class="ion-margin-vertical"
                animated
              ></ion-skeleton-text>
            }
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <ion-skeleton-text
              style="width: 55%"
              class="ion-margin-vertical"
              animated
            ></ion-skeleton-text>
            @for (_c of [].constructor(15); track _c) {
              <ion-skeleton-text
                class="ion-margin-vertical"
                animated
              ></ion-skeleton-text>
            }
          </ion-col>
        </ion-row>
      </ion-grid>
      <!-- This part can be removed now
      {{ 'loading_content' | translate }} ...
      <ion-spinner name="crescent" padding></ion-spinner>
      -->
    </div>
  }
  <!--  } Skeleton text -->
  <!-- error container { -->
  @if (this.wasError) {
    <ion-grid class="statusMessage ion-text-center">
      <ion-row>
        <ion-col>
          <ion-icon color="primary" name="alert-circle-outline"></ion-icon>
          <h3>{{ 'error_loading_page' | translate }}</h3>
        </ion-col>
      </ion-row>
    </ion-grid>
  }
  <!-- } error container -->
  @if (!(this.content.length==0) && !this.wasError) {
    <app-external-content
      [content]="this.content"
    ></app-external-content>
  }
</ion-content>
