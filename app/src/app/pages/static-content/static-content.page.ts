import { Component, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { UtilsService } from "src/app/services/utils.service";

@Component({
  selector: "app-static-content",
  templateUrl: "./static-content.page.html",
  styleUrls: ["./static-content.page.scss"],
  standalone: false,
})
export class StaticContentPage implements OnInit {
  constructor(
    private utilsService: UtilsService,
    private route: ActivatedRoute,
  ) {}

  private name: string = "";
  public title: string = "";
  public content: string = "";
  public wasError: boolean = false;

  private loadPageContent() {
    this.wasError = false;

    this.utilsService.getStaticPage(this.name).subscribe({
      next: (data) => {
        // if the page exists
        if (data) {
          this.title = data.title;
          this.content = data.content;
        } else {
          this.wasError = true;
        }
      },
      error: (error) => {
        this.wasError = true;
      },
    });
  }

  ngOnInit() {
    this.name = this.route.snapshot.params["pageName"];
    this.loadPageContent();
  }
}
