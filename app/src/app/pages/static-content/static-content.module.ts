import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { StaticContentPageRoutingModule } from "./static-content-routing.module";

import { StaticContentPage } from "./static-content.page";
import { TranslateModule } from "@ngx-translate/core";
import { ExternalContentComponentModule } from "src/app/components/external-content/external-content.component.module";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    StaticContentPageRoutingModule,
    TranslateModule.forChild(),
    ExternalContentComponentModule,
  ],
  declarations: [StaticContentPage],
})
export class StaticContentPageModule {}
