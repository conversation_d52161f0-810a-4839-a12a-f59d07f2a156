import { Component, OnInit } from "@angular/core";
import { TME_reset_password } from "src/app/classes/gtm-event.class";
import { AnalyticsService } from "src/app/services/analytics.service";
import { ApiCommands } from "src/app/services/api/api.commands";
import { ApiService } from "src/app/services/api/api.service";
import { AuthService } from "src/app/services/auth.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { StateService } from "src/app/services/state.service";

@Component({
  selector: "app-forgot-password",
  templateUrl: "./forgot-password.page.html",
  styleUrls: ["./forgot-password.page.scss"],
  standalone: false,
})
export class ForgotPasswordPage implements OnInit {
  constructor(
    private api: ApiService,
    private auth: AuthService,
    private dlg: DialogHelperService,
    private analytics: AnalyticsService,
    public stateService: StateService,
  ) {}

  ngOnInit() {
    this.email = this.stateService.get("lastSignUpEmailTry", "", "");
  }

  public email: string = "";

  public isInProgress() {
    return this.api.isInEventList({
      command: ApiCommands.AuthResetPassword.name,
    });
  }

  public resetPassword() {
    this.analytics.logEvent("reset_password", <TME_reset_password>{});

    this.auth
      .resetPassword(this.email)
      .then(() => {
        this.dlg.showToast("reset_password", "request_sent", "info");
        this.email = "";
      })
      .catch((err) => {
        this.dlg.showToastBaseOnErrorResponse(err);
      });
  }
}
