<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ 'title_forgot_password' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row class="ion-justify-content-center">
      <ion-col class="ion-text-center ion-padding-vertical" size="9">
        <h4>{{ 'password_reset_help' | translate }}</h4>
      </ion-col>
    </ion-row>
    <ion-row class="ion-justify-content-center ion-padding-horizontal">
      <ion-col size-md="6">
        <ion-item appAutoFocus class="ion-margin-bottom">
          <ion-input [(ngModel)]="this.email" placeholder="Email"></ion-input>
        </ion-item>
      </ion-col>
    </ion-row>
    <ion-row class="ion-justify-content-center ion-padding-horizontal">
      <ion-col size-md="6">
        <ion-button
          [disabled]="this.isInProgress()"
          type="submit"
          expand="block"
          class="ion-margin-bottom"
          (click)="this.resetPassword()"
          >{{ 'reset_password' | translate }}</ion-button
        >
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
