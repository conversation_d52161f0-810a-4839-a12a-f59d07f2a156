import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { ForgotPasswordPageRoutingModule } from "./forgot-password-routing.module";

import { ForgotPasswordPage } from "./forgot-password.page";

import { TranslateModule } from "@ngx-translate/core";
import AutoFocusModule from "src/app/auto-focus.module";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    ForgotPasswordPageRoutingModule,
    AutoFocusModule,
  ],
  declarations: [ForgotPasswordPage],
})
export class ForgotPasswordPageModule {}
