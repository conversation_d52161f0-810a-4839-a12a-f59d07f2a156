import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { ForgotPasswordPage } from "./forgot-password.page";

const routes: Routes = [
  {
    path: "",
    component: ForgotPasswordPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ForgotPasswordPageRoutingModule {}
