import { Component, OnInit } from "@angular/core";
import { UnreadCountService } from "../../services/unread-count.service";
import { NavigationEnd, Router } from "@angular/router";
import { LoaderService } from "../../services/loader.service";
import { AccountService } from "../../services/account.service";

@Component({
  selector: "app-my-lists",
  templateUrl: "./my-lists.page.html",
  styleUrls: ["./my-lists.page.scss"],
  standalone: false,
})
export class MyListsPage implements OnInit {
  public title: string = "";

  constructor(
    public loaderService: LoaderService,
    public accountService: AccountService,
    public unreadCountService: UnreadCountService,
    public router: Router,
  ) {}

  ngOnInit() {}
}
