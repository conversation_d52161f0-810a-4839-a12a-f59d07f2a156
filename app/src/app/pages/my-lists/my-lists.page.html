<ion-header class="ion-text-center">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title style="margin-right: 48px">
      {{ 'title-viewed-my-profile' | translate }}
    </ion-title>
  </ion-toolbar>
</ion-header>
<ion-content>
  <ion-tabs>
    <ion-tab-bar
      style="padding: 0px !important"
      slot="top"
      class="tab-nav ion-tabs-top"
    >
      <ion-tab-button tab="viewed-me">
        {{'tabs_list_viewed_me' | translate}}
      </ion-tab-button>
      <ion-tab-button tab="i-viewed">
        {{'tabs_list_i_viewed' | translate}}
      </ion-tab-button>
    </ion-tab-bar>
  </ion-tabs>
</ion-content>
