import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { TranslateModule } from "@ngx-translate/core";
import { MyListsPage } from "./my-lists.page";

const routes: Routes = [
  {
    path: "",
    component: MyListsPage,

    children: [
      {
        path: "i-viewed",
        loadChildren: () =>
          import("../list-i-viewed/list-i-viewed.module").then(
            (m) => m.ListIViewedPageModule
          ),
      },
      {
        path: "viewed-me",
        loadChildren: () =>
          import("../list-viewed-me/list-viewed-me.module").then(
            (m) => m.ListViewedMePageModule
          ),
      },
      {
        path: "",
        redirectTo: "/app/my-lists/viewed-me",
        pathMatch: "full",
      },
    ],
  },

  {
    path: "",
    redirectTo: "/app/my-lists/viewed-me",
    pathMatch: "full",
  },
];
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MyListsPageRoutingModule {}
