.tab-nav {
  --background: #FFF;
  --background-focused: #FFF;
  border: 0;

  ion-tab-button {
    --padding-start: 8px;
    --padding-end: 8px;
    font-size: 13px;
    font-weight: 600;
    position: relative;
    &:after {
      background-color: var(--ion-color-silver-shade);
      width: 100%;
      height: 1px;
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }

  .tab-selected {
    &:after {
      background-color: var(--ion-color-primary);
      height: 3px;
    }
  }
}