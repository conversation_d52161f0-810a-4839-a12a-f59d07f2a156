import { Component, OnInit } from "@angular/core";
import { ListPaginator } from "../../services/lists/list-paginator";
import { SearchService } from "../../services/search.service";
import { StateService } from "../../services/state.service";
import { AccountService } from "../../services/account.service";
import { ApiParams } from "../../services/api/api.params";
import { ApiService } from "src/app/services/api/api.service";
import _ from "lodash";
import { UtilsService } from "src/app/services/utils.service";
import { NavController } from "@ionic/angular";

@Component({
  selector: "app-search-results",
  templateUrl: "./search-results.page.html",
  styleUrls: ["./search-results.page.scss"],
  standalone: false,
})
export class SearchResultsPage implements OnInit {
  public listPaginator: ListPaginator = new ListPaginator(
    this.searchService.searchItems,
    10,
    "2",
  );

  private searchParams: ApiParams.SearchBasic.type;

  public getErrorString() {
    return this.utilsService.figureOutTheErrorMessage(
      { message: "error_list" },
      "error_list",
    );
  }

  constructor(
    public searchService: SearchService,
    public stateService: StateService,
    public accountService: AccountService,
    public utilsService: UtilsService,
    public navCtrl: NavController,
  ) {
    this.searchParams = this.stateService.get(
      "search_params",
      this.accountService.account.username,
    );
  }

  public goBack() {
    this.navCtrl.back();
  }

  ngOnInit() {}

  ionViewDidEnter() {
    if (!this.listPaginator.didLoad()) {
      this.searchService.searchItems.changeParams(this.searchParams);
      this.listPaginator.next();
    }
  }

  ionViewDidLeave() {
    this.stateService.delete(
      "search_params",
      this.accountService.account.username,
    );
  }
}
