<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button></ion-back-button>
    </ion-buttons>
    <ion-title>{{ 'search_results' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <!-- empty container { -->
  <ion-grid
    class="statusMessage ion-text-center"
    [class.hidden]="!this.listPaginator.isEmpty()"
    >
    <ion-row>
      <ion-col>
        <ion-icon color="primary" name="heart-dislike-outline"></ion-icon>
        <h2>{{ 'search_results_title_empty' | translate }}</h2>
        <h6>{{ 'search_results_description_empty' | translate }}</h6>
      </ion-col>
    </ion-row>
  </ion-grid>
  <!-- } empty container -->

  <!-- loading container { -->
  @if (searchService.searchItems.list$ | async; as searchItems) {
    <ion-grid
      [class.hidden]="searchService.searchItems.wasError()"
      >
      <ion-row>
        @for (profile of searchItems; track profile) {
          <ion-col size="6" size-md="3">
            <ion-card
              [ngClass]="profile?.is_active ? 'active' : 'inactive'"
              class="ion-no-margin"
              [routerLink]="[ '/app/profile', profile?.username ]"
              >
              <div class="distance">{{ profile?.distance | distance }}</div>
              <div class="img-placeholder">
                <ion-img
                  [src]="profile?.avatar_url"
                  style="pointer-events: none"
                ></ion-img>
              </div>
              <ion-card-header>
                <ion-card-title color="dark">
                  {{ (profile?.first_name || profile?.username) | slice:0:14 }}, {{
                  profile?.age }}
                  <span class="status-indicator"></span>
                </ion-card-title>
                <ion-text>
                  @if (profile?.city) {
                    <span>{{ profile?.city }},</span>
                    } {{
                    profile?.country === 'US' ? profile?.state : profile?.country }}
                  </ion-text>
                </ion-card-header>
              </ion-card>
            </ion-col>
          }
        </ion-row>
      </ion-grid>
    }
    <!-- } loading container -->

    <ion-infinite-scroll
      [class.hidden]="this.listPaginator.didReachEnd()"
      threshold="100px"
      id="infinite-scroll"
      (ionInfinite)="this.listPaginator.next($event)"
      >
      <ion-infinite-scroll-content loadingSpinner="crescent">
        <!-- sceleton content before loading { -->
        <ion-grid
          class="ion-text-start"
          [class.hidden]="!(searchService.searchItems.isLoading() && !this.listPaginator.didLoad())"
          >
          @for (_r of [].constructor(3); track _r) {
            <ion-row>
              @for (_c of [].constructor(4); track _c) {
                <ion-col size="6" size-md="3">
                  <ion-card class="ion-no-margin">
                    <ion-skeleton-text
                      style="width: 20%"
                      class="ion-float-left ion-margin"
                      animated
                    ></ion-skeleton-text>
                    <ion-thumbnail class="img-placeholder">
                      <ion-skeleton-text></ion-skeleton-text>
                    </ion-thumbnail>
                    <ion-card-header class="skeleton">
                      <ion-card-title>
                        <ion-skeleton-text
                          animated
                          style="width: 40%"
                          class="ion-float-left"
                        ></ion-skeleton-text>
                        <span class="status-indicator"></span>
                      </ion-card-title>
                      <ion-text>
                        <ion-skeleton-text
                          animated
                          style="width: 60%"
                        ></ion-skeleton-text>
                      </ion-text>
                    </ion-card-header>
                  </ion-card>
                </ion-col>
              }
            </ion-row>
          }
        </ion-grid>
        <!-- } end sceleton content -->
      </ion-infinite-scroll-content>
    </ion-infinite-scroll>

    <!-- error container { -->
    <ion-grid
      class="statusMessage ion-text-center"
      [class.hidden]="!searchService.searchItems.wasError()"
      >
      <ion-row>
        <ion-col>
          <ion-icon color="primary" name="alert-circle-outline"></ion-icon>
          <h3>{{ getErrorString() | translate }}</h3>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <!--
          <ion-button color="primary" (click)="this.listPaginator.refresh()"
            >{{ 'tabs_search' | translate }}</ion-button
            >
            -->
            <ion-button color="primary" (click)="this.goBack()"
              >{{ 'tabs_search' | translate }}</ion-button
              >
            </ion-col>
          </ion-row>
        </ion-grid>
        <!-- } error container -->

        <div
          style="text-align: center; display: none"
          [class.hidden]="!this.listPaginator.didReachEnd()"
          >
          ¯\_(ツ)_/¯
        </div>
      </ion-content>
