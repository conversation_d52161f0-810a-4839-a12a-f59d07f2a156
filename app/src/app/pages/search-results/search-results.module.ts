import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { SearchResultsPageRoutingModule } from "./search-results-routing.module";

import { SearchResultsPage } from "./search-results.page";
import { TranslateModule } from "@ngx-translate/core";
import { PipesModule } from "../../pipes/pipes.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    SearchResultsPageRoutingModule,
    PipesModule,
  ],
  declarations: [SearchResultsPage],
})
export class SearchResultsPageModule {}
