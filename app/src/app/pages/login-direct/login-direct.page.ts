import {
  <PERSON>mpo<PERSON>,
  <PERSON>ementR<PERSON>,
  <PERSON><PERSON><PERSON>,
  On<PERSON>nit,
  ViewChild,
} from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import _ from "lodash";
import { Debug } from "src/app/helpers/debug";
import { Utils } from "src/app/helpers/utils";
import { ApiService } from "src/app/services/api/api.service";
import { AuthService } from "src/app/services/auth.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { ErrorHelper } from "src/app/services/error-helper";
import { MiscService } from "src/app/services/misc.service";
import { UtilsService } from "src/app/services/utils.service";
import { SavePassword } from "capacitor-ios-autofill-save-password";
import { AnalyticsService } from "src/app/services/analytics.service";
import {
  TME_login_error,
  TME_password_saved,
} from "src/app/classes/gtm-event.class";
import { IonInput } from "@ionic/angular";
import { environment as Env } from "src/environments/environment";

@Component({
  selector: "app-login",
  templateUrl: "./login-direct.page.html",
  styleUrls: ["./login-direct.page.scss"],
  standalone: false,
})
export class LoginDirectPage implements OnInit {
  loginForm: FormGroup;

  @ViewChild("usernameInput", { static: true }) usernameInput: IonInput;
  @ViewChild("passwordInput", { static: true }) passwordInput: IonInput;

  public vUsername: string = "";
  public vPassword: string = "";
  public pageType: "email" | "apple" | "website" = "email";
  public loginHash: string = "";
  public appData: any = {};

  /**
   * fix for autofill issue with webkit
   * https://github.com/ionic-team/ionic-framework/issues/23335
   */
  async fixAutofill() {
    const nativeUsernameInput = await this.usernameInput.getInputElement();
    const nativePasswordInput = await this.passwordInput.getInputElement();

    nativeUsernameInput.addEventListener("change", (ev: Event) => {
      requestAnimationFrame(() => {
        this.vUsername = (ev.target as HTMLInputElement).value;
      });
    });

    nativePasswordInput.addEventListener("change", (ev: Event) => {
      requestAnimationFrame(() => {
        this.vPassword = (ev.target as HTMLInputElement).value;
      });
    });
  }

  constructor(
    public formBuilder: FormBuilder,
    public errorHelper: ErrorHelper,
    public auth: AuthService,
    public api: ApiService,
    public debug: Debug,
    public router: Router,
    public dialog: DialogHelperService,
    public utils: UtilsService,
    public misc: MiscService,
    public analytics: AnalyticsService,
    public elementRef: ElementRef,
    public ngZone: NgZone,
    public route: ActivatedRoute,
  ) {
    this.loginHash = this.route.snapshot.queryParamMap.get("hash") || "";

    switch (this.route.snapshot.queryParamMap.get("type")) {
      case "apple":
        this.pageType = "apple";
        break;

      case "website":
        this.pageType = this.loginHash ? "website" : "email";
        break;

      default:
        this.pageType = "email";
    }

    this.appData = {
      name: Env.APP_NAME,
      site: Env.APP_WEBSITE,
    };
  }

  /**
   * Local form error container
   */
  public formErrorStrings = [];

  /**
   * indicator whether a related api call is in progress
   */
  isLoading() {
    return this.auth.isSimpleInProgress();
  }

  /**
   * Concat and get local and api errors
   * @returns
   */
  getErrors() {
    let errList = _.map(this.auth.getSimpleErrors(), (e) => {
      return e["error"];
    });
    return this.formErrorStrings.concat(errList);
  }

  /**
   * Run local form validation
   * @returns
   */
  validateForm() {
    this.formErrorStrings = [];
    this.api.clearErrorList();

    if (!this.loginForm.valid) {
      this.formErrorStrings = this.errorHelper.getFormValidationErrors(
        this.loginForm,
      );

      this.analytics.logEvent("login_error", <TME_login_error>{
        message: "client_side_validation: " + this.formErrorStrings.join(" ; "),
      });

      return false;
    } else {
      return true;
    }
  }

  private usernameToSave: string = "";
  private passwordToSave: string = "";

  /**
   * Run login api call
   */
  doLogin() {
    if (this.validateForm()) {
      this.usernameToSave = this.loginForm.value["usernameEmail"];
      this.passwordToSave = this.loginForm.value["password"];

      this.auth
        .simple(
          this.loginForm.value["usernameEmail"],
          this.loginForm.value["password"],
        )
        .subscribe({
          next: (data) => {
            // manual login successful

            // save password
            if (this.misc.devicePlatform == "ios") {
              SavePassword.promptDialog({
                username: this.usernameToSave,
                password: this.passwordToSave,
              })
                .then(() => {
                  this.analytics.logEvent("password_saved", <
                    TME_password_saved
                  >{
                    action: "login",
                  });
                  this.debug.l("Password save dialog success");
                })
                .catch((err) => {
                  this.analytics.logError(
                    err["key"] ??
                      err["error"] ??
                      err["message"] ??
                      "password_manager_error",
                    "console",
                  );
                  this.debug.l("Password save dialog failure", err);
                });
            }
          },
        });
    }
  }

  /**
   * Use session from appsflyer to log the user in
   */
  public async loginweb() {
    this.debug.l("Web login using appsflyer session data");

    try {
      await this.auth.withUrlSessionId(this.loginHash);
    } catch (e) {
      this.dialog.showAlert("woops", "session_login_error", "ok", () => {
        this.router.navigate(["/welcome"]);
      });
    }
  }

  public loginapple() {
    try {
      this.auth.apple();
    } catch (error) {
      this.dialog.showAlert(
        "error",
        this.utils.figureOutTheErrorMessage(error),
      );
    }
  }

  public doShowAppleLogin() {
    return this.misc.devicePlatform !== "android";
  }

  async ngOnInit() {
    this.loginForm = this.formBuilder.group({
      usernameEmail: [
        "",
        [
          Validators.required,
          Validators.pattern(Utils.getEmailOrUsernameRegexp()),
        ],
      ],
      password: ["", [Validators.required, Validators.minLength(3)]],
    });

    this.fixAutofill();
  }
}
