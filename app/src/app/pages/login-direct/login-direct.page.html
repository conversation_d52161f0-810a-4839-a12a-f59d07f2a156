<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button text="" icon="close"></ion-back-button>
    </ion-buttons>
    <ion-title mode="ios">
      <img
        src="assets/img/app-logo.svg"
        style="pointer-events: none"
        class="logoSvg"
      />
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <span [class.hidden]="this.pageType!='email'">
      <ion-row class="ion-justify-content-center">
        <ion-col size-md="6" class="ion-text-center">
          <h3>{{ "welcome_back_direct" | translate: this.appData }}</h3>
          <h4>{{ "thanks_for_downloading" | translate: this.appData }}</h4>
        </ion-col>
      </ion-row>

      <ion-row class="ion-justify-content-center">
        <ion-col size-md="6" class="ion-text-center">
          <h5>{{ "direct_login_help" | translate: this.appData }}</h5>
        </ion-col>
      </ion-row>

      <ion-row class="ion-justify-content-center ion-padding-horizontal">
        <ion-col size-md="6">
          <form [formGroup]="loginForm" (ngSubmit)="doLogin()">
            <ion-item class="ion-no-padding">
              <ion-input
                inputTrim
                appAutoFocus
                formControlName="usernameEmail"
                type="text"
                autocomplete="username"
                placeholder="{{ 'username_or_email' | translate }}"
                required
                [ngModel]="vUsername"
                #usernameInput
              ></ion-input>
            </ion-item>
            <ion-item class="ion-no-padding">
                <ion-input
                  formControlName="password"
                  type="password"
                  autocomplete="password"
                  placeholder="{{ 'password' | translate }}"
                  required
                  [ngModel]="vPassword"
                  #passwordInput
                ><ion-input-password-toggle slot="end"></ion-input-password-toggle></ion-input>
            </ion-item>
            <ion-item class="ion-no-padding ion-text-right" lines="none">
              <a
                [routerLink]="['/forgot-password']"
                class="ion-no-margin small-text"
                slot="end"
                >{{ "forgot_password" | translate }}</a
              >
            </ion-item>
            <app-form-error-list
              [errorList]="getErrors()"
            ></app-form-error-list>
            <ion-button type="submit" expand="block" [disabled]="isLoading()">
              {{ "sign_in" | translate }}
              <ion-spinner
                name="crescent"
                [class.hidden]="!isLoading()"
              ></ion-spinner>
            </ion-button>
          </form>
        </ion-col>
      </ion-row>
    </span>

    <span [class.hidden]="this.pageType!='apple'">
      <ion-row class="ion-justify-content-center">
        <ion-col size-md="6" class="ion-text-center">
          <h3>{{ "welcome_back_direct" | translate: this.appData }}</h3>
          <h4>{{ "thanks_for_downloading" | translate: this.appData }}</h4>
        </ion-col>
      </ion-row>

      <ion-row class="ion-justify-content-center">
        <ion-col size-md="6" class="ion-text-center">
          <h5 class="large-margin">
            {{ "tap_to_continue_apple_site" | translate: this.appData }}
          </h5>
        </ion-col>
      </ion-row>

      <ion-row
        class="ion-padding-horizontal ion-no-padding ion-justify-content-center"
      >
        <ion-col class="ion-text-center" size-md="6" style="text-align: center">
          <ion-button
            [class.hidden]="!this.doShowAppleLogin()"
            id="apple_signup_button"
            class="ion-margin-vertical"
            expand="block"
            color="dark"
            (click)="loginapple()"
          >
            <ion-icon slot="start" name="logo-apple"></ion-icon>
            <span>{{ 'continue_with_apple' | translate }}</span>
          </ion-button>
        </ion-col>
      </ion-row>
    </span>

    <span [class.hidden]="this.pageType!='website'">
      <ion-row class="ion-justify-content-center">
        <ion-col size-md="6" class="ion-text-center">
          <h3>{{ "welcome_back_direct" | translate: this.appData }}</h3>
          <h4>{{ "thanks_for_downloading" | translate: this.appData }}</h4>
        </ion-col>
      </ion-row>

      <ion-row class="ion-justify-content-center">
        <ion-col size-md="6" class="ion-text-center">
          <h5 class="large-margin">
            {{ "tap_to_continue_web" | translate: this.appData }}
          </h5>
        </ion-col>
      </ion-row>

      <ion-row
        class="ion-padding-horizontal ion-no-padding ion-justify-content-center"
      >
        <ion-col class="ion-text-center" size-md="6" style="text-align: center">
          <ion-button
            id="web_signin_button"
            class="ion-margin-vertical"
            expand="block"
            color="primary"
            (click)="loginweb()"
          >
            <ion-icon slot="start" name="log-in"></ion-icon>
            <span>{{ 'continue_web_session' | translate: this.appData }}</span>
          </ion-button>
        </ion-col>
      </ion-row>
    </span>
  </ion-grid>
</ion-content>
