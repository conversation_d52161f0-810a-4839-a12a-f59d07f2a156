import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { LoginDirectPageRoutingModule } from "./login-direct-routing.module";

import { LoginDirectPage } from "./login-direct.page";
import { TranslateModule } from "@ngx-translate/core";
import { FormErrorListComponentModule } from "src/app/components/form-error-list/form-error-list.component.module";
import AutoFocusModule from "src/app/auto-focus.module";
import TrimModule from "src/app/directives/trim.directive.module";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    LoginDirectPageRoutingModule,
    TranslateModule.forChild(),
    FormErrorListComponentModule,
    AutoFocusModule,
    TrimModule,
  ],
  declarations: [LoginDirectPage],
})
export class LoginDirectPageModule {}
