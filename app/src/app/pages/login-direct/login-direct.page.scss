ion-button {
    height: 46px;
    text-transform: none;
}

ion-item {
  --highlight-height: 0px;
}

h4 {
  font-size: large;
}

h5 {
  color: var(--ion-color-medium-tint);
  font-size: medium;
  padding-left: 5%;
  padding-right: 5%;
}

.large-margin {
  margin-top: 20%;
  margin-bottom: 20%;
}

.logoSvg {
  height: 25px;
}

.horizontal-line {
  height: 22px;
  outline: 0;
  border: 0;
  text-align: center;
  text-transform: uppercase;
  position: relative;
  &:before {
    content: '';
    background: rgba(var(--ion-color-medium-rgb), .5);
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 1px;
  }
  &:after {
    content: attr(data-content);
    font-size: 14px;
    position: relative;
    display: inline-block;
    color: var(--ion-color-medium-tint);
    padding: 0 12px;
    line-height: 20px;
    background-color: #FFF;
  }
}