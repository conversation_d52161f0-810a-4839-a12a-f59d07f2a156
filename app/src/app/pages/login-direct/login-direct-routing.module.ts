import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { LoginDirectPage } from "./login-direct.page";

const routes: Routes = [
  {
    path: "",
    component: LoginDirectPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LoginDirectPageRoutingModule {}
