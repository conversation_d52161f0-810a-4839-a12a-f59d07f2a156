import { Component, OnInit } from "@angular/core";
import { LocationService } from "../../services/location.service";
import { Debug } from "../../helpers/debug";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { AccountService } from "../../services/account.service";

@Component({
  selector: "app-location-zip",
  templateUrl: "./location-zip.page.html",
  styleUrls: ["./location-zip.page.scss"],
  standalone: false,
})
export class LocationZipPage {
  constructor(
    public locationService: LocationService,
    public dbg: Debug,
    public dlg: DialogHelperService,
    public accountService: AccountService,
  ) {}

  private resetForm() {
    this.locationService.selectedZip = "";
  }

  public ionViewWillEnter() {
    this.resetForm();
  }

  /**
   * Get city list.
   */
  public getCityList() {
    this.locationService.isProcessing = true;

    this.locationService
      .locationHelper({
        get_countries: 1,
        country_code: this.locationService.selectedCountry,
        zip: this.locationService.selectedZip,
      })
      .then(async ({ data }) => {
        this.locationService.selectedCountry =
          data.selected_country ?? this.locationService.selectedCountry;
        this.locationService.selectedCity =
          data.selected_city ?? this.locationService.selectedCity;

        this.locationService.isProcessing = false;

        if (data.whatToShow.indexOf("zip_input") > -1) {
          this.locationService.nextPage = "location-zip";
          await this.dlg.showToast("Zip code", "not found!");

          return;
        }

        if (data.whatToShow.indexOf("city_select") > -1) {
          this.locationService.nextPage = "location-city";
        }

        if (!data.whatToShow.length) {
          this.locationService.nextPage = "/app/my-profile-edit";
          this.locationService.actionTitle = "finish";
          this.accountService.setLocation({
            country: data.selected_country,
            city: data.selected_city,
            zip: data.selected_zip,
          });
        }

        this.locationService.next();
      })
      .catch((error) => this.dbg.le("zip select error", error))
      .then(() => (this.locationService.isProcessing = false));
  }
}
