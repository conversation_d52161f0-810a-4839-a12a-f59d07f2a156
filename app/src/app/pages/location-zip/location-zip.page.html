<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ "title_location_zip" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list>
    @if (locationService.selectedCountry) {
      <ion-item>
        <ion-input
          label="{{ 'selected_location_country' | translate }}:"
          type="text"
          text-center
          [(ngModel)]="locationService.selectedCountry"
          readonly
        ></ion-input>
      </ion-item>
    }
    <ion-item>
      <ion-input
        placeholder="{{ 'enter_zip' | translate }}"
        [(ngModel)]="locationService.selectedZip"
      ></ion-input>
    </ion-item>
  </ion-list>

  @if (locationService.selectedZip && locationService.selectedZip.length) {
    <ion-grid
      >
      <ion-row class="ion-justify-content-center ion-margin-top">
        <ion-col size="12" size-sm="6" class="ion-padding-horizontal">
          <ion-button
            expand="block"
            (click)="getCityList()"
            [disabled]="locationService.isProcessing"
            >
            {{ locationService.actionTitle | translate }}
          </ion-button>
        </ion-col>
      </ion-row>
    </ion-grid>
  }
</ion-content>
