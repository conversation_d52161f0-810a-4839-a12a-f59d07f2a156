import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { LocationZipPageRoutingModule } from "./location-zip-routing.module";

import { LocationZipPage } from "./location-zip.page";

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    LocationZipPageRoutingModule,
  ],
  declarations: [LocationZipPage],
})
export class LocationZipPageModule {}
