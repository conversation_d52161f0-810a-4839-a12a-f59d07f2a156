import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { LocationZipPage } from "./location-zip.page";

const routes: Routes = [
  {
    path: "",
    component: LocationZipPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LocationZipPageRoutingModule {}
