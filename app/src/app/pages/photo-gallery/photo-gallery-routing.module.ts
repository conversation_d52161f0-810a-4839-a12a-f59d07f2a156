import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { PhotoGalleryPage } from "./photo-gallery.page";

const routes: Routes = [
  {
    path: "",
    component: PhotoGalleryPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PhotoGalleryPageRoutingModule {}
