<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title
      >{{ profile?.first_name || profile?.username }} {{ 'photos' | translate
      }}</ion-title
      >
    </ion-toolbar>
  </ion-header>

  <ion-content>
    @if (this.hasData() && !this.isLoading() && !this.wasError()) {
      <ion-grid>
        <ion-row>
          <ion-col>
            @for (photo of profile?.photos; track photo; let i = $index) {
              <ion-card
                [ngClass]="photo.image_url ? 'image' : 'no-image'"
                id="card_index_{{i}}"
                >
                <ion-img
                  (click)="openPhoto(i)"
                  [src]="photo.image_url"
                  onerror="this.style.display='none'"
                ></ion-img>
                <div class="tile-icons">
                  <ion-button
                    fill="clear"
                    (click)="likePhoto(photo)"
                    [class.liked-photo]="photo.liked"
                    >
                    <ion-icon slot="start" name="thumbs-up"></ion-icon>
                    <span>{{ photo.likes }}</span>
                  </ion-button>
                  <ion-button fill="clear" (click)="openPhotoComments(photo)">
                    <ion-icon slot="start" name="chatbox"></ion-icon>
                    <span>{{ photo.comments }}</span>
                  </ion-button>
                </div>
              </ion-card>
            }
          </ion-col>
        </ion-row>
      </ion-grid>
    }

    <div
      [class.hidden]="!this.isLoading() || this.wasError()"
      style="text-align: center; padding-top: 40%"
      >
      <ion-spinner name="crescent"></ion-spinner>
    </div>

    <!-- error container { -->
    <ion-grid class="statusMessage ion-text-center" [class.hidden]="!this.wasError()">
      <ion-row>
        <ion-col>
          <ion-icon color="primary" name="sad"></ion-icon>
          <h3>{{ 'error_loading_gallery' | translate }}</h3>
        </ion-col>
      </ion-row>
    </ion-grid>
    <!-- } error container -->
  </ion-content>
