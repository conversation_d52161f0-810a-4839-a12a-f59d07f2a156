import { Component, OnInit } from "@angular/core";
import { ProfileUser } from "../../schemas/profile-user";
import _ from "lodash";
import { ProfileService } from "src/app/services/profile.service";
import { ActivatedRoute, Router } from "@angular/router";
import { StateService } from "src/app/services/state.service";
import { AccountService } from "src/app/services/account.service";
import { GalleryImage } from "../../schemas/gallery-image";
import { PhotoService } from "../../services/photo.service";
import { PurchaseService } from "../../services/purchase.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";

@Component({
  selector: "app-photo-gallery",
  templateUrl: "./photo-gallery.page.html",
  styleUrls: ["./photo-gallery.page.scss"],
  standalone: false,
})
export class PhotoGalleryPage implements OnInit {
  /**
   * UserProfile.
   */
  public profile: ProfileUser.type;
  public username: string = "";

  /**
   * Photos.
   */
  public photos: string[] = [];

  constructor(
    private profileService: ProfileService,
    private activatedRoute: ActivatedRoute,
    private stateService: StateService,
    private dialog: DialogHelperService,
    private router: Router,
    private photoService: PhotoService,
    private purchaseService: PurchaseService,
    public accountService: AccountService,
  ) {
    this.username = this.activatedRoute.snapshot.params?.username;
  }

  public openPhoto(i) {
    if (
      !this.accountService.isPremium() &&
      this.accountService.account.user_id != this.profile.user_id
    ) {
      this.stateService.set("profileFullSizeLimit", null, this.profile);
      this.purchaseService
        .setDialogType("photo_full_size_limit")
        .openUpgradeDialog((error) => {
          this.dialog.showToastBaseOnErrorResponse(error);
        });
      return;
    }

    this.stateService.set("profile", this.username, this.profile);
    this.router.navigate(["/app/photo-full-view", "profile", this.username, i]);
  }

  public hasData() {
    return !_.isEmpty(this.profile);
  }

  public isLoading() {
    return this.profileService.isGetInProgress(this.username);
  }

  public wasError() {
    return this.profileService.wasGetError(this.username);
  }

  ngOnInit() {}

  ionViewDidLeave() {
    this.stateService.delete("photo", "comments");
  }

  private sortPhotos(): void {
    if (this.hasData()) {
      let photos = this.profile.photos;
      photos.sort((a, b) => {
        return a.main_photo == true ? -1 : 1;
      });
      photos[0].main_photo = true;
      this.profile.photos = photos;
    }
  }

  ionViewWillEnter() {
    // try to get profile object from navigation params

    this.profile = this.stateService.get("profile", this.username, null);

    if (!this.hasData()) {
      // fetch profile from server
      this.profileService.get(this.username).subscribe({
        next: (data) => {
          // set data
          this.profile = data.data;
          this.sortPhotos();
        },
      });
    } else {
      this.sortPhotos();
    }

    let photoComments = this.stateService.get("photo", "comments");

    if (photoComments && !isNaN(photoComments.comments)) {
      this.profile.photos.forEach((photo) => {
        if (
          photo.image_id === photoComments.image_id &&
          photo.gallery_id === photoComments.gallery_id
        ) {
          photo.comments = photoComments.comments;
        }
      });
    }
  }

  /**
   * Like/unlike photo.
   *
   * @param photo
   */
  public likePhoto(photo: GalleryImage.type) {
    let command: string = photo.liked ? "unlike" : "like";
    this.photoService[command]({
      gallery_id: photo.gallery_id,
      image_id: photo.image_id,
      owner_id: this.profile.user_id,
    }).subscribe({
      next: (res) => {
        photo.liked = !photo.liked;
        photo.liked ? photo.likes++ : photo.likes--;
      },
    });
  }

  /**
   * Open photo comments page.
   *
   * @param photo
   */
  public async openPhotoComments(photo: GalleryImage.type) {
    this.stateService.set("photo", "comments", {
      gallery_id: photo.gallery_id,
      image_id: photo.image_id,
      owner_id: this.profile.user_id,
      comments: photo.comments,
    });
    await this.router.navigate([
      "/app/photo-comments",
      this.profile.username,
      photo.gallery_id,
      photo.image_id,
    ]);
  }
}
