ion-content {
  & > div {
     > ion-grid {
      > ion-row {
        > ion-col {

          ion-card {
            display: flex;
            flex-direction: column;
            box-shadow: 0 6px 12px rgb(0 0 0 / 25%);
          }
          &.no-image {
            display: none;
          }
        }
      }
    }
  }
}

ion-button.liked-photo {

  ion-icon, span {
    color: var(--ion-color-dark-tint);
  }
}

.statusMessage {
  margin-top: 10vh;

  ion-icon {
    font-size: 7rem;
    opacity: .3;
  }
}

.tile-icons {
  ion-icon {
    color: var(--ion-color-medium);
  }
  span {
    color: var(--ion-color-medium);
  }
}
