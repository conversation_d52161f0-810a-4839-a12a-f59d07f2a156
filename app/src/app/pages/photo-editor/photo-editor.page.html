<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button
        icon="chevron-back-sharp"
        (click)="navCtrl.back()"
      ></ion-button>
    </ion-buttons>
    <ion-title>{{ "title_photo_editor" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content padding>
  <div class="editor_container">
    <image-cropper
      [imageChangedEvent]="imageChangedEvent"
      [imageURL]="photo.image_url"
      [maintainAspectRatio]="false"
      [containWithinAspectRatio]="containWithinAspectRatio"
      [aspectRatio]="4 / 3"
      [canvasRotation]="canvasRotation"
      [transform]="transform"
      [alignImage]="'left'"
      [style.display]="showCropper ? null : 'none'"
      [cropperMinWidth]="MIN_CROP_WIDTH"
      [cropperMinHeight]="MIN_CROP_HEIGHT"
      (imageCropped)="imageCropped($event)"
      (imageLoaded)="imageLoaded($event)"
      (cropperReady)="cropperReady($event)"
      (loadImageFailed)="loadImageFailed()"
    ></image-cropper>
  </div>

  <!-- <img [src]="croppedImage" /> -->
</ion-content>

<ion-footer>
  <div class="btn_container">
    <div class="btn_group">
      <ion-buttons>
        <ion-button (click)="rotateLeft()">
          <ion-icon name="refresh" class="mirror"></ion-icon>
        </ion-button>
        <ion-button (click)="rotateRight()">
          <ion-icon name="refresh"></ion-icon>
        </ion-button>
        <ion-button (click)="resetImage()">
          <ion-icon name="sync"></ion-icon>
        </ion-button>
        <ion-button (click)="toggleContainWithinAspectRatio()">
          <ion-icon
            name="{{ containWithinAspectRatio ? 'contract-outline' : 'expand-outline' }}"
          ></ion-icon>
        </ion-button>
      </ion-buttons>
    </div>
    <div class="btn_group">
      <ion-buttons>
        <ion-button color="secondary" (click)="save()">
          <ion-icon name="checkmark"></ion-icon>
        </ion-button>
        <ion-button color="secondary" (click)="navCtrl.back()">
          <ion-icon name="close"></ion-icon>
        </ion-button>
      </ion-buttons>
    </div>
  </div>
</ion-footer>
