import { Component, OnInit } from "@angular/core";
import { StateService } from "../../services/state.service";
import { AccountService } from "../../services/account.service";
import { GalleryImage } from "../../schemas/gallery-image";
import {
  base64ToFile,
  Dimensions,
  ImageCroppedEvent,
  ImageTransform,
} from "ngx-image-cropper";
import { Debug } from "src/app/helpers/debug";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { NavController } from "@ionic/angular";
import { PictureService } from "../../services/picture.service";
import * as _ from "lodash";
import { firstValueFrom } from "rxjs";
import { ApiResponses } from "../../services/api/api.responses";

@Component({
  selector: "app-photo-editor",
  templateUrl: "./photo-editor.page.html",
  styleUrls: ["./photo-editor.page.scss"],
  standalone: false,
})
export class PhotoEditorPage implements OnInit {
  /**
   * Photo.
   */
  public photo: GalleryImage.type;

  public imageChangedEvent: any = "";
  public croppedImage: any = "";
  public canvasRotation = 0;
  public rotation = 0;
  public scale = 1;
  public showCropper = false;
  public containWithinAspectRatio = false;
  public transform: ImageTransform = {};
  public MIN_CROP_HEIGHT = 400;
  public MIN_CROP_WIDTH = 400;
  private bckImgPosition: Object = null;
  public isCropped: boolean = false;
  private loadedImgData: { width: number; height: number } = null;
  private imageCroppedEvent: ImageCroppedEvent = null;

  constructor(
    public accountService: AccountService,
    public stateService: StateService,
    public dbg: Debug,
    public dlg: DialogHelperService,
    public navCtrl: NavController,
    public pictureService: PictureService,
  ) {}

  ngOnInit() {
    this.photo = this.stateService.get(
      "photo",
      this.accountService.account.username,
    );
  }

  /**
   * Cropped image.
   */
  public imageCropped(event: ImageCroppedEvent) {
    if (!this.bckImgPosition) {
      this.bckImgPosition = event.imagePosition;
    } else {
      this.isCropped = this.bckImgPosition != event.imagePosition;
    }

    this.imageCroppedEvent = event;
    this.croppedImage = event.base64;
  }

  /**
   * Image loaded.
   */
  public imageLoaded(event) {
    this.loadedImgData = event.original.size;
    this.showCropper = true;
  }

  /**
   * Cropper ready.
   */
  public cropperReady(sourceImageDimensions: Dimensions) {
    this.dbg.l("Cropper ready", sourceImageDimensions);
  }

  /**
   * Load img failed.
   */
  public loadImageFailed() {
    this.dbg.le("Load failed");
    this.dlg.showErrorAlert("error", "load_img_failed");
  }

  /**
   * Rotate left.
   */
  public rotateLeft() {
    this.canvasRotation--;
    if (this.canvasRotation <= -4) {
      this.canvasRotation = 0;
    }
    this.flipAfterRotate();
  }

  /**
   * Rotate right.
   */
  public rotateRight() {
    this.canvasRotation++;
    if (this.canvasRotation >= 4) {
      this.canvasRotation = 0;
    }
    this.flipAfterRotate();
  }

  // /**
  //  * Flip horizontal.
  //  */
  // public flipHorizontal() {
  //   this.transform = {
  //     ...this.transform,
  //     flipH: !this.transform.flipH,
  //   };
  // }

  // /**
  //  * Flip vertical.
  //  */
  // public flipVertical() {
  //   this.transform = {
  //     ...this.transform,
  //     flipV: !this.transform.flipV,
  //   };
  // }

  /**
   * Reset img.
   */
  public resetImage() {
    this.scale = 1;
    this.rotation = 0;
    this.canvasRotation = 0;
    this.transform = {};
  }

  // /**
  //  * Zoom OUT.
  //  */
  // public zoomOut() {
  //   this.scale -= 0.1;
  //   this.transform = {
  //     ...this.transform,
  //     scale: this.scale,
  //   };
  // }

  // /**
  //  * Zoom IN.
  //  */
  // public zoomIn() {
  //   this.scale += 0.1;
  //   this.transform = {
  //     ...this.transform,
  //     scale: this.scale,
  //   };
  // }

  /**
   * Toggle aspect ratio.
   */
  public toggleContainWithinAspectRatio() {
    this.containWithinAspectRatio = !this.containWithinAspectRatio;
  }

  // /**
  //  * Update rotation.
  //  */
  // public updateRotation() {
  //   this.transform = {
  //     ...this.transform,
  //     rotate: this.rotation,
  //   };
  // }

  /**
   * Flip after rotate.
   */
  private flipAfterRotate() {
    const flippedH = this.transform.flipH;
    const flippedV = this.transform.flipV;
    this.transform = {
      ...this.transform,
      flipH: flippedV,
      flipV: flippedH,
    };
  }

  /**
   * update image on server
   */
  public async save() {
    await this.dlg.showLoading("saving", "💾");

    let data = null;

    if (this.canvasRotation != 0) {
      data = await this.savePictureRotate();
    }

    if (this.isCropped) {
      data = await this.savePictureCrop();
    }

    if (data) {
      await this.updateAccountPhotos(data);
    }

    await this.dlg.hideLoading();
    await this.navCtrl.back();
  }

  /**
   * Update account data.
   */
  private async updateAccountPhotos(data) {
    await this.accountService.account.photos.forEach((photo, i) => {
      if (
        photo.image_id == data.data.image_id &&
        photo.gallery_id == data.data.gallery_id
      ) {
        this.accountService.account.photos[i].image_url = data.data.image_url;
        this.accountService.account.photos[i].thumb_url = data.data.thumb_url;
        if (this.accountService.account.photos[i].main_photo == true) {
          //force change
          this.accountService.account.avatar_url =
            this.accountService.account.photos[i].thumb_url;
        }
      }
    });
  }

  /**
   * Rotate.
   */
  private savePictureRotate(): Promise<ApiResponses.ResponseBase.type> {
    return firstValueFrom(
      this.pictureService.rotate({
        gallery_id: this.photo.gallery_id,
        image_id: this.photo.image_id,
        rotation: _.toString(this.canvasRotation),
      }),
    );
  }

  /**
   * Crop.
   */
  private savePictureCrop(): Promise<ApiResponses.ResponseBase.type> {
    return firstValueFrom(
      this.pictureService.crop({
        gallery_id: this.photo.gallery_id,
        image_id: this.photo.image_id,
        h:
          this.imageCroppedEvent.height > this.loadedImgData.height
            ? this.loadedImgData.height
            : this.imageCroppedEvent.height,
        w:
          this.imageCroppedEvent.width > this.loadedImgData.width
            ? this.loadedImgData.width
            : this.imageCroppedEvent.width,
        img_h: this.loadedImgData.height,
        img_w: this.loadedImgData.width,
        ratio: 1,
        x: this.imageCroppedEvent.imagePosition.x1,
        y: this.imageCroppedEvent.imagePosition.y1,
      }),
    );
  }
}
