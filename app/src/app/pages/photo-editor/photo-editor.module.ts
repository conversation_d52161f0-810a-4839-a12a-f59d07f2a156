import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { PhotoEditorPageRoutingModule } from "./photo-editor-routing.module";

import { PhotoEditorPage } from "./photo-editor.page";

import { TranslateModule } from "@ngx-translate/core";
import { ImageCropperComponent } from "ngx-image-cropper";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    PhotoEditorPageRoutingModule,
    ImageCropperComponent,
  ],
  declarations: [PhotoEditorPage],
})
export class PhotoEditorPageModule {}
