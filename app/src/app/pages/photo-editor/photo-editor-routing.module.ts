import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { PhotoEditorPage } from "./photo-editor.page";

const routes: Routes = [
  {
    path: "",
    component: PhotoEditorPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PhotoEditorPageRoutingModule {}
