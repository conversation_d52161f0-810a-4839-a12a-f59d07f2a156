import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from "@angular/core";
import { Debug } from "src/app/helpers/debug";

import { ListPaginator } from "src/app/services/lists/list-paginator";
import { PaginatorHelperService } from "src/app/services/paginator-helper.service";

import { UnreadCountService } from "src/app/services/unread-count.service";
import { ProfileService, VIEWED_ME } from "../../services/profile.service";
import { AccountService } from "../../services/account.service";
import { StateService } from "src/app/services/state.service";
import { PurchaseService } from "src/app/services/purchase.service";
import { NavController, Platform } from "@ionic/angular";
import { DeviceInfoService } from "src/app/services/device-info.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";

import { environment as Env } from "../../../environments/environment";
import { ProfileUser } from "../../schemas/profile-user";
import { StorageService } from "../../services/storage.service";
import { AnalyticsService } from "src/app/services/analytics.service";
import { TME_initiate_upgrade } from "src/app/classes/gtm-event.class";
import { IapItem } from "src/app/services/iap-item.interface";

@Component({
  selector: "app-list-viewed-me",
  templateUrl: "./list-viewed-me.page.html",
  styleUrls: ["./list-viewed-me.page.scss"],
  standalone: false,
})
export class ListViewedMePage implements OnInit, OnDestroy {
  public listPaginator: ListPaginator;
  public profile: ProfileUser.type;
  private sub = null;

  constructor(
    public purchaseService: PurchaseService,
    public profileService: ProfileService,
    public accountService: AccountService,
    private paginatorHelper: PaginatorHelperService,
    public unreadCountService: UnreadCountService,
    private navController: NavController,
    private dialog: DialogHelperService,
    private deviceInfo: DeviceInfoService,
    private platform: Platform,
    private stateService: StateService,
    private debug: Debug,
    private storage: StorageService,
    private analyticsService: AnalyticsService,
  ) {
    this.listPaginator = this.paginatorHelper.listViewedMe;
  }

  ionViewDidEnter() {
    // send analytics api call, set params for purchase
    if (!this.accountService.isPremium()) {
      this.purchaseService.setPurchaseExtraInfo(
        "initiate_upgrade_category",
        "upgrade_page",
      );
      this.purchaseService.setPurchaseExtraInfo(
        "initiate_upgrade_title",
        "viewed_my_profile",
      );

      this.analyticsService.logEvent("initiate_upgrade", <TME_initiate_upgrade>{
        initiate_upgrade_category: "upgrade_page",
        initiate_upgrade_title: "viewed_my_profile",
      });
    }

    this.profileService.viewedMeBadge = 0;
    this.storage
      .set(
        VIEWED_ME + this.accountService.account.user_id,
        this.profileService.viewedMeBadgeTotal,
      )
      .catch((err) => this.debug.le("failed to set viewed me", err));

    this.sub = this.platform.backButton.subscribeWithPriority(999, () => {
      this.close();
    });
  }

  ngOnDestroy() {
    this.stateService.delete("sendMessageProfile", null);

    // reset dialog type
    this.purchaseService.setDialogType("");
  }

  async ionViewWillEnter() {
    this.profile = this.stateService.get("sendMessageProfile", null);
  }

  get dialogType() {
    return this.purchaseService.dialogType;
  }

  ionViewWillLeave() {
    // enable when exiting
    this.sub.unsubscribe();

    // reset dialog type
    this.purchaseService.setDialogType("");
  }

  public inProgress() {
    return this.purchaseService.inProgress();
  }

  get env() {
    return Env;
  }

  public data: IapItem[];

  public selectedItem: IapItem;

  public selectProduct(item: IapItem) {
    this.selectedItem = item;
  }

  public isIos() {
    return this.deviceInfo.platform == "ios";
  }

  ngOnInit() {
    if (this.accountService.account.is_premium) return;

    if (this.purchaseService.subscriptions.length == 0) {
      // initialize mock if page opened directly for design ( for example )
      this.dialog.showToast(
        "",
        "DEV_ONLY: Dialog was not initialized properly, using mock data!",
        "error",
      );
      this.purchaseService.init_MOCK();
    }

    // center item as default
    this.selectProduct(
      this.purchaseService.subscriptions[1] ||
        this.purchaseService.subscriptions[0],
    );

    this.data = this.purchaseService.subscriptions;
  }

  public upgradeClicked() {
    this.purchaseService.buySubscription(this.selectedItem.store_id);
  }

  public close() {
    this.navController.pop().then(() => {
      this.purchaseService.executeCloseCallback();
    });
  }
}
