import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { ListViewedMePageRoutingModule } from "./list-viewed-me-routing.module";
import { ListViewedMePage } from "./list-viewed-me.page";
import { TranslateModule } from "@ngx-translate/core";
import { PipesModule } from "src/app/pipes/pipes.module";
import { CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    ListViewedMePageRoutingModule,
    PipesModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [ListViewedMePage],
})
export class ListViewedMePageModule {}
