ion-header {
  ion-title {
    ion-img {
      padding-top: 5px;
      padding-bottom: 5px;
      &.md {
        height: 56px;
      }
      &.ios {
        height: 44px;
      }
    }
  }
}

.nt-indicator {
  pointer-events: none;
  background-color: var(--toolbar-nt-indicator);
  width: 12px;
  height: 12px;
  border: 2px solid var(--toolbar-nt-indicator-border);
  border-radius: 50%;
  display: block;
  position: absolute;
  right: -5px;
  top: 0;

  &.for-menu {
    right: 10px;
    top: 15px;
  }
}

.ios .nt-indicator.for-menu {
  top:9px;
  right: 5px;
}

.spinner-wrapper {
  margin-top: 16px;
}

ion-grid {
  padding-bottom: 0;
}

ion-infinite-scroll {

  ion-grid {
    padding-top: 0;
  }
}

ion-list {
  border-radius: 8px;
  box-shadow: 0 6px 12px rgba(0,0,0,.08);
  margin: 10px 10px 0;
}

ion-item {
  --ripple-color: transparent;
}

ion-card {
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  border-radius: 8px;
  height: 100%;

  .username {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .status-indicator {
      background-color: transparent;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      display: inline-block;
  }

  &.active {
      .status-indicator {
          background-color: var(--ion-color-success);
      }
  }

  ion-skeleton-text + .status-indicator {
    margin-left: 5px;
  }

  .img-placeholder {
    position: relative;
    width: 100%;
    height: 0;
    padding-top: 100%;

    ion-img,
    ion-skeleton-text {
      position: absolute;
      width: 100%;
      left: 0;
      top: 0;
    }
  }
}

ion-card-header {
  color: var(--ion-color-white);
  padding: 12px;
  padding-top: 60px;
  position: absolute;
  bottom: 0;
  width: 100%
}

ion-card-header:not(.skeleton) {
  background-image: var(--gl-gradient-bottom-to-top-60);
}

ion-card-title {
  color: var(--ion-color-white);
  font-size: 14px;
  font-weight: 600;
}

ion-card-content {
  padding: 0 12px 12px;
}

ion-text {
  width: 100%;
  font-size: 12px;
  opacity: 0.8;
}

.distance {
  background-color: rgba(var(--ion-color-dark-rgb), 0.4);
  color: var(--ion-color-white);
  border-radius: 4px;
  font-size: 12px;
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  z-index: 4;

  &:empty {
    display: none;
  }
}

.statusMessage {
  margin-top: 15vh;

  ion-icon {
    font-size: 7rem;
    opacity: .3;
  }

  h6 {
    color: var(--ion-color-medium);
  }
}


/** go premium css copy */
.try-premium-title-wrapper {
  padding-top: constant(safe-area-inset-top) !important;
  padding-top: env(safe-area-inset-top) !important;

  .try-premium-title {
    margin-left: 40px;
    margin-right: 40px;
  }
}

.features-list {
  background-color: var(--ion-color-primary);
  padding: 24px 0;

  &--color {
    color: var(--ion-color-white);
  }

  .swiper-container {
    padding-bottom: 0;

    swiper-slide {
      flex-direction: column;
    }
  }

  swiper-slide {
    ion-thumbnail {
      --size: 84px;
      --border-radius: 50%;
      margin: 6px;
    }

    ion-icon {
      background-color: var(--ion-color-white);
      border-radius: 50%;
      padding: 14px;
      font-size: 3.5rem;

      &.custom-icon {
        width: 42px;
        height: 42px;
        padding: 20px;
      }
    }

    h4 {
      text-transform: uppercase;
      font-weight: bold;
      letter-spacing: 0;
    }
  }
}

.max-width--300 {
  max-width: 300px;
  @media screen and (min-width:480px) {
    max-width: 360px;
  }
}

.p--size {
  font-size: 14px;
  margin: 2px auto 0;
}

.swiper-pagination {
  bottom: 0 !important;
}

.product-items {
  padding: 32px 16px;
  margin: 0 -4px;

  .item-inner {
    height: 100%;
    border: 2px solid transparent;
    border-radius: 6px;
    box-shadow: 0 0 0 1px var(--ion-color-light-shade);
    margin: 0 4px;
    padding: 0 10px 8px;
  }

  .selected {
    .item-inner {
      border: 2px solid var(--ion-color-primary);
      box-shadow: none;
    }
  }

  .best-label,
  .savings {
    background-color: var(--ion-color-primary);
    color: var(--ion-color-white);
    height: 16px;
    border-radius: 3px;
    font-size: 10px;
    text-transform: uppercase;
    white-space: nowrap;
    padding: 2px 8px;
    position: absolute;
    left: 50%;
    top: -8px;
    transform: translateX(-50%);
  }
  .savings {
    background-color: var(--ion-color-success);
    top: auto;
    bottom: -8px;
  }

  .period-length {
    font-size: 14px;
    font-weight: 700;
  }

  .price {
    color: var(--ion-color-primary);
    font-weight: 700;
    margin: 8px 0 2px;
  }

  .period-unit {
    font-size: 12px;
    margin-top: 0;
  }

}

.billed {
  font-size: 14px;
}

.legal {
  font-size: 12px;
  line-height: 1.4;
  margin-top: 0;
  text-align: justify;
}
