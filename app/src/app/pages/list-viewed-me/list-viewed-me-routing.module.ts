import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { ListViewedMePage } from "./list-viewed-me.page";

const routes: Routes = [
  {
    path: "",
    component: ListViewedMePage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ListViewedMePageRoutingModule {}
