import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { ProfileVerificationPageRoutingModule } from "./profile-verification-routing.module";

import { ProfileVerificationPage } from "./profile-verification.page";
import { TranslateModule } from "@ngx-translate/core";
import { ExternalContentComponentModule } from "src/app/components/external-content/external-content.component.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    IonicModule,
    ProfileVerificationPageRoutingModule,
    ExternalContentComponentModule,
  ],
  declarations: [ProfileVerificationPage],
})
export class ProfileVerificationPageModule {}
