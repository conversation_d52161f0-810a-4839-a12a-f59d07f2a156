import { Component } from "@angular/core";
import { AccountService } from "../../services/account.service";
import { PictureUploadHelperService } from "../../picture-upload-helper.service";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { firstValueFrom } from "rxjs";
import { Router } from "@angular/router";
import { Debug } from "../../helpers/debug";

@Component({
  selector: "app-profile-verification",
  templateUrl: "./profile-verification.page.html",
  styleUrls: ["./profile-verification.page.scss"],
  standalone: false,
})
export class ProfileVerificationPage {
  constructor(
    public accountService: AccountService,
    public pictureUploadHelper: PictureUploadHelperService,
    public dlg: DialogHelperService,
    public router: Router,
    public dbg: Debug,
  ) {}

  ionViewWillEnter() {
    this.accountService.getProfileVerificationData();
  }

  /**
   * Upload photo.
   *
   * @param data
   * @param url
   */
  public uploadPhoto() {
    this.pictureUploadHelper.displayUploadMenu((data) => {
      this.dlg.showLoading();
      this.accountService
        .profileVerificationAdd({
          task_id: this.accountService.profileVerificationData.pvt_data.task_id,
          image_data: data,
        })
        .then((res) => this.accountService.getProfileVerificationData())
        .catch((e) => this.dbg.le("profile verification photo upload error", e))
        .then(() => this.dlg.hideLoading());
    });
  }

  public openEditProfilePage() {
    this.router.navigate(["/app/my-profile-edit"]);
  }

  public photoDelete() {
    this.dlg.showLoading();
    this.accountService
      .profileVerificationDelete({
        task_id: this.accountService.profileVerificationData.pvt_data.task_id,
      })
      .then(() => this.accountService.getProfileVerificationData())
      .catch((e) => this.dbg.le("profile verification delete error", e))
      .then(() => this.dlg.hideLoading());
  }
}
