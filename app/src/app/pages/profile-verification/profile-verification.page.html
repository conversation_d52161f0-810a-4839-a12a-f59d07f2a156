<ion-header class="ion-text-center">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title style="margin-right: 48px">
      {{ 'profile_verification' | translate }}
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  @if (accountService.profileVerificationData?.pv_status === 'verified') {
    <ion-list lines="none">
      <ion-item class="ion-text-center">
        <ion-label>
          <h1>{{ 'verification_7' | translate }}</h1>
        </ion-label>
      </ion-item>
    </ion-list>
  }

  @if (accountService.profileVerificationData?.pv_status === 'profile-photo-first') {
    <ion-list lines="none">
      <ion-item class="ion-text-center ion-margin-bottom">
        <ion-label>
          <h2>{{ 'verification_8' | translate }}</h2>
        </ion-label>
      </ion-item>
      <ion-item class="ion-text-center">
        <ion-col size="12" size-sm="5">
          <ion-button expand="block"
            class="ion-text-wrap ion-margin-bottom"
            (click)="openEditProfilePage()"
          >{{ 'upload_your_photo' | translate }}</ion-button>
        </ion-col>
      </ion-item>
    </ion-list>
  }

  @if (accountService.profileVerificationData?.pv_status === 'pending') {
    <ion-list lines="none">
      <ion-fab slot="fixed" vertical="top" horizontal="end">
        <ion-fab-button (click)="photoDelete()" size="small">
          <ion-icon name="trash"></ion-icon>
        </ion-fab-button>
      </ion-fab>
      <ion-item class="ion-text-center ion-margin-top ion-margin-bottom">
        <label class="verification-img">
          @if (accountService.profileVerificationData) {
            <img [src]="accountService.profileVerificationData['pvt_data']['photo_url']">
          }
        </label>
      </ion-item>
      <ion-item class="ion-text-center">
        <ion-label>
          <h2>{{ 'verification_9' | translate }}</h2>
        </ion-label>
      </ion-item>
      <ion-item class="ion-text-center">
        <ion-label>
          <h2>{{ 'verification_10' | translate }}</h2>
        </ion-label>
      </ion-item>
    </ion-list>
  }

  @if (accountService.profileVerificationData?.pv_status === 'unverified') {
    <ion-list lines="none">
      <ion-item class="ion-text-center">
        <ion-label>
          <h1>{{ 'verification_1' | translate }}</h1>
        </ion-label>
      </ion-item>
      <ion-item class="ion-text-center">
        <ion-label>
          <p>{{ 'verification_2' | translate }}</p>
        </ion-label>
      </ion-item>
      <ion-item class="ion-text-center ion-margin-top ion-margin-bottom">
        <label class="verification-img">
          <span>{{ 'verification_3' | translate }}</span>
          @if (accountService.profileVerificationData) {
            <img [src]="accountService.profileVerificationData['pvt_data']['images']['image'][0]">
          }
        </label>
      </ion-item>
      <ion-item class="ion-text-center ion-margin-top ion-margin-bottom">
        <label class="verification-img">
          <span>{{ 'verification_4' | translate }}</span>
          @if (accountService.profileVerificationData) {
            <img [src]="accountService.profileVerificationData['pvt_data']['images']['image2'][0]">
          }
        </label>
      </ion-item>
      <ion-item class="ion-text-center">
        <ion-col size="12" size-sm="5">
          <ion-button expand="block"
            class="ion-text-wrap ion-margin-bottom"
            (click)="uploadPhoto()"
          >{{ 'verification_5' | translate }}</ion-button>
        </ion-col>
      </ion-item>
      <ion-item class="ion-text-center">
        <p>{{ 'verification_6' | translate }}</p>
      </ion-item>
    </ion-list>
  }
</ion-content>
