import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { firstValueFrom } from "rxjs";
import { Utils } from "src/app/helpers/utils";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { MiscService } from "src/app/services/misc.service";
import { UtilsService } from "src/app/services/utils.service";
import { environment as Env } from "src/environments/environment";

@Component({
  selector: "app-error",
  templateUrl: "./error.page.html",
  styleUrls: ["./error.page.scss"],
  standalone: false,
})
export class ErrorPage implements OnInit {
  constructor(
    private router: Router,
    private utilsService: UtilsService,
    private miscService: MiscService,
    private dialog: DialogHelperService,
  ) {}

  public get isChecking() {
    return this.utilsService.isCheckingServerStatus();
  }

  public get isDebug() {
    return Env.IS_DEBUG;
  }

  public gotoDebugPage() {
    this.router.navigateByUrl("/debug");
  }

  private _timerLimit = 30;
  public cntd: number = this._timerLimit;

  /**
   * countdown
   */
  private tick() {
    if (!this.isChecking && Utils.isErrorUrl(this.router.url)) {
      this.cntd--;
      if (this.cntd <= 0) {
        this.cntd = this._timerLimit;
        this.retry(true);
      }
    }
  }

  public retry(isAutoCheck: boolean = false) {
    this.cntd = this._timerLimit;

    firstValueFrom(this.utilsService.checkServerStatus())
      .then(() => {
        this.dialog.showSplash();
        this.miscService.restartApplication();
      })
      .catch(() => {
        if (!isAutoCheck) {
          this.dialog.showToast("", "connection_error", "error");
        }
      });
  }

  private _interval = null;

  ionViewDidEnter() {
    this.dialog.hideSplash();
    this.cntd = this._timerLimit;

    if (!this._interval) {
      this._interval = setInterval(() => {
        this.tick();
      }, 1000);
    }
  }

  ngOnInit() {}
}
