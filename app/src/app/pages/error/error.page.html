<ion-content>
  <div style="margin-top: 20%; display: block; text-align: center">
    <lottie-player
      style="width: 80%; margin-left: 10%"
      autoplay
      loop
      src="/assets/json/no-connection.json"
    ></lottie-player>
    <!--
    <ion-icon name="alert" style="font-size: 100px; color: silver;" padding></ion-icon>
    -->
    <div style="margin: 10%">{{'connection_error' | translate}}</div>
    <br />
    <ion-button fill="clear" (click)="retry()" [disabled]="this.isChecking">
      <ion-icon name="refresh" padding-right></ion-icon> {{ 'retry' | translate
      }} ({{cntd}})
    </ion-button>
    @if (this.isDebug) {
      <div>
        <ion-button size="small" (click)="gotoDebugPage()" padding>
          DEBUG
        </ion-button>
      </div>
    }
  </div>
</ion-content>
