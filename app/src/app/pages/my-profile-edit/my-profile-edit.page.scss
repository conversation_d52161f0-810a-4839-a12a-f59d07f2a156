.list-md {
  padding: 0;
}

.my-photo {
  &-parent {
    background-color: var(--ion-color-light);
  }

  &-child {
    background-color: var(--ion-color-silver);
    border-radius: 6px;
    display: block;
    overflow: hidden;
    aspect-ratio: 1;
  }

  &-add,
  &-edit {
    background-color: var(--ion-color-primary);
    color: var(--ion-color-white);
    border-radius: 50%;
    padding: 4px;
    position: absolute;
    z-index: 2;
    right: 0;
    bottom: 0;
  }
}

.lock {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px;
  background: rgba(0,0,0,.5);
  border-radius: 5px;
}

.placeholder {
  border: 3px dashed var(--ion-color-light-shade);
}

ion-item-divider ion-label {
  padding: 28px 0 8px;
  margin: 0;
}

.main-label {
  color: var(--ion-color-charcoal);
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
}

.text-field {
  padding-bottom: 16px;
}

.profile-photos {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: flex-start;
  align-items: center;
  max-height:110vw;
  overflow: auto;
  margin-top:10px;
  &::-webkit-scrollbar {
    display: none;
  }

  .rejected-photo {
    position: relative;

    ion-img {
      opacity: 0.5;
    }

    .rejected-photos-handle {
      font-size: .8rem;
    }
  }
}

.question-name {
  font-size: 16px;
  font-weight: 600;
  transform: initial;

  &.spacing {
    padding-top: 16px;
  }
}

.description {
  font-size: 14px;
  line-height: 1.4;
}

.rejected-photos-handle {
  width: 100%;
  background-color: rgba(0, 0, 0, .65);
  color: #ccc;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  right: 50%;
  transform: translate(50%, -50%);
  padding: 5px 0;
  position: absolute;
  z-index: 9;

  ion-icon {
    margin-right: 3px;
  }
}


ion-accordion {
  ion-select {
    ion-text {
      font-size: 0.95rem;
      padding: 5px 0;
    }
  }

  & > ion-list:last-of-type {
    margin-bottom: 1.5rem;
  }
}

.my-photo-child {
  display: flex;
  object-fit: cover;
}

.my-photo-child.selected {
  border: 3px solid var(--ion-color-primary);
}

.cancel-photo-delete {
  position: absolute;
  left: -85px;
  top: 10px;
}

ion-range {

  ion-label {
    max-width: fit-content;
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
  overflow-y: hidden;
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.title-bar-holder {
  position: relative;
  display: block;
}

.title-bar {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  background-color: var(--ion-color-primary);
  opacity: 0.7;
  border-radius: 5px 5px 0 0;
  color: white;
  text-align: center;
  padding: 3px 3px; 
  font-size: 10px; 
  font-weight: bold;
}

