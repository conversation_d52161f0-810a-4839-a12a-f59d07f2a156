<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="updateAccount()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{ 'title_edit_profile' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

@if ((accountService.accountLoaded$ | async)) {
  <ion-content>
    <ion-item-group>
      <ion-fab vertical="top" horizontal="end" slot="fixed">
        <ion-fab-button (click)="photoUpload()" [class.hidden]="deleteImgsStatus">
          <ion-icon name="add"></ion-icon>
        </ion-fab-button>
        <ion-button
          expand="block"
          class="cancel-photo-delete"
          size="small"
          (click)="deselectImgs()"
          [class.hidden]="!deleteImgsStatus"
          >
          {{ 'cancel' | translate }}
        </ion-button>
        <ion-fab-button (click)="photoDelete()" [class.hidden]="!deleteImgsStatus">
          <ion-icon name="trash"></ion-icon>
        </ion-fab-button>
      </ion-fab>
      <ion-item-divider color="silver">
        <ion-label class="main-label"
          >{{ 'profile_photos' | translate }}</ion-label
          >
        </ion-item-divider>
        <ion-item lines="none" class="text-field ion-margin-top">
          <ion-row
            class="profile-photos"
            [class.hidden]="!accountService.account.photos.length"
            cdkDropList
            cdkDropListOrientation="mixed"
            class="example-list"
            (cdkDropListDropped)="drop($event)"
            >
            @for (photo of accountService.account.photos; track photo) {
              <ion-col
                size="4"
                size-md="2"
                cdkDrag
                [cdkDragDisabled]="sortPhotosStatus"
                [cdkDragStartDelay]="600"
                >
                <ion-icon
                  name="pencil"
                  class="my-photo-edit"
                  (click)="handleImgClick(photo)"
                ></ion-icon>
                @if (photo.approved && !photo.main_photo && photo.image_id && photo.access != 0) {
                  <ion-icon
                    name="lock-closed"
                    class="lock"
                    color="white"
                  ></ion-icon>
                }
                @if (photo.approved && !photo.main_photo && photo.image_id && photo.access == 0) {
                  <ion-icon
                    name="lock-open"
                    class="lock"
                    color="white"
                  ></ion-icon>
                }
                <span
                  (click)="handleImgClick(photo)" class="title-bar-holder"
                  >
                  @if (photo.main_photo) {
                    <span class="title-bar">Profile Photo</span>
                  }
                  <img
                    style="pointer-events: none"
                    src="{{ photo.thumb_url }}"
                    class="my-photo-child"
                    [class.selected]="photo.selected"
                    >
                </span>
              </ion-col>
            }
          </ion-row>
        </ion-item>
      </ion-item-group>
      @if (this.rejectedPhotos.length > 0) {
        <ion-item-group>
          <ion-item-divider color="silver">
            <ion-label class="main-label"
              >{{ 'rejected_photos' | translate }}</ion-label
              >
            </ion-item-divider>
            <ion-item lines="none" class="text-field">
              <ion-row
                class="profile-photos"
                >
                @for (photo of this.rejectedPhotos; track photo) {
                  <ion-col
                    (click)="toastRejected(photo.reason)"
                    size="4"
                    size-md="2"
                    >
                    <div class="rejected-photo">
                      <ion-img
                        style="pointer-events: none"
                        [src]="photo.thumb_url"
                        class="my-photo-child"
                      ></ion-img>
                      <div class="rejected-photos-handle"><ion-icon name="ban-outline"></ion-icon>{{ 'rejected'  | translate }}</div>
                    </div>
                  </ion-col>
                }
              </ion-row>
            </ion-item>
          </ion-item-group>
        }
        <ion-list>
          <ion-item-divider color="silver">
            <ion-label class="main-label">{{ 'first_name' | translate }}</ion-label>
          </ion-item-divider>
          <ion-item>
            <ion-input
              label="{{ 'my_first_name' | translate }}:"
              class="ion-text-right test-first-name"
              placeholder="{{ accountService.account.first_name }}"
              [(ngModel)]="accountService.account.first_name"
            ></ion-input>
          </ion-item>
          <ion-item-group>
            <ion-item-divider color="silver">
              <ion-label class="main-label"
                >{{ 'profile_headline' | translate }}</ion-label
                >
              </ion-item-divider>
              <ion-item lines="none" class="text-field">
                <ion-input
                  maxlength="70"
                  class="test-headline"
                  [(ngModel)]="accountService.account.title"
                  placeholder="{{ 'profile_headline' | translate }}"
                ></ion-input>
              </ion-item>
            </ion-item-group>
            <ion-item-group>
              <ion-item-divider color="silver">
                <ion-label class="main-label"
                  >{{ 'profile_description' | translate }}</ion-label
                  >
                </ion-item-divider>
                <ion-item lines="none" class="text-field">
                  <ion-textarea
                    [(ngModel)]="accountService.account.description"
                    placeholder="{{ 'profile_description_placeholder' | translate }}"
                  ></ion-textarea>
                </ion-item>
              </ion-item-group>
              <ion-item-group>
                <ion-item-divider color="silver">
                  <ion-label class="main-label"
                    >{{ 'my_location' | translate }}</ion-label
                    >
                    @if (isInProgress('account.geolocation') || isInProgress('account')) {
                      <ion-spinner
                        name="crescent"
                        slot="end"
                        class="ion-margin-end"
                      ></ion-spinner>
                    }
                  </ion-item-divider>
                  @if (accountService.account.city) {
                    <ion-item lines="none">
                      <ion-label>{{ 'current_location_country' | translate }}</ion-label>
                      <ion-label class="ion-text-end">{{ accountService.account.country }}</ion-label>
                    </ion-item>
                  }
                  @if (accountService.account.country) {
                    <ion-item lines="none">
                      <ion-label>{{ 'current_location_city' | translate }}</ion-label>
                      <ion-label class="ion-text-end">{{ accountService.account.city }}</ion-label>
                    </ion-item>
                  }
                  @if (accountService.account.zip) {
                    <ion-item lines="none">
                      <ion-label>{{ 'current_location_zip' | translate }}</ion-label>
                      <ion-label class="ion-text-end">{{ accountService.account.zip }}</ion-label>
                    </ion-item>
                  }
                  @if (!accountService.settings?.auto_update_location) {
                    <ion-grid>
                      <ion-row>
                        <ion-col>
                          <ion-button
                            class="btn-with-ico"
                            expand="block"
                            routerLink="/app/location-country"
                            >
                            {{ 'manual_set_location' | translate }}
                            <ion-icon name="locate-outline"></ion-icon>
                          </ion-button>
                        </ion-col>
                      </ion-row>
                    </ion-grid>
                  }
                </ion-item-group>
                <ion-item-group>
                  <ion-item-divider color="silver">
                    <ion-label class="main-label">{{ 'age_label' | translate }}</ion-label>
                  </ion-item-divider>
                  <ion-item lines="none">
                    <ion-label>{{ 'birthdate' | translate }}</ion-label>
                    <ion-datetime-button
                      slot="end"
                      presentation="date"
                      datetime="date"
                    ></ion-datetime-button>
                    <ion-modal [keepContentsMounted]="true">
                      <ng-template>
                        <ion-datetime
                          id="date"
                          presentation="date"
                          show-default-buttons="true"
                          [value]="accountService.account.birthdate"
                          (ionChange)="applyDateChange($event)"
                          displayFormat="MM DD YYYY"
                          pickerFormat="MM DD YYYY"
                          [min]="minDate"
                          [max]="maxDate"
                          size="cover"
                          prefer-wheel="”true”"
                        ></ion-datetime>
                      </ng-template>
                    </ion-modal>
                  </ion-item>
                </ion-item-group>
                <ion-item-group>
                  <ion-item-divider color="silver">
                    <ion-label class="main-label"
                      >{{ 'gender_label' | translate }}</ion-label
                      >
                    </ion-item-divider>
                    <ion-item lines="none">
                      <ion-select
                        label="{{ 'i_am' | translate }}"
                        [(ngModel)]="accountService.account.gender_id"
                        interface="popover"
                        value="{{ accountService.account?.gender_id }}"
                        >
                        @for (gender of genders; track gender) {
                          <ion-select-option value="{{ gender.id }}"
                            >{{ gender.name | translate }}</ion-select-option
                            >
                          }
                        </ion-select>
                      </ion-item>
                    </ion-item-group>
                  </ion-list>
                  @if (accountService.tags?.main_tag) {
                    <ion-item-group>
                      <ion-item-divider color="silver">
                        <ion-label class="main-label">{{ 'main_tag' | translate }}</ion-label>
                      </ion-item-divider>
                      <ion-item lines="none" class="text-field">
                        <ion-row class="tags">
                          <ion-button>{{ accountService.tags.main_tag.name }}</ion-button>
                        </ion-row>
                      </ion-item>
                    </ion-item-group>
                  }
                  @if (accountService.tags?.tags.length) {
                    <ion-item-group>
                      <ion-item-divider color="silver">
                        <ion-label class="main-label">{{ 'secondary_tags' | translate }}</ion-label>
                      </ion-item-divider>
                      <ion-item lines="none" class="text-field">
                        <ion-row class="tags">
                          @for (tag of accountService.tags.tags; track tag) {
                            <ion-button class="tag-{{ tag.tag_id }}"
                              (click)="handleTagClick(tag, 'disable')"
                            >{{ tag.title }}</ion-button>
                          }
                        </ion-row>
                      </ion-item>
                    </ion-item-group>
                  }
                  @if (accountService.tags?.disabled_tags.length) {
                    <ion-item-group>
                      <ion-item-divider color="silver">
                        <ion-label class="main-label">{{ 'deleted_tags' | translate }}</ion-label>
                      </ion-item-divider>
                      <ion-item lines="none" class="text-field">
                        <ion-row class="tags">
                          @for (tag of accountService.tags.disabled_tags; track tag) {
                            <ion-button class="tag-{{ tag.tag_id }}"
                              (click)="handleTagClick(tag, 'enable')"
                            >{{ tag.title }}</ion-button>
                          }
                        </ion-row>
                      </ion-item>
                    </ion-item-group>
                  }
                  <ion-accordion-group (ionChange)="dfChanged($event)" #accordionGroup>
                    @for (details of accountService.detailsForm; track details) {
                      <ion-accordion
                        value="df_group_id_{{ details.group_id }}"
                        >
                        <ion-item
                          color="silver"
                          slot="header"
                          (click)="headerClicked($event, true)"
                          >
                          <ion-label class="main-label">{{ details.group_name}}</ion-label>
                        </ion-item>
                        <ion-list slot="content" class="ion-padding-bottom">
                          <ion-item lines="none">
                            <div color="medium" class="ion-text-wrap description" [innerHTML]="details.group_text"></div>
                          </ion-item>
                        </ion-list>
                        @if (details.group_id === 'interests') {
                          <ion-list
                            slot="content"
                            class="ion-padding-bottom"
                            >
                            @if (this.isQuestionGroup(details)) {
                            @for (question of (details?.questions ?? []); track question) {
                              <div>
                                <!-- CHECK - checkbox -->
                                <ion-item>
                                  <ion-label class="question-name"
                                    >{{ question.page_title }}</ion-label
                                    >
                                  </ion-item>
                                  <ion-list>
                                    @for (answer of question.answers; track answer) {
                                      <ion-item
                                        (click)="updateInterests(answer)"
                                        >
                                        <ion-checkbox [(ngModel)]="answer.checked"
                                          >{{ answer.data }}</ion-checkbox
                                          >
                                        </ion-item>
                                      }
                                    </ion-list>
                                  </div>
                                }
                              } 
                              </ion-list>
                            }
                            @if (this.isQuestionGroup(details)) {
                              @for (question of (details?.questions ?? []); track question) {
                                <ion-list
                                  slot="content"
                                  class="ion-padding-bottom"
                                  >
                                  <!-- SELECT -->
                                  @if ((question.type === 'select') || (question.type === 'check' && question.multiselect == 0)) {
                                    <ion-item lines="none">
                                      <ion-select
                                        interface="action-sheet"
                                        cancelText="{{'cancel' | translate}}"
                                        [(ngModel)]="question['selectedAnswers']"
                                        >
                                        <ion-text class="ion-text-wrap" slot="label"
                                          >{{ question.question_name }}</ion-text
                                          >
                                          @for (answer of question.answers; track answer) {
                                            <ion-select-option
                                              [value]="answer"
                                              >{{ answer.data }}</ion-select-option
                                              >
                                            }
                                          </ion-select>
                                        </ion-item>
                                      }
                                      <!-- CHECK - checkbox -->
                                      @if (question.answers && question.type === 'check' && question.multiselect == 1) {
                                        <ion-list
                                          >
                                          <ion-item>
                                            <ion-label class="question-name"
                                              >{{ question.question_name }}</ion-label
                                              >
                                            </ion-item>
                                            @for (answer of question.answers; track answer) {
                                              <ion-item>
                                                <ion-checkbox [(ngModel)]="answer.checked"
                                                  ><span class="ion-text-wrap">{{ answer.data }}</span></ion-checkbox>
                                                </ion-item>
                                              }
                                            </ion-list>
                                          }
                                          <!-- CHAR -->
                                          @if (!question.answers && question.type === 'char') {
                                            <ion-list>
                                              <ion-item lines="inset">
                                                <ion-label position="stacked" class="question-name text spacing"
                                                  >{{ question.question_name }}</ion-label
                                                  >
                                                  <ion-input
                                                    aria-label=""
                                                    [value]="question['answer']"
                                                    placeholder="{{ 'type_here' | translate }}"
                                                    [max]="question.value_max"
                                                    [min]="question.value_min"
                                                    [(ngModel)]="question['answer']"
                                                  ></ion-input>
                                                </ion-item>
                                              </ion-list>
                                            }
                                            <!-- TEXT -->
                                            @if (!question.answers && question.type === 'text') {
                                              <ion-list>
                                                <ion-item>
                                                  <ion-label
                                                    position="stacked"
                                                    class="question-name text spacing ion-text-wrap"
                                                    >{{ question.question_name }}</ion-label
                                                    >
                                                    <ion-textarea
                                                      aria-label=""
                                                      [value]="question['answer']"
                                                      placeholder="{{ 'type_here' | translate }}"
                                                      [maxlength]="question.value_max > 0 ? question.value_max : 1000"
                                                      [minlength]="question.value_min"
                                                      rows="3"
                                                      [(ngModel)]="question['answer']"
                                                    ></ion-textarea>
                                                  </ion-item>
                                                </ion-list>
                                              }
                                              <!-- SLIDER -->
                                              @if (question.answers && question.type === 'slider' && question.multiselect === '0') {
                                                <ion-list
                                                  >
                                                  <ion-item lines="none">
                                                    <ion-label class="question-name text"
                                                      >{{ question.question_name }}</ion-label
                                                      >
                                                    </ion-item>
                                                    <ion-item lines="none">
                                                      <ion-label slot="start"><small>{{ question.rangeValueMin }}</small></ion-label>
                                                      @if (question) {
                                                        <ion-range
                                                          label=""
                                                          step="{{ question.range_step }}"
                                                          [snaps]="!!question.answers.length"
                                                          [min]="question.range_min"
                                                          [max]="question.range_max"
                                                          [value]="question.range_step_value"
                                                          (ionInput)="updateRangeData($event, question)"
                                                        ></ion-range>
                                                      }
                                                    </ion-item>
                                                  </ion-list>
                                                }
                                                <!-- SLIDER - multiselect -->
                                                @if (question && question.type === 'slider' && question.multiselect === '1') {
                                                  <ion-list
                                                    >
                                                    <ion-item lines="none">
                                                      <ion-label class="question-name text">{{ question.question_name }}</ion-label>
                                                    </ion-item>
                                                    <ion-item lines="none">
                                                      @if (question) {
                                                        <ion-range
                                                          label=""
                                                          [dualKnobs]="true"
                                                          [min]="18"
                                                          [max]="99"
                                                          [value]="{ lower: question.range_value_min, upper: question.range_value_max }"
                                                          (ionInput)="updateRangeData($event, question)"
                                                          >
                                                          <ion-label slot="start">{{ question.rangeValueMin }}</ion-label>
                                                          <ion-label slot="end">{{ question.rangeValueMax }}</ion-label>
                                                        </ion-range>
                                                      }
                                                    </ion-item>
                                                  </ion-list>
                                                }
                                              </ion-list>
                                            }
                                          }
                                        </ion-accordion>
                                      }
                                    </ion-accordion-group>
                                  </ion-content>
                                }
