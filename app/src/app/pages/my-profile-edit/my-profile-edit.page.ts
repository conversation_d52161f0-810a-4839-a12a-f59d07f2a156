import { Component, OnInit } from "@angular/core";
import { AccountService } from "../../services/account.service";
import { Debug } from "src/app/helpers/debug";
import { ApiCommands } from "src/app/services/api/api.commands";
import { ApiParams } from "src/app/services/api/api.params";
import { ApiResponses } from "src/app/services/api/api.responses";
import { ApiService } from "src/app/services/api/api.service";
import { MiscService } from "../../services/misc.service";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { LocationService } from "../../services/location.service";
import { Router } from "@angular/router";
import { StateService } from "../../services/state.service";
import { StorageService } from "../../services/storage.service";
import {
  <PERSON><PERSON><PERSON><PERSON>roller,
  Nav<PERSON><PERSON>roller,
  ActionSheetController,
} from "@ionic/angular";
import _, { isArray } from "lodash";
import { TranslateService } from "@ngx-translate/core";
import { PictureUploadHelperService } from "../../picture-upload-helper.service";
import { GeolocationService } from "../../services/geolocation.service";
import { PictureService } from "../../services/picture.service";
import { format, parseISO } from "date-fns";
import moment from "moment";
import { environment as Env } from "src/environments/environment";
import { MenuHelperService } from "src/app/services/menu-helper.service";
import { Subscription, firstValueFrom } from "rxjs";
import { GalleryImage } from "../../schemas/gallery-image";
import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";

@Component({
  selector: "app-my-profile-edit",
  templateUrl: "./my-profile-edit.page.html",
  styleUrls: ["./my-profile-edit.page.scss"],
  standalone: false,
})
export class MyProfileEditPage implements OnInit {
  public deleteImgsStatus: boolean = false;
  public rejectedPhotos = [];
  public sortPhotosStatus: boolean = false;

  public selectedImgs: any[] = [];

  /**
   * Date-time.
   */
  public minDate: string;
  public maxDate: string;

  /**
   * Genders list.
   */
  public genders: any = Env.APP_GENDERS;

  /**
   * When interest header is clicked, we want to scroll the options into view
   * @param event
   * @param doShow
   */
  public headerClicked(event, doShow) {
    if (doShow) {
      setTimeout(() => {
        event.target.scrollIntoView({ block: "start", behavior: "smooth" });
      }, 100);
    }
  }

  private accordionsActive: boolean = false;
  private activeDetailsFormGroup: string = null;
  private updatedDetailsFormGroups: any[] = [];

  private backupDetailsForm: string = null;

  /**
   * Bind account auto_update_location.
   */
  public locationStatus: boolean;

  constructor(
    public accountService: AccountService,
    public dbg: Debug,
    public misc: MiscService,
    public dlg: DialogHelperService,
    public api: ApiService,
    public router: Router,
    public stateService: StateService,
    public storageService: StorageService,
    public navCtrl: NavController,
    public pictureService: PictureService,
    public actionSheetCtrl: ActionSheetController,
    public translate: TranslateService,
    public pictureUploadHelper: PictureUploadHelperService,
    public locationService: LocationService,
    public geolocationService: GeolocationService,
    public menuHelper: MenuHelperService,
    public toastController: ToastController,
  ) {}

  private _backUrl: string = "";

  private subAccountLoaded: Subscription = new Subscription();
  private subAccountChanges: Subscription = new Subscription();

  async ngOnInit() {
    // save the url the user came from for back navigation
    this._backUrl = this.menuHelper.currentURL;
    this.accountService.refreshData();
    // gender_id is string, but gender enum object is number, so the binding will not work out of the box
    // we're fixing it by
    this.accountService.account.gender_id = parseInt(
      "" + this.accountService.account?.gender_id,
    );

    this.minDate = moment().subtract(99, "years").format("yyyy-MM-DD");
    this.maxDate = moment()
      .subtract(Env.APP_VALID_AGE, "years")
      .format("yyyy-MM-DD");

    this.accordionsActive = false;

    this.subAccountLoaded = this.accountService.accountLoaded$.subscribe({
      next: (data) => {
        if (!data) return;
        firstValueFrom(this.accountService.getTags());

        this.locationStatus = this.accountService.settings.auto_update_location;

        this.subAccountChanges = this.accountService.accountChanges$.subscribe({
          next: (data) => {
            if (!this.backupDetailsForm) {
              this.backupDetailsForm = JSON.stringify(
                this.getDetailsFormParams(),
              );
            }

            // Set auto_update_location to false - turn off autolocation update.
            if (this.accountService.settings.auto_update_location) {
              this.accountService.settings.auto_update_location = false;
              this.accountService.saveSettings();
            }

            let params = {
              get_countries: 1,
              country_code: this.accountService.account.country,
              city: this.accountService.account.city,
            };

            this.locationService
              .locationHelper(params)
              .catch((error) => this.dbg.le("location helper error", error));
          },
        });
      },
    });

    // if back button pressed on profile edit - save stuff

    if (!this.stateService.get("isEditProfileSubscribedToBack", "", false)) {
      // make sure that we only subscribe once
      this.stateService.set("isEditProfileSubscribedToBack", "", true);

      this.menuHelper.BackEventStream$.subscribe({
        next: (data) => {
          //back button pressed
          if (this.misc.checkUrl("app/my-profile-edit")) {
            // if we're on my-profile-edit, execute update
            this.updateAccount();
          }
        },
      });
    }
    this.accountService.getRejectedPhotos();
    this.rejectedPhotos = this.accountService.rejectedPhotos;
  }

  async toastRejected(msg: string) {
    if (!msg) {
      return;
    }
    const toast = await this.toastController.create({
      message: msg,
      duration: 2000,
      position: "middle",
    });
    toast.present();
  }

  /**
   * Handle image click function
   *
   * @param {Object} img Current clicked image data
   */
  public async handleImgClick(img) {
    if (this.deleteImgsStatus) return this.selectImg(img);

    const actionSheet = await this.actionSheetCtrl.create({
      header: this.translate.instant("profile_edit_image_title"),
      buttons: this.buttonsConfig(img),
    });

    await actionSheet.present();

    const { role, data } = await actionSheet.onDidDismiss();
  }

  /**
   * Set buttons config.
   *
   * @param img
   * @returns {{text: string; role: string; handler: (() => any)}[]}
   */
  private buttonsConfig(img) {
    let config = [
      {
        text: this.translate.instant("edit_photo"),
        handler: () => {
          this.stateService.set(
            "photo",
            this.accountService.account.username,
            img,
          );
          this.router.navigate(["/app/photo-editor"]);
        },
      },
      {
        text: this.translate.instant("select_photo"),
        handler: () => this.selectImg(img),
      },
      {
        text: this.translate.instant("cancel_action"),
        role: "cancel",
        handler: () => {},
      },
    ];

    if (!img.main_photo) {
      config.unshift({
        text: this.translate.instant("delete_action"),
        role: "destructive",
        handler: () => {
          this.deleteImg(img);
        },
      });
    }

    if (!img.main_photo && !img.access && img.approved) {
      config.unshift({
        text: this.translate.instant("set_avatar_action"),
        role: "",
        handler: () => {
          this.setAvatarImg(img.image_id, img.gallery_id);
        },
      });
    }

    let setPhotoAccess = {
      text: this.translate.instant(
        img.access != 0 ? "set_as_public" : "set_as_private",
      ),
      handler: () => this.setPhotoAccess(img),
    };

    if (!img.main_photo && img.approved) {
      config.splice(1, 0, setPhotoAccess);
    }

    return config;
  }
  public isQuestionGroup(details: any): details is { questions: any[] } {
    return Array.isArray(details?.questions);
  }

  public selectImg(img): void {
    img.selected = !img.selected;

    if (img.selected) {
      this.selectedImgs.push(img);
    } else {
      this.selectedImgs = this.selectedImgs.filter(
        (val) => img.image_id !== val.image_id,
      );
    }

    this.deleteImgsStatus = !!this.selectedImgs.length;
    this.sortPhotosStatus = !!this.selectedImgs.length;
  }

  public deselectImgs() {
    this.selectedImgs.forEach((img) => this.selectImg(img));
  }

  public async photoDelete(): Promise<void> {
    const actionSheet = await this.actionSheetCtrl.create({
      buttons: [
        {
          text: this.translate.instant("ok"),
          role: "destructive",
          handler: () => {
            this.deleteImgs(this.selectedImgs);
          },
        },
        {
          text: this.translate.instant("cancel_action"),
          role: "cancel",
          handler: () => {},
        },
      ],
    });

    await actionSheet.present();

    const { role, data } = await actionSheet.onDidDismiss();
  }

  protected deleteImgs(imgs) {
    //can't delete avatar
    imgs = imgs.filter(function (item) {
      return item.main_photo == false;
    });
    if (imgs.length == 0) {
      this.dlg.showToast(
        "Error",
        this.translate.instant("delete_avatar_error"),
        "error",
      );
      this.deselectImgs();
      return;
    }

    let params = imgs.map((val) => {
      return { image_id: val.image_id, gallery_id: val.gallery_id };
    });
    this.dlg.showLoading();
    this.pictureService
      .delete(params)
      .toPromise()
      .then((res) => {
        if (!res.data) {
          this.dlg.showToast("", "error_title");
          return;
        }

        if (res.data.length < params.length) {
          this.dlg.showToast("", "photos_delete_error");
        }

        this.accountService.account.photos =
          this.accountService.account.photos.filter((img) => {
            const _val = JSON.stringify({
              image_id: img.image_id,
              gallery_id: img.gallery_id,
            });
            const _data = JSON.stringify(res.data);
            return !_data.includes(_val);
          });
      })
      .catch((err) => {
        this.dlg.showToastBaseOnErrorResponse(err);
      })
      .then(() => {
        this.deselectImgs();
        this.deleteImgsStatus = false;

        this.dlg.hideLoading();
      });
  }

  /**
   * Delete image function
   *
   * @param param {GalleryImage}
   */
  protected deleteImg(param: GalleryImage.type) {
    //can't delete main photo
    if (param.main_photo == true) {
      this.dlg.showToast(
        "Error",
        this.translate.instant("delete_avatar_error"),
        "error",
      );
      return;
    }

    this.dlg.showLoading();
    firstValueFrom(
      this.pictureService.delete([
        {
          image_id: param.image_id,
          gallery_id: param.gallery_id,
        },
      ]),
    )
      .then(async () => {
        this.accountService.account.photos =
          this.accountService.account.photos.filter((img) => {
            return img.image_id != param.image_id;
          });

        // main photo deleted - get acc data.
        let mpd: boolean = !_.some(
          this.accountService.account.photos,
          (img) => img.main_photo,
        );
        if (mpd) {
          if (this.accountService.account.photos.length) {
            this.accountService.account.avatar_url =
              this.accountService.account.photos[0].thumb_url;
          } else {
            this.accountService.account.avatar_url = "/assets/img/avatar.svg";
          }
        }

        this.dlg.hideLoading();
        this.dlg.showToast("", "deleted");
      })
      .catch(({ errors }) => {
        this.dlg.hideLoading();
        this.dlg.showToast("", errors, "error");
      });
  }

  /**
   * Upload photo.
   */
  public photoUpload() {
    this.stateService.set("blockStatusChecker", "", true);

    this.pictureUploadHelper.displayUploadMenu((data) => {
      this.dlg.showLoading();
      firstValueFrom(
        this.pictureService.upload({
          gallery_id: 0,
          image_url: "",
          image_data: data,
        }),
      )
        .then(async ({ data }) => {
          this.accountService.account.photos.push(data.photo);

          // added first photo - set as avatar.
          if (this.accountService.account.photos.length === 1) {
            this.accountService.account.avatar_url = data.photo.thumb_url;
            this.accountService.account.photos[0].main_photo = true;
          }
        })
        .catch((err) => {
          this.dlg.showToastBaseOnErrorResponse(err);
        })
        .then(() => {
          this.stateService.delete("blockStatusChecker", "");
          this.dlg.hideLoading();
        });
    });
  }

  /**
   * Set avatar image function
   *
   * @param {number} imageId
   * @param {number} galleryId
   */
  private setAvatarImg(imageId: number, galleryId: number) {
    this.dlg.showLoading();
    firstValueFrom(
      this.pictureService.setAvatar({
        image_id: imageId,
        gallery_id: galleryId,
      }),
    )
      .then(() => {
        this.dlg.showToast("", "avatar_changed");
      })
      .catch(({ errors }) => this.dlg.showToast("", errors, "error"))
      .then(() => this.dlg.hideLoading());
  }

  /**
   * Set photo access.
   *
   * @param img
   */
  private setPhotoAccess(img) {
    let params: ApiParams.PictureSetAccessParams.type = {
      gallery_id: img.gallery_id,
      image_id: img.image_id,
      access: img.access != 0 ? 0 : 2,
    };

    firstValueFrom(this.pictureService.setAccess(params))
      .then(() => {
        let message: string =
          params.access != 0 ? "private_photo_message" : "public_photo_message";

        this.dlg.showToast("", message);
        img.access = params.access;
      })
      .catch((err) => this.dbg.le("error set access to the photo", err));
  }

  ionViewWillLeave() {
    // enable when exiting
    this.subAccountLoaded.unsubscribe();
    this.subAccountChanges.unsubscribe();
  }

  /**
   * Is command in progress?
   *
   * @param cmd string
   */
  public isInProgress(cmd: string) {
    return !!this.api.isInEventList({ command: cmd });
  }

  /**
   * Format date-time.
   *
   * @returns {string}
   */
  public formatDate(value: string | string[]): string {
    let mod_value = <string>(isArray(value) ? value[0] : value);
    return format(parseISO(mod_value), "yyyy-MM-dd");
  }

  /**
   * Date change binding
   */
  public applyDateChange(e) {
    this.accountService.account.birthdate = e.detail?.value
      ? this.formatDate(e.detail.value)
      : this.accountService.account.birthdate;
  }

  /**
   * Update account data.
   */
  public async updateAccount(): Promise<void> {
    let params = this.getDetailsFormParams();

    if (JSON.stringify(params) === this.backupDetailsForm) {
      return this.navBack();
    }

    await this.dlg.showLoading("saving");
    this.accountService
      .update(params)
      .then(async (res) => {
        await this.dlg.hideLoading();
        this.accountService.account = res.data;
        await this.navBack();
        this.dlg.showToast("success", "profile_updated", "success", 2000);
        this.accountService.backupDetailsForm();
        this.backupDetailsForm = null;
      })
      .catch(async (error) => {
        await this.dlg.hideLoading();
        let error_text = this.misc.getErrorText(error);
        this.dbg.le("my profile update error", error);
        await this.showConfirmDialog(error_text);
      });
  }

  /**
   * Update range data.
   *
   * @param e
   * @param question
   */
  public updateRangeData(e, question) {
    if (question.multiselect === "1") {
      if (e.detail.value.lower === undefined) {
        question["rangeValueMin"] = question.answers[e.detail.value].data;
        question.answers[e.detail.value]["checked"] = true;
        return;
      }

      if (question.answers.length) {
        let multipleAnswers: any[] = [];
        question.answers.forEach((answer) => (answer.checked = false));
        question["rangeValueMin"] = question.answers[e.detail.value.lower].data;
        question.answers[e.detail.value.lower]["checked"] = true;
        question["rangeValueMax"] = question.answers[e.detail.value.upper].data;
        question.answers[e.detail.value.upper]["checked"] = true;
      } else {
        question["rangeValueMin"] = e.detail.value.lower;
        question["rangeValueMax"] = e.detail.value.upper;
        question.answer = [e.detail.value.lower, e.detail.value.upper];
      }
    }

    if (question.multiselect === "0") {
      question["rangeValueMin"] = question.answers[e.detail.value].data;
      question.answers.forEach((answer, i) => {
        answer.checked = e.detail.value === i;
        if (answer.checked) question.answer = [answer.key];
      });
    }
  }

  /**
   * Update details-form.
   */
  public updateDF() {
    let params = {
      form: [],
      interests: null,
    };

    this.accountService.detailsForm.forEach((group) => {
      if (group.group_id === "interests" || group.group_id === "description")
        return;

      // Fix df question type - bug on dating side.
      group.questions.forEach((x, i) => {
        if (x.multiselect === "0" && x.type === "check") x.type = "select";
      });

      if (!this.updatedDetailsFormGroups.includes(group.group_id)) return;
      let data: any = { id: group.group_id };

      group.questions.forEach(async (question) => {
        // Type: text char slider select check

        if (question.multiselect === "1") {
          let multiselectAnswers: any;

          if (question.type === "slider") multiselectAnswers = "";
          if (question.type === "select") multiselectAnswers = "";
          if (question.type === "check") multiselectAnswers = [];

          if (question.answers.length) {
            let sliderMinMaxValues: any[] = [];
            question.answers.forEach((answer, i) => {
              if (answer.checked) {
                if (question.type === "slider" || question.type === "select") {
                  sliderMinMaxValues.push(i);
                  multiselectAnswers += answer.key + ",";
                }

                if (question.type === "check") {
                  multiselectAnswers.push(answer.key);
                }
              }
            });

            if (question.type === "slider" && sliderMinMaxValues.length === 1) {
              sliderMinMaxValues.push(sliderMinMaxValues[0]);
              multiselectAnswers += multiselectAnswers;
            }
            question["range_value_min"] = sliderMinMaxValues[0];
            question["range_value_max"] = sliderMinMaxValues[1];
          } else {
            if (question.answer.length) {
              question["range_value_min"] = question.answer[0];
              question["range_value_max"] = question.answer[1];
              multiselectAnswers = question.answer.join(",");
            }
          }

          if (typeof multiselectAnswers === "string") {
            multiselectAnswers = multiselectAnswers.replace(/,\s*$/, "");
          }

          Object.assign(data, { [question.key]: multiselectAnswers });
        }

        if (question.multiselect === "0") {
          if (question.type === "select") {
            Object.assign(data, {
              [question.key]: question["selectedAnswers"].key,
            });
          }

          if (question.type === "text" || question.type === "char") {
            if (question.answer) {
              Object.assign(data, { [question.key]: question.answer });
            }
          }

          if (question.type === "slider") {
            if (question.answer) {
              Object.assign(data, {
                [question.key]: question.answer.join(",").replace(/,\s*$/, ""),
              });
            }
          }
        }
      });

      params.form.push(data);
    });

    if (this.updatedDetailsFormGroups.includes("interests")) {
      params.interests = {
        interests: this.accountService.interests.data,
      };
    }

    return params;

    // this.accountService.update(<ApiParams.AccountUpdate.type>params);
    /*
    PARAMS:
      details: {form: [,…], interests: {interests: ["1", "4", "7"]}}
        form: [
          1: {
            id: '2',
            q_11: 0,
            q_12: 0,
            q_13: 0,
            q_14: 0,
            q_15: 0,
            q_16: 0,
            q_53: 0,
            q_17: [163, 154, 155],
            q_66: '1022,1025'    // slider
          }
          2: {
            id: "6"
            q_42: 418,
            q_43: 0,
            q_44: 0,
            q_45: "test test",
          }
        ]
        interests: {
          interests: ["1", "4", "7"]
        }
     */
  }

  /**
   * Update interests.
   */
  public updateInterests(answer): void {
    if (!answer.checked) {
      this.accountService.interests.data =
        this.accountService.interests.data.filter((val) => val !== answer.key);
      return;
    }

    if (this.accountService.interests.data.includes(answer.key)) return;

    this.accountService.interests.data.push(answer.key);
  }

  /**
   * Track changes.
   *
   * @param e
   */
  public dfChanged(e) {
    if (e.detail.value === undefined) {
      // undefined string object
      this.activeDetailsFormGroup = null;
      return;
    }

    if (typeof e.detail.value === "string") {
      if (e.detail.value.includes("df_group_id")) {
        this.accordionsActive = true;
        this.activeDetailsFormGroup = e.detail.value;
        return;
      }
    }

    if (!this.accordionsActive) return;

    let id = this.activeDetailsFormGroup.replace("df_group_id_", "");
    if (this.updatedDetailsFormGroups.includes(id)) return;
    this.updatedDetailsFormGroups.push(id);
  }

  /**
   * Display confirm dialog.
   *
   * @returns {Promise<any>}
   */
  private showConfirmDialog(err): Promise<any> {
    if (!err) err = "error_saving_profile_data";

    return this.dlg.advanceConfirm(
      err,
      () => {
        // Try Again - you try to save the data again.
        this.dlg.hideLoading();
        setTimeout(() => {
          this.updateAccount();
        }, 500);
      },
      () => {
        // Discard - you go back, and discard the changes.
        this.dlg.hideLoading();
        this.navBack();
        this.restoreDefailtAccountUpdateParams();
        this.accountService.restoreBckDetailsForm();
      },
      () => {
        // Cancel - you just close this dialog and stay on the edit page.
        this.dlg.hideLoading();
      },
      "try_again",
      "cancel",
      "discard",
    );
  }

  /**
   * DF params.
   *
   * @returns <ApiParams.AccoutUpdate.type|any>
   */
  private getDetailsFormParams(): ApiParams.AccountUpdate.type | any {
    return {
      first_name: this.accountService.account.first_name,
      description: this.accountService.account.description,
      title: this.accountService.account.title,
      birthdate: this.accountService.account.birthdate,
      country: this.accountService.account.country,
      city: this.accountService.account.city,
      zip: this.accountService.account.zip,
      gender_id: this.accountService.account.gender_id,
      details: this.updateDF(),
    };
  }

  /**
   * Restore account update params.
   */
  private restoreDefailtAccountUpdateParams(): void {
    let params = JSON.parse(this.backupDetailsForm);
    this.accountService.account.first_name = params.first_name;
    this.accountService.account.description = params.description;
    this.accountService.account.title = params.title;
    this.accountService.account.birthdate = params.birthdate;
    this.accountService.account.country = params.country;
    this.accountService.account.city = params.city;
    this.accountService.account.zip = params.zip;
    this.accountService.account.gender_id = params.gender_id;
  }

  /**
   * Nav back.
   */
  private navBack(): Promise<any> {
    return this.navCtrl.navigateBack(this._backUrl);
  }

  /**
   * Toggle auto update location.
   * Get new location.
   */
  // public toggleAutoLocation(e): void {
  //   this.accountService.settings.auto_update_location = e.detail.checked;
  //   this.accountService.saveSettings();

  //   if (!this.accountService.settings.auto_update_location) return;

  //   this.geolocationService
  //     .updateLocation()
  //     .then((data) => {
  //       if (!data) {
  //         this.accountService.settings.auto_update_location = false;
  //         this.dlg.showToast("", "get_location_error");
  //         return;
  //       }
  //     })
  //     .catch((e) => {
  //       this.accountService.settings.auto_update_location = false;
  //       this.dbg.le("auto update location error", e);
  //       this.dlg.showToast("", "get_location_error");
  //     });
  // }

  public drop(event: CdkDragDrop<string[]>): void {
    moveItemInArray(
      this.accountService.account.photos,
      event.previousIndex,
      event.currentIndex,
    );
    this.indexPhotos();
  }

  private indexPhotos(): void {
    this.accountService.account.photos.forEach((photo, i) => {
      photo["index"] = i;
    });
    this.updateOrder();
  }

  private updateOrder(): Subscription {
    let order = [];
    this.accountService.account.photos.forEach((photo, i) => {
      order.push(parseInt(photo["image_id"].toString()));
    });

    return this.api
      .call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.PhotosOrderSet.name,
        method: "post",
        useToken: true,
        params: <ApiParams.PhotoOrder.type>{
          order: "[" + order.join(",") + "]",
        },
      })
      .subscribe();
  }

  /**
   * Disable tag.
   */
  public disableTag(tag): void {
    this.accountService
      .disableTag(tag.tag_id)
      .then((res) => this.moveTag(tag, "enabled"))
      .catch((e) => this.dbg.le("tag disable error", e));
  }

  /**
   * Enable tag.
   */
  public enableTag(tag): void {
    this.accountService
      .enableTag(tag.tag_id)
      .then((res) => this.moveTag(tag, "disabled"))
      .catch((e) => this.dbg.le("tag enable error", e));
  }

  /**
   * Move tag.
   */
  private moveTag(tag, action: "enabled" | "disabled") {
    if (action === "disabled") {
      // Move tag from disabled to enable array.
      let index = this.accountService.tags.disabled_tags.indexOf(tag);
      let data = this.accountService.tags.disabled_tags.splice(index, 1);
      this.accountService.tags.tags.push(tag);
      // Sort tags.
      return this.accountService.tags.tags.sort((a, b) =>
        a.title.localeCompare(b.title),
      );
    }

    // Move tag from enabled to disabled array.
    let index = this.accountService.tags.tags.indexOf(tag);
    let data = this.accountService.tags.tags.splice(index, 1);
    this.accountService.tags.disabled_tags.push(tag);
    // Sort tags.
    this.accountService.tags.disabled_tags.sort((a, b) =>
      a.title.localeCompare(b.title),
    );
  }

  /**
   * Handle tag click.
   */
  public async handleTagClick(tag, action: "disable" | "enable") {
    const actionSheet = await this.actionSheetCtrl.create({
      header: this.translate.instant("tag_action"),
      buttons: this.tagButtonsConfig(tag, action),
    });

    await actionSheet.present();

    const { role, data } = await actionSheet.onDidDismiss();
  }

  /**
   * Tag button config.
   */
  private tagButtonsConfig(tag, action: "disable" | "enable") {
    let config = [
      {
        text: this.translate.instant(action + "_tag_action"),
        role: "destructive",
        handler: () =>
          action === "disable" ? this.disableTag(tag) : this.enableTag(tag),
      },
      {
        text: this.translate.instant("cancel_action"),
        role: "cancel",
        handler: () => {},
      },
    ];

    return config;
  }
}
