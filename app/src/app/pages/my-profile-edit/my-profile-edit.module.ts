import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { MyProfileEditPageRoutingModule } from "./my-profile-edit-routing.module";

import { MyProfileEditPage } from "./my-profile-edit.page";

import { TranslateModule } from "@ngx-translate/core";
import LongPressModule from "src/app/directives/long-press.directive.module";
import { DragDropModule } from "@angular/cdk/drag-drop";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    MyProfileEditPageRoutingModule,
    LongPressModule,
    DragDropModule,
  ],
  declarations: [MyProfileEditPage],
})
export class MyProfileEditPageModule {}
