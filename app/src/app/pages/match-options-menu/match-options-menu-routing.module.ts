import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { MatchOptionsMenuPage } from "./match-options-menu.page";

const routes: Routes = [
  {
    path: "",
    component: MatchOptionsMenuPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MatchOptionsMenuPageRoutingModule {}
