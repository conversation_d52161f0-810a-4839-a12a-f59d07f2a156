import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { MatchOptionsMenuPageRoutingModule } from "./match-options-menu-routing.module";

import { MatchOptionsMenuPage } from "./match-options-menu.page";

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    MatchOptionsMenuPageRoutingModule,
  ],
  declarations: [MatchOptionsMenuPage],
})
export class MatchOptionsMenuPageModule {}
