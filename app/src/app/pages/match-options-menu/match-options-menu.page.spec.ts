import { ComponentFixture, TestBed, waitForAsync } from "@angular/core/testing";
import { IonicModule } from "@ionic/angular";

import { MatchOptionsMenuPage } from "./match-options-menu.page";

describe("MatchOptionsMenuPage", () => {
  let component: MatchOptionsMenuPage;
  let fixture: ComponentFixture<MatchOptionsMenuPage>;

  beforeEach(
    waitForAsync(() => {
      TestBed.configureTestingModule({
        declarations: [MatchOptionsMenuPage],
        imports: [IonicModule.forRoot()],
      }).compileComponents();

      fixture = TestBed.createComponent(MatchOptionsMenuPage);
      component = fixture.componentInstance;
      fixture.detectChanges();
    })
  );

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
