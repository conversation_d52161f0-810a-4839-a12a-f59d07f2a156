<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="updateMatchPref()">
        <ion-icon name="chevron-back-sharp"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{ "title_match_options" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list>
    <ion-item-group>
      <ion-item-divider color="light">
        <ion-label class="main-label"
          >{{ 'search_distance' | translate }}</ion-label
          >
        </ion-item-divider>

        <ion-item class="text-field search-distance">
          <ion-range
            label="{{ distanceLabel + (distance ? 'mi' : '') }}"
            label-placement="end"
            [pin]="matchOptions.showPin"
            min="0"
            max="6"
            [step]="matchOptions.stepDistance"
            [(ngModel)]="distance"
            color="secondary"
            snaps="true"
            [disabled]="matchOptions.anyDistance"
            (ionInput)="setDistanceParams($event)"
          ></ion-range>
        </ion-item>
        <ion-item lines="none">
          <ion-checkbox
            label-placement="end"
            justify="start"
            [(ngModel)]="matchOptions.anyDistance"
            (ionChange)="selectAnyDistance($event)"
            >{{ 'any_distance' | translate }}</ion-checkbox
            >
          </ion-item>
        </ion-item-group>

        <ion-item-group>
          <ion-item-divider color="light">
            <ion-label class="main-label">{{ 'age_range' | translate }}</ion-label>
          </ion-item-divider>

          <ion-item class="text-field age-range">
            <ion-label slot="start"
              >{{ accountService.matchPref.age_from }}</ion-label
              >
              <ion-label slot="end">{{ accountService.matchPref.age_to }}</ion-label>
              <ion-range
                label=""
                [pin]="matchOptions.showPin"
                [dualKnobs]="true"
                [min]="matchOptions.ageFrom"
                [max]="matchOptions.ageTo"
                [step]="matchOptions.stepAge"
                [(ngModel)]="selectedAges"
                color="secondary"
                [disabled]="matchOptions.anyAge"
                (ionInput)="updateSelectedAges($event)"
              ></ion-range>
            </ion-item>
            <ion-item lines="none">
              <ion-checkbox
                label-placement="end"
                justify="start"
                [(ngModel)]="matchOptions.anyAge"
                (ionChange)="selectAnyAge($event)"
                >{{ 'any_age' | translate }}</ion-checkbox
                >
              </ion-item>
            </ion-item-group>

            <ion-item-group>
              <ion-item-divider color="light">
                <ion-label class="main-label"
                  >{{ 'looking_for' | translate }}</ion-label
                  >
                </ion-item-divider>

                @if (accountService.account) {
                  <ion-item lines="none">
                    <ion-select
                      label="{{ 'gender' | translate }}"
                      placeholder="{{ 'select_one' | translate }}"
                      [(ngModel)]="accountService.account.looking_id"
                      interface="popover"
                      [value]="accountService.account.looking_id"
                      >
                      @for (gender of genders; track gender) {
                        <ion-select-option [value]="gender.id"
                          >{{ gender.name | translate }}</ion-select-option
                          >
                        }
                      </ion-select>
                    </ion-item>
                  }
                </ion-item-group>

                <ion-item-group>
                  <ion-item-divider color="light">
                    <ion-label class="main-label ion-text-wrap"
                      >{{ 'filter_who_can_contact' | translate }}</ion-label
                      >
                    </ion-item-divider>

                    <ion-item>
                      <ion-checkbox
                        label-placement="end"
                        justify="start"
                        [(ngModel)]="accountService.matchPref.anybody"
                        (ionChange)="selectAllGenders()"
                        >{{ 'anybody' | translate }}</ion-checkbox
                        >
                      </ion-item>
                      @for (gender of genders; track gender) {
                        <ion-item>
                          <ion-checkbox
                            label-placement="end"
                            justify="start"
                            [(ngModel)]="gender.active"
                            (ionChange)="updateSingleGenderSelection()"
                            [disabled]="accountService.matchPref.anybody"
                            >{{ gender.name | translate }}</ion-checkbox
                            >
                          </ion-item>
                        }
                      </ion-item-group>
                    </ion-list>
                    <!-- <pre [innerHTML]="matchOptions | json"></pre> -->
                  </ion-content>
