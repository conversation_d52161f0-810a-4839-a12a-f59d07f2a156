import { Component, OnInit } from "@angular/core";
import { MatchPrefDistances } from "../../schemas/match-pref";
import { environment as Env } from "../../../environments/environment";
import { AccountService } from "../../services/account.service";
import { TranslateService } from "@ngx-translate/core";
import { GenderItem } from "src/app/schemas/gender-item";
import _ from "lodash";
import { ApiParams } from "../../services/api/api.params";
import { Debug } from "../../helpers/debug";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { StorageService } from "../../services/storage.service";
import { MenuHelperService } from "../../services/menu-helper.service";
import { MiscService } from "../../services/misc.service";
import { NavController } from "@ionic/angular";

const MATCH_OPTION_AGES = "match_option_ages";
const MATCH_OPTION_DISTANCE = "match_option_distance";

@Component({
  selector: "app-match-options-menu",
  templateUrl: "./match-options-menu.page.html",
  styleUrls: ["./match-options-menu.page.scss"],
  standalone: false,
})
export class MatchOptionsMenuPage implements OnInit {
  /**
   * MatchPref options.
   */
  public matchOptions = {
    minDistance: 0,
    maxDistance: 250,
    stepDistance: 1,
    minDistanceLabel: "any", // translate
    stepAge: 1,
    showPin: false,
    anyDistance: false,
    anyAge: false,
    ageFrom: Env.APP_VALID_AGE,
    ageTo: 99,
  };

  /**
   * Available distances.
   *
   * @type {[number]}
   */
  public availableDistances: MatchPrefDistances[] = [
    0, 10, 20, 30, 50, 100, 250,
  ];

  /**
   * Distance label.
   *
   * @type {string}
   */
  public distanceLabel: any = 0;

  /**
   * Distance option.
   */
  public distance: number;

  /**
   * Bck distance.
   *
   * @type {null}
   */
  private bckDistance: string = null;

  /**
   * DualKnobs options.
   */
  public selectedAges: { lower: number; upper: number };

  /**
   * Temporary backup the selected age range so we can update range after the user uncheck (any age) checkbox.
   */
  private bckSelectedAges: { lower: number; upper: number };

  /**
   * Genders list.
   */
  public genders: GenderItem[] = Env.APP_GENDERS as GenderItem[];

  /**
   * Backup genders list.
   */
  private bckGenders: string;

  private bckParams: string;

  private _backUrl: string;

  constructor(
    public accountService: AccountService,
    public translate: TranslateService,
    public dbg: Debug,
    public dlg: DialogHelperService,
    public storage: StorageService,
    public menuHelper: MenuHelperService,
    public misc: MiscService,
    public navCtrl: NavController,
  ) {}

  ionViewDidEnter() {
    this.accountService.account.looking_id = _.toNumber(
      this.accountService.account.looking_id,
    );

    this.bckParams = JSON.stringify(this.setMatchPrefParams());
  }

  ngOnInit() {
    this._backUrl = this.menuHelper.currentURL;

    this.menuHelper.BackEventStream$.subscribe({
      next: (data) => {
        //back button pressed
        if (this.misc.checkUrl("app/match-options-menu")) {
          // if we're on my-profile-edit, execute update
          this.updateMatchPref();
        }
      },
    });

    this.availableDistances.forEach((v, i) => {
      if (_.toNumber(this.accountService.matchPref.distance) === v) {
        this.distance = i;

        this.distanceLabel = i === 0 ? this.translate.instant("any") : v;
      }
    });

    this.storage
      .get(MATCH_OPTION_DISTANCE)
      .then((res) => (this.matchOptions.anyDistance = res ?? false))
      .catch((err) => this.dbg.le("match option distance not set", err));

    this.storage
      .get(MATCH_OPTION_AGES)
      .then((res) => {
        if (!res) {
          this.matchOptions.anyAge = false;
          // Try to set data from account.matchPref.
          if (this.accountService.matchPref) {
            this.selectedAges = {
              lower:
                this.accountService.matchPref.age_from ?? Env.APP_VALID_AGE,
              upper: this.accountService.matchPref.age_to ?? 99,
            };
          } else {
            // Set default age range.
            this.selectedAges = this.defaultSelectedAges();
          }
          return;
        }

        // We have anyAge = true from cache. Set default min/max.
        if (res.anyAge) {
          this.selectedAges = this.defaultSelectedAges();
          this.matchOptions.anyAge = true;
          return;
        }

        // Set data from cache.
        this.selectedAges = res.selectedAges ?? this.defaultSelectedAges();
        this.matchOptions.anyAge = false;
      })
      .catch((err) => this.dbg.le("match option ages not set", err));

    this.genders.forEach((gender) => {
      gender["active"] = _.includes(
        this.accountService.matchPref.genders,
        _.toString(gender.id),
      );
    });

    this.bckGenders = JSON.stringify(this.genders);
  }

  /**
   * Setting distance parameters on range change.
   *
   * @param {Object} data Data from event
   */
  public setDistanceParams(data) {
    this.availableDistances.forEach((v, i) => {
      if (data.detail.value === i) {
        this.distanceLabel = i === 0 ? this.translate.instant("any") : v;
        this.distance = i === 0 ? null : i;

        this.accountService.matchPref.distance = v;
      }
    });

    if (this.accountService.matchPref.distance > 0)
      this.matchOptions.anyDistance = false;
  }

  /**
   * Select any distance.
   */
  public selectAnyDistance(event) {
    if (this.matchOptions.anyDistance) {
      this.bckDistance = JSON.stringify(this.distance);
    }

    this.distance = this.matchOptions.anyDistance
      ? 0
      : JSON.parse(this.bckDistance);

    // set distance properly in case of any click ( fix for ionic 7 lib differences )
    this.setDistanceParams({ detail: { value: this.distance } });
  }

  /**
   * Update selected ages.
   *
   * @param event
   */
  public updateSelectedAges(event): void {
    this.accountService.matchPref.age_from = event.detail.value.lower;
    this.accountService.matchPref.age_to = event.detail.value.upper;
  }

  /**
   * Toggle select all age.
   */
  public selectAnyAge(event): void {
    if (this.matchOptions.anyAge) {
      this.bckSelectedAges = this.selectedAges;
      this.selectedAges = this.defaultSelectedAges();
      this.accountService.matchPref.age_from = this.selectedAges.lower;
      this.accountService.matchPref.age_to = this.selectedAges.upper;

      return;
    }

    this.selectedAges = this.bckSelectedAges ?? this.defaultSelectedAges();
    this.accountService.matchPref.age_from = this.selectedAges.lower;
    this.accountService.matchPref.age_to = this.selectedAges.upper;
  }

  /**
   * Toggle anybody.
   */
  public selectAllGenders(): void {
    this.accountService.matchPref.genders = [];
    this.genders.forEach((gender) => {
      gender["active"] = this.accountService.matchPref.anybody;
      if (gender["active"]) {
        this.accountService.matchPref.genders.push(_.toString(gender.id));
      }
    });
  }

  /**
   * Select gender.
   */
  public updateSingleGenderSelection(): void {
    this.accountService.matchPref.genders = [];

    this.genders.forEach((gender) => {
      if (gender["active"]) {
        this.accountService.matchPref.genders.push(_.toString(gender.id));
      }
    });
  }

  /**
   * Save match-pref
   */
  public async updateMatchPref(): Promise<void> {
    if (
      !this.accountService.matchPref.anybody &&
      !this.accountService.matchPref.genders.length
    ) {
      this.dlg.showToast("", "filter_who_can_contact_error", "warning");
      return;
    }

    this.storage.set(MATCH_OPTION_DISTANCE, this.matchOptions.anyDistance);

    this.storage.set(MATCH_OPTION_AGES, {
      anyAge: this.matchOptions.anyAge,
      selectedAges: {
        lower: this.accountService.matchPref.age_from,
        upper: this.accountService.matchPref.age_to,
      },
    });

    let params = this.setMatchPrefParams();

    if (JSON.stringify(params) === this.bckParams) {
      return this.navBack();
    }

    await this.dlg.showLoading("saving");

    this.accountService
      .updateMatchPref(params)
      .then(async (res) => {
        await this.dlg.hideLoading();
        this.dlg.showToast("success", "match_options_updated", "success", 2000);
        return this.navBack();
      })
      .catch(async (err) => {
        await this.dlg.hideLoading();
        this.dbg.le("ACC MATCH PREF UPDATE ERROR", err);
        this.showConfirmDialog();
      });
  }

  private setMatchPrefParams(): ApiParams.AccountMatchPref.type {
    return {
      gender_id: this.accountService.account.gender_id,
      looking_id: this.accountService.account.looking_id,
      age_from: this.accountService.matchPref.age_from,
      age_to: this.accountService.matchPref.age_to,
      distance: _.toNumber(this.accountService.matchPref.distance),
      genders: this.accountService.matchPref.genders,
      anybody: this.accountService.matchPref.anybody,
    };
  }

  /**
   * Nav back.
   */
  private navBack(): Promise<any> {
    return this.navCtrl.navigateBack(this._backUrl);
  }

  /**
   * Display confirm dialog.
   *
   * @returns {Promise<any>}
   */
  private showConfirmDialog(): Promise<any> {
    return this.dlg.advanceConfirm(
      "error_saving_match_options_data",
      () => {
        // Try Again - you try to save the data again.
        this.dlg.hideLoading();
        setTimeout(() => {
          this.updateMatchPref();
        }, 500);
      },
      () => {
        // Discard - you go back, and discard the changes.
        this.dlg.hideLoading();
        this.navBack();
        this.restoreDefailtMatchPrefParams();
      },
      () => {
        // Cancel - you just close this dialog and stay on the edit page.
        this.dlg.hideLoading();
      },
      "try_again",
      "cancel",
      "discard",
    );
  }

  /**
   * Restore account update params.
   */
  private restoreDefailtMatchPrefParams(): void {
    let params: ApiParams.AccountMatchPref.type = JSON.parse(this.bckParams);
    this.accountService.account.gender_id = params.gender_id;
    this.accountService.account.looking_id = params.looking_id;
    this.accountService.matchPref.age_from = params.age_from;
    this.accountService.matchPref.age_to = params.age_to;
    this.accountService.matchPref.distance = params.distance;
    this.accountService.matchPref.genders = params.genders;
    this.accountService.matchPref.anybody = params.anybody;
  }

  private defaultSelectedAges() {
    return { lower: Env.APP_VALID_AGE, upper: 99 };
  }
}
