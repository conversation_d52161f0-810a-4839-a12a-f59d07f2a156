<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        text=""
        icon="chevron-back-sharp"
        (click)="locationService.filter = ''"
      ></ion-back-button>
    </ion-buttons>
    <ion-title>{{ "title_location_city" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list>
    @if (locationService.selectedCountry) {
      <ion-item>
        <ion-input
          label="{{ 'selected_location_country' | translate }}"
          type="text"
          text-center
          [(ngModel)]="locationService.selectedCountry"
          readonly
        ></ion-input>
      </ion-item>
    }

    @if (locationService.selectedCity) {
      <ion-item>
        <ion-input
          label="{{ 'selected_location_city' | translate }}"
          type="text"
          text-center
          [(ngModel)]="locationService.selectedCity"
          readonly
        ></ion-input>
      </ion-item>
    }

    @if (locationService.selectedZip) {
      <ion-item>
        <ion-input
          label="{{ 'current_location_zip' | translate }}"
          type="text"
          text-center
          [(ngModel)]="locationService.selectedZip"
          readonly
        ></ion-input>
      </ion-item>
    }

    <ion-item lines="none" class="ion-margin-vertical">
      <ion-searchbar
        [(ngModel)]="locationService.filter"
        [debounce]="500"
        (ionInput)="filterCity()"
      ></ion-searchbar>
    </ion-item>

    @if (locationService.filteredList) {
      <ion-radio-group
        [value]="locationService.selectedCity"
        (click)="citySelected($event)"
        >
        @for (item of locationService.filteredList; track item) {
          <ion-item>
            <ion-radio [value]="item.key" [disabled]="locationService.isProcessing"
              >{{ item.value }}</ion-radio
              >
            </ion-item>
          }
        </ion-radio-group>
      }
    </ion-list>
  </ion-content>
