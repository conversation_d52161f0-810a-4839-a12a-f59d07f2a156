import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { LocationCityPage } from "./location-city.page";

const routes: Routes = [
  {
    path: "",
    component: LocationCityPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LocationCityPageRoutingModule {}
