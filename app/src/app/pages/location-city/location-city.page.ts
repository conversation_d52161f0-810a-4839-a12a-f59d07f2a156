import { Component, OnInit } from "@angular/core";
import { LocationService } from "../../services/location.service";
import { Debug } from "../../helpers/debug";
import { AccountService } from "../../services/account.service";
import { firstValueFrom, timer } from "rxjs";

@Component({
  selector: "app-location-city",
  templateUrl: "./location-city.page.html",
  styleUrls: ["./location-city.page.scss"],
  standalone: false,
})
export class LocationCityPage {
  constructor(
    public locationService: LocationService,
    public dbg: Debug,
    public accountService: AccountService,
  ) {}

  private resetForm() {
    this.locationService.filter = "";
    this.locationService.setFilteredList("city_list");
  }

  public ionViewWillEnter() {
    this.resetForm();
  }

  /**
   * Filter country list.
   */
  public filterCity() {
    if (
      this.locationService.filter &&
      this.locationService.filter.trim() !== ""
    ) {
      this.locationService.shouldShowCancel = true;

      return (this.locationService.filteredList =
        this.locationService.cityList.filter((item) => {
          return item.value
            .toLowerCase()
            .includes(this.locationService.filter.toLowerCase());
        }));
    }

    this.locationService.filteredList = this.locationService.cityList;
  }

  /**
   * Get location data after select city.
   */
  public async citySelected(e) {
    this.locationService.isProcessing = true;

    await firstValueFrom(timer(100));
    this.locationService.selectedCity = e.target.value;

    this.locationService
      .locationHelper({
        get_countries: 1,
        country_code: this.locationService.selectedCountry,
        city: this.locationService.selectedCity,
        zip: this.locationService.selectedZip
          ? this.locationService.selectedZip
          : null,
      })
      .then(async ({ data }) => {
        this.locationService.isProcessing = false;

        if (data.whatToShow.indexOf("zip_input") > -1) {
          this.locationService.nextPage = "location-zip";
        }

        if (data.whatToShow.indexOf("city_select") > -1) {
          this.locationService.nextPage = "location-city";
        }

        if (!data.whatToShow.length) {
          this.locationService.nextPage = "/app/my-profile-edit";
          this.locationService.actionTitle = "finish";
          this.accountService.setLocation({
            country: data.selected_country,
            city: data.selected_city,
            zip: data.selected_zip,
          });
        }

        this.locationService.next();
      })
      .catch((error) => this.dbg.le("city select error", error))
      .then(() => (this.locationService.isProcessing = false));
  }
}
