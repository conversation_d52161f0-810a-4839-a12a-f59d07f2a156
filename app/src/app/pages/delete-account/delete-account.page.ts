import { Component, OnInit } from "@angular/core";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { AccountService } from "../../services/account.service";
import { Debug } from "../../helpers/debug";
import { SessionService } from "../../services/session.service";

@Component({
  selector: "app-delete-account",
  templateUrl: "./delete-account.page.html",
  styleUrls: ["./delete-account.page.scss"],
  standalone: false,
})
export class DeleteAccountPage implements OnInit {
  /**
   * @type {string}
   */
  public confirm: string = "";

  /**
   * Confirmed.
   *
   * @type {boolean}
   */
  public confirmed: boolean = false;

  constructor(
    public dialog: DialogHelperService,
    public account: AccountService,
    public dbg: Debug,
    public session: SessionService,
  ) {}

  ngOnInit() {}

  /**
   * Delete account
   */
  public deleteAccount(): void {
    this.dialog.showLoading("processing_please_wait", "🤓");

    this.confirmed = false;

    this.account
      .delete()
      .then(({ messages }) => {
        if (messages.length) {
          this.dialog.showToast("", messages[0].message);
        }
      })
      .catch((error) => this.dbg.le("account delete error", error))
      .then(() => this.resetData());
  }

  /**
   * Check confirmation.
   *
   * @param e
   */
  public isConfirmed(e) {
    return (this.confirmed = e.detail.value.trim().toLowerCase() === "ok");
  }

  /**
   * Reset data.
   */
  private resetData() {
    this.dialog.hideLoading();
    this.confirm = "";

    setTimeout(() => this.session.logoutAndRestart(), 3000);
  }
}
