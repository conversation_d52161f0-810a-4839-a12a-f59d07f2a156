<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ "title_account_delete" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row class="ion-justify-content-center">
      <ion-col size-sm="6" class="ion-text-center ion-padding-bottom">
        <div>
          <h1>{{ "account_delete" | translate }}</h1>
          <h5>{{ "account_delete_intro" | translate }}</h5>
          <p>
            {{ "account_delete_deactivate_instead" | translate }}
            <a [routerLink]="['/app/deactivation']" class="small-text"
              >{{ "account_deactivate" | translate }}</a
            >
          </p>
        </div>
        <div class="ion-margin-top ion-padding-top">
          <form>
            <h3>{{ "account_delete_confirm" | translate }}</h3>
            <p>
              {{ "account_delete_instructions" | translate }}
              <strong>{{ "account_delete_word" | translate }}</strong>
            </p>
            <ion-item class="ion-margin-bottom">
              <ion-input
                class="ion-text-center"
                [(ngModel)]="confirm"
                (ionInput)="isConfirmed($event)"
                name="confirmation"
                type="text"
              ></ion-input>
            </ion-item>
            <ion-button
              type="submit"
              expand="block"
              (click)="deleteAccount()"
              [disabled]="! confirmed"
            >
              {{ "account_delete" | translate }}
            </ion-button>
          </form>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
