import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { DeleteAccountPage } from "./delete-account.page";

const routes: Routes = [
  {
    path: "",
    component: DeleteAccountPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DeleteAccountPageRoutingModule {}
