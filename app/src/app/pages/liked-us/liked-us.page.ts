import { Component } from "@angular/core";
import { Debug } from "src/app/helpers/debug";
import { ListPaginator } from "src/app/services/lists/list-paginator";
import { PaginatorHelperService } from "src/app/services/paginator-helper.service";
import { UnreadCountService } from "src/app/services/unread-count.service";
import { ProfileService } from "../../services/profile.service";
import { AccountService } from "../../services/account.service";
import { PurchaseService } from "../../services/purchase.service";
import { IapItem } from "src/app/services/iap-item.interface";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { DeviceInfoService } from "../../services/device-info.service";
import { environment as Env } from "src/environments/environment";

@Component({
  selector: "app-liked-us",
  templateUrl: "./liked-us.page.html",
  styleUrls: ["./liked-us.page.scss"],
  standalone: false,
})
export class LikedUsPage {
  public listPaginator: ListPaginator;
  public data: IapItem[];
  public selectedItem: IapItem;

  public env = Env;

  constructor(
    public profileService: ProfileService,
    private paginatorHelper: PaginatorHelperService,
    private debug: Debug,
    public unreadCountService: UnreadCountService,
    public accountService: AccountService,
    public purchaseService: PurchaseService,
    public dialog: DialogHelperService,
    private deviceInfo: DeviceInfoService,
  ) {
    this.listPaginator = this.paginatorHelper.likedUsPaginator;
  }

  ngOnInit() {
    if (this.accountService.isPremium()) return;

    if (this.purchaseService.subscriptions.length == 0) {
      // initialize mock if page opened directly for design ( for example )
      this.dialog.showToast(
        "",
        "DEV_ONLY: Dialog was not initialized properly, using mock data!",
        "error",
      );
      this.purchaseService.init_MOCK();
    }

    // center item as default
    this.selectProduct(
      this.purchaseService.subscriptions[1] ||
        this.purchaseService.subscriptions[0],
    );

    this.data = this.purchaseService.subscriptions;
  }

  ionViewDidEnter() {
    if (!this.listPaginator.didLoad()) {
      this.listPaginator.next();
    }
  }

  public inProgress() {
    return this.purchaseService.inProgress();
  }

  public selectProduct(item: IapItem) {
    this.selectedItem = item;
  }

  public upgradeClicked() {
    this.purchaseService.buySubscription(this.selectedItem.store_id);
  }

  public isIos() {
    return this.deviceInfo.platform == "ios";
  }
}
