@if (!accountService.account.is_premium) {
  <ion-content>
    <ion-grid class="ion-text-center ion-no-padding">
      <ion-row class="features-list">
        <ion-col class="ion-padding-horizontal">
          <swiper-container pagination="false" class="ion-flex-direction-row">
            <swiper-slide>
              <ion-row>
                <ion-col>
                  <ion-icon
                    src="assets/icons/ico-heart.svg"
                    color="danger"
                    size="large"
                    class="custom-icon"
                  ></ion-icon>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <h4 class="features-list--color">
                      {{ "slide_premium_liked_us_title" | translate }}
                    </h4>
                  </ion-text>
                </ion-col>
              </ion-row>
              <ion-row class="max-width--300">
                <ion-col>
                  <ion-text>
                    <p class="p--size features-list--color">
                      {{ "slide_premium_liked_us_message" | translate }}
                    </p>
                  </ion-text>
                </ion-col>
              </ion-row>
            </swiper-slide>
          </swiper-container>
        </ion-col>
      </ion-row>
      <ion-row class="product-items ion-padding">
        @for (item of data; track item; let i = $index) {
          <ion-col
            size="4"
            (click)="selectProduct(item)"
            [ngClass]="{'selected': item.id == selectedItem.id}"
            class="item"
            >
            <div class="item-inner">
              <div [class.hidden]="i == 0 || i == 2" class="best-label">
                <ion-text> {{ 'popular' | translate }} </ion-text>
              </div>
              <ion-text>
                <h2>{{ item.ribbonText }}</h2>
              </ion-text>
              <ion-text class="period-length">{{ item.titleText }}</ion-text>
              <div>
                <ion-text>
                  <p class="price">{{ item.price }}</p>
                </ion-text>
                <ion-text>
                  <p class="period-unit">{{ item.periodText }}</p>
                </ion-text>
              </div>
              <div
                [class.hidden]="item.savingsInPercentage == null || item.savingsInPercentage <= 0"
                class="savings"
                >
                <ion-text>
                  {{ 'premium_save' | translate }} {{ item.savingsInPercentage }}%
                </ion-text>
              </div>
            </div>
          </ion-col>
        }
      </ion-row>
      <ion-row class="ion-padding-horizontal">
        <ion-col>
          <ion-button
            [disabled]="this.inProgress() || !this.selectedItem.canPurchase"
            expand="block"
            color="primary"
            (click)="upgradeClicked()"
            >{{ "upgrade_now" | translate }}
            <ion-spinner
              name="crescent"
              [class.hidden]="!this.inProgress()"
            ></ion-spinner>
          </ion-button>
        </ion-col>
      </ion-row>
      <ion-row class="ion-padding-horizontal">
        <ion-col>
          <ion-text>
            <p class="billed">{{ selectedItem.description }}</p>
          </ion-text>
        </ion-col>
      </ion-row>
      @if (isIos()) {
        <ion-row class="ion-padding-horizontal">
          <ion-col class="legal" padding-horizontal>
            <ion-text>
              {{ 'iap_info_text_1' | translate }}
              <a class="terms-link" routerLink="/page/{{env.PAGE_NAME_TOS}}"
                >{{ 'terms_and_conditions' | translate }}</a
                >
                {{ 'and' | translate }}
                <a
                  class="privacy-policy-link"
                  routerLink="/page/{{env.PAGE_NAME_PRIVACY_POLICY}}"
                  >{{ 'privacy_policy' | translate }}</a
                  >
                </ion-text>
              </ion-col>
            </ion-row>
          }
          <ion-row class="ion-padding-horizontal ion-padding-top">
            <ion-col class="legal" padding-horizontal>
              <ion-text><!--{{ 'iap_info_text_2' | translate }}--></ion-text>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-content>
    }

    @if (accountService.account.is_premium) {
      <ion-content>
        <app-happy-hour></app-happy-hour>
        <ion-refresher slot="fixed" (ionRefresh)="this.listPaginator.refresh($event)">
          <ion-refresher-content refreshingSpinner="crescent"></ion-refresher-content>
        </ion-refresher>
        <!-- empty container { -->
        <ion-grid
          class="statusMessage ion-text-center"
          [class.hidden]="!this.listPaginator.isEmpty()"
          >
          <ion-row>
            <ion-col>
              <ion-icon color="primary" name="heart-dislike-outline"></ion-icon>
              <h2>{{ 'liked_us_empty_title' | translate }}</h2>
              <h6>{{ 'liked_us_empty_description' | translate }}</h6>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <ion-button color="primary" [routerLink]="['/app/tabs/browse']">
                {{ "liked_us_empty_button" | translate }}
              </ion-button>
            </ion-col>
          </ion-row>
        </ion-grid>
        <!-- } empty container -->
        <!-- loading container { -->
        @if (profileService.listLikedUs.list$ | async; as listLikedUs) {
          <ion-grid
            [class.hidden]="this.profileService.listLikedUs.wasError()"
            >
            <ion-row>
              @for (profile of listLikedUs; track profile) {
                <ion-col size="6" size-md="3">
                  <ion-card
                    [ngClass]="profile.is_active ? 'active' : 'inactive'"
                    class="ion-no-margin"
                    [routerLink]="[ '/app/profile', profile.username ]"
                    >
                    <div class="distance">{{ profile.distance | distance }}</div>
                    <div class="img-placeholder">
                      <ion-img
                        style="pointer-events: none"
                        [src]="profile.avatar_url"
                      ></ion-img>
                    </div>
                    <ion-card-header>
                      <ion-card-title color="dark">
                        {{ (profile.first_name || profile.username) | slice:0:14 }}, {{
                        profile.age }}
                        <span class="status-indicator"></span>
                      </ion-card-title>
                      <ion-text>
                        @if (profile.city) {
                          <span>{{ profile.city }},</span>
                          } {{
                          profile.country === 'US' ? profile.state : profile.country }}
                        </ion-text>
                      </ion-card-header>
                    </ion-card>
                  </ion-col>
                }
              </ion-row>
            </ion-grid>
          }
          <!-- } loading container -->
          <ion-infinite-scroll
            [class.hidden]="this.listPaginator.didReachEnd()"
            threshold="100px"
            id="infinite-scroll"
            (ionInfinite)="this.listPaginator.next($event)"
            >
            <ion-infinite-scroll-content loadingSpinner="crescent">
              <!-- sceleton content before loading { -->
              <div
                class="ion-text-start"
                [class.hidden]="!(this.profileService.listLikedUs.isLoading() && !this.listPaginator.didLoad())"
                >
                <ion-grid>
                  @for (_r of [].constructor(5); track _r) {
                    <ion-row>
                      @for (_c of [].constructor(4); track _c) {
                        <ion-col size="6" size-md="3">
                          <ion-card class="ion-no-margin">
                            <ion-skeleton-text
                              style="width: 20%"
                              class="ion-float-left ion-margin"
                              animated
                            ></ion-skeleton-text>
                            <ion-thumbnail class="img-placeholder">
                              <ion-skeleton-text></ion-skeleton-text>
                            </ion-thumbnail>
                            <ion-card-header class="skeleton">
                              <ion-card-title>
                                <ion-skeleton-text
                                  animated
                                  style="width: 40%"
                                  class="ion-float-left"
                                ></ion-skeleton-text>
                                <span class="status-indicator"></span>
                              </ion-card-title>
                              <ion-text>
                                <ion-skeleton-text
                                  animated
                                  style="width: 60%"
                                ></ion-skeleton-text>
                              </ion-text>
                            </ion-card-header>
                          </ion-card>
                        </ion-col>
                      }
                    </ion-row>
                  }
                </ion-grid>
              </div>
              <!-- } end sceleton content -->
            </ion-infinite-scroll-content>
          </ion-infinite-scroll>
          <!-- error container { -->
          <ion-grid
            class="statusMessage ion-text-center"
            [class.hidden]="!this.profileService.listLikedUs.wasError()"
            >
            <ion-row>
              <ion-col>
                <ion-icon color="primary" name="alert-circle-outline"></ion-icon>
                <h3>{{ 'error_list' | translate }}</h3>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col>
                <ion-button color="primary" (click)="this.listPaginator.refresh()"
                  >{{ 'refresh' | translate }}</ion-button
                  >
                </ion-col>
              </ion-row>
            </ion-grid>
            <!-- } error container -->
            <div
              style="text-align: center; display: none"
              [class.hidden]="!this.listPaginator.didReachEnd() && !this.listPaginator.isEmpty()"
              >
              ¯\_(ツ)_/¯
            </div>
          </ion-content>
        }
