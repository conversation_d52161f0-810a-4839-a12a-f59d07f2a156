import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { LikedUsPage } from "./liked-us.page";

const routes: Routes = [
  {
    path: "",
    component: LikedUsPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LikedUsPageRoutingModule {}
