import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { LikedUsPageRoutingModule } from "./liked-us-routing.module";
import { LikedUsPage } from "./liked-us.page";
import { TranslateModule } from "@ngx-translate/core";
import { PipesModule } from "src/app/pipes/pipes.module";
import { HappyHourComponentModule } from "../../components/happy-hour/happy-hour.component.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    LikedUsPageRoutingModule,
    PipesModule,
    HappyHourComponentModule,
  ],
  declarations: [LikedUsPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class LikedUsPageModule {}
