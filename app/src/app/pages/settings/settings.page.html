<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        text=""
        icon="chevron-back-sharp"
        (click)="updateSettings()"
      ></ion-back-button>
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>{{ "title_settings" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content #content>
  @if (accountService.settings) {
    <ion-list>
      <ion-item-group>
        <ion-item-divider color="silver">
          <ion-label class="main-label">
            {{ "push_notifications_settings" | translate }}
          </ion-label>
        </ion-item-divider>
        <ion-item [hidden]="this.isEnabledPushNotification">
          <ion-button (click)="this.enablePushNotificationDialog()">{{ 'enable_app_notifications' | translate }}</ion-button>
        </ion-item>
        <ion-item [disabled]="!this.isEnabledPushNotification">
          <ion-toggle
            [(ngModel)]="accountService.settings.push_notify_messages"
            label-placement="start"
            >{{ "messages" | translate }}</ion-toggle
            >
          </ion-item>
          <ion-item [disabled]="!this.isEnabledPushNotification">
            <ion-toggle
              [(ngModel)]="accountService.settings.push_new_flirt"
              label-placement="start"
              >{{ "push_new_flirt" | translate }}</ion-toggle
              >
            </ion-item>
            <ion-item [disabled]="!this.isEnabledPushNotification">
              <ion-toggle
                [(ngModel)]="accountService.settings.push_notify_likes"
                label-placement="start"
                >{{ "new_likes" | translate }}</ion-toggle
                >
              </ion-item>
              <ion-item [disabled]="!this.isEnabledPushNotification">
                <ion-toggle
                  [(ngModel)]="accountService.settings.push_chat_request"
                  label-placement="start"
                  >{{ "push_chat_request" | translate }}</ion-toggle
                  >
                </ion-item>
                <ion-item [disabled]="!this.isEnabledPushNotification" lines="none">
                  <ion-toggle
                    [(ngModel)]="accountService.settings.push_notify_campaigns"
                    label-placement="start"
                    >{{ "updates_discounts" | translate }}</ion-toggle
                    >
                  </ion-item>
                  <ion-item [disabled]="!this.isEnabledPushNotification">
                    <ion-toggle
                      [(ngModel)]="accountService.settings.push_notify_new_matches"
                      label-placement="start"
                      >{{ 'new_matches' | translate }}</ion-toggle
                      >
                    </ion-item>
                    <ion-item [disabled]="!this.isEnabledPushNotification">
                      <ion-toggle
                        [(ngModel)]="accountService.settings.push_photo_uploaded"
                        label-placement="start"
                        >{{ "push_photo_uploaded" | translate }}</ion-toggle
                        >
                      </ion-item>
                      <ion-item [disabled]="!this.isEnabledPushNotification">
                        <ion-toggle
                          [(ngModel)]="accountService.settings.push_profile_viewed"
                          label-placement="start"
                          >{{ "push_profile_viewed" | translate }}</ion-toggle
                          >
                        </ion-item>
                      </ion-item-group>
                    </ion-list>
                  }

                  @if (accountService.settings) {
                    <ion-list>
                      <ion-item-group>
                        <ion-item-divider color="silver">
                          <ion-label class="main-label">
                            {{ "email_notifications_settings" | translate }}
                          </ion-label>
                        </ion-item-divider>
                        <ion-item>
                          <ion-toggle
                            [(ngModel)]="accountService.settings.email_notify_messages"
                            label-placement="start"
                            >{{ "messages" | translate }}</ion-toggle
                            >
                          </ion-item>
                          <ion-item>
                            <ion-toggle
                              [(ngModel)]="accountService.settings.email_notify_likes"
                              label-placement="start"
                              >{{ "new_likes" | translate }}</ion-toggle
                              >
                            </ion-item>
                            <ion-item>
                              <ion-toggle
                                [(ngModel)]="accountService.settings.email_add_friend"
                                label-placement="start"
                                >{{ "email_ads_me_as_friend" | translate }}</ion-toggle
                                >
                              </ion-item>
                              <ion-item>
                                <ion-toggle
                                  [(ngModel)]="accountService.settings.email_profile_visits"
                                  label-placement="start"
                                  >{{ "email_visit_my_profile" | translate }}</ion-toggle
                                  >
                                </ion-item>
                                <ion-item>
                                  <ion-toggle
                                    [(ngModel)]="accountService.settings.email_newsletter"
                                    label-placement="start"
                                    >{{ "email_service_updates" | translate }}</ion-toggle
                                    >
                                  </ion-item>
                                  <ion-item lines="none">
                                    <ion-toggle
                                      [(ngModel)]="accountService.settings.email_promo"
                                      label-placement="start"
                                      >{{ "promotions" | translate }}</ion-toggle
                                      >
                                    </ion-item>
                                    <ion-item>
                                      <ion-toggle
                                        [(ngModel)]="accountService.settings.email_notify_new_matches"
                                        label-placement="start"
                                        >{{ "new_matches" | translate }}</ion-toggle
                                        >
                                      </ion-item>
                                      <ion-item>
                                        <ion-toggle
                                          [(ngModel)]="accountService.settings.email_first_photo"
                                          label-placement="start"
                                          >{{ "email_first_photo" | translate }}</ion-toggle
                                          >
                                        </ion-item>
                                        <ion-item>
                                          <ion-toggle
                                            [(ngModel)]="accountService.settings.email_online_user"
                                            label-placement="start"
                                            >{{ "email_online_user" | translate }}</ion-toggle
                                            >
                                          </ion-item>
                                          <ion-item>
                                            <ion-toggle
                                              [(ngModel)]="accountService.settings.email_msg_read"
                                              label-placement="start"
                                              >{{ "email_reads_my_message" | translate }}</ion-toggle
                                              >
                                            </ion-item>
                                          </ion-item-group>
                                        </ion-list>
                                      }

                                      @if (accountService.settings) {
                                        <ion-list lines="none">
                                          <ion-item-group>
                                            <ion-item-divider color="silver">
                                              <ion-label class="main-label">
                                                {{ "privacy_settings" | translate }}
                                              </ion-label>
                                            </ion-item-divider>
                                            <ion-item>
                                              <ion-label>{{ "show_distance" | translate }}</ion-label>
                                            </ion-item>
                                            <ion-item>
                                              <ion-toggle
                                                [(ngModel)]="accountService.settings.privacy_show_distance"
                                                label-placement="start"
                                                >
                                                <ion-text class="ion-text-wrap"
                                                  >{{ "show_distance_subtext" | translate }}</ion-text
                                                  >
                                                </ion-toggle>
                                              </ion-item>
                                              <ion-item
                                                lines="none"
                                                [class.hidden]="!accountService.isCCPARequired"
                                                class="ion-margin-top"
                                                >
                                                <ion-toggle
                                                  [(ngModel)]="accountService.CCPAStatus"
                                                  [disabled]="accountService.isCCPAInProgress()"
                                                  (click)="updateCCPAStatus()"
                                                  label-placement="start"
                                                  >
                                                  <ion-text class="ion-text-wrap"
                                                    >{{ 'do_not_sell_my_info' | translate }}</ion-text
                                                    >
                                                  </ion-toggle>
                                                  <ion-icon
                                                    name="information-circle"
                                                    (click)="openDoNotSellMyInfo()"
                                                    slot="start"
                                                    class="ion-no-margin ion-padding-end"
                                                  ></ion-icon>
                                                </ion-item>
                                              </ion-item-group>
                                            </ion-list>
                                          }

                                          <ion-list>
                                            <ion-item-group>
                                              <ion-item-divider color="silver">
                                                <ion-label class="main-label"> {{ "language" | translate }} </ion-label>
                                              </ion-item-divider>
                                              <ion-item button (click)="lang.showLanguageSelectDialog()" lines="none">
                                                {{ lang.supportedLanguages[lang.getAppLanguageId()] }}
                                              </ion-item>
                                            </ion-item-group>
                                          </ion-list>

                                          <ion-list>
                                            <ion-item-group>
                                              <ion-item-divider color="silver">
                                                <ion-label class="main-label">
                                                  {{ "my_account_settings" | translate }}
                                                </ion-label>
                                              </ion-item-divider>
                                              <ion-item button (click)="openManageMenu()">
                                                {{ "manage_sub_menu" | translate }}
                                              </ion-item>
                                              <ion-item button (click)="openProfileVerificationPage()">
                                                {{ "profile_verification" | translate }}
                                              </ion-item>
                                              <ion-item button (click)="appLogout()" lines="none">
                                                {{ "app_logout" | translate }}
                                              </ion-item>
                                            </ion-item-group>
                                          </ion-list>

                                          <ion-list>
                                            <ion-item-group>
                                              <ion-item-divider color="silver">
                                                <ion-label class="main-label">
                                                  {{ "app_info_settings" | translate }}
                                                </ion-label>
                                              </ion-item-divider>
                                              <ion-item button (click)="openCustomerSupport()">
                                                {{ "app_info_customer_support" | translate }}
                                              </ion-item>
                                              <ion-item button (click)="openTerms()">
                                                {{ "app_info_terms" | translate }}
                                              </ion-item>
                                              <ion-item button (click)="openPrivacyPolicy()">
                                                {{ "app_info_privacy_policy" | translate }}
                                              </ion-item>
                                              <ion-item button (click)="openFAQ()">
                                                {{ "app_info_faq" | translate }}
                                              </ion-item>
                                              <ion-item button (click)="openBillingPage()">
                                                {{ "title_billing_history" | translate }}
                                              </ion-item>
                                              <!--       <ion-item button (click)="openAppTour()">
                                              {{ "app_info_app_tour" | translate }}
                                            </ion-item> -->
                                            <!-- no need Promo Code for now
                                            <ion-item button (click)="enterPromoCode()" lines="none">
                                              {{ "promo_code" | translate }}
                                            </ion-item>
                                            -->
                                          </ion-item-group>
                                        </ion-list>

                                        <ion-list>
                                          <ion-item-group>
                                            <ion-item-divider color="silver">
                                              <ion-label class="main-label">
                                                {{ "social_settings" | translate }}
                                              </ion-label>
                                            </ion-item-divider>
                                            <ion-item button (click)="rateOnStore()" detail="false" lines="none">
                                              {{ "app_store_settings" | translate }}
                                              <ion-icon name="heart" slot="end"></ion-icon>
                                            </ion-item>
                                          </ion-item-group>
                                        </ion-list>
                                        <ion-item><small>v{{ this.getVersion() }}</small></ion-item>
                                      </ion-content>
