import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { SettingsPageRoutingModule } from "./settings-routing.module";

import { SettingsPage } from "./settings.page";
import { TranslateModule } from "@ngx-translate/core";
import { PushNotificationsPopupComponentModule } from "src/app/components/push-notifications-popup/push-notifications-popup.component.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    SettingsPageRoutingModule,
    PushNotificationsPopupComponentModule,
  ],
  declarations: [SettingsPage],
})
export class SettingsPageModule {}
