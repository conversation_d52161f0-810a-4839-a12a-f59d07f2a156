import { Compo<PERSON>, OnInit, ViewChild } from "@angular/core";
import { AccountService } from "../../services/account.service";
import {
  APP_LANGUAGE,
  APP_TRANSLATION,
  LanguageService,
} from "../../services/language.service";
import { Router } from "@angular/router";
import { SessionService } from "../../services/session.service";
import { Debug } from "../../helpers/debug";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { firstValueFrom, timer } from "rxjs";
import { AlertController, AlertInput } from "@ionic/angular";
import { StorageService } from "../../services/storage.service";
import { TranslateService } from "@ngx-translate/core";
import { MiscService } from "../../services/misc.service";
import { environment as Env } from "../../../environments/environment";
import { <PERSON>rowser } from "@capacitor/browser";
import { PromoService } from "src/app/services/promo.service";
import { AnalyticsService } from "src/app/services/analytics.service";
import { TME_logout } from "src/app/classes/gtm-event.class";
import { AppLauncher } from "@capacitor/app-launcher";
import { PushNotificationService } from "src/app/services/push-notification.service";
import {
  AndroidSettings,
  IOSSettings,
  NativeSettings,
} from "capacitor-native-settings";

@Component({
  selector: "app-settings",
  templateUrl: "./settings.page.html",
  styleUrls: ["./settings.page.scss"],
  standalone: false,
})
export class SettingsPage implements OnInit {
  @ViewChild("content", { static: false }) contentRef: any;

  ionViewDidEnter() {
    // scroll content to top when entering page
    this.contentRef.scrollToTop();
  }

  public doShowFbLink: boolean = true;

  public appTitle: string = "app title";

  public copyrightYear: string = "copyrightYear";

  public companyName: string = "companyName";

  public companySite: string = "companySite";
  public isEnabledPushNotification: boolean = false;

  constructor(
    public accountService: AccountService,
    public lang: LanguageService,
    public router: Router,
    public sessionService: SessionService,
    public dbg: Debug,
    public dlg: DialogHelperService,
    public alertController: AlertController,
    public storage: StorageService,
    public translate: TranslateService,
    public misc: MiscService,
    public promoService: PromoService,
    public analytics: AnalyticsService,
    public pushNotificationService: PushNotificationService,
  ) {}
  public async preInit() {
    if ((await this.pushNotificationService.canInitialize()) == "yes") {
      this.isEnabledPushNotification = true;
    }
  }

  ngOnInit() {
    this.preInit();
    this.accountService.getCCPARequired().subscribe();
    this.accountService.getCCPAStatus().subscribe();
  }

  public async enablePushNotificationDialog() {
    let res = await this.pushNotificationService.canInitialize();
    if (res == "need_prompt") {
      try {
        await this.pushNotificationService.init();
      } catch (e) {
        this.dlg.showAlert(
          "enable_push_notification_title",
          "enable_push_notification_info",
        );
      }
    } else if (res == "no") {
      await NativeSettings.open({
        optionAndroid: AndroidSettings.ApplicationDetails,
        optionIOS: IOSSettings.App,
      });
    }

    if ((await this.pushNotificationService.canInitialize()) == "yes") {
      this.isEnabledPushNotification = true;
    }
  }

  public enterPromoCode() {
    this.promoService.showManualPromo();
  }

  /**
   * Open ccpa page.
   */
  public async openDoNotSellMyInfo() {
    await this.router.navigate(["/page", "ccpa"]);
  }

  /**
   * Set ccpa status - bind to the GUI
   */
  async updateCCPAStatus() {
    await firstValueFrom(timer(10));
    this.accountService.updateCCPAStatus().subscribe({
      next: () => {
        this.dlg.showToast(
          "CCPA",
          this.accountService.CCPAStatus
            ? "ccpa_info_active"
            : "ccpa_info_inactive",
        );
      },
    });
  }

  public openManageMenu(): void {
    this.router.navigate(["/app/manage-menu"]);
  }

  public appLogout() {
    this.dlg.confirm("app_logout", "message_logout", () => {
      this.analytics.logEvent("logout", <TME_logout>{});
      this.sessionService
        .logoutAndRestart()
        .catch((error) => this.dbg.le("logout and restart error", error));
    });
  }

  public openCustomerSupport() {
    this.router.navigate(["/app/customer-support-menu"]);
  }

  public openTerms() {
    this.router.navigate(["/page/" + Env.PAGE_NAME_TOS]);
  }

  public openPrivacyPolicy() {
    this.router.navigate(["/page/" + Env.PAGE_NAME_PRIVACY_POLICY]);
  }

  public openBillingPage() {
    this.router.navigate(["/app/billing-history"]);
  }

  public openFAQ() {
    this.router.navigate(["/app/faq"]);
  }

  public openAppTour() {
    this.router.navigate(["/app/app-tour"]);
  }

  public openDebug() {}

  public openTestHelperPage() {}

  /**
   * Rate app on store function
   */
  public rateOnStore() {
    switch (this.misc.devicePlatform) {
      case "ios":
        Browser.open({ url: Env.APP_IOS_PAGE, windowName: "__self" });
        break;

      case "android":
        AppLauncher.openUrl({ url: Env.APP_ANDROID_PAGE });
        break;

      default:
        this.dlg.showToast("", "no store for current platform");
        break;
    }
  }

  public enableLanguageMenu() {}

  public updateSettings() {
    this.accountService.saveSettings();
  }

  public getVersion() {
    return Env.APP_VERSION + (Env.BADGE ? " " + Env.BADGE : "");
  }

  public openProfileVerificationPage() {
    this.router.navigate(["/app/profile-verification"]);
  }
}
