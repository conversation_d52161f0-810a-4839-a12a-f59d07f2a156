import { Component, ElementRef, OnInit, ViewChild } from "@angular/core";
import { LoaderService } from "src/app/services/loader.service";
import { AccountService } from "../../services/account.service";
import { StateService } from "src/app/services/state.service";
import { NavigationEnd, Router } from "@angular/router";
import { Utils } from "src/app/helpers/utils";
import Swiper from "swiper";
import { filter } from "rxjs";

@Component({
  selector: "app-my-profile",
  templateUrl: "./my-profile.page.html",
  styleUrls: ["./my-profile.page.scss"],
  standalone: false,
})
export class MyProfilePage implements OnInit {
  @ViewChild("swiper")
  swiperRef: ElementRef | undefined;
  swiper: Swiper;

  constructor(
    public loaderService: LoaderService,
    public accountService: AccountService,
    public stateService: StateService,
    public router: Router,
  ) {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        if (event.url === "/app/tabs/my-profile") {
          setTimeout(() => {
            this.swiper?.update();
            this.swiper?.scrollbar.setTranslate();
          }, 10);
        }
      });
  }

  swiperReady() {
    setTimeout(
      () => (this.swiper = this.swiperRef?.nativeElement.swiper),
      1000,
    );
  }

  ngOnInit() {}

  public showMore = false;
  public utils = Utils;

  public imageClicked(i) {
    this.stateService.set(
      "profile",
      this.accountService.account.username,
      this.accountService.account,
    );

    this.router.navigate([
      "/app/photo-gallery",
      this.accountService.account.username,
    ]);
  }

  public openMyProfileEditPage(): void {
    this.router.navigate(["/app/my-profile-edit"]);
  }
}
