h6 {
  font-weight: 600;
}

.profile-progress {
  position: absolute;
  top: 12px;
  right: 0;
}

.slide {
  width: 100%;
  position: relative;
  width: 100%;
  height: 0;
  margin-top: -100%;
  padding-top: 100%;

  ion-img,
  img {
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
  }

}

.status-indicator {
  background-color: var(--ion-color-light-shade);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  vertical-align: middle;

  &.active {
    background-color: var(--ion-color-success);
  }

}

.user-location * {
  vertical-align: middle;
}

.details {
  ion-text {
    display: block;
  }

}

.action-buttons {
  max-width: 400px;
  margin-right: auto;
  margin-left: auto;
}

ion-text {
  line-height: 1.3;
}

ion-popover {

  ion-content {
    font-size: .8rem;
  }
}

.profile-progress-percentage {
  --width: 280px;
}