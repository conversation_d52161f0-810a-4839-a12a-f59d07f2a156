<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>{{ "title_my_profile" | translate }}</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="openMyProfileEditPage()">
        <ion-icon name="pencil"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  @if (accountService.account) {
    <div [class.hidden]="this.loaderService.isLoading()">
      <swiper-container
        #swiper
        id="profile-photos"
        (swiperafterinit)="swiperReady()"
        [pager]="accountService.account.photos.length > 1"
        [scrollbar]="true"
        >
        @if (accountService.account.photos.length == 0) {
          <swiper-slide>
            <div>
              <ion-img
                style="pointer-events: none"
                src="/assets/img/avatar.svg"
                (click)="openMyProfileEditPage()"
              ></ion-img>
            </div>
          </swiper-slide>
        }
        @for (photo of accountService.account.photos; track photo; let i = $index) {
          <swiper-slide
            >
            <div
              (click)="imageClicked(i)"
              [class.hidden]="accountService.account.photos.length < 1"
              >
              <ion-img
                [src]="photo.thumb_url"
                style="pointer-events: none"
              ></ion-img>
            </div>
          </swiper-slide>
        }
        <!-- If we need pagination -->
      </swiper-container>
      <ion-grid class="ion-no-padding grid-content">
        <ion-row class="border-bottom ion-padding-horizontal ion-padding-bottom">
          <ion-col>
            <h2>
              <b
                >{{ accountService.account.first_name ||
                accountService.account.username }},</b
                >
                {{ accountService.account?.age }}
                <span
                  [ngClass]="accountService.account.is_active ? 'active' : 'inactive'"
                  class="status-indicator"
                ></span>
                <ion-chip
                  id="profile-percentage"
                  color="primary"
                  class="profile-progress"
                  >
                  <ion-label>
                    {{ accountService.account.profile_progress_pct }}%
                  </ion-label>
                </ion-chip>
                <ion-popover
                  type="HTMLElement"
                  trigger="profile-percentage"
                  triggerAction="hover"
                  cssClass="profile-progress-percentage"
                  >
                  <ng-template>
                    <ion-content
                      class="ion-padding"
                  [innerHTML]="'profile_percentage' | translate: {'prc':
                  accountService.account.profile_progress_pct}"
                    ></ion-content>
                  </ng-template>
                </ion-popover>
              </h2>
              <div class="user-location">
                <ion-icon name="location-outline" color="primary"></ion-icon>
                <ion-text color="medium">
                  {{ accountService.account.city }}
                </ion-text>
              </div>
            </ion-col>
          </ion-row>
          @if (accountService.account.title || accountService.account.description) {
            <ion-row
              class="border-bottom ion-padding-horizontal ion-padding-bottom"
              >
              <ion-col>
                @if (accountService.account.title) {
                  <h6>
                    {{ accountService.account.title }}
                  </h6>
                }
                @if (accountService.account.description) {
                  <ion-text color="medium">
                    @if (accountService.account.description.length >= 500) {
                      {{(showMore) ? accountService.account.description :
                      accountService.account.description | slice:0:500}}
                      @if (!showMore) {
                        <span>...</span>
                      }
                      @if (!showMore) {
                        <a href="javascript:;" (click)="showMore=true"
                          >{{ "show_more" | translate }} »</a
                          >
                        }
                        @if (showMore) {
                          <a href="javascript:;" (click)="showMore=false">
                            « {{ "show_less" | translate }}</a
                            >
                          }
                        }
                        @if (accountService.account.description.length < 500) {
                          {{accountService.account.description}}
                        }
                      </ion-text>
                    }
                  </ion-col>
                </ion-row>
              }
              @if (accountService.account.interests && accountService.account.interests.length) {
                <ion-row
                  class="border-bottom ion-padding-horizontal ion-padding-bottom"
                  >
                  <ion-col>
                    <h6>{{ "my_interests" | translate }}</h6>
                    @for (interest of accountService.account.interests; track interest) {
                      <ion-chip
                        outline
                        color="medium"
                        >
                        <ion-label>{{ interest }}</ion-label>
                      </ion-chip>
                    }
                  </ion-col>
                </ion-row>
              }
              @for (item of accountService.account.details; track item) {
                <ion-row
                  class="border-bottom ion-padding-horizontal ion-padding-bottom"
                  >
                  <ion-col>
                    <h6>{{ item.title }}</h6>
                    @for (question of (item?.data ?? []); track question) {
                      <div>
                        @if (question?.question) {
                          <div class="details ion-padding-bottom">
                            <ion-text class="question">
                              <small>{{ question?.question }}</small>
                            </ion-text>
                            <ion-text color="medium" [innerHTML]="question.answer"></ion-text>
                          </div>
                        }
                      </div>
                    }
                  </ion-col>
                </ion-row>
              }
            </ion-grid>
          </div>
        }

        <div [class.hidden]="!this.loaderService.isLoading()">
          <swiper-container pagination="false">
            <swiper-slide>
              <div class="slide">
                <img src="assets/placeholder.svg" />
              </div>
            </swiper-slide>
          </swiper-container>

          <ion-grid class="ion-no-padding grid-content">
            <ion-row class="border-bottom ion-padding-horizontal ion-padding-bottom">
              <ion-col>
                <h2>
                  <ion-skeleton-text animated style="width: 60%"></ion-skeleton-text>
                </h2>
                <div class="user-location">
                  <ion-skeleton-text animated style="width: 40%"></ion-skeleton-text>
                </div>
              </ion-col>
            </ion-row>

            <ion-row class="border-bottom ion-padding-horizontal ion-padding-bottom">
              <ion-col>
                <h4>
                  <ion-skeleton-text animated style="width: 100%"></ion-skeleton-text>
                </h4>
                <ion-skeleton-text animated style="width: 60%"></ion-skeleton-text>
              </ion-col>
            </ion-row>

            <ion-row class="border-bottom ion-padding-horizontal ion-padding-bottom">
              <ion-col>
                <h4>
                  <ion-skeleton-text animated style="width: 40%"></ion-skeleton-text>
                </h4>
                <ion-skeleton-text animated style="width: 20%"></ion-skeleton-text>
              </ion-col>
            </ion-row>

            <ion-row class="border-bottom ion-padding-horizontal ion-padding-bottom">
              <ion-col>
                <h4>
                  <ion-skeleton-text animated style="width: 40%"></ion-skeleton-text>
                </h4>
                <ion-skeleton-text animated style="width: 20%"></ion-skeleton-text>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>
      </ion-content>
