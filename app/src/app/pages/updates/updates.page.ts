import { Component, OnInit } from "@angular/core";
import { UserUpdatesService } from "../../services/user-updates.service";
import { ListPaginator } from "../../services/lists/list-paginator";
import { Router } from "@angular/router";
import { ProfileService } from "src/app/services/profile.service";
import { AccountService } from "../../services/account.service";

@Component({
  selector: "app-updates",
  templateUrl: "./updates.page.html",
  styleUrls: ["./updates.page.scss"],
  standalone: false,
})
export class UpdatesPage implements OnInit {
  /**
   * Pagination list.
   */
  public listPaginator: ListPaginator;

  constructor(
    public userUpdatesService: UserUpdatesService,
    private router: Router,
    private profileService: ProfileService,
    private accountService: AccountService,
  ) {
    this.listPaginator = new ListPaginator(this.userUpdatesService.listUpdates);
  }

  ngOnInit() {}

  ionViewDidEnter() {
    if (!this.listPaginator.didLoad()) {
      this.listPaginator.next();
    }

    this.userUpdatesService.markAllUpdatesRead();
  }

  loadData(event) {
    // add load tada for Infinity scroll
  }

  public onClick(update) {
    let param2;
    if (update.param2) {
      param2 = JSON.parse(update.param2);
    }
    switch (update.name) {
      case "new_message":
      case "promo_messages":
        this.router.navigate(["/app/inbox"]);
        break;
      case "new_photo_like":
        let index = 0;
        this.accountService.account.photos.forEach((v, i) => {
          if (v.image_id === param2.image_id) index = i;
        });
        this.router.navigate([
          "/app/photo-full-view",
          "profile",
          update.param1,
          index,
        ]);
        break;
      case "new_photo_comment":
        this.router.navigate([
          "/app/photo-comments",
          update.param1,
          param2.gallery_id,
          param2.image_id,
        ]);

        break;
      case "new_photo_comment_reply":
        this.router.navigate([
          "/app/photo-comments",
          update.username,
          param2.gallery_id,
          param2.image_id,
        ]);
        break;
      default:
        if (
          this.profileService.isKnownSystemUser(update.username.toLowerCase())
        ) {
          return;
        }

        if (update?.username) {
          this.router.navigate(["/app/profile", update?.username]);
        }

        break;
    }
  }
}
