<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>{{ "title_updates" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="listPaginator.refresh($event)">
    <ion-refresher-content refreshingSpinner="crescent"></ion-refresher-content>
  </ion-refresher>

  <!-- empty container { -->
  <ion-grid
    class="statusMessage ion-text-center ion-padding"
    [class.hidden]="!this.listPaginator.isEmpty()"
    >
    <ion-row>
      <ion-col>
        <ion-icon color="primary" name="notifications-off-outline"></ion-icon>
        <h2>{{ 'no_user_updates_title' | translate }}</h2>
        <h6>{{ 'no_user_updates_description' | translate }}</h6>
      </ion-col>
    </ion-row>
  </ion-grid>
  <!-- } empty container -->

  <ion-list>
    @if (userUpdatesService.listUpdates.list$ | async; as listUpdates) {
      <ion-item-group
        class="ion-padding-bottom"
        >
        @for (update of listUpdates; track update) {
          <ion-item (click)="onClick(update)">
            <ion-avatar slot="start">
              <img
                src="{{ update.img }}"
                onerror="this.onerror=null;this.src='assets/img/avatar.svg';"
                />
            </ion-avatar>
            <ion-label>
              <h2>
                <span class="username">{{update.username}}</span
                  ><small class="date"
                  >{{ +update.ts * 1000 | date: "MMM d, y H:mm" }}</small
                  >
                </h2>
                <h3>{{update.msg}}</h3>
              </ion-label>
            </ion-item>
          }
        </ion-item-group>
      }
    </ion-list>

    <!-- Start Sceleton content before loading, but must be better option to do this like template -->
    <ion-list
      [class.hidden]="!(userUpdatesService.listUpdates.isLoading() && !listPaginator.didLoad())"
      >
      @for (_c of [].constructor(10); track _c) {
        <ion-item>
          <ion-avatar slot="start">
            <ion-skeleton-text></ion-skeleton-text>
          </ion-avatar>
          <ion-label>
            <h2>
              <ion-skeleton-text
                animated
                class="ion-float-left"
                style="width: 40%"
              ></ion-skeleton-text>
              <ion-skeleton-text
                animated
                class="ion-float-right"
                style="width: 20%"
              ></ion-skeleton-text>
            </h2>
            <p>
              <ion-skeleton-text animated style="width: 100%"></ion-skeleton-text>
            </p>
          </ion-label>
        </ion-item>
      }
    </ion-list>
    <!-- End Sceleton content here -->

    <ion-infinite-scroll
      [class.hidden]="listPaginator.didReachEnd()"
      threshold="100px"
      id="infinite-scroll"
      (ionInfinite)="listPaginator.next($event)"
      >
      <ion-infinite-scroll-content
        loadingSpinner="crescent"
      ></ion-infinite-scroll-content>
    </ion-infinite-scroll>

    <!-- error container { -->
    <ion-grid
      class="statusMessage ion-text-center"
      [class.hidden]="!userUpdatesService.listUpdates.wasError()"
      >
      <ion-row>
        <ion-col>
          <ion-icon color="primary" name="alert-circle-outline"></ion-icon>
          <h3>{{ 'error_list' | translate }}</h3>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-button color="primary" (click)="this.listPaginator.refresh()"
            >{{ 'refresh' | translate }}</ion-button
            >
          </ion-col>
        </ion-row>
      </ion-grid>
      <!-- } error container -->

      <div
        style="text-align: center; display: none"
        [class.hidden]="!listPaginator.didReachEnd()"
        >
        ¯\_(ツ)_/¯
      </div>
    </ion-content>
