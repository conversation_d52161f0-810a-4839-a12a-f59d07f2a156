ion-label {

	h2 {
		font-weight: bold;
	}

	&.main-label {
		font-size: 14px;
		font-weight: bold;
		text-transform: uppercase;
	}
}

ion-item-divider ion-label {
  padding: 28px 0 8px;
  margin: 0;
}

ion-item {
	h2 { position: relative; }
	.date {
		color: var(--ion-color-medium);
		font-weight: normal;
		position: absolute;
		top: 50%;
		right: 0;
		transform: translateY(-50%);
	}
}

.statusMessage {
  margin-top: 15vh;

  ion-icon {
    font-size: 7rem;
    opacity: .3;
  }

  h6 {
    color: var(--ion-color-medium);
  }
}
