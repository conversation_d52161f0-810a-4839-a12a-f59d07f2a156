import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { UpdatesPage } from "./updates.page";

const routes: Routes = [
  {
    path: "",
    component: UpdatesPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UpdatesPageRoutingModule {}
