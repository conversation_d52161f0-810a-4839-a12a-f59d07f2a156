<ion-content scroll-y="false">
  <ion-grid class="ion-justify-content-between ion-padding">
    <div class="welcome-bg"></div>

    <ion-row class="first-row ion-align-items-center">
      <ion-col class="ion-text-center">
        <div class="logo">
          <img src="assets/img/WelcomeLogo.svg" />
        </div>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center second-row">
      <ion-col align-self-center size-md="6" size-lg="5" size-xs="12">
        <ion-text class="ion-text-center">
          <ion-row class="ion-justify-content-center">
            <ion-col>
              <ion-chip
                outline
                class="lang-selector"
                (click)="lang.showLanguageSelectDialog()"
              >
                <ion-icon name="earth-outline"></ion-icon>
                <ion-label
                  >{{ this.lang.supportedLanguages[this.lang.appLanguage]
                  }}</ion-label
                >
              </ion-chip>
            </ion-col>
          </ion-row>
        </ion-text>
        <div class="auth-buttons">
          <ion-button
            id="email_signup_button"
            class="ion-margin-vertical"
            expand="block"
            color="primary"
            (click)="signupemail()"
          >
            <ion-icon slot="start" name="mail"></ion-icon>
            <span>{{ 'sign_up_with_email' | translate }}</span>
          </ion-button>

          <ion-button
            [class.hidden]="!this.doShowAppleLogin()"
            id="apple_signup_button"
            class="ion-margin-vertical"
            expand="block"
            color="dark"
            (click)="loginapple()"
          >
            <ion-icon slot="start" name="logo-apple"></ion-icon>
            <span>{{ 'continue_with_apple' | translate }}</span>
          </ion-button>

          <ion-button
            id="email_signin_button"
            class="login ion-margin-vertical"
            expand="block"
            fill="outline"
            color="primary"
            (click)="loginemail()"
          >
            {{ 'memeber_login_here' | translate }}
          </ion-button>
        </div>
        <ion-text class="ion-text-center">
          <p class="notice-text">
            <span
              class="notice-text-second"
              [innerHTML]="'notice_text_second_link' | translate"
            ></span>
            <br /><br />v{{ getVersion() }}
          </p>
        </ion-text>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
