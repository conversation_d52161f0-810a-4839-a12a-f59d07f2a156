
.welcome-bg {
  background-image: url("/../assets/img/startBackground.jpg");
  background-size: cover;
  background-position: var(--welcome-bg-position);
  filter: var(--welcome-bg-blur);
  position: absolute;
  top: -30px;
  right: -30px;
  bottom: -30px;
  left: -30px;
  opacity: var(--welcome-bg-opacity);
}

ion-content{
  ion-grid {
    background-color: var(--welcome-bg-color);
    display: flex;
    flex-direction: column;
    height: 100%;

    .first-row {
      flex-grow: 1;
      flex-shrink: 0;
      flex-basis: auto;

      .logo {
        width:50%;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
      }
    }
    .second-row {
      flex-grow: 0;
      flex-shrink: 0;
      flex-basis: auto;

      ion-button {
        height: 46px;
        text-transform: none;
        margin: 12px 0;
      }
    }
    p {
      font-size: 0.75em;
    }
  }
}

.lang-selector {
  color: var(--welcome-lang-select-color);
  &.chip-outline {
    border-color: var(--welcome-lang-select-color);
  }

  ion-icon {
    color: var(--welcome-lang-select-color);
  }
}

.auth-buttons {
  margin: 16px 0;
}
