import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { AuthService } from "src/app/services/auth.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { MiscService } from "src/app/services/misc.service";
import { UtilsService } from "src/app/services/utils.service";
import { LanguageService } from "../../services/language.service";
import { environment as Env } from "src/environments/environment";
import { Debug } from "src/app/helpers/debug";
import { TrackingService } from "src/app/services/tracking.service";
import { StorageService } from "src/app/services/storage.service";
import { Utils } from "src/app/helpers/utils";
import { SignupWizardService } from "src/app/services/signup-wizard.service";

export const WAS_FIRST_RUN = "WAS_FIRST_RUN";

@Component({
  selector: "app-welcome",
  templateUrl: "./welcome.page.html",
  styleUrls: ["./welcome.page.scss"],
  standalone: false,
})
export class WelcomePage implements OnInit {
  private wasSubscribe: boolean = false;

  constructor(
    private auth: AuthService,
    private router: Router,
    private dialog: DialogHelperService,
    private utils: UtilsService,
    public lang: LanguageService,
    private misc: MiscService,
    private dbg: Debug,
    private trackingService: TrackingService,
    private storage: StorageService,
    private signupWizard: SignupWizardService,
  ) {}

  ngOnInit() {}

  public doShowAppleLogin() {
    return this.misc.devicePlatform !== "android";
  }

  public getVersion() {
    return Env.APP_VERSION + (Env.BADGE ? " " + Env.BADGE : "");
  }

  public setLinkFunctionality() {
    Utils.retryUntil(
      () => {
        // wait until the condition is meat and the elements is available
        let link1 = document.getElementsByClassName("terms")[0];
        let link2 = document.getElementsByClassName("privacy-policy")[0];

        return !!(link1 && link2);
      },
      () => {
        this.openLinkFromTranslation("terms");
        this.openLinkFromTranslation("privacy-policy");
      },
      50, // try max 20 times
      100, // every 250ms
    );
  }

  ionViewDidEnter() {
    if (!this.wasSubscribe) {
      this.lang.translate.onTranslationChange.subscribe({
        next: (res) => {
          // we fire it a bit later, when the ui update is done, then refresh the link handling
          setTimeout(() => {
            this.setLinkFunctionality();
          }, 500);
        },
      });

      this.wasSubscribe = true;
    }

    // we open the links after some delay to ensure we have the data

    this.setLinkFunctionality();

    // some delay to ensure we have the data

    /*
    setTimeout(() => {
      this.handleAppsflyerRedirects();
    }, 1000);
    */

    // wait until we have appsflyer data (or if we don't have it, try 10 times every 500ms)

    /**

     organic example:
     {"update_ts":1684253873,"INSTALL_APPSFLYER":{"install_time":"2023-05-16 16:17:50.865","update_ts":1684253873,"af_status":"Organic","af_message":"organic install","is_first_launch":true}

     non organic example:
     {"update_ts":1684255416,"INSTALL_APPSFLYER":{"retargeting_conversion_type":"none","update_ts":1684255416,"is_incentivized":"false","orig_cost":"0.0","creative_id":"1c205a72-540d-44e0-aa0a-ded095243c55","is_first_launch":true,"af_click_lookback":"7d","iscache":true,"click_time":"2023-05-16 16:42:45.982","af_banner_sdk_ver":"2","af_fp_lookback_window":"30m","match_type":"gp_referrer","af_channel":"af_web_banner","af_banner_config":"forward_utm","af_banner":"true","install_time":"2023-05-16 16:43:33.089","af_banner_build":"utm","media_source":"membership_free","is_smart_banner":"1","is_apple_login":"0","af_status":"Non-organic","cost_cents_USD":"0","campaign":"email_confirm_new_user","http_referrer":"https://rs.meetspiritualsingles.com/","is_retargeting":"false"}

     */

    Utils.retryUntil(
      () => {
        // wait until the condition is meat and the data is available
        console.log(
          "Retry condition",
          JSON.stringify(
            this?.trackingService?.currentData["INSTALL_APPSFLYER"],
          ),
        );
        return this?.trackingService?.currentData["INSTALL_APPSFLYER"];
      },
      () => {
        console.log("Execute appsflyer redirect");
        // this.api.setDebugData(this?.trackingService?.currentData);
        this.handleAppsflyerRedirects();
      },
      20, // try max 20 times
      250, // every 250ms
    );
  }

  // var to ensure we redirect only once
  private wasRedirected = false;
  private autologin_hash = "";

  public async handleFirstRun(): Promise<boolean> {
    // check if this is the first time we start the app

    let wasFirstRun = (await this.storage.get(WAS_FIRST_RUN)) || false;

    if (wasFirstRun) {
      this.dbg.l("was first run already");
      return Promise.resolve(false);
    }

    // we save this as first run
    await this.storage.set(WAS_FIRST_RUN, true);

    // we grab the autologin hash from appsflyer

    let media_source =
      this?.trackingService?.currentData?.INSTALL_APPSFLYER?.media_source || "";

    // custom param that can be used if media_source needs to differ from "website"
    let is_smart_banner =
      this?.trackingService?.currentData?.INSTALL_APPSFLYER?.is_smart_banner ||
      false;

    // we grab and unify the autologin hash

    this.autologin_hash = (
      this?.trackingService?.currentData?.INSTALL_APPSFLYER?.autologin_hash ||
      "[autologin_hash]"
    )
      .toLowerCase()
      .replace("[autologin_hash]", "");

    // debug

    this.dbg.l("media_source: " + media_source);
    this.dbg.l("autologin_hash: " + this.autologin_hash);
    this.dbg.l("is_smart_banner: " + is_smart_banner);

    // if the media source is not website we don't care

    if (
      // media_source.toLowerCase() != "website" &&
      // media_source.toLowerCase() != "appsflyer_sdk_test_int" &&
      // media_source.toLowerCase() != "af_banner" &&
      !is_smart_banner
    ) {
      this.dbg.l("it's not a supported smart banner or onelink");
      return Promise.resolve(false);
    }

    // all ok, we can handle redirection

    return Promise.resolve(true);
  }

  /**
   * Function that opens appropriate pages if the app was just installed
   * or tries to log the user in with existing session
   */
  private async handleAppsflyerRedirects() {
    console.log("Appsflyer data");
    console.log(this.trackingService.currentData["INSTALL_APPSFLYER"]);

    if (this.wasRedirected) return;

    this.wasRedirected = true;

    // do we redirect?
    if (!(await this.handleFirstRun())) {
      return;
    }

    // default is email
    let _type = "email";
    let _hash = "";

    // banner will have is_apple_login="1" value if the user's session was started with apple sign in on site
    let wasAppleSignInSession =
      this?.trackingService?.currentData?.INSTALL_APPSFLYER?.is_apple_login ==
      "1";

    // if it's apple os, and the user session with smartbanner was apple sign in, we show the apple login button
    if (this.doShowAppleLogin() && wasAppleSignInSession) {
      _type = "apple";
    }

    // unless we have a login hash, then we show that
    if (this.autologin_hash.length > 10) {
      _type = "website";
      _hash = this.autologin_hash;
    }

    this.router.navigate(["/login-direct"], {
      queryParams: {
        type: _type,
        hash: _hash,
      },
    });
  }

  public loginapple() {
    try {
      this.auth.apple();
    } catch (error) {
      this.dialog.showAlert(
        "error",
        this.utils.figureOutTheErrorMessage(error),
      );
    }
  }

  public loginemail() {
    this.router.navigate(["/login"]);
  }

  /*
  public loginemail() {
    this.router.navigate(["/login-direct"],{
      queryParams: {
        type: "apple"
      }
    });
  }
  */

  public signupemail() {
    this.signupWizard.reset();
    this.router.navigate(["/signup/email"]);
  }

  /**
   * Open link from translation file.
   *
   * @param id
   */
  private openLinkFromTranslation(id) {
    let link = document.getElementsByClassName(id)[0];

    if (!link) return false;

    link.addEventListener(
      "click",
      () => {
        window.event.preventDefault();

        let transform = {
          terms: Env.PAGE_NAME_TOS,
          "privacy-policy": Env.PAGE_NAME_PRIVACY_POLICY,
        };

        if (transform[id]) {
          this.router.navigate(["/page/" + transform[id]]);
        } else {
          this.dbg.le("Bad link on welcome page", id);
        }
      },
      false,
    );

    return true;
  }
}
