import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { DeactivationPage } from "./deactivation.page";

const routes: Routes = [
  {
    path: "",
    component: DeactivationPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DeactivationPageRoutingModule {}
