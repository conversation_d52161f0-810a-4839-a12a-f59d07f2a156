<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ "title_account_deactivate" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row class="ion-justify-content-center">
      <ion-col size-sm="6" class="ion-text-center ion-padding-bottom">
        <div>
          <h1>{{ "account_deactivate" | translate }}</h1>
          <h5>{{ "account_deactivate_intro" | translate }}</h5>
        </div>
        <div class="ion-margin-top ion-padding-top">
          <h3>{{ "account_deactivate_confirm" | translate }}</h3>
          <p>{{ "account_deactivate_why" | translate }}</p>
          <ion-item class="ion-text-center">
            <ion-input
              text-center
              [(ngModel)]="subject"
              type="text"
              name="subject"
              placeholder="{{'type_here' | translate}} ..."
            ></ion-input>
          </ion-item>
          <p class="ion-padding-top">
            {{ "account_deactivate_instructions" | translate }}
            <strong>OK</strong>
          </p>
          <ion-item class="ion-margin-bottom">
            <ion-input
              class="ion-text-center"
              name="confirmation"
              [(ngModel)]="confirm"
              (ionInput)="isConfirmed($event)"
              type="text"
            ></ion-input>
          </ion-item>
          <ion-button
            type="submit"
            expand="block"
            (click)="deactivateAccount()"
            [disabled]="! confirmed"
          >
            {{ "account_deactivate" | translate }}
          </ion-button>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
