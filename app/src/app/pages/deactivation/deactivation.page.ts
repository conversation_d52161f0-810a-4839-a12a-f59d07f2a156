import { Component, OnInit } from "@angular/core";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { AccountService } from "../../services/account.service";
import { ApiParams } from "../../services/api/api.params";
import { Debug } from "../../helpers/debug";
import { SessionService } from "../../services/session.service";

@Component({
  selector: "app-deactivation",
  templateUrl: "./deactivation.page.html",
  styleUrls: ["./deactivation.page.scss"],
  standalone: false,
})
export class DeactivationPage implements OnInit {
  /**
   * Deactivation reason params.
   *
   * @type {{subject: string; body: string}}
   */
  public accountDelete: ApiParams.AccountDeactivate.type | any = {
    subject: "",
  };

  /**
   * Subject.
   *
   * @type {string}
   */
  public subject: string = "";

  /**
   * @type {string}
   */
  public confirm: string = "";

  /**
   * Confirmed.
   *
   * @type {boolean}
   */
  public confirmed: boolean = false;

  constructor(
    public dialog: DialogHelperService,
    public account: AccountService,
    public dbg: Debug,
    public session: SessionService,
  ) {}

  ngOnInit() {}

  public deactivateAccount() {
    this.dialog.showLoading("processing_please_wait", "🤓");

    this.confirmed = false;

    this.accountDelete.subject = this.subject.length
      ? this.subject
      : "NO_REASON";

    this.account
      .deactivate(this.accountDelete)
      .then(({ messages }) => {
        if (messages.length) {
          this.dialog.hideLoading();
          this.dialog.showToast("", messages[0].message);
        }

        setTimeout(() => {
          this.session.logoutAndRestart();
        }, 3000);
      })
      .catch((error) => {
        this.dialog.hideLoading();
        this.dialog.showToastBaseOnErrorResponse(error);
        this.dbg.le("deactivation error", error);
      });
  }

  /**
   * Check confirmation.
   *
   * @param e
   */
  public isConfirmed(e) {
    return (this.confirmed = e.detail.value.trim().toLowerCase() === "ok");
  }
}
