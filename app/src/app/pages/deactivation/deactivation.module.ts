import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { DeactivationPageRoutingModule } from "./deactivation-routing.module";

import { DeactivationPage } from "./deactivation.page";

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    DeactivationPageRoutingModule,
  ],
  declarations: [DeactivationPage],
})
export class DeactivationPageModule {}
