.toolbar-title {
  display: flex;
  justify-content: center;
}

.avatarUsername {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.userName {
  font-size: 17px;
  font-weight: 600;
  line-height: 1;
}

.avatar {
  display: inline-block;
  margin-right: 8px;
}

.chatContainer {
  --ion-grid-padding: 16px
}

.bubbleWrapper {

  justify-content: flex-start;

  &.ownMessage {
    justify-content: flex-end;
  }
}

.chatMessage {
  width: auto;
  min-width: 130px;
  flex: 0 1 auto;
  background-color: var(--ion-color-light);
  padding: 8px 12px;
  margin-bottom: 6px;
  margin-right: 20%;
  font-size: 14px;
  text-align: left;
  border-radius: 16px;
  color: #000000;
}

.bubbleWrapper:not(.ownMessage) + .ownMessage,
.ownMessage + .bubbleWrapper:not(.ownMessage) {
  margin-top: 12px;
}

.chatMessage.ownMessage {
  background-color: rgba(var(--ion-color-primary-rgb), 0.2);
  color: var(--ion-color-primary);
  margin-right: 0;
  margin-left: 20%;
}

.messageText {
  font-size: 14px;
  display: inline-block;
  letter-spacing: .2px;
  word-break: break-word;
  margin-right: 8px;
}

.chatImage {
  padding: 5px;
  display:block;
  border-radius: 10px;
  height: 10em !important;
  width: auto !important;
  object-fit: contain !important;
}

.chatTime {
  display: inline-block;
  opacity: .6;
  padding-top: 8px;
}

.messageText + .chatImage {
  margin-top: 8px;
}

.messageInput {
  background-color: var(--ion-color-light);
  border-radius: 18px;
  font-size: 14px;
  --padding-start: 10px !important;
  --padding-end: 10px !important;
  width: calc(100% - 80px);
}

.native-input.sc-ion-input-ios {
  padding: 10px !important;
}

.sc-ion-input-md-h, .sc-ion-input-md-s {
  min-height: 40px;
}

.laodchatMessages {
  margin-top: 10px;

  ion-skeleton-text {
    padding: 12px;
    --border-radius: 20px;
  }
}

.scroll-x {
  overflow-x: scroll;
}

.attach-images {
  min-width: 56px;
  min-height: 56px;
  width: 56px;
  height: 56px;
  position: relative;
  margin-right: 8px;

  img {
    border-radius: 8px;
  }

  ion-icon {
    position: absolute;
    right: 4px;
    top: 4px;
    padding: 1px;
    background: rgba(0,0,0,.5);
    border-radius: 4px;
  }
}

.attachmentContainer {
  position: relative;
  max-width: 130px;
}
.attachmentEdit {
  background-color: var(--ion-color-primary);
  color: var(--ion-color-white);
  border-radius: 50%;
  padding: 4px;
  float:right;
  position: absolute;
  bottom: 0;
  right:0;
}

ion-footer {
  ion-toolbar {
    ion-icon {
      font-size: 1.5rem;
    }
  }
}