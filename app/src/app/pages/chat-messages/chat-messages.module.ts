import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { ChatMessagesPageRoutingModule } from "./chat-messages-routing.module";

import { ChatMessagesPage } from "./chat-messages.page";
import { TranslateModule } from "@ngx-translate/core";
import { ExternalContentComponentModule } from "src/app/components/external-content/external-content.component.module";

import { ProfileMenuComponentModule } from "src/app/components/profile-menu/profile-menu.component.module";
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ChatMessagesPageRoutingModule,
    TranslateModule.forChild(),
    ExternalContentComponentModule,
    ProfileMenuComponentModule,
  ],
  declarations: [ChatMessagesPage],
})
export class ChatMessagesPageModule {}
