<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <div class="avatarUsername" (click)="openProfile()">
      <ion-avatar class="avatar" style="width: 22px; height: 22px">
        <img
          [src]="(this.userProfile?.avatar_url || 'assets/img/avatar.svg')"
          />
      </ion-avatar>
      <span class="userName"
        >{{ this.userProfile?.first_name ||
        this.chatContactItem?.username}}</span
        >
      </div>
      <ion-buttons slot="end">
        <ion-spinner
          name="crescent"
          [class.ion-hide]="!this.isLoading()"
        ></ion-spinner>
        <app-profile-menu
          [class.ion-hide]="this.isLoading() || this.isSystemUser()"
          [profileData]="this.userProfile"
        ></app-profile-menu>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>

  <ion-content #content>
    <ion-list
      [class.ion-hide]="!this.isLoading()"
      lines="none"
      class="laodchatMessages"
      >
      @for (_m of this.loaderArray; track _m) {
        <ion-item>
          <ion-skeleton-text
            [slot]="_m.slot"
            [style]="_m.style"
            animated
          ></ion-skeleton-text>
        </ion-item>
      }
    </ion-list>

    <ion-infinite-scroll
      [class.ion-hide]="this.paginatedList.didReachEnd() || this.isLoading()"
      threshold="50px"
      id="infinite-scroll"
      position="top"
      (ionInfinite)="this.paginatedList.next($event)"
      >
      <ion-infinite-scroll-content></ion-infinite-scroll-content>
    </ion-infinite-scroll>

    @if (this.chatMessageList.list$ | async; as msgList;) {
      <ion-list
        >
        <ion-grid class="chatContainer">
          @for (msg of msgList; track msg; let j = $index) {
            <ion-row
              class="bubbleWrapper"
              [class.ownMessage]="msg.isOwn"
              >
              <ion-col class="chatMessage" [class.ownMessage]="msg.isOwn">
                @if ((this.isSystemUser() || msg.system_msg) && msg) {
                  <app-external-content
                    [content]="msg.text"
                    (linkClick)="this.linkClick($event)"
                  ></app-external-content>
                }
                <app-external-content
                  class="messageText"
                  [content]="(this.isSystemUser() || msg.system_msg) ? '' : msg.text"
                  [doRemoveAnchor]="true"
                ></app-external-content>
                @for (img of msg.attachments; track img; let i = $index) {
                  <span>
                    <div class="attachmentContainer">
                      <div (click)="openPhoto(j , i)">
                        @if (img?.thumb_url) {
                          <img
                            class="chatImage"
                            [src]="img?.thumb_url"
                            style="pointer-events: none"
                            />
                        }
                      </div>
                      <ion-icon
                        name="pencil"
                        class="attachmentEdit"
                        (click)="handleImgClick(img)"
                        [class.hidden]="!msg.isOwn"
                      ></ion-icon>
                    </div>
                  </span>
                }
                <ion-row>
                  <ion-col class="ion-no-padding">
                    <small class="chatTime"
                      >{{msg.time*1000 | date:'shortTime'}}</small
                      >
                    </ion-col>
                    <ion-col class="ion-no-padding ion-text-end">
                      <small class="chatTime"
                        >{{msg.time*1000 | date:'shortDate'}}</small
                        >
                      </ion-col>
                    </ion-row>
                  </ion-col>
                </ion-row>
              }
            </ion-grid>
          </ion-list>
        }
      </ion-content>

      <ion-footer [class.ion-hide]="this.doShowFooter() || this.isSystemUser()">
        <div
          style="height: 55px; text-align: center; padding-left: 5%; padding-top: 5px"
          >
          <ion-skeleton-text
            animated
            style="width: 95%; height: 35px; border-radius: 17px"
          ></ion-skeleton-text>
        </div>
      </ion-footer>

      <ion-footer [class.ion-hide]="!this.doShowFooter()">
        <ion-progress-bar
          [value]="this.pictureService.percentage/100"
          [class.ion-hide]="this.pictureService.percentage==100 || this.pictureService.percentage ==0"
        ></ion-progress-bar>
        <ion-toolbar [class.ion-hide]="this.canSendMessage()">
          <ion-button
            color="primary"
            expand="block"
            (click)="this.openUpgradeDialog()"
            >
            {{ ( this.chatMessageList.isStoredListEmpty() ? 'upgrade_to_message' :
            'upgrade_reply' ) | translate }}
          </ion-button>
        </ion-toolbar>
        <ion-toolbar [class.ion-hide]="!this.canSendMessage()" color="white">
          @if (attachImages?.length) {
            <ion-item class="scroll-x">
              @for (img of attachImages; track img) {
                <ion-thumbnail
                  class="attach-images"
                  width="56"
                  height="56"
                  (click)="removeFromAttachImages(img)"
                  >
                  <img [src]="img.image_url" />
                  <ion-icon name="close" color="white"></ion-icon>
                </ion-thumbnail>
              }
            </ion-item>
          }
          <ion-item
            [disabled]="this.chatMessagesService.isSending() || this.isLoading() || this.pictureService.isPhotoUploading() || this.delayHelper.isActive"
            lines="none"
            >
            <ion-button (click)="openPicturesPage()" fill="clear">
              <ion-icon name="image" slot="icon-only"></ion-icon>
            </ion-button>
            <ion-input
              position="floating"
              [(ngModel)]="this.textToSend"
              class="messageInput"
              placeholder="{{'your_message' | translate}}"
              maxlength="2048"
              aria-label="send message"
            ></ion-input>

            <ion-button
              [class.ion-hide]="this.delayHelper.isActive"
              (click)="sendMessage()"
              fill="clear"
              >
              <ion-icon name="send" slot="icon-only"></ion-icon>
            </ion-button>

            <ion-label [class.ion-hide]="!this.delayHelper.isActive" fill="clear"
              >[ {{ this.delayHelper.delay }} ]</ion-label
              >
            </ion-item>
          </ion-toolbar>
        </ion-footer>
