import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, ViewChild } from "@angular/core";
import { ChatMessageService } from "../../services/chat-message.service";
import { ProfileBase } from "src/app/schemas/profile-base";
import { Activated<PERSON><PERSON><PERSON>, Router } from "@angular/router";
import _ from "lodash";
import { StateService } from "../../services/state.service";
import { ChatContactItem } from "../../services/chat-contact-item.interface";
import { ChatService } from "../../services/chat.service";
import { ProfileService } from "../../services/profile.service";
import { ChatMessageItemList } from "../../services/lists/chat-messages.item-list";
import { ApiService } from "../../services/api/api.service";
import { Debug } from "../../helpers/debug";
import { ApiCommands } from "src/app/services/api/api.commands";
import { ListPaginator } from "../../services/lists/list-paginator";
import { ChatIncomingService } from "src/app/services/chat-incoming.service";
import { filter } from "rxjs/operators";
import { ChatMessage } from "src/app/services/chat-message.interface";
import { UtilsService } from "../../services/utils.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { AccountService } from "src/app/services/account.service";
import { PictureService } from "src/app/services/picture.service";
import { PictureUploadHelperService } from "src/app/picture-upload-helper.service";
import { PurchaseService } from "src/app/services/purchase.service";
import { DelayHelperService } from "src/app/services/delay-helper.service";
import { MiscService } from "src/app/services/misc.service";
import { firstValueFrom } from "rxjs";
import { AnalyticsService } from "src/app/services/analytics.service";
import { TME_open_chat } from "src/app/classes/gtm-event.class";
import { NavController, ActionSheetController } from "@ionic/angular";
import { TranslateService } from "@ngx-translate/core";
import { DeeplinkService } from "src/app/services/deeplink.service";

@Component({
  selector: "app-chat-messages",
  templateUrl: "./chat-messages.page.html",
  styleUrls: ["./chat-messages.page.scss"],
  standalone: false,
})
export class ChatMessagesPage implements OnInit, OnDestroy {
  @ViewChild("content", { static: false }) contentRef: any;

  constructor(
    public chatMessagesService: ChatMessageService,
    private route: ActivatedRoute,
    private router: Router,
    private stateService: StateService,
    private chatService: ChatService,
    private profileService: ProfileService,
    private api: ApiService,
    private debug: Debug,
    private chatIncoming: ChatIncomingService,
    private utils: UtilsService,
    private accountService: AccountService,
    private dialog: DialogHelperService,
    public pictureService: PictureService,
    private pictureUploadHelper: PictureUploadHelperService,
    private purchaseService: PurchaseService,
    public delayHelper: DelayHelperService,
    private miscService: MiscService,
    private analytics: AnalyticsService,
    private navCtrl: NavController,
    public translate: TranslateService,
    public actionSheetCtrl: ActionSheetController,
  ) {}

  public loaderArray = [];

  private _canSendMesage: boolean = true;

  public loaderStartEnd() {
    return Math.random() * 100 < 50 ? "start" : "end";
  }

  public loaderStyle() {
    return (
      "width: " +
      Math.floor(Math.random() * 50 + 30) +
      "%; height: " +
      Math.floor(1 + Math.random() * 3) * 20 +
      "%;"
    );
  }

  public canSendMessage() {
    return this._canSendMesage;
  }

  public openUpgradeDialog() {
    this.purchaseService.setDialogType("messages_limit").openUpgradeDialog(
      () => {},
      () => {},
    );
  }

  public isSystemUser() {
    return this.profileService.isKnownSystemUser(this?.userProfile?.username);
  }

  public scrollToBottom() {
    setTimeout(() => {
      if (this.chatMessageList.getStorage().length <= 1) return;

      this.contentRef.scrollToBottom(500);
    }, 500);
  }

  public chatMessageList: ChatMessageItemList = new ChatMessageItemList(
    this.api,
    ApiCommands.MessagesGet,
    this.debug,
  );

  public paginatedList: ListPaginator = new ListPaginator(
    this.chatMessageList,
    50,
  );

  // page data

  private username: string;
  public userProfile: ProfileBase.type;
  public chatContactItem: ChatContactItem;

  // input stuff

  public textToSend: string = "";
  public threadId: string = null;

  // figure out latest thread id ...
  private getLatestThreadId() {
    let latestThreadId = this.chatMessageList.isStoredListEmpty()
      ? { thread_id: 0 }
      : _.last(this.chatMessageList.getStorage());

    if (_.isEmpty(this.threadId) || this.threadId == "0") {
      this.threadId = "" + latestThreadId?.thread_id;
    }

    return this.threadId;
  }

  public hasData() {
    return !this.chatMessageList.isStoredListEmpty();
  }

  /**
   * Send message to user
   */
  public sendMessage(attachment: any = null) {
    if (!this.username && !this.userProfile.username) return;

    attachment = attachment
      ? [attachment]
      : this.chatMessagesService.selectedAttachImages;

    if (!attachment.length && !this.textToSend) return;

    this.chatMessagesService
      .sendMessage(
        {
          username: this.username || this.userProfile.username,
          subject: null,
          body: this.textToSend ?? "",
          image_ids: !attachment
            ? []
            : _.map(
                this.chatMessagesService.selectedAttachImages,
                (img) => img.image_id,
              ), // @todo add attachments
          thread_id: this.getLatestThreadId(),
          delivery_notify: true,
        },
        attachment,
      )
      .then((data) => {
        // set thread id if we did not have any till now

        if (!this.threadId) {
          data.data.thread_id;
        }

        this.textToSend = "";

        this.scrollToBottom();
      })
      .catch((e) => {
        this.dialog.showToastBaseOnErrorResponse(e);
      })
      .then(() => {
        this.attachImages = [];
        this.chatMessagesService.attachImages = [];
        this.chatMessagesService.selectedAttachImages = [];
      });
  }

  public wasError() {
    return this.chatMessagesService.wasGetError(this.userProfile?.user_id);
  }

  public isLoading() {
    return (
      !this.hasData() &&
      !this.wasError() &&
      (this.profileService.isGetInProgress(this.userProfile?.username) ||
        this.chatMessagesService.isInProgress() ||
        this.chatService.isLoading())
    );
  }

  private setChatContactItemData(): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.chatContactItem = this.chatService.getChatContactItem(this.username);

      if (!_.isEmpty(this.chatContactItem)) {
        resolve({});
        return;
      }

      firstValueFrom(this.chatService.getThreadList())
        .then((data) => {
          this.chatContactItem =
            _.find(data, {
              username: this.username,
            }) || null;

          resolve({});
          return;
        })
        .catch((e) => {
          reject(e);
        });
    });
  }

  private setProfileData(): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      let cached_userProfile = this.stateService.get("profile", this.username);

      if (!_.isEmpty(cached_userProfile)) {
        this.userProfile = cached_userProfile;
        resolve({});
        return;
      }

      firstValueFrom(this.profileService.get(this.username, this.userProfile))
        .then((data) => {
          this.userProfile = data?.data;
          resolve({});
          return;
        })
        .catch((e) => {
          reject(e);
        });
    });
  }

  public attachImages: any[] = [];

  private _interval = null;

  private scrollWhenLoaded() {
    this._interval = setInterval(() => {
      if (!this.isLoading()) {
        clearInterval(this._interval);
        this.scrollToBottom();
      }
    }, 500);
  }

  private init() {
    // generate random loader array

    let loaderSize = 10;

    for (let i = 0; i < loaderSize; i++) {
      this.loaderArray.push({
        slot: this.loaderStartEnd(),
        style: this.loaderStyle(),
      });
    }

    this.username = this.route.snapshot.params?.username;
    this.userProfile = this.stateService.get(
      "chat_message_open",
      this.username,
    );
    this.chatMessageList.convertItems = (items) => {
      for (let i = 0; i < items.length; i++) {
        let x = this.chatMessagesService.converToChatMessage(items[i]);
        items[i] = _.merge(items[i], x);
      }

      return items;
    };
  }

  public async ionViewDidEnter() {
    this.delayHelper.clearDelayInterval();

    // send stats for open chat
    this.analytics.logEvent("open_chat", <TME_open_chat>{
      page_name: this.router.url,
    });
  }

  public async ionViewWillEnter() {
    // Remove sendMessageProfile if exists.
    this.stateService.delete("sendMessageProfile", null);

    this.init();

    this.attachImages = this.chatMessagesService.getSelectedAttachableImages();

    this.scrollWhenLoaded();

    this.chatMessageList.clearStorage();

    await this.setChatContactItemData();

    await this.setProfileData();
    let thread_id: number | null = this.route.snapshot.params?.thread_id;
    this.chatMessageList.changeParams({
      sort_by: "msg_ts",
      sort_dir: "desc",
      thread_id: thread_id,
      // we're not sending this, because we want to get all the messages, not only specific thread
      //thread_id: this.threadData.thread_id,
      user_id: this?.userProfile?.user_id ?? this?.chatContactItem?.user_id,
    });

    // reset list
    this.paginatedList.reset();

    // fetch elements
    this.paginatedList.next(
      null,
      () => {
        this.scrollToBottom();

        // mark thread as read
        this.chatMessagesService.markAsRead([
          parseInt(this.getLatestThreadId()),
        ]);

        // check if we can send message to the user
        this.chatMessagesService
          .canSendMessage(this.getLatestThreadId())
          .then(() => {
            this._canSendMesage = true;
            this.miscService.ngForceTemplateUpdate();
            this.scrollToBottom();
          })
          .catch(async (e) => {
            // Premium acc with invalid email address.
            // error message - You need to have valid email address.
            if (e.error?.errors[0]?.key == "error_force_email_update") {
              this._canSendMesage = true;
            } else {
              this._canSendMesage = false;
              this.miscService.ngForceTemplateUpdate();
              this.scrollToBottom();
              this.stateService.set(
                "sendMessageProfile",
                null,
                this.userProfile,
              );
            }
          });
      },
      async (error) => {
        // Show upgrade with user's profile
        this.stateService.set("sendMessageProfile", null, this.userProfile);
        // error => 'cant_read_messages'
        // Close the chat-message page so we don't fall into an infinite cycle when we return
        // from the upgrading page, and this function automatically reopen the upgrade page.
        await this.navCtrl.pop();
        // Open upgrade page.
        this.purchaseService
          .setDialogType("upgrade_to_read")
          .openUpgradeDialog((error) => {
            this.dialog.showToastBaseOnErrorResponse(error, null, false);
          });
      },
    );

    // mark messages as received and read
    this.chatMessagesService.setReadTs(this.userProfile.user_id); //this.chatContactItem.user_id);

    // se if we subscribed already, to avoid double subscription

    let oldSub = this.stateService.get(
      "chat_messages_subscribed",
      this.username,
      null,
    );

    if (oldSub) {
      oldSub.unsubscribe();
    }

    // subscribe to incoming message stuff

    let subscription = this.chatIncoming.messageStream$
      .pipe(
        filter((data) => {
          // add to the list only if the window open with the given user

          return (
            (data.userId == this.userProfile.user_id ||
              data.userId == this.accountService.account.user_id) &&
            (this.utils.isChatWithUserActive(this.username) ||
              this.utils.isChatWithUserActive(data["target_username"]))
          );
        }),
      )
      .subscribe((data: ChatMessage | any) => {
        this.debug.l("incoming message", data);

        // mark messages as received and read
        this.chatMessagesService.setReadTs(data.userId);

        // mark thread as read
        this.chatMessagesService.markAsRead([
          parseInt(this.getLatestThreadId()),
        ]);

        this.chatMessageList.addItem(data);

        this.scrollToBottom();
      });

    this.stateService.set(
      "chat_messages_subscribed",
      this.username,
      subscription,
    );
  }

  ionViewDidLeave() {
    this.chatMessagesService.clearAttachableImages();
  }

  public doShowFooter() {
    return !(
      this.isSystemUser() || this.chatMessagesService.isCanSendInProgress()
    );
  }

  public openPhoto(j = 0, i = 0) {
    this.stateService.set(
      "pictureCollection",
      this.username,
      this.chatMessageList.getStorage()[j].attachments,
    );
    this.stateService.set("profile", this.username, this.userProfile);

    this.router.navigate([
      "/app/photo-full-view",
      "photo_list",
      this.username,
      i,
    ]);
  }

  public openProfile() {
    if (this.isLoading() || this.isSystemUser()) {
      return;
    }

    let profile = this.userProfile;

    // store it in state so we don't have to re-fetch it
    this.stateService.set("profile", profile.username, profile);

    // open profile
    this.router.navigate(["/app/profile", profile.username]);
  }

  /**
   * Open pictures page.
   */
  public openPicturesPage() {
    this.router.navigate(["/app/pictures"]);
  }

  /**
   * Upload attachment.
   */
  public photoUpload() {
    this.pictureUploadHelper.displayUploadMenu((data) => {
      firstValueFrom(
        this.pictureService.uploadAttachment({
          image_data: data,
        }),
      )
        .then((data) => {
          let sendData = {
            body: "",
            delivery_notify: false,
            subject: "",
            thread_id: this.getLatestThreadId(),
            username: this.username,
            image_ids: [data.data.photo.image_id],
          };

          this.sendMessage(data.data.photo);
        })
        .catch((err) => {
          this.dialog.showToastBaseOnErrorResponse(err);
        });
    }, true);
  }

  ngOnInit() {}
  ngOnDestroy() {}

  public removeFromAttachImages(img): void {
    this.attachImages = _.filter(
      this.attachImages,
      (data) => data.image_id !== img.image_id,
    );
    this.chatMessagesService.removeFromSelectedImages(img);
  }

  public async handleImgClick(img) {
    const actionSheet = await this.actionSheetCtrl.create({
      header: this.translate.instant("profile_edit_image_title"),
      buttons: this.buttonsConfig(img),
    });

    await actionSheet.present();

    const { role, data } = await actionSheet.onDidDismiss();
  }

  /**
   * Set buttons config.
   *
   * @param img
   * @returns {{text: string; role: string; handler: (() => any)}[]}
   */
  private buttonsConfig(img) {
    let config = [
      {
        text: this.translate.instant("edit_photo"),
        handler: () => {
          this.stateService.set(
            "photo",
            this.accountService.account.username,
            img,
          );
          this.router.navigate(["/app/photo-editor"]);
        },
      },
      {
        text: this.translate.instant("cancel_action"),
        role: "cancel",
        handler: () => {},
      },
    ];
    return config;
  }

  public linkClick(deeplink: DeeplinkService) {
    this.debug.l("message click register", deeplink.getParamObject());
    this.analytics.messageClick(deeplink.getParamObject());
  }
}
