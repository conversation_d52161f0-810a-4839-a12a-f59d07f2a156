import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { LocationCountryPage } from "./location-country.page";

const routes: Routes = [
  {
    path: "",
    component: LocationCountryPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LocationCountryPageRoutingModule {}
