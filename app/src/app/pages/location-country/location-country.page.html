<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        text=""
        icon="chevron-back-sharp"
        (click)="locationService.filter = ''"
      ></ion-back-button>
    </ion-buttons>
    <ion-title>{{ "title_location_country" | translate }}</ion-title>
    <ion-buttons slot="end">
      @if (locationService.isProcessing) {
        <ion-spinner
          name="crescent"
        ></ion-spinner>
      }
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list>
    <ion-item lines="none" class="ion-margin-vertical">
      <ion-searchbar
        [(ngModel)]="locationService.filter"
        [debounce]="500"
        (ionInput)="filterCountry()"
      ></ion-searchbar>
    </ion-item>

    @if (locationService.filteredList) {
      <ion-radio-group
        [value]="locationService.selectedCountry"
        (click)="countrySelected($event)"
        >
        @for (item of locationService.filteredList; track item) {
          <ion-item>
            <ion-radio [value]="item.key" [disabled]="locationService.isProcessing"
              >{{ item.value }}</ion-radio
              >
            </ion-item>
          }
        </ion-radio-group>
      }
    </ion-list>
  </ion-content>
