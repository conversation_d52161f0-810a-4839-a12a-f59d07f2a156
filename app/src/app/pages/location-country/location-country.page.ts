import { Component } from "@angular/core";
import { LocationService } from "../../services/location.service";
import { Debug } from "../../helpers/debug";
import { firstValueFrom, timer } from "rxjs";
import { AccountService } from "../../services/account.service";

@Component({
  selector: "app-location-country",
  templateUrl: "./location-country.page.html",
  styleUrls: ["./location-country.page.scss"],
  standalone: false,
})
export class LocationCountryPage {
  constructor(
    public locationService: LocationService,
    public dbg: Debug,
    public accountService: AccountService,
  ) {}

  private resetForm() {
    this.locationService.filter = "";
    this.locationService.setFilteredList("country_list");
    this.locationService.selectedCountry = this.accountService.account.country;
  }

  public ionViewWillEnter() {
    this.resetForm();
    this.locationService.resetSelectionData();
  }

  /**
   * Reset filter.
   */
  public onCancel() {
    this.locationService.setFilteredList("country_list");

    this.locationService.shouldShowCancel = false;
  }

  /**
   * Filter country list.
   */
  public filterCountry() {
    if (
      this.locationService.filter &&
      this.locationService.filter.trim() !== ""
    ) {
      this.locationService.shouldShowCancel = true;

      return (this.locationService.filteredList =
        this.locationService.countryList.filter((item) => {
          return item.value
            .toLowerCase()
            .includes(this.locationService.filter.toLowerCase());
        }));
    }

    this.locationService.filteredList = this.locationService.countryList;
  }

  /**
   * Select country.
   */
  public async countrySelected(e) {
    this.locationService.isProcessing = true;

    await firstValueFrom(timer(100));
    this.locationService.selectedCountry = e.target.value;

    this.locationService
      .locationHelper({
        get_countries: 1,
        country_code: this.locationService.selectedCountry,
      })
      .then(({ data }) => {
        this.locationService.location.selected_country =
          this.locationService.selectedCountry;

        this.locationService.isProcessing = false;

        if (
          this.locationService.location.whatToShow.indexOf("zip_input") > -1
        ) {
          this.locationService.nextPage = "location-zip";
        }

        if (
          this.locationService.location.whatToShow.indexOf("city_select") > -1
        ) {
          this.locationService.nextPage = "location-city";
        }

        this.locationService.filter = "";

        this.locationService.next();
      })
      .catch((error) => this.dbg.le("city select error", error))
      .then(() => (this.locationService.isProcessing = false));
  }
}
