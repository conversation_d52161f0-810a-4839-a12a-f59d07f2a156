import { Component, OnInit } from "@angular/core";
import _ from "lodash";
import { SignupWizardPageBase } from "../signupBase";
import { GenderItem } from "src/app/schemas/gender-item";
import { environment as Env } from "../../../../environments/environment";
import { FormBuilder, Validators } from "@angular/forms";
import { SignupWizardGuardService } from "src/app/services/signup-wizard.guard.service";
import { Router } from "@angular/router";
import { Debug } from "src/app/helpers/debug";
import { AnalyticsService } from "src/app/services/analytics.service";
@Component({
  selector: "app-gender",
  templateUrl: "./gender.page.html",
  styleUrls: ["./gender.page.scss"],
  standalone: false,
})
export class GenderPage extends SignupWizardPageBase implements OnInit {
  public genderList: GenderItem[];

  constructor(
    public guard: SignupWizardGuardService,
    public formBuilder: FormBuilder,
    public route: Router,
    public debug: Debug,
    public analyticsService: AnalyticsService,
  ) {
    // initialize
    super();

    this.initPageParams();

    // set gender list
    this.genderList = Env.APP_GENDERS;
  }

  ngOnInit() {
    // set default selection if tehre's none
    if (!this.stepData.isPopulated) {
      this.stepData.data.genderId = "" + Env.APP_DEFAULT_GENDER_ID;
      this.stepData.data.lookingId = "" + Env.APP_DEFAULT_GENDER_LOOKING_ID;
    }

    // setup form validation
    this.formGroup = this.formBuilder.group({
      genderId: ["", Validators.required],
      lookingId: ["", Validators.required],
    });
  }

  /**
   * Select gender.
   */
  public selectGender(e): void {
    let gender = _.find(
      this.genderList,
      (item) => item["id"] == e.detail.value,
    );
    let looking = _.find(
      this.genderList,
      (item) => item["id"] == this.stepData.data.lookingId,
    );

    if (gender["pair"] && looking["pair"]) {
      this.stepData.data.lookingId = gender["pair"].toString();
    }
  }
}
