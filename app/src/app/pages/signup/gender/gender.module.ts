import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { GenderPageRoutingModule } from "./gender-routing.module";

import { GenderPage } from "./gender.page";

import { TranslateModule } from "@ngx-translate/core";
import { FormErrorListComponentModule } from "src/app/components/form-error-list/form-error-list.component.module";
import { WizardPaginationComponentModule } from "src/app/components/wizard-pagination/wizard-pagination.component.module";
import { WizardSubmitButtonComponentModule } from "src/app/components/wizard-submit-button/wizard-submit-button.component.module";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    GenderPageRoutingModule,
    FormErrorListComponentModule,
    WizardPaginationComponentModule,
    WizardSubmitButtonComponentModule,
  ],
  declarations: [GenderPage],
})
export class GenderPageModule {}
