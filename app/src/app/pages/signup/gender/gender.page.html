<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title mode="ios">
      <img
        style="pointer-events: none"
        src="assets/img/app-logo.svg"
        class="logoSvg"
        />
    </ion-title>
  </ion-toolbar>
</ion-header>

<app-wizard-pagination [items]="this.pagination"></app-wizard-pagination>

<ion-content>
  <ion-grid>
    <ion-row class="ion-justify-content-center">
      <ion-col size-md="6" class="ion-text-center">
        <h3>{{ "enter_gender" | translate }}</h3>
        <h3></h3>
      </ion-col>
    </ion-row>
    <ion-row class="ion-justify-content-center">
      <ion-col class="ion-text-center"> </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center">
      <ion-col size-md="6">
        <form [formGroup]="this.formGroup" (ngSubmit)="submit()">
          <ion-list class="ion-margin-bottom">
            <ion-item id="gender-id">
              <ion-select
                label="{{ 'i_am' | translate}}"
                formControlName="genderId"
                name="genderId"
                [(ngModel)]="this.stepData.data.genderId"
                (ionChange)="selectGender($event)"
                interface="popover"
                >
                @for (g of this.genderList; track g) {
                  <ion-select-option
                    name="genderId"
                    value="{{ g.id }}"
                    >{{ g.name | translate }}</ion-select-option
                    >
                  }
                </ion-select>
              </ion-item>

              <ion-item id="looking-id">
                <ion-select
                  label="{{ 'looking_for_a' | translate }}"
                  formControlName="lookingId"
                  name="lookingId"
                  [(ngModel)]="this.stepData.data.lookingId"
                  interface="popover"
                  >
                  @for (g of this.genderList; track g) {
                    <ion-select-option
                      value="{{ g.id }}"
                      >{{ g.name | translate }}</ion-select-option
                      >
                    }
                  </ion-select>
                </ion-item>
              </ion-list>
              <app-wizard-submit-button
                [isProgress]="this.isValidating || this.isWizardProcessing"
                [text]="this.stepData.isLast ? ('finish' | translate ) : ('next' | translate)"
              ></app-wizard-submit-button>
            </form>
          </ion-col>
        </ion-row>
        <app-form-error-list [errorList]="this.errorList"></app-form-error-list>
      </ion-grid>
    </ion-content>
