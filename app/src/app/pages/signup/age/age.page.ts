import { Component, OnInit } from "@angular/core";
import { SignupWizardPageBase } from "../signupBase";
import { environment as Env } from "../../../../environments/environment";
import { FormBuilder, Validators } from "@angular/forms";
import { ApiService } from "src/app/services/api/api.service";
import { ErrorHelper } from "src/app/services/error-helper";
import { SignupWizardGuardService } from "src/app/services/signup-wizard.guard.service";
import { Router } from "@angular/router";
import { Debug } from "src/app/helpers/debug";
import { AnalyticsService } from "src/app/services/analytics.service";

@Component({
  selector: "app-age",
  templateUrl: "./age.page.html",
  styleUrls: ["./age.page.scss"],
  standalone: false,
})
export class AgePage extends SignupWizardPageBase implements OnInit {
  constructor(
    public guard: SignupWizardGuardService,
    public formBuilder: Form<PERSON>uilder,
    public api: ApiService,
    public errorHelper: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    public route: Router,
    public debug: Debug,
    public analyticsService: AnalyticsService,
  ) {
    super();
    this.initPageParams();
  }

  public validAge: number = Env.APP_VALID_AGE;

  protected validation(): Promise<any> {
    if (this.validateForm()) {
      return Promise.resolve();
    }

    return Promise.reject();
  }

  ngOnInit() {
    // initialize form validation
    this.formGroup = this.formBuilder.group({
      age: [
        "",
        [
          Validators.required,
          Validators.min(Env.APP_VALID_AGE),
          Validators.max(100),
        ],
      ],
    });
  }
}
