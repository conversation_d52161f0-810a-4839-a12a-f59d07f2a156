<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title mode="ios">
      <img
        style="pointer-events: none"
        src="assets/img/app-logo.svg"
        class="logoSvg"
      />
    </ion-title>
  </ion-toolbar>
</ion-header>

<app-wizard-pagination [items]="this.pagination"></app-wizard-pagination>

<ion-content>
  <ion-grid>
    <ion-row class="ion-justify-content-center">
      <ion-col size-md="6" class="ion-text-center">
        <h3>{{ "enter_age" | translate }}</h3>
      </ion-col>
    </ion-row>
    <ion-row class="ion-justify-content-center ion-margin-bottom">
      <ion-col size-md="6" class="ion-text-center">
        <ion-text color="medium"
          >{{ 'age_description' | translate:{'value': this.validAge}
          }}</ion-text
        >
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center">
      <ion-col size-md="6">
        <form [formGroup]="this.formGroup" (ngSubmit)="submit()">
          <ion-item class="ion-margin-bottom">
            <ion-input
              appAutoFocus
              formControlName="age"
              type="number"
              pattern="[0-9]*"
              inputmode="numeric"
              [(ngModel)]="this.stepData.data.age"
            ></ion-input>
          </ion-item>

          <app-form-error-list
            [errorList]="this.errorList"
          ></app-form-error-list>

          <app-wizard-submit-button
            [isProgress]="this.isValidating || this.isWizardProcessing"
            [text]="this.stepData.isLast ? ('finish' | translate ) : ('next' | translate)"
          ></app-wizard-submit-button>
        </form>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
