<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title mode="ios">
      <img
        style="pointer-events: none"
        src="assets/img/app-logo.svg"
        class="logoSvg"
      />
    </ion-title>
  </ion-toolbar>
</ion-header>

<app-wizard-pagination [items]="this.pagination"></app-wizard-pagination>

<ion-content>
  <ion-grid>
    <ion-row class="ion-justify-content-center">
      <ion-col size-md="6" class="ion-text-center">
        <h3>{{ "enter_email" | translate }}</h3>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center ion-margin-bottom">
      <ion-col size-md="6" class="ion-text-center">
        <ion-text color="medium">{{ "email_message" | translate }}</ion-text>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center">
      <ion-col size-md="6">
        <form [formGroup]="this.formGroup" (ngSubmit)="submit()">
          <ion-item class="ion-margin-bottom">
            <ion-input
              appAutoFocus
              [(ngModel)]="this.stepData.data.email"
              formControlName="email"
              type="email"
              placeholder="{{ 'email' | translate }}"
            ></ion-input>
          </ion-item>

          <app-form-error-list
            [errorList]="this.errorList"
          ></app-form-error-list>

          <app-wizard-submit-button
            [isProgress]="this.isValidating || this.isWizardProcessing"
            [text]="this.stepData.isLast ? ('finish' | translate ) : ('next' | translate)"
          ></app-wizard-submit-button>
        </form>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
