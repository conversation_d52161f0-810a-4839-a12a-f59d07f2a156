import { Component, OnInit } from "@angular/core";
import { FormBuilder, Validators } from "@angular/forms";
import { Utils } from "src/app/helpers/utils";
import { ApiService } from "src/app/services/api/api.service";
import { AuthService } from "src/app/services/auth.service";
import { ErrorHelper } from "src/app/services/error-helper";
import { SignupWizardPageBase } from "../signupBase";
import _ from "lodash";
import { SignupWizardGuardService } from "src/app/services/signup-wizard.guard.service";
import { Router } from "@angular/router";
import { Debug } from "src/app/helpers/debug";
import { firstValueFrom } from "rxjs";
import { AnalyticsService } from "src/app/services/analytics.service";
import { StateService } from "src/app/services/state.service";

@Component({
  selector: "app-email",
  templateUrl: "./email.page.html",
  styleUrls: ["./email.page.scss"],
  standalone: false,
})
export class EmailPage extends SignupWizardPageBase implements OnInit {
  constructor(
    public guard: SignupWizardGuardService,
    public formBuilder: FormBuilder,
    public errorHelper: ErrorHelper,
    public auth: AuthService,
    public api: ApiService,
    public route: Router,
    public debug: Debug,
    public analyticsService: AnalyticsService,
    private stateService: StateService,
  ) {
    super();
    this.initPageParams();
  }

  public validation(): Promise<any> {
    this.errorList = [];

    return new Promise<any>((resolve, reject) => {
      if (this.validateForm()) {
        firstValueFrom(this.auth.verifyEmail(this.formGroup.value["email"]))
          .then((data) => {
            resolve(data);
          })
          .catch(() => {
            let apiErrors = this.errorList.concat(
              this.api.getFromErrorList({ identifier: "*" }, true),
            );
            this.stateService.set(
              "lastSignUpEmailTry",
              "",
              this.formGroup.value["email"],
            );
            this.errorList = _.map(apiErrors, (o) => {
              return o["error"] || o["key"];
            });
            reject();
          });
      } else {
        reject();
      }
    });
  }

  ngOnInit() {
    // setup form validation

    this.formGroup = this.formBuilder.group({
      email: [
        "",
        [Validators.required, Validators.pattern(Utils.getEmailRegexp())],
      ],
    });
  }
}
