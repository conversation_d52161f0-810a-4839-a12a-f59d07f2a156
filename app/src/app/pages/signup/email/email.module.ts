import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { EmailPageRoutingModule } from "./email-routing.module";

import { EmailPage } from "./email.page";

import { TranslateModule } from "@ngx-translate/core";
import { FormErrorListComponentModule } from "src/app/components/form-error-list/form-error-list.component.module";
import { WizardPaginationComponentModule } from "src/app/components/wizard-pagination/wizard-pagination.component.module";
import { WizardSubmitButtonComponentModule } from "src/app/components/wizard-submit-button/wizard-submit-button.component.module";
import AutoFocusModule from "src/app/auto-focus.module";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    EmailPageRoutingModule,
    FormErrorListComponentModule,
    WizardPaginationComponentModule,
    WizardSubmitButtonComponentModule,
    AutoFocusModule,
  ],
  declarations: [EmailPage],
})
export class EmailPageModule {}
