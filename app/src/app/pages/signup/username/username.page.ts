import { Component, OnInit } from "@angular/core";
import { Form<PERSON><PERSON>er, Validators } from "@angular/forms";
import { Utils } from "src/app/helpers/utils";
import { ApiService } from "src/app/services/api/api.service";
import { AuthService } from "src/app/services/auth.service";
import { ErrorHelper } from "src/app/services/error-helper";
import { SignupWizardPageBase } from "../signupBase";
import _ from "lodash";
import { SignupWizardGuardService } from "src/app/services/signup-wizard.guard.service";
import { Router } from "@angular/router";
import { Debug } from "src/app/helpers/debug";
import { firstValueFrom } from "rxjs";
import { AnalyticsService } from "src/app/services/analytics.service";

@Component({
  selector: "app-username",
  templateUrl: "./username.page.html",
  styleUrls: ["./username.page.scss"],
  standalone: false,
})
export class UsernamePage extends SignupWizardPageBase implements OnInit {
  constructor(
    public guard: SignupWizardGuardService,
    public formBuilder: FormBuilder,
    public errorHelper: ErrorHelper,
    public api: ApiService,
    public auth: AuthService,
    public route: Router,
    public debug: Debug,
    public analyticsService: AnalyticsService,
  ) {
    super();
    this.initPageParams();
  }

  public doHidePassword() {
    return this.stepData.data["doHidePassword"];
  }

  public validation(): Promise<any> {
    this.errorList = [];

    return new Promise<any>((resolve, reject) => {
      if (this.validateForm()) {
        firstValueFrom(
          this.auth.verifyUsername(this.formGroup.value["username"]),
        )
          .then((data) => {
            resolve(data);
          })
          .catch(() => {
            let apiErrors = this.errorList.concat(
              this.api.getFromErrorList({ identifier: "*" }, true),
            );
            this.errorList = _.map(apiErrors, (o) => {
              return o["error"] || o["key"];
            });
            reject();
          });
      } else {
        // error
        reject();
      }
    });
  }

  ngOnInit() {
    let groupData = {
      username: [
        "",
        [
          Validators.required,
          Validators.pattern(Utils.getUsernameRegexString()),
          Validators.minLength(6),
        ],
      ],
    };

    // password can be hidden if required
    if (!this.doHidePassword()) {
      groupData["password"] = [
        "",
        [Validators.required, Validators.minLength(3)],
      ];
    } else {
      groupData["password"] = [""];
    }

    this.formGroup = this.formBuilder.group(groupData);
  }
}
