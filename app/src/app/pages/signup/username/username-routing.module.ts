import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { UsernamePage } from "./username.page";

const routes: Routes = [
  {
    path: "",
    component: UsernamePage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UsernamePageRoutingModule {}
