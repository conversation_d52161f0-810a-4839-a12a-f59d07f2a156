import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { UsernamePageRoutingModule } from "./username-routing.module";

import { UsernamePage } from "./username.page";

import { TranslateModule } from "@ngx-translate/core";
import { FormErrorListComponentModule } from "src/app/components/form-error-list/form-error-list.component.module";
import { WizardPaginationComponentModule } from "src/app/components/wizard-pagination/wizard-pagination.component.module";
import { WizardSubmitButtonComponentModule } from "src/app/components/wizard-submit-button/wizard-submit-button.component.module";
import { AutoFocusDirective } from "src/app/auto-focus.directive";
import AutoFocusModule from "src/app/auto-focus.module";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    UsernamePageRoutingModule,
    FormErrorListComponentModule,
    WizardPaginationComponentModule,
    WizardSubmitButtonComponentModule,
    AutoFocusModule,
  ],
  declarations: [UsernamePage],
})
export class UsernamePageModule {}
