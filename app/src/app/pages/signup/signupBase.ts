import { FormGroup } from "@angular/forms";
import { Activated<PERSON>oute, Router } from "@angular/router";
import { Debug } from "src/app/helpers/debug";
import { ApiService } from "src/app/services/api/api.service";
import { <PERSON>rror<PERSON>elper } from "src/app/services/error-helper";
import { SignupWizardGuardService } from "src/app/services/signup-wizard.guard.service";
import { IPaginator, StepData } from "src/app/services/wizard.service";
import { AnalyticsService } from "src/app/services/analytics.service";
import { TME_sign_up_interaction } from "src/app/classes/gtm-event.class";

export class SignupWizardPageBase {
  public guard: SignupWizardGuardService;
  public signupStepName: string;
  public stepData: StepData;
  public pagination: IPaginator[];
  public errorHelper: ErrorHelper;
  public errorList: string[] = [];
  public formGroup: FormGroup;
  public api: ApiService;
  public route: Router;
  public debug: Debug;
  public analyticsService: AnalyticsService;

  ionViewWillEnter() {
    // init page on back button
    this.initPageParams();
  }

  protected initPageParams() {
    let _url: string = this.route.url.split("#")[0];

    // init signup step name
    this.signupStepName =
      this.guard.getSignupWizardInstance.nameStepByUrl(_url);

    // get step data
    this.stepData = this.guard.getSignupWizardInstance.getStepByName(
      this.signupStepName
    );

    // get paginations
    this.pagination = this.guard.getSignupWizardInstance.getPagination(
      this.stepData.index
    );

    this.analyticsService.logEvent("sign_up_interaction", <
      TME_sign_up_interaction
    >{
      step_number: (this.stepData?.index || 0) + 1 + " step",
      step_type: (this.signupStepName ?? "N/A").replace("signup_signup", ""),
      total_steps: this.guard.getSignupWizardInstance.stepCount + " steps",
    });

    console.log("STEP DATA", [
      this.signupStepName,
      this.stepData?.index,
      this.guard.getSignupWizardInstance.getStepList().length,
    ]);
  }

  protected gotoNextStep() {
    console.log("STEP", this.stepData);
    this.guard.getSignupWizardInstance.navigateByIndex(this.stepData.nextIndex);
  }

  public isValidating: boolean;

  protected validation(): Promise<any> {
    return Promise.resolve();
    // add validation core here
  }

  public validateForm() {
    this.errorList = [];
    this.api.clearErrorList();

    this.api.clearErrorList();

    if (!this.formGroup.valid) {
      this.errorList = this.errorHelper.getFormValidationErrors(this.formGroup);
      return false;
    } else {
      return true;
    }
  }

  /**
   * Tells if wizard is processing ( so page not validating, but some global stuff going on )
   */
  get isWizardProcessing(): boolean {
    return this.guard.getSignupWizardInstance.isPorcessing;
  }

  protected validateData(): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.isValidating = true;

      this.validation()
        .then((data) => {
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          this.isValidating = false;
        });
    });
  }

  public submit() {
    // delete error list
    this.errorList = [];

    // check if this is the last step

    this.validateData()
      .then(() => {
        this.stepData.isPopulated = true;

        if (!this.stepData.isLast) {
          this.gotoNextStep();
        } else {
          this.guard.getSignupWizardInstance.finishWizard();
        }
      })
      .catch((error) => {
        this.debug.l("signup page error", error);
      });
  }
}
