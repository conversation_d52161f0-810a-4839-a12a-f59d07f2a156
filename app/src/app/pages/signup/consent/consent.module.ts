import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { ConsentPageRoutingModule } from "./consent-routing.module";

import { ConsentPage } from "./consent.page";

import { TranslateModule } from "@ngx-translate/core";
import { WizardPaginationComponentModule } from "src/app/components/wizard-pagination/wizard-pagination.component.module";
import { WizardSubmitButtonComponentModule } from "src/app/components/wizard-submit-button/wizard-submit-button.component.module";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    ConsentPageRoutingModule,
    ReactiveFormsModule,
    WizardPaginationComponentModule,
    WizardSubmitButtonComponentModule,
  ],
  declarations: [ConsentPage],
})
export class ConsentPageModule {}
