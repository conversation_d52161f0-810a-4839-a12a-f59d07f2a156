import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { ConsentPage } from "./consent.page";

const routes: Routes = [
  {
    path: "",
    component: ConsentPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ConsentPageRoutingModule {}
