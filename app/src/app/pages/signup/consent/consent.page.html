<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title mode="ios">
      <img
        style="pointer-events: none"
        src="assets/img/app-logo.svg"
        class="logoSvg"
        />
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid class="ion-no-padding">
    <ion-row>
      <ion-col class="ion-text-center">
        <app-wizard-pagination
          [items]="this.pagination"
        ></app-wizard-pagination>
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center">
      <ion-col size-sm="6">
        <form [formGroup]="this.formGroup" (ngSubmit)="submit()">
          @for (ci of this.consentItemList; track ci) {
            <ion-card
              class="{{ 'consent-' + ci.id }}"
              >
              <ion-card-header>
                <h4 color="dark">{{ ci.title | translate}}</h4>
              </ion-card-header>
              <ion-card-content>
                {{ ci.description | translate }}
                <ion-list>
                  @if (ci.link!='') {
                    <ion-item lines="none">
                      <a routerLink="{{ ci.link }}"
                        >{{ ci.linkTitle | translate }}</a
                        >
                      </ion-item>
                    }
                    <ion-item lines="none">
                      <ion-checkbox
                        [(ngModel)]="this.stepData.data[ci.id]"
                        formControlName="{{ ci.id }}"
                        label-placement="end"
                        justify="start"
                        >{{ ci.checkboxTitle | translate }}</ion-checkbox
                        >
                      </ion-item>
                    </ion-list>
                  </ion-card-content>
                </ion-card>
              }
              <div class="btn-wizard-wrapper">
                <app-wizard-submit-button
                  [disabled]="!this.formGroup.valid"
                  [isProgress]="this.isValidating || this.isWizardProcessing"
                  [text]="this.stepData.isLast ? ('finish' | translate ) : ('next' | translate)"
                ></app-wizard-submit-button>
              </div>
            </form>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-content>
