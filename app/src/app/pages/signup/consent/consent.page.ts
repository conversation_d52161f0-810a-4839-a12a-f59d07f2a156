import { Component, OnInit } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON>, Validators } from "@angular/forms";
import { AccountService } from "src/app/services/account.service";
import { ApiService } from "src/app/services/api/api.service";
import { AuthService } from "src/app/services/auth.service";
import { ErrorHelper } from "src/app/services/error-helper";
import { SignupWizardPageBase } from "../signupBase";
import { environment as Env } from "../../../../environments/environment";
import { SignupWizardGuardService } from "src/app/services/signup-wizard.guard.service";
import { Router } from "@angular/router";
import { Debug } from "src/app/helpers/debug";
import { AnalyticsService } from "src/app/services/analytics.service";

@Component({
  selector: "app-consent",
  templateUrl: "./consent.page.html",
  styleUrls: ["./consent.page.scss"],
  standalone: false,
})
export class ConsentPage extends SignupWizardPageBase implements OnInit {
  public consentItemList: ConsentItem[] = [];

  private gatherConsentItemList() {
    let termsItem = new ConsentItem();
    let privacyItem = new ConsentItem();
    let specialItem = new ConsentItem();

    termsItem.id = "terms";
    termsItem.title = "terms_and_conditions";
    termsItem.description = "terms_and_conditions_description";
    termsItem.checkboxTitle = "i_agree";

    // @todo add terms page with static content
    termsItem.link = "/page/" + Env.PAGE_NAME_TOS;
    termsItem.linkTitle = "read_terms_and_conditions";

    privacyItem.id = "privacy";
    privacyItem.title = "privacy_policy";
    privacyItem.description = "privacy_policy_description";
    privacyItem.checkboxTitle = "i_agree";

    // @todo add privacy page with static content
    privacyItem.link = "/page/" + Env.PAGE_NAME_PRIVACY_POLICY;
    privacyItem.linkTitle = "read_privacy_policy";

    specialItem.id = "special";
    specialItem.title = "special_offers";
    specialItem.description = "special_offers_description";
    specialItem.checkboxTitle = "i_agree";

    // @todo add privacy page with static content
    specialItem.link = "";
    specialItem.linkTitle = "";
    specialItem.isItMandatory = false;

    this.consentItemList = [termsItem, privacyItem];

    // add only if required by current country/server etc
    if (this.accountService.consentRequirement.offers_consent) {
      this.consentItemList.unshift(specialItem);
    }
  }

  constructor(
    public guard: SignupWizardGuardService,
    public formBuilder: FormBuilder,
    public errorHelper: ErrorHelper,
    public auth: AuthService,
    public api: ApiService,
    private accountService: AccountService,
    public route: Router,
    public debug: Debug,
    public analyticsService: AnalyticsService,
  ) {
    super();
    this.initPageParams();
    this.gatherConsentItemList();
  }

  public validation(): Promise<any> {
    // simple validation
    return new Promise<any>((resolve, reject) => {
      if (this.validateForm()) {
        this.stepData.isPopulated = true;
        // valid
        resolve(true);
      } else {
        // invalid
        reject();
      }
    });
  }

  ngOnInit() {
    let groupObject = {};

    // build form validator
    this.consentItemList.forEach((e) => {
      groupObject[e.id] = [""];
      if (e.isItMandatory) {
        groupObject[e.id].push(Validators.requiredTrue);
      }
    });

    this.formGroup = this.formBuilder.group(groupObject);
  }
}

export class ConsentItem {
  public id: string = "";
  public title: string = "";
  public description: string = "";
  public checkboxTitle: string = "";
  public checked: boolean = false;
  public linkTitle: string = "";
  public link: string = "";
  public isItMandatory: boolean = true;
}
