import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { ProfileTabPage } from "./profile-tab.page";

const routes: Routes = [
  {
    path: "",
    component: ProfileTabPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ProfileTabPageRoutingModule {}
