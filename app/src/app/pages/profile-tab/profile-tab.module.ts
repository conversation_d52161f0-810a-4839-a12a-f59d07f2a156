import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { ProfileTabPageRoutingModule } from "./profile-tab-routing.module";

import { ProfileTabPage } from "./profile-tab.page";

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    ProfileTabPageRoutingModule,
  ],
  declarations: [ProfileTabPage],
})
export class ProfileTabPageModule {}
