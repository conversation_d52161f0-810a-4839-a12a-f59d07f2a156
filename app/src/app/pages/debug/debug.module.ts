import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { DebugPageRoutingModule } from "./debug-routing.module";

import { DebugPage } from "./debug.page";
import { TranslateModule } from "@ngx-translate/core";
import { IntroductionPopupComponentModule } from "../../components/introduction-popup/introduction-popup.component.module";
import { ProfileMenuComponentModule } from "src/app/components/profile-menu/profile-menu.component.module";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    DebugPageRoutingModule,
    TranslateModule.forChild(),
    IntroductionPopupComponentModule,
    ProfileMenuComponentModule,
  ],
  declarations: [DebugPage],
})
export class DebugPageModule {}
