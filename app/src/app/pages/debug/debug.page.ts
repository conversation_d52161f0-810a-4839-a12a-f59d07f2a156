import { Component, OnInit } from "@angular/core";
import { Debug } from "src/app/helpers/debug";
import { ApiService } from "src/app/services/api/api.service";
import { AuthService } from "src/app/services/auth.service";
import { FaqService } from "src/app/services/faq.service";
import { FlirtService } from "src/app/services/flirt.service";
import { ProfileService } from "src/app/services/profile.service";
import { SessionService } from "src/app/services/session.service";
import { UserUpdatesService } from "src/app/services/user-updates.service";
import { UtilsService } from "src/app/services/utils.service";
import { AccountService } from "../../services/account.service";
import { TranslateService } from "@ngx-translate/core";
import { Router } from "@angular/router";
import { DeviceInfoService } from "src/app/services/device-info.service";
import _ from "lodash";
import { environment as Env } from "../../../environments/environment";
import { PurchaseService } from "src/app/services/purchase.service";
import { WelcomeWizardService } from "../../services/welcome-wizard.service";
import { WelcomeProfileService } from "../../services/welcome-profile.service";
import { MiscService } from "src/app/services/misc.service";
import { firstValueFrom } from "rxjs";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { PictureService } from "src/app/services/picture.service";
import { PushNotificationService } from "src/app/services/push-notification.service";
import { ModalController } from "@ionic/angular";
import { AnalyticsService } from "src/app/services/analytics.service";
import { AppsflyerHelperService } from "src/app/services/appsflyer-helper.service";
import { DeeplinkService } from "src/app/services/deeplink.service";
import { ChatService } from "../../services/chat.service";
import { ChatMessageService } from "../../services/chat-message.service";
import { StatusCheckerService } from "src/app/status-checker.service";
import { HappyHourService } from "../../services/happy-hour.service";
import { MenuHelperService } from "src/app/services/menu-helper.service";
import { OfferHandlerService } from "../../services/offer-handler";
import { StateService } from "src/app/services/state.service";
import { CredentialData } from "src/app/schemas/credentials";
import { ProfileBase } from "src/app/schemas/profile-base";
import { ReminderIntroductionService } from "../../services/reminder-introduction.service";
import { CameraService } from "src/app/services/camera.service";
import { PromoDialogType } from "src/app/services/upgrade-dialog.type";
import { TME_test_event } from "src/app/classes/gtm-event.class";
import { IapPluginService } from "src/app/plugins/iap-plugin.service";
import { RefresherService } from "../../services/refresher.service";

@Component({
  selector: "app-debug",
  templateUrl: "./debug.page.html",
  styleUrls: ["./debug.page.scss"],
  standalone: false,
})
export class DebugPage implements OnInit {
  public requestGET: boolean = false;
  public requestPOST: boolean = false;
  public responseGET: string = "";
  public responsePOST: string = "";

  public showRoutes: boolean = false;

  constructor(
    public cam: CameraService,
    public messages: ChatMessageService,
    public chat: ChatService,
    public picService: PictureService,
    public misc: MiscService,
    public api: ApiService,
    public utilsService: UtilsService,
    public debug: Debug,
    public auth: AuthService,
    public profileService: ProfileService,
    public sessionService: SessionService,
    public updateService: UserUpdatesService,
    public faqService: FaqService,
    public flirtService: FlirtService,
    public account: AccountService,
    public translate: TranslateService,
    public router: Router,
    public deviceInfo: DeviceInfoService,
    public purchase: PurchaseService,
    public welcomeWizard: WelcomeWizardService,
    public welcomeProfile: WelcomeProfileService,
    public dialog: DialogHelperService,
    public pushNotification: PushNotificationService,
    public modalController: ModalController,
    public analytics: AnalyticsService,
    public appsflyer: AppsflyerHelperService,
    public deeplink: DeeplinkService,
    public cm: ChatMessageService,
    public statusChecker: StatusCheckerService,
    public hhService: HappyHourService,
    public menuHelperService: MenuHelperService,
    public offerService: OfferHandlerService,
    public stateService: StateService,
    public reminderIntroduction: ReminderIntroductionService,
    public iap: IapPluginService,
    public refresherService: RefresherService,
  ) {}

  public async refresh() {
    if (this.utilsService.config["REFRESHER"]) {
      return await this.refresherService.refresh();
    }

    this.utilsService.config["REFRESHER"] = true;
    await this.refresherService.refresh();
    this.utilsService.config["REFRESHER"] = false;
  }

  public showNotificationPopup() {
    this.pushNotification.showNotificationPopup = true;
    this.router.navigate(["/app/tabs/browse"]);
  }

  public testAnalytics() {
    this.analytics.logEvent("", <TME_test_event>{
      message: "test event",
    });
  }

  public offer1() {
    this.offerService.setOfferData({
      title: "Get premium features for FREE!!",
      description: "Add 5 photos and get a free Premium membership for 1 week",
      subtitle: "Get your award NOW!",
      imageUrl:
        "https://files.dvipcdn.com/data/dating/offers_ads_templates/6/add_photos_sketch.svg",
      buttonText: "Upload Photos",
      uid: "222",
      action: "photo_upload",
    });
    this.offerService.executeOfferAction(true);
  }

  public offer2() {
    this.offerService.setOfferData({
      title: "Get premium features for FREE!",
      description:
        "Fill 75% of your profile and become a VIP member for 3 days",
      subtitle: "Get your award NOW!",
      imageUrl:
        "https://files.dvipcdn.com/data/dating/offers_ads_templates/6/add_photos_sketch.svg",
      buttonText: "Edit Profile",
      uid: "111",
      action: "edit_profile",
    });
    this.offerService.executeOfferAction(true);
  }

  public offer3() {
    this.offerService.setOfferData({
      title: "Become featured for FREE!",
      description: "Add 5 photos and become Featured for 2 weeks",
      subtitle: "Get your award NOW!",
      imageUrl:
        "https://files.dvipcdn.com/data/dating/offers_ads_templates/6/add_photos_sketch.svg",
      buttonText: "Upload Photos",
      uid: "444",
      action: "photo_upload",
    });
    this.offerService.executeOfferAction(true);
  }

  public offer24h() {
    // Store default registration ts.
    let art = JSON.stringify(this.account.account.registration_ts);
    //set reg time 5h ago
    this.account.account.registration_ts =
      Math.floor(Date.now() / 1000) - 5 * 60 * 60;
    this.purchase.openDiscounted24hDialog(
      (error) => {
        this.dialog.showErrorAlert(
          "purchase_error",
          this.utilsService.figureOutTheErrorMessage(error),
          this.utilsService.figureOutTheErrorKey(error),
        );
      },
      () => {
        alert("try cancel callback");
      },
      true,
    );
    setTimeout(() => {
      // Reset default registration ts.
      this.account.account.registration_ts = JSON.parse(art);
    }, 1000);
  }

  public initOffers(): void {
    this.offerService
      .fetchOffer()
      .then((res) => {
        this.offerService.executeOfferAction(true);
      })
      .catch((err) => {
        console.log(">>> offers res error <<<", err);
      });
  }

  public hhEnabled: boolean = false;
  public hhStart: number = 50;
  public hhEnd: number = 1000;

  public enableHH(): void {
    this.hhEnabled = true;
    this.hhService.data = {
      start: this.hhStart,
      end: this.hhEnd,
    };
  }

  public hhReset(): void {
    this.hhEnabled = false;
    this.hhService.time = 0;
    setTimeout(() => {
      this.hhService.data = { start: 0, end: 0 };
    }, 1000);
  }

  public inputData: any = {
    deeplink: "https://" + Env.APP_WEBSITE + "/edit",
  };

  public async deeplink404Profile() {
    this.deeplink
      .setLinkData("https://" + Env.APP_WEBSITE + "/profile/1234", false)
      .handle();
  }

  public async deeplinkProfile() {
    this.deeplink
      .setLinkData("https://" + Env.APP_WEBSITE + "/profile/rerere17", false)
      .handle();
  }

  public deeplinkTest() {
    const url = this.inputData?.deeplink;
    this.debug.l("URL", url);
    this.deeplink.setLinkData(url, false).handle();
  }

  public async deeplinkChat() {
    const threads = await firstValueFrom(this.chat.getThreadList());
    const url =
      "https://" + Env.APP_WEBSITE + "/message?thread_id=" + threads[0].id;
    this.deeplink.setLinkData(url, false).handle();
  }

  public async deeplinkIntroduction() {
    this.deeplink
      .setLinkData("https://" + Env.APP_WEBSITE + "/introduction", true)
      .handle();
  }

  public async newIapTest(num: number) {
    switch (num) {
      case 4:
        const x = this.iap.getOwnedProducts();
        console.log("owned", x);
      case 1:
        // get data

        let products = null;

        try {
          products = await this.iap.initialize();
          console.log("IAP debug product fetch success");
          console.log(products);
        } catch (e) {
          console.error("IAP Debug product fetch error", e);
        }

        break;

      case 2:
        try {
          const x = await this.iap.purchase(
            this.iap.getProductsByCategory("upgrade")[0]?.id,
          );
          console.log("IAP Debug purchase success");
          console.log(x);
        } catch (e) {
          console.log("IAP Debug purchase error");
          console.log(e);
        }
        break;

      case 3:
        this.iap.manageSubscription();

        break;
    }
  }

  async purchaseCampaign(dtype: PromoDialogType = "default") {
    // this.router.navigate(["app/modals/go-premium-promo"]);

    this.purchase.setPromoDialogType(dtype).openCampaignDialog(
      "test_campaign",
      (error) => {
        this.dialog.showErrorAlert(
          "purchase_error",
          this.utilsService.figureOutTheErrorMessage(error),
          this.utilsService.figureOutTheErrorKey(error),
        );
      },
      () => {
        alert("try cancel callback");
      },
      true,
    );
  }

  async purchaseDiscounted() {
    this.purchase.openDiscountedDialog(
      (error) => {
        this.dialog.showErrorAlert(
          "purchase_error",
          this.utilsService.figureOutTheErrorMessage(error),
          this.utilsService.figureOutTheErrorKey(error),
        );
      },
      () => {
        alert("try cancel callback");
      },
      true,
    );
  }

  async purchaseTry() {
    this.purchase.openTrialDialog(
      (error) => {
        this.dialog.showErrorAlert(
          "purchase_error",
          this.utilsService.figureOutTheErrorMessage(error),
          this.utilsService.figureOutTheErrorKey(error),
        );
      },
      () => {
        alert("try cancel callback");
      },
      true,
    );
  }

  async purchaseUpgradeToSend() {
    const _t = await firstValueFrom(this.profileService.get("bbtest"));

    console.log("USR PROFILE", _t.data);

    this.stateService.set("sendMessageProfile", null, _t.data);

    this.purchase.setDialogType("upgrade_to_read").openUpgradeDialog(
      (error) => {
        this.dialog.showErrorAlert(
          "purchase_error",
          this.utilsService.figureOutTheErrorMessage(error),
          this.utilsService.figureOutTheErrorKey(error),
        );
      },
      () => {
        alert("upgarde cancel callback");
      },
      true,
    );
  }

  async purchaseGoMessage() {
    this.stateService.set("sendMessageProfile", null, <ProfileBase.type>{
      username: "TEST",
      first_name: "TEST",
    });

    this.purchase.setDialogType("messages_limit").openUpgradeDialog(
      (error) => {
        this.dialog.showErrorAlert(
          "purchase_error",
          this.utilsService.figureOutTheErrorMessage(error),
          this.utilsService.figureOutTheErrorKey(error),
        );
      },
      () => {
        alert("upgarde cancel callback");
      },
      true,
    );
  }

  async purchaseGoCustomMessage() {
    this.purchase.setDialogType("photo_comments_limit").openUpgradeDialog(
      (error) => {
        this.dialog.showErrorAlert(
          "purchase_error",
          this.utilsService.figureOutTheErrorMessage(error),
          this.utilsService.figureOutTheErrorKey(error),
        );
      },
      () => {
        alert("upgarde cancel callback");
      },
      true,
    );
  }

  async purchaseGo() {
    this.stateService.delete("sendMessageProfile", null);
    this.purchase.openUpgradeDialog(
      (error) => {
        this.dialog.showErrorAlert(
          "purchase_error",
          this.utilsService.figureOutTheErrorMessage(error),
          this.utilsService.figureOutTheErrorKey(error),
        );
      },
      () => {
        alert("upgarde cancel callback");
      },
      true,
    );
  }

  private wasSubscribe = false;
  public percentage = 0;

  ngOnInit() {}

  private _light: boolean = false;

  public toggleLightDark() {
    document.body.setAttribute("app-theme", this._light ? "light" : "dark");
    this._light = !this._light;
  }

  public openWelcomeWizard() {
    firstValueFrom(this.welcomeProfile.get())
      .then((data) => {
        const wdata = data.data;

        // there's no welcome page data, just go to root and log
        if (!wdata) {
          this.debug.le("no welcome page data", null);
          this.router.navigate([Env.HOME_PATH]);
        }

        this._openWelcomeWizard(wdata);
      })
      .catch((err) => {
        this.debug.le("welcome profile get error", err);
      });
  }

  private _openWelcomeWizard(wData) {
    this.debug.l(">>> open welcome wizard <<<", wData);
    this.welcomeWizard.reset();
    this.welcomeWizard.doHideFirstBackButton = true;

    this.welcomeWizard.addPageList(
      this.welcomeWizard.getPageListFromData(wData),
    );

    let i = 0;
    if (this.auth.regType == CredentialData.CredType.Apple) {
      i = 1;
    }

    this.welcomeWizard.open(i);
  }

  public openCountdown(): void {
    this.stateService.set("countdown", this.account.account.username, {
      type: "swipes",
      renewal: Math.floor(Date.now() / 1000) + 10,
    });
    this.router.navigate(["/app/modals/countdown"]);
  }

  public openFreeCountdown(): void {
    this.stateService.set("countdown", this.account.account.username, {
      type: "swipes",
      renewal: Math.floor(Date.now() / 1000) + 10,
    });
    this.router.navigate(["/app/modals/free-countdown"]);
  }

  public openUpgrade(new_member: boolean = false) {
    this.account.account.registration_ts = new_member
      ? Math.floor(Date.now() / 1000) - 5 * 60 * 60
      : null;

    this.purchase.openUpgradeDialog((error) => {
      this.dialog.showToastBaseOnErrorResponse(error, null, false);
    });
  }

  public likeLimit() {
    this.purchase
      .setDialogType("like_limit_reached")
      .openUpgradeDialog((error) => {
        this.dialog.showToastBaseOnErrorResponse(error, null, false);
      });
  }

  public photoCommentsLimit() {
    this.purchase
      .setDialogType("photo_comments_limit")
      .openUpgradeDialog((error) => {
        this.dialog.showToastBaseOnErrorResponse(error, null, false);
      });
  }

  public flirtLimit() {
    this.purchase.setDialogType("flirt_limit").openUpgradeDialog((error) => {
      this.dialog.showToastBaseOnErrorResponse(error, null, false);
    });
  }

  public messagesLimit() {
    this.stateService.set("sendMessageProfile", null, <ProfileBase.type>{
      username: "TEST",
      first_name: "TEST",
    });

    this.purchase.setDialogType("messages_limit").openUpgradeDialog(
      (error) => {
        this.dialog.showErrorAlert(
          "purchase_error",
          this.utilsService.figureOutTheErrorMessage(error),
          this.utilsService.figureOutTheErrorKey(error),
        );
      },
      () => {
        alert("upgarde cancel callback");
      },
      true,
    );
  }

  public photoFullSizeLimit() {
    this.stateService.set("profileFullSizeLimit", null, <ProfileBase.type>{
      username: "TEST",
      first_name: "TEST",
    });

    this.purchase.setDialogType("photo_full_size_limit").openUpgradeDialog(
      (error) => {
        this.dialog.showErrorAlert(
          "purchase_error",
          this.utilsService.figureOutTheErrorMessage(error),
          this.utilsService.figureOutTheErrorKey(error),
        );
      },
      () => {
        alert("upgarde cancel callback");
      },
      true,
    );
  }

  public isValentines = false;

  public openIntroduction(): void {
    this.reminderIntroduction.introduction.doShow = true;
    this.isValentines = false;
  }

  public openValentinesPopup(): void {
    this.reminderIntroduction.introduction.doShow = true;
    this.isValentines = true;
  }

  public deeplinkPhotoTest() {
    this.deeplink
      .setLinkData(
        "https://front-m1.dvipdev.com/profile/BoldizsarBednarik?utm_campaign=new_comment&utm_medium=email&utm_source=membership_premiumplus&utm_term=primili_ste_novi_komentar&h=1047210g48149b7199bd3401857cc6a0497db938&eid=103188148#photo-5",
      )
      .handle();
  }

  public deeplinkPhotoTest2() {
    this.deeplink
      .setLinkData(
        "https://front-m1.dvipdev.com/profile/bbtest2?utm_campaign=new_comment&utm_medium=email&utm_source=membership_premiumplus&utm_term=primili_ste_novi_komentar&h=1047210g48149b7199bd3401857cc6a0497db938&eid=103188148#photo-5",
      )
      .handle();
  }

  public deeplinkPhotoTest3() {
    this.deeplink
      .setLinkData(
        "https://front-m1.dvipdev.com/profile/bbtest2?utm_campaign=new_comment&utm_medium=email&utm_source=membership_premiumplus&utm_term=primili_ste_novi_komentar&h=1047210g48149b7199bd3401857cc6a0497db938&eid=103188148#gecc-5",
      )
      .handle();
  }

  public testLoading() {
    this.dialog.showLoading("test loading", ":)");
  }

  public testLoading2() {
    this.dialog.showLoading("test loading 2", ":)", 5000);
  }

  public restart() {
    this.misc.restartApplication();
  }

  public openGoPremiumDownloadedPage() {
    this.purchase.openGoPremiumDownloaded(
      (error) => {
        this.dialog.showErrorAlert(
          "purchase_error",
          this.utilsService.figureOutTheErrorMessage(error),
          this.utilsService.figureOutTheErrorKey(error),
        );
      },
      () => {
        alert("try cancel callback");
      },
      true,
    );
  }

  public temp() {
    this.utilsService.log({ tets: "123" });
    /*
        this.dialog.clickableToast(
          "John",
          "Hello there :) This should be a much longer message, so we can se what's up ... lorem ipsum, i tako dalje!",
          "https://avatar.com",
          "💬",
          () => {
            this.router.navigate([
              "/app/chat-messages/" + "bbtest",
            ]);
          }, 100000
        );

      this.dialog.showToast("TITLE", "TEXT","error");
      */
  }

  public offersLength: number = 1;
  public offersList: any[] = [
    {
      id: *********,
      ts: **********,
      body:
        '<p><b>Suza,</b></p>\r\n<p>your profile is in demand. <br />Try our 30 days of premium membership for <b>only $1.00</b> and use the full potential of your account.</p>\r\n<h5><a class="ib-btn" href="https://www.' +
        Env.DEEPLINK_HOST +
        '/upgrade?campaign=views&utm_campaign=inl_views_v5&utm_medium=internal_inbox&utm_source=membership_free&h=23246071gf290c16c1b0ac9efb21adc0d3a232cf7">I Accept</a></h5>\r\n<p>Activate unlimited access <b>before midnight</b> and view all photos, reply to messages, chat and flirt as much as you like.</p>',
      type: 5,
      type_name: "promo",
    },
    {
      id: ********,
      ts: **********,
      body:
        '<p><span style="font-size:20px;">Hurry up!</span></p>\r\n<p>We are giving away a limited number of VIP passes for</p>\r\n<p><span style="font-size:25px;"><strong>Only 1,39 \u20ac</strong></span></p>\r\n<p>Get an entire month of unlimited access.<br />\r\n<p><strong><a class="btn" href="https://www.' +
        Env.DEEPLINK_HOST +
        '/upgrade?campaign=counter&utm_campaign=inl_counter_1116&utm_medium=internal_inbox&utm_source=membership_premium&h=17g3b02eb04c818bf0396a6aec99c378fe7">Upgrade Now</a></strong></p>\r\n<p>With premium access you boost your chances of finding love.</p>\r\n<hr>',
      type: 10,
      type_name: "promo-mobile",
    },
    {
      id: 43365778,
      ts: 1693138810,
      body:
        '<p><span style="font-size:22px;">Rumor Has It</span></p><br />\r\n<p>Have you heard? Great deals are back! <br />Don\'t miss the chance to save on premium membership.</p>\r\n<p><span style="font-size:20px;">1,67 \u20ac for VIP access</span></p><br />\r\n<p><span style="font-size:24px;"><a class="btn" href="https://www.' +
        Env.DEEPLINK_HOST +
        '/upgrade?campaign=rumor&utm_campaign=inl_rumor_0516&utm_medium=internal_inbox&utm_source=membership_premium&h=17g3b02eb04c818bf0396a6aec99c378fe7">Take it!</a></span></p>\r\n<br /><br />\r\n<hr>',
      type: 5,
      type_name: "promo",
    },
  ];

  public setOffersData() {
    this.chat.offers = [];
    if (this.offersLength) {
      for (let i = 0; i < this.offersLength; i++) {
        this.chat.offers.push(this.offersList[Math.round(Math.random())]);
      }
    }
    this.router.navigate(["/app/inbox"]);
  }
}
