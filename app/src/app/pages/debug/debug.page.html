<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>debug</ion-title>
  </ion-toolbar>
  <ion-progress-bar
    [value]="this.percentage/100"
    [class.hidden]="this.percentage==100 || this.percentage ==0"
  ></ion-progress-bar>
</ion-header>
<ion-content>
  <app-introduction-popup
    [(doShow)]="reminderIntroduction.introduction.doShow"
    [isDebug]="true"
    [isValentines]="isValentines"
  ></app-introduction-popup>

  <ion-list>
    <ion-card>
      <ion-card-header>
        <ion-card-title>HAPPY HOUR [ {{ hhService.time }} ]</ion-card-title>
      </ion-card-header>

      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-input
              label="Starts In (seconds)"
              type="number"
              [(ngModel)]="hhStart"
            ></ion-input>
          </ion-item>
          <ion-item>
            <ion-input
              label="Ends In (seconds)"
              type="number"
              [(ngModel)]="hhEnd"
            ></ion-input>
          </ion-item>
          <ion-item>
            <ion-button
              (click)="enableHH()"
              color="primary"
              [disabled]="hhEnabled"
              >start</ion-button
            >
            <ion-button
              (click)="hhReset()"
              color="danger"
              [disabled]="!hhEnabled"
              >stop</ion-button
            >
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <ion-card>
      <ion-card-header>
        <ion-card-title
          >INBOX OFFERS
          <ion-checkbox class="ion-float-end" [(ngModel)]="chat.debugOffers"
            >{{ chat.debugOffers ? 'ON' : 'OFF' }}</ion-checkbox
          >
        </ion-card-title>
      </ion-card-header>

      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-input
              label="number of offers"
              type="number"
              [(ngModel)]="offersLength"
              max="10"
            ></ion-input>
          </ion-item>
          <ion-item>
            <ion-button (click)="setOffersData()" color="primary"
              >OPEN INBOX PAGE</ion-button
            >
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <ion-card>
      <ion-card-header>
        <ion-card-title>GO PREMIUM</ion-card-title>
      </ion-card-header>

      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-button (click)="openGoPremiumDownloadedPage()"
              >App Downloaded Offer</ion-button
            >
          </ion-item>
          <ion-item>
            <ion-button (click)="newIapTest(1)"
              >NEW Iap Test 1 ( get data )</ion-button
            ><br />
          </ion-item>
          <ion-item>
            <ion-button (click)="newIapTest(2)"
              >NEW Iap Test 2 ( purchase )</ion-button
            ><br />
          </ion-item>
          <ion-item>
            <ion-button (click)="newIapTest(3)"
              >NEW Iap Test 3 ( manage subscription )</ion-button
            ><br />
          </ion-item>
          <ion-item>
            <ion-button (click)="newIapTest(4)"
              >NEW Iap get owned produts</ion-button
            ><br />
          </ion-item>
          <ion-item>
            <ion-button (click)="purchaseDiscounted()"
              >Open Discounted Upgrade</ion-button
            >
          </ion-item>
          <!-- upgrade to send -->
          <ion-item>
            <ion-button (click)="purchaseUpgradeToSend()"
              >Read message Premium</ion-button
            ><br />
          </ion-item>
          <!-- campaign 1 usd first month -->
          <ion-item>
            <ion-button (click)="purchaseCampaign('default')"
              >Campaign default</ion-button
            ><br />
          </ion-item>
          <ion-item>
            <ion-button (click)="purchaseCampaign('welcome')"
              >Campaign welcome</ion-button
            ><br />
          </ion-item>
          <!-- Try premium -->
          <ion-item>
            <ion-button (click)="purchaseTry()">TRY Premium</ion-button><br />
          </ion-item>
          <!-- UNLOCK ALL FEATURES -->
          <ion-item>
            <ion-button (click)="openUpgrade()">default</ion-button>
          </ion-item>
          <ion-item>
            <ion-button (click)="openUpgrade(true)"
              >Open upgrade new member</ion-button
            >
          </ion-item>
          <!-- FREE LIKES - like_limit_reached -->
          <ion-item>
            <ion-button (click)="likeLimit()">like_limit</ion-button>
          </ion-item>
          <!-- COMMENTS LIMIT - photo_comments_limit -->
          <ion-item>
            <ion-button (click)="photoCommentsLimit()"
              >photo_comments_limit</ion-button
            >
          </ion-item>
          <!-- FLIRTS LIMIT - flirt_limit -->
          <ion-item>
            <ion-button (click)="flirtLimit()">flirt_limit</ion-button>
          </ion-item>
          <!-- MESSAGES LIMIT - messages_limit -->
          <ion-item>
            <ion-button (click)="messagesLimit()">messages_limit</ion-button>
          </ion-item>
          <!-- PHOTO FULL SIZE LIMIT - photo_full_size_limit -->
          <ion-item>
            <ion-button (click)="photoFullSizeLimit()"
              >photo_full_size_limit</ion-button
            >
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>
    <ion-card>
      <ion-card-header>
        <ion-card-title>Misc</ion-card-title>
      </ion-card-header>

      <ion-card-content>
        <ion-item>
          <ion-button (click)="temp()">Temp</ion-button>
        </ion-item>

        <ion-item>
          <ion-button (click)="openCountdown()">Countdown</ion-button>
        </ion-item>

        <ion-item>
          <ion-button (click)="testLoading()">test loading</ion-button>
        </ion-item>

        <ion-item>
          <ion-button (click)="testLoading2()">test loading 2</ion-button>
        </ion-item>

        <ion-item>
          <ion-button (click)="openFreeCountdown()">Free Countdown</ion-button>
        </ion-item>

        <ion-item>
          <ion-button (click)="openIntroduction()">INTRODUCTION</ion-button>
        </ion-item>

        <ion-item>
          <ion-button (click)="openValentinesPopup()">VALENTINES</ion-button>
        </ion-item>

        <ion-item>
          <ion-button (click)="initOffers()">OFFERS</ion-button>
        </ion-item>

        <ion-item>
          <ion-button (click)="offer1()">Offer1</ion-button>
          <ion-button (click)="offer2()">Offer2</ion-button>
          <ion-button (click)="offer3()">Offer3</ion-button>
          <ion-button (click)="offer24h()">Offer 24h</ion-button>
        </ion-item>

        <ion-item>
          <a routerLink="/page/privacy-policy">test privacy policy</a> /
          <a routerLink="/page/terms">terms</a>
        </ion-item>

        <ion-item>
          <ion-button (click)="toggleLightDark()"
            >Toggle Light/Dark theme</ion-button
          >
        </ion-item>

        <ion-item>
          <ion-button (click)="deeplink404Profile()"
            >deeplink 404 profile</ion-button
          >
        </ion-item>

        <ion-item>
          <ion-button (click)="deeplinkProfile()">deeplink profile</ion-button>
        </ion-item>

        <ion-item>
          <ion-input [(ngModel)]="inputData.deeplink"></ion-input>
          <ion-button (click)="deeplinkTest()">deeplink test</ion-button>
        </ion-item>

        <ion-item>
          <ion-button (click)="deeplinkChat()">deeplink chat</ion-button>
        </ion-item>

        <ion-item>
          <ion-button (click)="deeplinkIntroduction()"
            >deeplink introduction</ion-button
          >
        </ion-item>

        <ion-item>
          <ion-button (click)="testAnalytics()">Test analytics</ion-button>
        </ion-item>

        <ion-item>
          <ion-button (click)="openWelcomeWizard()"
            >open welcome wizard</ion-button
          >
        </ion-item>
        <ion-item>
          <ion-button (click)="restart()">restart</ion-button>
        </ion-item>
        <ion-item>
          <ion-button (click)="deeplinkPhotoTest()"
            >photo deeplink test</ion-button
          >
        </ion-item>
        <ion-item>
          <ion-button (click)="deeplinkPhotoTest2()"
            >photo deeplink test 2</ion-button
          >
        </ion-item>
        <ion-item>
          <ion-button (click)="deeplinkPhotoTest3()"
            >photo deeplink test 3</ion-button
          >
        </ion-item>
        <ion-item>
          <ion-button (click)="showNotificationPopup()"
            >Show Notification Popup</ion-button
          >
        </ion-item>
        <!--
        <ion-item
          >Profile menu:
          <app-profile-menu [profileData]="this.testUser"></app-profile-menu
        ></ion-item>
          -->
      </ion-card-content>
    </ion-card>

    <ion-card>
      <ion-card-header>
        <ion-card-title>Refresher</ion-card-title>
      </ion-card-header>

      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-button (click)="refresh()" color="primary"
              >refresh services and data</ion-button
            >
          </ion-item>
        </ion-list>
        <ion-list>
          <ion-item>
            <ion-button (click)="temp()" color="primary">temp</ion-button>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <ion-card>
      <ion-card-header>
        <ion-card-title>PRODUCTS SCREENSHOTS</ion-card-title>
      </ion-card-header>

      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-button (click)="openUpgrade()" id="purchase-default">Default</ion-button>
          </ion-item>
          <ion-item>
            <ion-button (click)="purchaseDiscounted()" id="purchase-new-member"
              >New Member Special Offer</ion-button
            >
          </ion-item>
          <ion-item>
            <ion-button (click)="openGoPremiumDownloadedPage()" id="purchase-app-downloaded"
              >App Downloaded Offer</ion-button
            >
          </ion-item>
          <ion-item>
            <ion-button (click)="offer24h()" id="purchase-app-downloaded-24h"
              >App Downloaded Offer 24h</ion-button
            >
          </ion-item>
          <ion-item>
            <ion-button (click)="purchaseCampaign('default')" id="purchase-try-1-month"
              >Try 1 month 1$</ion-button
            >
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>
  </ion-list>
</ion-content>
