import { ComponentFixture, TestBed, waitForAsync } from "@angular/core/testing";
import { IonicModule } from "@ionic/angular";

import { DebugPage } from "./debug.page";
import { LoaderService } from "../../services/loader.service";
import { ApiService } from "../../services/api/api.service";
import { UtilsService } from "../../services/utils.service";
import { Debug } from "../../helpers/debug";
import { StorageService } from "../../services/storage.service";
import { HttpClient } from "@angular/common/http";
import { Observable } from "rxjs";

describe("DebugPage", () => {
  let component: DebugPage;
  let fixture: ComponentFixture<DebugPage>;
  let loaderSpy: LoaderService;
  let httpSpy: HttpClient;
  let storageSpy: StorageService;

  beforeEach(
    waitForAsync(() => {
      loaderSpy = jasmine.createSpyObj("LoaderService", [
        "storeLoaderStatus",
        "isLoading",
      ]);
      httpSpy = jasmine.createSpyObj("HttpClient", {
        request: new Observable<any>(),
      });
      storageSpy = jasmine.createSpyObj("StorageService", ["get", "set"]);

      TestBed.configureTestingModule({
        declarations: [DebugPage],
        imports: [IonicModule.forRoot()],
        providers: [
          { provide: LoaderService, useValue: loaderSpy },
          { provide: HttpClient, useValue: httpSpy },
          ApiService,
          UtilsService,
          Debug,
          { provide: StorageService, useValue: storageSpy },
        ],
      }).compileComponents();

      fixture = TestBed.createComponent(DebugPage);
      component = fixture.componentInstance;
      fixture.detectChanges();
    })
  );

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
