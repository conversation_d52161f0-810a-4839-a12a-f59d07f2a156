import { OnInit } from "@angular/core";
import { WelcomePageData } from "../welcome/welcome-page-data.class";
import { WelcomeWizardService } from "../../services/welcome-wizard.service";
import { Debug } from "../../helpers/debug";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { TranslateService } from "@ngx-translate/core";
import { Router } from "@angular/router";
import { ApiParams } from "../../services/api/api.params";
import { WelcomeProfileService } from "../../services/welcome-profile.service";
import { MiscService } from "src/app/services/misc.service";
import { firstValueFrom } from "rxjs";
import { AnalyticsService } from "src/app/services/analytics.service";
import {
  TME_welcome_error,
  TME_welcome_form_interaction,
} from "src/app/classes/gtm-event.class";
import { UtilsService } from "src/app/services/utils.service";

export class WelcomeWizardPage {
  public pageData: WelcomePageData;
  protected wpIndex: number;

  protected processing: boolean = false;

  /**
   * We do not allow back button on the first page
   * the user must complete this
   * @returns
   */
  public doHideBackButton(): boolean {
    return this.isFirstPage();
  }

  public isFirstPage(): boolean {
    return this.wpIndex == 0;
  }

  constructor(
    public welcomeProfileService: WelcomeProfileService,
    public welcomeWizard: WelcomeWizardService,
    public debug: Debug,
    public dialog: DialogHelperService,
    public translate: TranslateService,
    public router: Router,
    public misc: MiscService,
    public analytics: AnalyticsService,
    public utils: UtilsService
  ) {
    if (this.router.getCurrentNavigation().extras.state) {
      this.wpIndex = this.router.getCurrentNavigation().extras.state.index;
    }

    this.analytics.logEvent("welcome_form_interaction", <
      TME_welcome_form_interaction
    >{
      step_number: this.wpIndex + 1 + " step",
      step_type:
        this.welcomeWizard.pages[this.wpIndex]?.pageData?.title_raw ||
        this.welcomeWizard.pages[this.wpIndex]?.pageData?.question_name_raw ||
        "N/A",
      total_steps: this.welcomeWizard.pages.length + " steps",
    });

    // we bind it to the outter data so we can set it directly ( reference )
    this.pageData = this.welcomeWizard.pages[this.wpIndex]?.pageData;
    if (!this.pageData) {
      this.debug.le(
        "welcome wizard page data is not found, using default object"
      );
      this.pageData = new WelcomePageData();
      this.pageData.title = "ERROR: Page data empty";
      this.pageData.action = "ERROR";
    }
  }

  public isProcessing() {
    return this.processing;
  }

  public getButtonText() {
    // if we have a definitive text, show that
    // if (this.pageData.button_yes) {
    //   return this.pageData.button_yes;
    // }

    // if we don't have it, show default
    return this.translate.instant(this.isLastPage() ? "finish" : "next");
  }

  public alternateButtonClick() {
    this.skip();
  }

  /**
   * Skip current page
   */
  public skip() {
    // this.welcomeWizard.analytics.sendEvent(
    //   this.getStepString() + " | skipped",
    //   this.getAnalyticsCategory(),
    //   this.getAnalyticsLabel()
    // );

    // finish if last page
    if (this.isLastPage()) {
      this.finish();
      return;
    }

    // open next page if needed - do validation if needed
    this.welcomeWizard.open(this.wpIndex + 1);
  }

  /**
   * replace <> tags on appropriate places
   * @param input input string
   */
  public replaceTags(input: string) {
    let _fname =
      this.welcomeWizard.firstname ||
      this.welcomeWizard.ownProfile?.account?.first_name ||
      this.welcomeWizard.ownProfile?.account?.username ||
      null;

    if (!_fname) {
      return input;
    }

    return input.replace(/<firstname>/gi, _fname);
  }

  public onWpgChange(e) {
    this.welcomeWizard.pages[this.wpIndex].pageData = e;
  }

  public executeMainButtonAction() {
    // @todo get the string and execute button action - implement
    // for now it will just go to next page, but that will be the default
    return this.next();
  }

  public mainButtonClick() {
    if (this.pageData.action == "ERROR") {
      this.misc.restartApplication();
      return;
    }

    if (this.pageData.action) {
      this.executeMainButtonAction();
      return;
    }

    return this.next();
  }

  /**
   * Dummy function for async validation - individual pages can override
   */
  public validate(): Promise<any> {
    return Promise.resolve();
  }

  public save(): Promise<any> {
    let value = this.pageData.values;

    if (Array.isArray(this.pageData.values)) {
      value = value.join(",");
    }

    if (this.pageData.name == "firstname") {
      // if user gives us new first name, update it locally
      let _fname = Array.isArray(this.pageData.values)
        ? this.pageData.values[0]
        : this.pageData.values;
      this.welcomeWizard.firstname = _fname;
      this.welcomeWizard.ownProfile.account.first_name = _fname;
    }

    if (this.pageData.name == "headline") {
      // if user gives us nenw title, update it locally
      let _title = Array.isArray(this.pageData.values)
        ? this.pageData.values[0]
        : this.pageData.values;
      this.welcomeWizard.ownProfile.account.title = _title;
    }

    if (this.pageData.name == "description") {
      // if user gives us new  description, update it locally
      let _description = Array.isArray(this.pageData.values)
        ? this.pageData.values[0]
        : this.pageData.values;
      this.welcomeWizard.ownProfile.account.description = _description;
    }

    let params: ApiParams.WelcomeProfileSave.type = {
      value: value,
      what: this.pageData._key,
      id: 0,
    };

    if (this.pageData._id >= 0) {
      params.id = this.pageData._id;
    }

    // set first name if changed

    if (this.pageData._key == "firstname") {
      this.welcomeWizard.firstname = this.pageData.values;
    }

    // save data to server
    return firstValueFrom(this.welcomeProfileService.save(params));
  }

  /**
   * Common function to go to the next page / or finish wizard
   */
  public next() {
    this.processing = true;

    this.validate()
      .then(() => {
        this.save()
          .then(() => {
            this.processing = false;

            // finish if last page
            if (this.isLastPage()) {
              this.finish();
              return;
            }

            // open next page if needed - do validation if needed
            this.welcomeWizard.open(this.wpIndex + 1);
          })
          .catch((err) => {
            this.analytics.logError(
              "welcome_error",
              this.utils.figureOutTheErrorKey(err, "default_error")
            );

            if (err) {
              this.dialog.showToastBaseOnErrorResponse(err);
            }

            this.processing = false;
            // @todo add errors to the list
            this.debug.le("welcome_wizard_error", err);
          });
      })
      .catch((err) => {
        this.processing = false;

        // @todo add errors to the list
        this.debug.le("welcome_wizard_error", err);

        this.analytics.logError(
          "welcome_error",
          this.utils.figureOutTheErrorKey(err, "welcome_validation_error")
        );

        if (err) {
          this.dialog.showToastBaseOnErrorResponse(err);
        }
      });
  }

  /**
   * Is the current page the last in the list?
   */
  public isLastPage() {
    return this.welcomeWizard.pages.length - 1 <= this.wpIndex;
  }

  /**
   * Finish function
   */
  public finish() {
    // @todo open payment for user, and if user closes payment page
    // DONT FORGET !!!!!!!!!! to go to next in syncQueue

    // save data completion flag

    this.welcomeWizard.closeWizard(true);
  }

  public getAlternateButtonText() {
    return this.pageData.button_no
      ? this.pageData.button_no
      : this.translate.instant("skip");
  }
}
