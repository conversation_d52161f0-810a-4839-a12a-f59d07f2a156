<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <div class="avatarUsername">
      <ion-avatar class="avatar" style="width: 22px; height: 22px">
        <img [src]="'assets/img/sales.jpg'" />
      </ion-avatar>
      <span class="userName">{{ 'inbox_deals' | translate }}</span>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content #content>
  <ion-list>
    <ion-grid class="chatContainer">
      @for (msg of chatService.offers; track msg) {
        <ion-row class="bubbleWrapper">
          <ion-col class="chatMessage">
            <app-external-content [content]="msg.body"></app-external-content>
            <ion-row>
              <ion-col class="ion-no-padding">
                <small class="chatTime">{{msg.ts*1000 | date:'shortTime'}}</small>
              </ion-col>
              <ion-col class="ion-no-padding ion-text-end">
                <small class="chatTime">{{msg.ts*1000 | date:'shortDate'}}</small>
              </ion-col>
            </ion-row>
          </ion-col>
        </ion-row>
      }
    </ion-grid>
  </ion-list>
</ion-content>
