import { Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { ChatService } from "../../services/chat.service";

@Component({
  selector: "app-offers",
  templateUrl: "./offers.page.html",
  styleUrls: ["./offers.page.scss"],
  standalone: false,
})
export class OffersPage implements OnInit {
  @ViewChild("content", { static: false }) contentRef: any;

  private _interval = null;

  constructor(
    public router: Router,
    public chatService: ChatService,
  ) {}

  ngOnInit() {}

  ionViewWillEnter() {
    this.scrollWhenLoaded();
  }

  public scrollToBottom() {
    setTimeout(() => {
      if (this.chatService.offers.length <= 1) return;

      this.contentRef.scrollToBottom(500);
    }, 200);
  }

  private scrollWhenLoaded() {
    this._interval = setInterval(() => {
      clearInterval(this._interval);
      this.scrollToBottom();
    }, 200);
  }
}
