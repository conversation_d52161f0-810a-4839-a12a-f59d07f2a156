import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { OffersPageRoutingModule } from "./offers-routing.module";

import { OffersPage } from "./offers.page";
import { TranslateModule } from "@ngx-translate/core";
import { ExternalContentComponentModule } from "src/app/components/external-content/external-content.component.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    OffersPageRoutingModule,
    ExternalContentComponentModule,
  ],
  declarations: [OffersPage],
})
export class OffersPageModule {}
