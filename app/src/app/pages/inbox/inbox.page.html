<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ 'title_inbox' | translate }}</ion-title>
    <ion-buttons slot="end" class="ion-margin-right">
      <ion-spinner
        name="crescent"
        [class.hidden]="!chatService.isLoading()"
      ></ion-spinner>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <app-happy-hour></app-happy-hour>

  <ion-refresher slot="fixed" (ionRefresh)="chatService.refreshRoster($event)">
    <ion-refresher-content refreshingSpinner="crescent"></ion-refresher-content>
  </ion-refresher>

  <ion-fab slot="fixed" vertical="top" horizontal="end" [edge]="true"  #fab
    [class.hidden]="(chatService.isRosterEmpty() && chatService.isOffersEmpty()) || chatService.isLoading()">
    <ion-fab-button (click)="this.showCheckboxes = !this.fab.activated">
      <ion-icon name="chevron-down-circle"></ion-icon>
    </ion-fab-button>
    <ion-fab-list side="bottom" stayOpen="true">
      <ion-fab-button (click)="this.toggleSelection($event, true)">
        <ion-icon name="checkbox-outline" [attr.aria-label]="'select_all'|translate"></ion-icon>
      </ion-fab-button>
      <ion-fab-button (click)="this.toggleSelection($event, false)">
        <ion-icon name="square-outline" [attr.aria-label]="'deselect_all' | translate"></ion-icon>
      </ion-fab-button>
      <ion-fab-button (click)="this.deleteSelected($event)">
        <ion-icon name="trash-outline" [attr.aria-label]="'delete_selected' | translate"></ion-icon>
      </ion-fab-button>
      <ion-fab-button (click)="this.markSelected($event, 'read')">
        <ion-icon name="mail-open-outline" [attr.aria-label]="'mark_as_read_selected' | translate"></ion-icon>
      </ion-fab-button>
      <ion-fab-button (click)="this.markSelected($event, 'unead')">
        <ion-icon name="mail-unread-outline" [attr.aria-label]="'mark_as_unread_unselected' | translate"></ion-icon>
      </ion-fab-button>
    </ion-fab-list>
  </ion-fab>

  <ion-grid
    [class.hidden]="!chatService.isRosterEmpty() || !chatService.isOffersEmpty()"
    >
    <ion-row>
      <ion-col class="ion-text-center">
        <h1>{{ 'chat_empty_title' | translate }}</h1>
        <ion-text>{{ 'chat_empty_description' | translate }}</ion-text><br />
        <ion-button class="ion-margin-top" [routerLink]="['/app/tabs/browse']">
          {{ "chat_empty_button" | translate }}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>

  <ion-searchbar
    [class.hidden]="chatService.isRosterEmpty() && chatService.isOffersEmpty()"
    [(ngModel)]="filterQuery"
    showCancelButton="never"
    (ionInput)="search()"
    placeholder="{{'search_inbox' | translate}}"
  ></ion-searchbar>


  <ion-grid
    [class.hidden]="chatService.isRosterEmpty() || !filterNoResults"
    class="ion-text-center inbox-filter-no-results"
    >
    <ion-row>
      <ion-col>
        <ion-icon color="primary" name="mail"></ion-icon>
        <h3>{{ 'inbox_filter_no_results' | translate }}</h3>
      </ion-col>
    </ion-row>
  </ion-grid>
  @if (!chatService.isLoading() && !chatService.isOffersEmpty()) {
    <ion-list>
      <ng-container>
        <ion-item-sliding #itemSliding (click)="openOffersPage()">
          <ion-item>
            <ion-avatar slot="start" class="avatar">
              <img [src]="'assets/img/sales.jpg'" />
            </ion-avatar>
            <div class="short-text">
              <h2 class="offer-title">
                <span>{{ 'inbox_deals' | translate }}</span>
              </h2>
              <p class="offer-message ion-text-uppercase">
                <app-external-content
                  [content]="(('inbox_special_upgrade' | translate) | truncate:[80,'...'])"
                ></app-external-content>
              </p>
            </div>
          </ion-item>
        </ion-item-sliding>
      </ng-container>
    </ion-list>
  }

  @if (chatService.roster$ | async; as roster) {
    <ion-list
      [class.hidden]="chatService.isRosterEmpty()"
      >
      @if (adsService.getAdData()) {
        <ion-item-sliding>
          <ion-item (click)="openAdLink()">
            <ion-avatar slot="start">
              <ion-img [src]="adsService.getAdData().image"></ion-img>
            </ion-avatar>
            <ion-label>
              <h2>{{ adsService.getAdData().title }}</h2>
              <p>
                <span class="ad-sign">{{ "ad_prefix" | translate }}</span
                  >&nbsp;<span>{{ adsService.getAdData().description }}</span>
                </p>
              </ion-label>
            </ion-item>
          </ion-item-sliding>
        }
        @for (item of roster | filter:filterQuery; track item) {
          @if (item.username) {
            <ion-item-sliding #itemSliding class="inbox-item-{{item.id}}">
              <ion-checkbox slot="start" [class.hidden]="!this.showCheckboxes" #leftCheckbox class="left-checkbox" [value]="item.id"></ion-checkbox>
              <ion-item (click)="open(item)" (touchstart)="this.onTouchStart()"
                (touchmove)="this.sclearTimeout()"
                (touchend)="this.sclearTimeout()">
                <ion-avatar slot="start" class="avatar">
                  <img src="{{ item.avatar_url }}" />
                </ion-avatar>
                <div
                  class="short-text"
                  [ngClass]="item?.is_read == true ? 'read' : 'unread'"
                  >
                  <h2 class="username">
                    <span
                      >{{ item.appended_data?.first_name || item?.username || "?"
                      }}</span
                      ><span class="status"></span>
                      <span class="date-arrow">
                        <small class="date" [class.hidden]="!item?.ts"
                          >{{ 1000 * item?.ts | date: 'shortDate' }}</small
                          >
                          <ion-icon name="chevron-forward" class="arrow"></ion-icon>
                        </span>
                      </h2>
                      <p class="message">
                        <app-external-content
                [content]="
                !doOpenUpgrade(item) ?
                ((item?.last_message ?? '') | truncate:[80,'...']) :
                ((item?.last_message ?? '') | truncate:[20,'...'])
                "
                        ></app-external-content>
                      </p>
                      <p [class.hidden]="!doOpenUpgrade(item)" class="upgrade">
                        {{ ( item?._is_match ? 'upgrade_to_send_message' :
                        'upgrade_to_read' ) | translate }}
                      </p>
                    </div>
                  </ion-item>
                </ion-item-sliding>
              }
            }
          </ion-list>
        }

        <!-- Start Sceleton content before loading, but must be better option to do this like template -->
        <!--[class.hidden]="!chatService.isLoading()"-->
        <ion-list class="ion-no-padding" [class.hidden]="!chatService.isLoading()">
          @for (_c of [].constructor(15); track _c) {
            <ion-item class="skeletonLoad">
              <ion-avatar slot="start">
                <ion-skeleton-text></ion-skeleton-text>
              </ion-avatar>
              <div style="width: 100%">
                <h2>
                  <ion-skeleton-text
                    animated
                    class="ion-float-left"
                    style="width: 40%"
                  ></ion-skeleton-text>
                  <ion-skeleton-text
                    animated
                    class="ion-float-right"
                    style="width: 20%"
                  ></ion-skeleton-text>
                </h2>
                <p>
                  <ion-skeleton-text animated style="width: 100%"></ion-skeleton-text>
                </p>
              </div>
            </ion-item>
          }
        </ion-list>
        <!-- End Sceleton content here -->

        <ion-infinite-scroll
          [class.hidden]="chatService.didReachEnd()"
          threshold="100px"
          id="infinite-scroll"
          (ionInfinite)="chatService.next($event)"
          >
          <ion-infinite-scroll-content
            loadingSpinner="crescent"
          ></ion-infinite-scroll-content>
        </ion-infinite-scroll>

        <!-- error container { -->
        <ion-grid
          class="statusMessage ion-text-center"
          [class.hidden]="!chatService.isError()"
          >
          <ion-row>
            <ion-col>
              <ion-icon color="primary" name="alert-circle-outline"></ion-icon>
              <h3>{{ 'error_list' | translate }}</h3>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <ion-button color="primary" (click)="this.chatService.refreshRoster()"
                >{{ 'refresh' | translate }}</ion-button
                >
              </ion-col>
            </ion-row>
          </ion-grid>
          <!-- } error container -->

          <div
            style="text-align: center; display: none"
            [class.hidden]="!(chatService.didReachEnd() && !chatService.isRosterEmpty() && !chatService.isLoading() && !chatService.isError())"
            >
            ¯\_(ツ)_/¯<br /><br />
          </div>
        </ion-content>
