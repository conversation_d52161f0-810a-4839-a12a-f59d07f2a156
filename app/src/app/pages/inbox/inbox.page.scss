.avatar {
  position: relative;

  img {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    -ms-user-drag: none;
    user-drag: none; 
  }
}

.username {
  font-weight: 600;
  position: relative;
  font-size: 16px;
  margin: 8px 0 5px;

  * {
    vertical-align: middle;
  }
}

.short-text {
  display: block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
  ;
}

.status {
  background-color: var(--ion-color-primary);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: none;
  margin: 0 6px;
}

.unread {
  .username {
    color: var(--ion-color-primary);
  }

  .status {
    margin-top: 2px;
    display: inline-block;
  }
}

.upgrade {
  color: var(--ion-color-danger);
}

.message {
  color: var(--ion-color-medium-tint);
  overflow: hidden;
  margin: 7px 10px 10px 0px;
  font-size: 14px;
  line-height: 20px;
  text-overflow: inherit;
}

app-external-content {
  white-space: normal;
}

.upgrade {
  margin: 10px 0 15px;
}

.date-arrow {
  position: absolute;
  right: 0;
  top: 0;

  * {
    color: var(--ion-color-medium-tint);
    font-weight: normal;
    vertical-align: middle;
  }

  .date {
    margin-right: 6px;
  }

  .arrow {
    font-size: 14px;
  }
}

.option {
  width: 61px;
}

.ico-option {
  font-size: 24px;
}

.statusMessage {
  margin-top: 15vh;

  ion-icon {
    font-size: 7rem;
    opacity: .3;
  }

  h6 {
    color: var(--ion-color-medium);
  }
}

ion-item.skeletonLoad {
  width: 100%;
  margin-bottom: .5rem;

  h2 {
    float: left;
    width: 100%;
    margin-top: 0;
  }

  p {
    clear: both;
    margin: .5rem 0;
  }
}

.inbox-filter-no-results {
  ion-icon {
    font-size: 7rem;
    opacity: .3;
  }
}

.offer-title {
  font-weight: 600;
  position: relative;
  font-size: 16px;
  margin: 8px 0 5px;

  * {
    vertical-align: middle;
  }
}

.offer-message {
  font-weight: bold;
}

.ad-sign {
  font-weight: 500;
  color: var(--ion-color-light);
  background: var(--ion-color-dark);
  padding: 3px;
  border-radius: 5px;
}

ion-item-sliding {
  display: flex;
  align-items: center;
}

.left-checkbox {
  margin:5px 0px 5px 10px;
}

ion-item {
  flex: 1; 
}

ion-checkbox {
  margin-right: 10px; 
}

ion-fab-list ion-icon { color: var(--ion-color-primary)}
