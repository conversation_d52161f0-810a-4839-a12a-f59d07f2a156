import {
  Compo<PERSON>,
  OnInit,
  <PERSON><PERSON>hildren,
  <PERSON>Child,
  QueryList,
} from "@angular/core";
import { ChatService } from "src/app/services/chat.service";
import { ChatContactItem } from "../../services/chat-contact-item.interface";
import { Router } from "@angular/router";
import { StateService } from "src/app/services/state.service";
import _ from "lodash";
import { AccountService } from "src/app/services/account.service";
import { PurchaseService } from "src/app/services/purchase.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { AdsService } from "../../services/ads.service";
import { Browser } from "@capacitor/browser";
import { IonCheckbox, IonFab } from "@ionic/angular";
import { ApiService } from "src/app/services/api/api.service";
import { ChatMessageService } from "src/app/services/chat-message.service";
import { firstValueFrom } from "rxjs";

enum MessageAction {
  Read = "read",
  Unread = "unread",
}

@Component({
  selector: "app-inbox",
  templateUrl: "./inbox.page.html",
  styleUrls: ["./inbox.page.scss"],
  standalone: false,
})
export class InboxPage implements OnInit {
  @ViewChild("fab") fab: IonFab;
  @ViewChildren("leftCheckbox") checkboxes!: QueryList<IonCheckbox>;
  @ViewChildren("itemSliding")
  public itemSliding;

  public filterQuery: string = "";
  public filterNoResults: boolean = false;

  public showCheckboxes: boolean = false;
  public longPressTimer: any;
  private isLongPress: boolean = false;

  constructor(
    public chatService: ChatService,
    public chatMessagesService: ChatMessageService,
    private router: Router,
    public stateService: StateService,
    private accountService: AccountService,
    private purchaseService: PurchaseService,
    private dialog: DialogHelperService,
    public adsService: AdsService,
    public api: ApiService,
  ) {}
  ngOnInit() {}

  public search() {
    setTimeout(() => {
      this.filterNoResults = !!!this.itemSliding.length;
    }, 10);
  }

  public switchMassAction() {
    this.showCheckboxes = !this.fab.activated;
  }

  public async open(item: ChatContactItem) {
    // pass item data throught state object
    item = _.merge(item, item?.appended_data);
    this.stateService.set("chat_message_open", item.username, item);
    this.stateService.set("sendMessageProfile", null, item);
    // save the item as the last clicked item
    this.stateService.set("lastClickedInboxItem", null, item);
    if (this.doOpenUpgrade(item)) {
      this.purchaseService
        .setDialogType("upgrade_to_read")
        .openUpgradeDialog((error) => {
          this.dialog.showToastBaseOnErrorResponse(error, null, false);
        });
    } else {
      let queryParams = {};
      if (item.add_thread_id) {
        queryParams = { thread_id: item.id };
      }

      this.router.navigate(["/app/chat-messages", item.username, queryParams]);
    }
  }

  public doOpenUpgrade(item: ChatContactItem): boolean {
    // if the item requires upgrade, and the user in not premium already
    return (item?.do_open_upgrade ?? true) && !this.accountService.isPremium();
  }

  ionViewWillEnter() {
    // refresh roster on page enter
    this.chatService.refreshRoster();
  }

  public openOffersPage() {
    this.router.navigate(["/app/offers"]);
  }

  public async openAdLink() {
    await Browser.open({ url: this.adsService.getAdData().redirect });
  }

  public onTouchStart() {
    this.isLongPress = false;
    this.longPressTimer = setTimeout(() => {
      this.isLongPress = true;
      this.fab.activated = this.showCheckboxes = !this.showCheckboxes;
    }, 800);
  }

  public sclearTimeout() {
    clearTimeout(this.longPressTimer);
  }

  /**
   * Toggle checkboxes selected unselected all
   *
   * @param event
   * @param isSelected
   */
  public toggleSelection(event: Event, isSelected: boolean) {
    event.stopImmediatePropagation();
    this.checkboxes.forEach((checkbox) => {
      checkbox.checked = isSelected;
    });
  }

  /**
   * Mark selected as read or unread
   *
   * @param event
   * @param action
   */
  public async markSelected(event: Event, action) {
    event.stopImmediatePropagation();
    const values = await this.getSelectedValues();
    try {
      const response =
        action === MessageAction.Read
          ? await this.chatMessagesService.markAsRead(values)
          : await this.chatMessagesService.markAsUnread(values);

      if (response?.errors?.length === 0) {
        values.forEach((value) => {
          const el = document.querySelector(
            `.inbox-item-${value} div.short-text`,
          );
          if (el) {
            if (action === "read") {
              el.classList.remove("unread");
              el.classList.add("read");
            } else {
              el.classList.remove("read");
              el.classList.add("unread");
            }
          }
        });
      } else {
        console.warn("The operation did not complete successfully.", response);
      }
    } catch (error) {
      console.error(
        `Error during markAs${action === MessageAction.Read ? "Read" : "Unread"}:`,
        error,
      );
    }
  }

  /**
   * Delete selected threads and his subthreads
   * @webcmd ?c=inbox&a=delete-message&thread%5B1336571%5D=-1&thread%5B1336570%5D=-1
   * @webcmd cmd=inbox.deletemessage&threads[]=1334617&threads[]=6456496
   * @param event
   */
  public async deleteSelected(event: Event) {
    event.stopImmediatePropagation();
    const values = await this.getSelectedValuesAndSubvalues();

    try {
      const response = await this.chatMessagesService.deleteThreads(values);

      if (response?.errors?.length === 0) {
        values.forEach((value) => {
          const el = document.querySelector(`.inbox-item-${value}`);
          if (el) {
            el.remove();
          }
        });
      } else {
        console.warn(
          "The operation deleteSelected did not complete successfully.",
          response,
        );
      }
    } catch (error) {
      console.error(`Error during deleteSelected`, error);
    }
  }

  /**
   * Get selected threads ids
   *
   * @returns thread_ids array
   */
  private async getSelectedValues(): Promise<number[]> {
    return this.checkboxes
      .filter((checkbox) => checkbox.checked)
      .map((checkbox) => {
        const value = parseInt(checkbox.value, 10);
        return isNaN(value) ? null : value;
      })
      .filter((value) => value !== null) as number[];
  }

  /**
   * Get selected threads and subthreads ids
   *
   * @returns thread_ids array
   */
  private async getSelectedValuesAndSubvalues(): Promise<number[]> {
    const values = await this.getSelectedValues();
    let subvalues: number[] = [];

    const roster = await firstValueFrom(this.chatService.roster$);
    roster.forEach((val) => {
      if (values.includes(Number(val.id))) {
        subvalues = subvalues.concat(val.thread_ids);
      }
    });

    return values.concat(subvalues);
  }
}
