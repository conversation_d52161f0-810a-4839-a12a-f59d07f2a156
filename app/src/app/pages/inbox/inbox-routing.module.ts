import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { InboxPage } from "./inbox.page";

const routes: Routes = [
  {
    path: "",
    component: InboxPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class InboxPageRoutingModule {}
