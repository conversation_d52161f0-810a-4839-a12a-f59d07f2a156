import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { InboxPageRoutingModule } from "./inbox-routing.module";
import { InboxPage } from "./inbox.page";
import { TranslateModule } from "@ngx-translate/core";
import { HappyHourComponentModule } from "../../components/happy-hour/happy-hour.component.module";
import { ExternalContentComponentModule } from "src/app/components/external-content/external-content.component.module";
import { PipesModule } from "src/app/pipes/pipes.module";
import LongPressModule from "src/app/directives/long-press.directive.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    InboxPageRoutingModule,
    HappyHourComponentModule,
    ExternalContentComponentModule,
    PipesModule,
    LongPressModule,
  ],
  declarations: [InboxPage],
})
export class InboxPageModule {}
