import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { ProfilePageRoutingModule } from "./profile-routing.module";

import { ProfilePage } from "./profile.page";

import { TranslateModule } from "@ngx-translate/core";
import { PipesModule } from "src/app/pipes/pipes.module";
import { HappyHourButtonComponentModule } from "../../components/happy-hour-button/happy-hour-button.component.module";
import { RulesPopupComponentModule } from "../../components/rules-popup/rules-popup.component.module";
import { CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { ProfileMenuComponentModule } from "src/app/components/profile-menu/profile-menu.component.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    ProfilePageRoutingModule,
    PipesModule,
    HappyHourButtonComponentModule,
    RulesPopupComponentModule,
    ProfileMenuComponentModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [ProfilePage],
})
export class ProfilePageModule {}
