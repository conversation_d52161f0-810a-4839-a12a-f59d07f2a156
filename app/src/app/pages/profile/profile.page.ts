import { Component, <PERSON><PERSON>hil<PERSON>, ElementRef } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ProfileService } from "src/app/services/profile.service";
import _ from "lodash";
import { StateService } from "src/app/services/state.service";
import { ApiCommands } from "src/app/services/api/api.commands";
import { ApiService } from "src/app/services/api/api.service";
import { FlirtService } from "src/app/services/flirt.service";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { ProfileUser } from "src/app/schemas/profile-user";
import { DelayHelperService } from "src/app/services/delay-helper.service";
import { HappyHourService } from "../../services/happy-hour.service";
import { Debug } from "../../helpers/debug";
import { ChatMessageService } from "../../services/chat-message.service";
import { AccountService } from "src/app/services/account.service";
import { AclService } from "src/app/services/acl.service";
import { firstValueFrom } from "rxjs";
import { TranslateService } from "@ngx-translate/core";
import { NavController } from "@ionic/angular";
import { PurchaseService } from "../../services/purchase.service";
import { PictureUploadHelperService } from "../../picture-upload-helper.service";
import { PictureService } from "../../services/picture.service";
import { Swiper } from "swiper";
import { Utils } from "src/app/helpers/utils";

@Component({
  selector: "app-profile",
  templateUrl: "./profile.page.html",
  styleUrls: ["./profile.page.scss"],
  standalone: false,
})
export class ProfilePage {
  @ViewChild("swiper") swiperRef: ElementRef | undefined;
  swiper: Swiper;

  public showMore = false;

  public utils = Utils;

  /**
   * Data.
   */
  public data: ProfileUser.type;
  private username: string = "";

  /**
   * requestPhoto text.
   *
   * @type {boolean}
   */
  public requestPhotoText: string = "request_photo";

  public disableRequestPhotoButtonIfRequested: boolean = false;

  /**
   * Request photo button status?
   *
   * @type {boolean}
   */
  public showRequestPhotoButton: boolean = true;

  /**
   * Display request photo dialog?
   *
   * @type {boolean}
   */
  public showRequestPhotoDialog: boolean = false;

  /**
   * Gender translation (1=>his, 2=>her)
   *
   * @type {string}
   */
  public gender: string = "";

  constructor(
    private route: ActivatedRoute,
    public profileService: ProfileService,
    private stateService: StateService,
    private api: ApiService,
    private flirtService: FlirtService,
    private dialog: DialogHelperService,
    private router: Router,
    public delayHelper: DelayHelperService,
    public hhService: HappyHourService,
    public dbg: Debug,
    public inbox: ChatMessageService,
    public accountService: AccountService,
    private acl: AclService,
    private translateService: TranslateService,
    private purchaseService: PurchaseService,
    public pictureUploadHelper: PictureUploadHelperService,
    public pictureService: PictureService,
    public navCtrl: NavController,
  ) {
    this.username = this.route.snapshot.params?.username;

    // you cannot open your own profile in this view
    if (this.accountService.isOwnProfile(this.username)) {
      this.username = "";
    }
  }

  /**
   * Open upgrade page if account is free.
   */
  public async openUpgradePage() {
    if (this.accountService.isPremium()) return;
    if ((await this.swiperRef?.nativeElement.swiper.activeIndex) <= 0) return;
    this.swiperRef?.nativeElement.swiper.slidePrev();
    this.setProfileDataAndOpenUpgradePage();
  }

  public async imageClicked(i) {
    if (!this.accountService.isPremium()) {
      this.setProfileDataAndOpenUpgradePage();
      return;
    }

    this.stateService.set("profile", this.username, this.data);
    await this.router.navigate([
      "/app/photo-full-view",
      "profile",
      this.username,
      i,
    ]);
  }

  private setProfileDataAndOpenUpgradePage() {
    this.stateService.set("profileFullSizeLimit", null, this.data);
    this.purchaseService
      .setDialogType("photo_full_size_limit")
      .openUpgradeDialog((error) => {
        this.dialog.showToastBaseOnErrorResponse(error, null, false);
      });
  }

  public hasData() {
    return !_.isEmpty(this.data);
  }

  public isLoading() {
    return (
      !this.hasData() &&
      !this.wasError() &&
      this.profileService.isGetInProgress(this.username)
    );
  }

  public wasError() {
    return this.profileService.wasGetError(this.username);
  }

  private sortPhotos(): void {
    if (this.hasData()) {
      let photos = this.data.photos;
      if (!photos.length) return;
      photos.sort((a, b) => {
        return a.main_photo == true ? -1 : 1;
      });
      photos[0].main_photo = true;
      this.data.photos = photos;
    }
  }

  public ionViewWillEnter() {
    if (this.data) return;
    this.profileService.get(this.username, null, 1).subscribe({
      next: (data) => {
        // set data
        this.data = data.data;
        this.sortPhotos();
        this.stateService.set("profile", this.username, this.data);
        this.gender =
          this.data.gender_id == 1
            ? this.translateService.instant("his")
            : this.translateService.instant("her");
        this.initializeDisableActionList();
        this.checkIfPhotoRequestAlreadySent();
        this.profileService.addToListIViewed(this.data);
      },
      error: async (e) => {
        await this.navCtrl.navigateRoot("/app/tabs/browse", {
          animated: false,
        });
        this.dialog.showAlert("profile_unavailable", " ", "ok", () => {});
      },
    });
  }

  public ionViewDidEnter(): void {
    this.swiper = this.swiperRef?.nativeElement.swiper;
    this.swiper?.scrollbar.updateSize();
    this.swiper?.update();
  }

  /**
   * See if certain event is in progress
   * @param what
   * @returns
   */
  public isInProgress(
    what: "like" | "pass" | "superlike" | "wink" | "unlike",
  ): boolean {
    let _cmd = {
      like: ApiCommands.ProfileLikeSimple.name,
      unlike: ApiCommands.ProfileUnLikeSimple.name,
      pass: ApiCommands.ProfilePass.name,
      superlike: ApiCommands.ProfileSuperlike.name,
      wink: ApiCommands.FlirtWink.name,
    };

    return this.api.isInEventList({ command: _cmd[what] });
  }

  public flirt() {
    this.router.navigate(["/app/modals/flirt/", this.username]);
  }

  /**
   * Initialize profile button enabled states based on user relationship
   */
  private initializeDisableActionList() {
    this.disableActionList.like =
      ["likes", "superlike", "match"].indexOf(this.data.relationship) >= 0;
    this.disableActionList.superlike =
      ["superlike", "match"].indexOf(this.data.relationship) >= 0;
    this.disableActionList.pass =
      ["dislikes"].indexOf(this.data.relationship) >= 0;
  }

  // list to keep button statuses
  private disableActionList = {
    like: false,
    pass: false,
    superlike: false,
    wink: false,
  };

  /**
   * Should we disable the action
   * @param what
   * @returns
   */
  public doDisableAction(
    what: "like" | "pass" | "superlike" | "wink",
  ): boolean {
    return this.isInProgress(what) || this.disableActionList[what];
  }

  public like() {
    firstValueFrom(this.profileService.simpleLike(this.username))
      .then((res) => {
        this.disableActionList["like"] = true;
        this.disableActionList["pass"] = false;
      })
      .catch((error) => {
        if (
          error?.errors &&
          error?.errors[0]?.key == "likes_limits_reached_free"
        ) {
          this.purchaseService
            .setDialogType("like_limit_reached")
            .openUpgradeDialog((error) => {
              this.dialog.showToastBaseOnErrorResponse(error, null, false);
            });
        }
      });
  }

  public unlike() {
    firstValueFrom(this.profileService.simpleUnlike(this.username))
      .then((res) => {
        firstValueFrom(this.profileService.unmatch(this.username));
        this.disableActionList["like"] = false;
        this.disableActionList["pass"] = true;
      })
      .catch((err) => this.dbg.le("simpleUnlike error"));
  }

  public likeToggle() {
    this.doDisableAction("like") ? this.unlike() : this.like();
  }

  public pass() {
    this.dialog
      .apiCallWithMessage(
        this.profileService.pass(this.username),
        "profile_passed",
      )
      .then(() => {
        this.disableActionList["pass"] = true;
        this.disableActionList["like"] = false;
        this.disableActionList["superlike"] = false;
      });
  }

  public wink() {
    this.dialog
      .apiCallWithMessage(
        this.flirtService.sendWink(this.username),
        "wink_sent",
      )
      .then(() => {
        this.disableActionList["wink"] = true;
      });
  }

  public superlike() {
    this.acl
      .init("superlike")
      .then((res) => {
        if (!res) {
          this.dbg.l("Superlike failed!");
          return;
        }

        this.dialog
          .apiCallWithMessage(
            this.profileService.superLike(this.username),
            "profile_superliked",
          )
          .then(() => {
            this.disableActionList["superlike"] = true;
            this.disableActionList["like"] = true;
            this.disableActionList["pass"] = false;
          });
      })
      .catch((err) => this.dbg.l("Superlike api call failed"));
  }

  /**
   * Open send message page.
   */
  public sendMessage() {
    this.stateService.set("sendMessageProfile", null, this.data);

    if (!this.accountService.isPremium()) {
      this.purchaseService
        .setDialogType("messages_limit")
        .openUpgradeDialog((error) => {
          this.dialog.showToastBaseOnErrorResponse(error, null, false);
        });

      return;
    }

    // @todo add send message
    this.router.navigate(["app/chat-messages", this.username]);
  }

  /**
   * Request photo.
   */
  public reqestPhoto(): void {
    firstValueFrom(this.profileService.requestPhoto(this.username))
      .then((res) => {
        this.disableRequestPhotoButtonIfRequested = true;
        this.requestPhotoText = "photo_requested";
        this.inbox.photoRequests.push(this.data.user_id);
      })
      .catch(({ error }) => {
        if (
          error.errors.length &&
          error.errors[0].key === "photo_request_add_photo_first"
        ) {
          this.showRequestPhotoDialog = true;
        } else {
          this.dialog.showToastBaseOnErrorResponse(error);
          this.showRequestPhotoButton = true;
        }
      });
  }

  /**
   * Close request photo dialog.
   */
  public closeRequestPhotoDialog(): void {
    this.showRequestPhotoDialog = false;
    this.showRequestPhotoButton = true;
  }

  /**
   * Check if photo request already sent for this profile?
   */
  private checkIfPhotoRequestAlreadySent() {
    this.inbox.photoRequests.forEach((id) => {
      if (id == this.data.user_id) {
        this.showRequestPhotoDialog = false;
        this.showRequestPhotoButton = false;
        this.disableRequestPhotoButtonIfRequested = true;
        this.requestPhotoText = "photo_requested";
      }
    });
  }

  /**
   * Upload photo.
   */
  public photoUpload() {
    this.stateService.set("blockStatusChecker", "", true);

    this.pictureUploadHelper.displayUploadMenu((data) => {
      this.dialog.showLoading();
      firstValueFrom(
        this.pictureService.upload({
          gallery_id: 0,
          image_url: "",
          image_data: data,
        }),
      )
        .then(async ({ data }) => {
          this.accountService.account.photos.push(data.photo);

          // added first photo - set as avatar.
          if (this.accountService.account.photos.length === 1) {
            this.accountService.account.avatar_url = data.photo.thumb_url;
          }
          this.closeRequestPhotoDialog();
        })
        .catch((err) => {
          this.dialog.showToastBaseOnErrorResponse(err);
        })
        .then(() => {
          this.stateService.delete("blockStatusChecker", "");
          this.dialog.hideLoading();
        });
    });
  }
}
