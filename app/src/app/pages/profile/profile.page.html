<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ data?.first_name || data?.username }}</ion-title>
    <ion-buttons slot="end">
      <app-profile-menu
        [profileData]="data"
        [class.hidden]="!hasData()"
      ></app-profile-menu>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  @if (accountService.accountLoaded$ | async) {
    <app-rules-popup
      [profile]="accountService.account"
      [(doShow)]="profileService.doShowRulesPopup"
      [text_1]="'rules_popup_header_1'|translate"
      [text_2]="'rules_popup_header_2'|translate"
      [text_3]="'rules_popup_header_3'|translate"
    ></app-rules-popup>
  }

  @if (this.hasData() && !this.isLoading() && !this.wasError()) {
    <div>
      <ion-card
        class="request-photo-error ion-text-center"
        [class.hidden]="!showRequestPhotoDialog"
        >
        <ion-card-header>
          <ion-icon
            name="close"
            class="ion-float-end"
            (click)="closeRequestPhotoDialog()"
          ></ion-icon>
          <ion-card-title color="primary"
            >{{ 'photo_request_dialog_title' | translate }}</ion-card-title
            >
          </ion-card-header>
          <ion-card-content>
            <p>
              <ion-text color="primary"
                >{{ 'photo_request_dialog_text1' | translate: {gender} }}</ion-text
                >
              </p>
              <p>
                <ion-text>{{ 'photo_request_dialog_text2' | translate }}</ion-text>
              </p>
              <p><ion-icon class="camera-icon" name="camera"></ion-icon></p>
              <ion-button class="ion-text-capitalize" (click)="photoUpload()"
                >{{ 'photo_request_dialog_button' | translate }}</ion-button
                >
              </ion-card-content>
            </ion-card>
            @if (!data.photos.length && !showRequestPhotoDialog) {
              <ion-button
                class="reqest-photo"
                color="light"
                (click)="reqestPhoto()"
                [disabled]="disableRequestPhotoButtonIfRequested"
                >
                <ion-icon
                  name="image-outline"
                  size="small"
                  class="request-photo__icon"
                ></ion-icon>
                <span>{{ requestPhotoText | translate }}</span>
              </ion-button>
            }
            <swiper-container
              #swiper
              [pager]="data.photos.length > 1"
              (swiperslidechange)="openUpgradePage()"
              scrollbar="true"
              >
              @if (data.photos.length > 0) {
                @for (photo of data.photos; track photo; let i = $index) {
                  <swiper-slide>
                    <div (click)="imageClicked(i)" class="slide">
                      <!-- request access for private image {
                      <ion-row class="private-image ion-padding" [class.hidden]="photo.has_access == true">
                        <ion-col>
                          <ion-icon name="lock-closed" class="ion-padding"></ion-icon>
                          <h4>{{ private_image | translate }}</h4>
                          <ion-button>{{ request_access | translate }}</ion-button>
                        </ion-col>
                      </ion-row>
                      } request access for private image -->
                      <ion-img
                        style="pointer-events: none"
                        [src]="photo.thumb_url"
                      ></ion-img>
                    </div>
                  </swiper-slide>
                }
              } @else {
                @if (data.photos.length == 0) {
                  <swiper-slide>
                    <div class="slide">
                      <ion-img
                        style="pointer-events: none"
                        src="assets/placeholder.svg"
                      ></ion-img>
                    </div>
                  </swiper-slide>
                }
              }
            </swiper-container>
            <ion-grid class="ion-no-padding grid-content">
              <ion-row
                class="profile-main-info ion-padding-horizontal ion-padding-bottom"
                >
                <ion-col>
                  <h2 class="profile-username-age">
                    <b>{{ data?.first_name || data?.username }},</b> {{ data.age }}
                  </h2>
                  <div class="user-location">
                    <ion-text>
                      <span
                        >{{ data.city }}, {{ data.country === "US" ? data.state :
                        data.country }}</span
                        >
                      </ion-text>
                    </div>
                  </ion-col>
                </ion-row>
                <ion-row class="ion-padding-horizontal action-buttons">
                  <ion-col class="ion-text-center">
                    <ion-fab-button
                      color="white"
                      class="round-button flirt"
                      (click)="flirt()"
                      >
                      <ion-icon src="assets/icons/ico-flirt.svg"></ion-icon>
                    </ion-fab-button>
                    <ion-label>{{ 'profile_flirt' | translate }}</ion-label>
                  </ion-col>
                  <ion-col class="ion-text-center">
                    <ion-fab-button
                      color="white"
                      class="round-button send-message"
                      (click)="sendMessage()"
                      >
                      <ion-icon src="assets/icons/ico-chat.svg"></ion-icon>
                    </ion-fab-button>
                    <ion-label>{{ 'profile_message' | translate }}</ion-label>
                  </ion-col>
                  <ion-col class="ion-text-center">
                    <ion-fab-button
                      color="white"
                      class="round-button like"
                      (click)="likeToggle()"
                      [disabled]="isInProgress('like')"
                      >
                      <!-- This icon will be displayed when user is not liked yet, when user presses it, it will like the profile -->
                      <ion-icon
                        [class.hidden]="doDisableAction('like') || isInProgress('like') || isInProgress('unlike')"
                        src="assets/icons/ico-heart.svg"
                        class="ico-like"
                        aria-label="Like"
                      ></ion-icon>
                      <!-- This icon will be displayed when user is already liked, when user presses it, it will unlike the profile -->
                      <ion-icon
                        [class.hidden]="!doDisableAction('like') || isInProgress('like') || isInProgress('unlike')"
                        src="assets/icons/ico-broken-heart.svg"
                        aria-label="Unlike"
                        class="ico-unlike"
                      ></ion-icon>
                      <ion-spinner
                        name="crescent"
                        color="primary"
                        [class.hidden]="!isInProgress('like') && !isInProgress('unlike')"
                        aria-label="In progress"
                      ></ion-spinner>
                    </ion-fab-button>
                    <ion-label [class.hidden]="doDisableAction('like')"
                      >{{ 'profile_like' | translate }}</ion-label
                      >
                      <ion-label [class.hidden]="!doDisableAction('like')"
                        >{{ 'profile_unlike' | translate }}</ion-label
                        >
                      </ion-col>
                    </ion-row>
                    <!--
                    <ion-row class="border-bottom ion-padding-horizontal ion-padding-bottom">
                      <ion-col>
                        <app-happy-hour-button
                          *ngIf="hhService.showHH()"
                          (onClick)="sendMessage()"
                        ></app-happy-hour-button>
                        <ion-button
                          expand="block"
                          (click)="sendMessage()"
                          *ngIf="!hhService.showHH()"
                          class="send-msg"
                          >{{ "send_message" | translate }}</ion-button
                          >
                        </ion-col>
                      </ion-row>
                      -->
                      @if (data.title || data.description) {
                        <ion-row
                          class="border-bottom ion-padding-horizontal ion-padding-bottom"
                          >
                          <ion-col>
                            @if (data.title) {
                              <h6>{{ data.title }}</h6>
                            }
                            @if (data.description) {
                              <ion-text color="medium">
                                @if (data.description.length >= 500) {
                                  {{(showMore) ? data.description : data.description | slice:0:500}}
                                  @if (!showMore) {
                                    <span>...</span>
                                  }
                                  @if (!showMore) {
                                    <a href="javascript:;" (click)="showMore=true"
                                      >{{ "show_more" | translate }} »</a
                                      >
                                    }
                                    @if (showMore) {
                                      <a href="javascript:;" (click)="showMore=false">
                                        « {{ "show_less" | translate }}</a
                                        >
                                      }
                                    }
                                    @if (data.description.length < 500) {
                                      {{data.description}}
                                    }
                                  </ion-text>
                                }
                              </ion-col>
                            </ion-row>
                          }
                          @if (data.interests && data.interests.length) {
                            <ion-row
                              class="border-bottom ion-padding-horizontal ion-padding-bottom"
                              >
                              <ion-col>
                                <h6>{{ "my_interests" | translate }}</h6>
                                @for (interest of data.interests; track interest) {
                                  <ion-chip
                                    outline
                                    color="medium"
                                    >
                                    <ion-label>{{ interest }}</ion-label>
                                  </ion-chip>
                                }
                              </ion-col>
                            </ion-row>
                          }
                          @for (item of data.details; track item) {
                            <ion-row
                              class="border-bottom ion-padding-horizontal ion-padding-bottom"
                              >
                              <ion-col>
                                <h6>{{ item.title }}</h6>
                                @for (item of item.data; track item) {
                                  <div
                                    class="details ion-padding-bottom"
                                    >
                                    <ion-text class="question">
                                      <small>{{ item.question }}</small>
                                    </ion-text>
                                    <ion-text color="medium" class="answer" [innerHTML]="item.answer"></ion-text>
                                  </div>
                                }
                              </ion-col>
                            </ion-row>
                          }
                        </ion-grid>
                      </div>
                    }

                    <div [class.hidden]="!this.isLoading() || this.wasError()">
                      <swiper-container>
                        <swiper-slide>
                          <div class="slide">
                            <ion-img
                              style="pointer-events: none"
                              src="assets/placeholder.svg"
                            ></ion-img>
                          </div>
                        </swiper-slide>
                      </swiper-container>

                      <ion-grid class="ion-no-padding grid-content">
                        <ion-row
                          class="profile-main-info profile-main-info-skeleton ion-padding-horizontal ion-padding-bottom"
                          >
                          <ion-col>
                            <h2>
                              <ion-skeleton-text
                                animated
                                class="skeleton-center"
                                style="width: 60%"
                              ></ion-skeleton-text>
                            </h2>
                            <div class="user-location">
                              <ion-skeleton-text
                                animated
                                class="skeleton-center"
                                style="width: 40%"
                              ></ion-skeleton-text>
                            </div>
                          </ion-col>
                        </ion-row>

                        <ion-row class="ion-padding-horizontal action-buttons">
                          <ion-col class="ion-text-center">
                            <ion-fab-button color="white" class="round-button flirt">
                              <ion-icon
                                color="medium"
                                src="assets/icons/ico-flirt.svg"
                              ></ion-icon>
                            </ion-fab-button>
                            <ion-skeleton-text
                              animated
                              class="skeleton-center"
                              style="width: 60%"
                            ></ion-skeleton-text>
                          </ion-col>
                          <ion-col class="ion-text-center">
                            <ion-fab-button color="white" class="round-button send-message">
                              <ion-icon color="medium" src="assets/icons/ico-chat.svg"></ion-icon>
                            </ion-fab-button>
                            <ion-skeleton-text
                              animated
                              class="skeleton-center"
                              style="width: 60%"
                            ></ion-skeleton-text>
                          </ion-col>
                          <ion-col class="ion-text-center">
                            <ion-fab-button color="white" class="round-button like">
                              <ion-icon
                                color="medium"
                                src="assets/icons/ico-heart.svg"
                              ></ion-icon>
                            </ion-fab-button>
                            <ion-skeleton-text
                              animated
                              class="skeleton-center"
                              style="width: 60%"
                            ></ion-skeleton-text>
                          </ion-col>
                        </ion-row>

                        <ion-row class="border-bottom ion-padding-horizontal ion-padding-bottom">
                          <ion-col>
                            <h4>
                              <ion-skeleton-text animated style="width: 100%"></ion-skeleton-text>
                            </h4>
                            <ion-skeleton-text animated style="width: 60%"></ion-skeleton-text>
                          </ion-col>
                        </ion-row>

                        <ion-row class="border-bottom ion-padding-horizontal ion-padding-bottom">
                          <ion-col>
                            <h4>
                              <ion-skeleton-text animated style="width: 40%"></ion-skeleton-text>
                            </h4>
                            <ion-skeleton-text animated style="width: 20%"></ion-skeleton-text>
                          </ion-col>
                        </ion-row>
                      </ion-grid>
                    </div>

                    <!-- error container { -->
                    <ion-grid class="statusMessage ion-text-center" [class.hidden]="!this.wasError()">
                      <ion-row>
                        <ion-col>
                          <ion-icon color="primary" name="sad"></ion-icon>
                          <h3>{{ 'error_loading_profile' | translate }}</h3>
                        </ion-col>
                      </ion-row>
                    </ion-grid>
                    <!-- } error container -->
                  </ion-content>
