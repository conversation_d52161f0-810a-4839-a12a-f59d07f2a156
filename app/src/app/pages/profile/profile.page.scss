h6 {
  font-weight: 600;
}

ion-chip {
  pointer-events: none;
}

.slide {
  width: 100%;
  position: relative;
  height: 0;
  padding-top: 100%;

  ion-img {
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
  }

  img {
    width: 100%;
  }

}

.request-photo__icon {
  margin-right: 4px;
}

.request-photo-error {
  position: absolute;
  z-index: 999;
  border: 3px solid #0f9aff;
  border-radius: 16px;
  margin: 10% 0 0;
  width: 80%;
  max-width: 310px;
  margin-left: 50%;
  transform: translateX(-50%);

  ion-card-header {
    padding: 8px 10px 2px;
    display: block;

    ion-icon {
      font-size: 1.5rem;
      color: var(--ion-color-light-shade);
    }

    ion-card-title {
      font-size: 1rem;
      font-weight: bold;
      clear: both;
    }
  }

  ion-card-content {
    padding: 0 10px 40px;

    p {
      line-height: 1;
    }

    ion-text {
      font-size: 0.7rem;
      line-height: 1;
    }

    .camera-icon {
      margin: 8px 0;
      font-size: 3.5rem;
      color: var(--ion-color-light-shade);
    }

    ion-button {
      font-size: .85rem;
    }
  }
}

.profile-main-info {
  background-color: #F7F7F7;
  border-bottom: 1px solid #EAEAEA;
  text-align: center;
  padding-bottom: 52px;

  .profile-username-age {
    margin: 14px 0 2px;
  }

  .user-location {
    color: #A5A7AD;
  }
}

.details {
  ion-text {
    display: block;
  }

}

.action-buttons {
  align-items: baseline;
  justify-content: center;
  gap: 14px;
  margin: -44px 0 8px;

  ion-col {
    flex-grow: 0;
  }

  .round-button {
    --border-width: 1px;
    --border-color: #DEDEDE;
    --border-style: solid;
    --box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.07);
    width: 74px;
    height: 74px;

    ion-icon {
      font-size: 34px;
    }
  }

  .flirt {
    ion-icon {
      color: #FFB84D;
    }
  }

  .send-message {
    width: 88px;
    height: 88px;
    margin: 0 auto 4px;

    ion-icon {
      color: #4DABF7;
      font-size: 40px;
    }
  }

  .like {
    ion-icon {
      color: #99C369;
    }

    .ico-unlike {
      color: #C8604F;
    }
  }

  .flirt,
  .like {
    margin: 0 auto 11px;
  }

  ion-label {
    font-weight: 600;
  }

}

.statusMessage {
  margin-top: 10vh;

  ion-icon {
    font-size: 7rem;
    opacity: .3;
  }
}

.send-msg {
  height: 46px;
}

ion-text {
  line-height: 1.3;
}

.reqest-photo {
  position: absolute;
  margin: 45% 10%;
  z-index: 999;
  width: 80%;
}

.private-image {
  position: absolute;
  bottom: 0;
  width: 100%;
  z-index: 2;
  background: var(--ion-color-dark-tint);
  color: var(--ion-color-white);

  ion-icon {
    font-size: 3rem;
    margin: -60px auto 0;
    display: block;
    background: var(--ion-color-dark-tint);
    border-radius: 100%;
  }
}

/* Skeleton */

.skeleton-center {
  margin: 0 auto;
}

.profile-main-info-skeleton {
  padding: 10px 0 70px;
}
