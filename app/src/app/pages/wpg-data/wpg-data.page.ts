import { Component, OnInit } from "@angular/core";
import { WelcomeWizardPage } from "../welcome-wizard/welcome-wizard.page";
import { Debug } from "../../helpers/debug";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { WelcomeWizardService } from "../../services/welcome-wizard.service";
import { TranslateService } from "@ngx-translate/core";
import { Router } from "@angular/router";
import { WelcomeProfileService } from "../../services/welcome-profile.service";
import { MiscService } from "src/app/services/misc.service";
import { AnalyticsService } from "src/app/services/analytics.service";
import { UtilsService } from "src/app/services/utils.service";

@Component({
  selector: "app-wpg-data",
  templateUrl: "./wpg-data.page.html",
  styleUrls: ["./wpg-data.page.scss"],
  standalone: false,
})
export class WpgDataPage extends WelcomeWizardPage implements OnInit {
  constructor(
    public welcomeProfileService: WelcomeProfileService,
    public welcomeWizard: WelcomeWizardService,
    public debug: Debug,
    public dialog: DialogHelperService,
    public translate: TranslateService,
    public router: Router,
    public misc: MiscService,
    public analytics: AnalyticsService,
    public utils: UtilsService,
  ) {
    super(
      welcomeProfileService,
      welcomeWizard,
      debug,
      dialog,
      translate,
      router,
      misc,
      analytics,
      utils,
    );

    if (this.pageData?.values) {
      this.pageData.answered = this.pageData.values;
    }
  }

  ngOnInit() {}

  /**
   * can user advance
   */
  public canAdvance() {
    // for some reason even if the data is not required
    // the server returns error if we send empty value
    // so commenting out
    // if (this.pageData.required == "0") return true;

    return !(!this.pageData.values || this.pageData.values.length == 0);
  }

  /**
   * is step required?
   */
  public isRequired() {
    return !(this.pageData.required == "0");
  }
}
