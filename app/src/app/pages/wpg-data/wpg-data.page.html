<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button
        text=""
        icon="chevron-back-sharp"
        [class.hidden]="this.doHideBackButton()"
      ></ion-back-button>
    </ion-buttons>
    <ion-title mode="ios">
      <img
        style="pointer-events: none"
        src="assets/img/app-logo.svg"
        class="logoSvg"
        />
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-progress-bar
    value="{{ (1/welcomeWizard.pages.length)*welcomeWizard.getRealIndex() }}"
  ></ion-progress-bar>

  <ion-grid class="ion-text-center ion-padding-horizontal">
    <ion-row class="ion-justify-content-center">
      @if (this.pageData?.photo) {
        <ion-col size="12" size-sm="6">
          <img [src]="this.pageData?.photo" />
        </ion-col>
      }

      @if (this.pageData.title) {
        <ion-col size="12" size-sm="8">
          <h1>{{ this.replaceTags(this.pageData.title) }}</h1>
        </ion-col>
      }

      @if (this.pageData.description) {
        <ion-col size="12" size-sm="8">
          <ion-text>{{ this.replaceTags(this.pageData.description) }}</ion-text>
        </ion-col>
      }

      <ion-col size-sm="8">
        <wpg-form
          [show]="true"
          [data]="this.pageData"
          (onWpgChange)="onWpgChange($event)"
          [capitalizeFirstLetter]="pageData.name == 'firstname'"
        ></wpg-form>
      </ion-col>

      @if (this.pageData.note) {
        <ion-col size="12" size-sm="8">
          <ion-text
            ><small>{{ this.replaceTags(this.pageData.note) }}</small></ion-text
            >
          </ion-col>
        }

        <ion-col size="12" size-sm="5">
          <ion-button
            expand="block"
            class="ion-margin-bottom"
            (click)="mainButtonClick()"
            [disabled]="this.isProcessing() || !this.canAdvance()"
            >{{ getButtonText() }}
            <ion-spinner
              class="ion-padding-vertical"
              [class.hidden]="!this.isProcessing()"
              ></ion-spinner
            ></ion-button>
            @if (this.pageData.button_no || !this.isRequired()) {
              <a
                (click)="alternateButtonClick()"
                class="skip"
                >
                {{ getAlternateButtonText() }}
              </a>
            }
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-content>
