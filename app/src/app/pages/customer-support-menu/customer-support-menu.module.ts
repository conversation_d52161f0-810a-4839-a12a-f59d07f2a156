import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { CustomerSupportMenuPageRoutingModule } from "./customer-support-menu-routing.module";

import { CustomerSupportMenuPage } from "./customer-support-menu.page";

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    CustomerSupportMenuPageRoutingModule,
  ],
  declarations: [CustomerSupportMenuPage],
})
export class CustomerSupportMenuPageModule {}
