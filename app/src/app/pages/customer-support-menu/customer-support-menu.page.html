<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="" icon="chevron-back-sharp"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ "title_customer_support" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row class="ion-justify-content-center">
      <ion-col size="12" size-sm="6">
        <ion-item class="ion-no-padding">
          <!-- This is from old app but now its not working
          <ion-select interface="popover" placeholder="{{ 'customer_support_type' | translate }}" [(ngModel)]="type">
            <ion-option
              *ngFor="let type of supportTypes"
              [value]="type | translate">
            {{ type | translate }}</ion-option>
          </ion-select>
          -->
          <ion-select
            interface="popover"
            placeholder="{{ 'customer_support_type' | translate }}"
            [(ngModel)]="type"
            >
            @for (type of supportTypes; track type) {
              <ion-select-option
                [value]="type | translate"
                >{{ type | translate }}</ion-select-option
                >
              }
            </ion-select>
          </ion-item>

          <ion-item class="ion-no-padding" [class.hidden]="this.hasSession">
            <ion-input
              label="{{ 'email' | translate }}"
              label-placement="floating"
              type="email"
              name="email"
              required="true"
              [(ngModel)]="email"
            ></ion-input>
          </ion-item>

          <ion-item class="ion-no-padding">
            <ion-textarea
              label="{{'write_message_here' | translate}}"
              label-placement="floating"
              #default_input
              name="message"
              rows="5"
              minlength="3"
              [(ngModel)]="message"
              required="true"
            ></ion-textarea>
          </ion-item>
        </ion-col>
      </ion-row>
      <ion-row class="ion-justify-content-center ion-margin-top">
        <ion-col size="12" size-sm="6">
          <ion-button
            expand="block"
            [disabled]="message.length < 3 || this.isInProgress() || !this.isFormValid()"
            (click)="sendFeedback()"
            >{{ "send_message" | translate }}</ion-button
            >
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-content>
