import { Component, OnInit } from "@angular/core";
import { ContactService } from "../../services/contact.service";
import { Router, ActivatedRoute } from "@angular/router";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { Debug } from "../../helpers/debug";
import { ApiParams } from "../../services/api/api.params";
import { TranslateService } from "@ngx-translate/core";
import { ApiService } from "src/app/services/api/api.service";
import { ApiCommands } from "src/app/services/api/api.commands";
import { NavController } from "@ionic/angular";
import { AccountService } from "src/app/services/account.service";
import { Utils } from "src/app/helpers/utils";

@Component({
  selector: "app-customer-support-menu",
  templateUrl: "./customer-support-menu.page.html",
  styleUrls: ["./customer-support-menu.page.scss"],
  standalone: false,
})
export class CustomerSupportMenuPage implements OnInit {
  /**
   * Customer support type.
   *
   * @type {string}
   */
  public type: string = "";

  /**
   * Customer support message.
   *
   * @type {string}
   */
  public message: string = "";

  /**
   * Support types.
   *
   * @type {[string , string , string]}
   */
  public supportTypes: string[] = [];

  public email: string = "";
  public hasSession: boolean = false;

  constructor(
    public contacts: ContactService,
    public router: Router,
    private activeRouter: ActivatedRoute,
    public dialog: DialogHelperService,
    public dbg: Debug,
    public translate: TranslateService,
    public api: ApiService,
    private navCtrl: NavController,
    public account: AccountService,
  ) {
    this.sessionCheck();
  }
  public isFormValid() {
    if (this.hasSession) {
      return true;
    }

    if (this.email.length < 5) {
      return false;
    }

    return Utils.getEmailRegexp().test(this.email);
  }

  private sessionCheck() {
    this.hasSession = !!this.account?.account?.user_id;

    this.supportTypes = [
      "general_questions",
      "billing_questions",
      "found_bug",
      "data_protection",
    ];

    if (this.hasSession) {
      this.email = this.account.account.email;
    } else {
      this.supportTypes.unshift("login_issue");
    }
  }

  public isInProgress() {
    return this.api.isInEventList({
      command: ApiCommands.ContactAdd.name,
    });
  }

  ngOnInit() {
    this.sessionCheck();
    this.message = "";
    this.email = "";
    let fragmet: string = this.activeRouter.snapshot.fragment;

    if (fragmet === "data-protection") {
      this.type = this.translate.instant("data_protection");
    } else if (this.hasSession) {
      this.type = this.translate.instant("general_questions");
    } else {
      // Set default type.
      this.type = this.translate.instant("login_issue");
    }
  }

  /**
   * Send feedback function
   */
  public sendFeedback(): void {
    // Prepare params.
    let params: ApiParams.ContactAdd.type = {
      topic: 100,
      subject:
        "[" + this.translate.instant("sent_from_mobile_app") + "] " + this.type,
      msg: this.message,
      email: this.email,
    };

    // Send.

    if (this.hasSession) {
      this.contacts
        .add(params)
        .then(() => {
          this.message = "";
          this.email = "";

          this.dialog.showAlert(
            "title_customer_support_response",
            "customer_support_response_message",
            "ok",
            () => {
              this.navCtrl.pop();
            },
          );
        })
        .catch((error) => {
          this.dialog.showToastBaseOnErrorResponse(
            "",
            "customer_support_error",
          );
          this.dbg.le("customer support failure", error);

          this.message = params.msg;
        });
    } else {
      this.contacts
        .anonymous(params)
        .then(() => {
          this.message = "";
          this.email = "";

          this.dialog.showAlert(
            "title_customer_support_response",
            "customer_support_response_message",
            "ok",
            () => {
              this.navCtrl.pop();
            },
          );
        })
        .catch((error) => {
          this.dialog.showToastBaseOnErrorResponse(
            "",
            "customer_support_error",
          );
          this.dbg.le("customer support failure", error);

          this.message = params.msg;
        });
    }
  }
}
