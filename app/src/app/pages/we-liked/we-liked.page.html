<ion-content>
  <app-happy-hour></app-happy-hour>

  <ion-refresher slot="fixed" (ionRefresh)="this.listPaginator.refresh($event)">
    <ion-refresher-content refreshingSpinner="crescent"></ion-refresher-content>
  </ion-refresher>

  <!-- empty container { -->
  <ion-grid
    class="statusMessage ion-text-center"
    [class.hidden]="!this.listPaginator.isEmpty()"
    >
    <ion-row>
      <ion-col>
        <ion-icon color="primary" name="heart-dislike-outline"></ion-icon>
        <h2>{{ 'you_liked_empty_title' | translate }}</h2>
        <h6>{{ 'you_liked_empty_description' | translate }}</h6>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-button color="primary" [routerLink]="['/app/tabs/browse']">
          {{ "you_liked_empty_button" | translate }}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
  <!-- } empty container -->

  <!-- loading container { -->
  @if (profileService.listWeLiked.list$ | async; as listWeLiked) {
    <ion-grid
      [class.hidden]="this.profileService.listWeLiked.wasError()"
      >
      <ion-row>
        @for (profile of listWeLiked; track profile) {
          <ion-col size="6" size-md="3">
            <ion-card
              [ngClass]="profile.is_active ? 'active' : 'inactive'"
              class="ion-no-margin"
              [routerLink]="[ '/app/profile', profile.username ]"
              >
              <div class="distance">{{ profile.distance | distance }}</div>
              <div class="img-placeholder">
                <ion-img
                  style="pointer-events: none"
                  [src]="profile.avatar_url"
                ></ion-img>
              </div>
              <ion-card-header>
                <ion-card-title color="dark">
                  {{ (profile.first_name || profile.username) | slice:0:14 }}, {{
                  profile.age }}
                  <span class="status-indicator"></span>
                </ion-card-title>
                <ion-text>
                  @if (profile.city) {
                    <span>{{ profile.city }},</span>
                    } {{
                    profile.country === 'US' ? profile.state : profile.country }}
                  </ion-text>
                </ion-card-header>
              </ion-card>
            </ion-col>
          }
        </ion-row>
      </ion-grid>
    }
    <!-- } loading container -->

    <ion-infinite-scroll
      [class.hidden]="this.listPaginator.didReachEnd()"
      threshold="100px"
      id="infinite-scroll"
      (ionInfinite)="this.listPaginator.next($event)"
      >
      <ion-infinite-scroll-content loadingSpinner="crescent">
        <!-- sceleton content before loading { -->
        <div
          class="ion-text-start"
          [class.hidden]="!(this.profileService.listWeLiked.isLoading() && !this.listPaginator.didLoad())"
          >
          <ion-grid>
            @for (_r of [].constructor(5); track _r) {
              <ion-row>
                @for (_c of [].constructor(4); track _c) {
                  <ion-col size="6" size-md="3">
                    <ion-card class="ion-no-margin">
                      <ion-skeleton-text
                        style="width: 20%"
                        class="ion-float-left ion-margin"
                        animated
                      ></ion-skeleton-text>
                      <ion-thumbnail class="img-placeholder">
                        <ion-skeleton-text></ion-skeleton-text>
                      </ion-thumbnail>
                      <ion-card-header class="skeleton">
                        <ion-card-title>
                          <ion-skeleton-text
                            animated
                            style="width: 40%"
                            class="ion-float-left"
                          ></ion-skeleton-text>
                          <span class="status-indicator"></span>
                        </ion-card-title>
                        <ion-text>
                          <ion-skeleton-text
                            animated
                            style="width: 60%"
                          ></ion-skeleton-text>
                        </ion-text>
                      </ion-card-header>
                    </ion-card>
                  </ion-col>
                }
              </ion-row>
            }
          </ion-grid>
        </div>
        <!-- } end sceleton content -->
      </ion-infinite-scroll-content>
    </ion-infinite-scroll>

    <!-- error container { -->
    <ion-grid
      class="statusMessage ion-text-center"
      [class.hidden]="!this.profileService.listWeLiked.wasError()"
      >
      <ion-row>
        <ion-col>
          <ion-icon color="primary" name="alert-circle-outline"></ion-icon>
          <h3>{{ 'error_list' | translate }}</h3>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-button color="primary" (click)="this.listPaginator.refresh()"
            >{{ 'refresh' | translate }}</ion-button
            >
          </ion-col>
        </ion-row>
      </ion-grid>
      <!-- } error container -->

      <div
        style="text-align: center; display: none"
        [class.hidden]="!this.listPaginator.didReachEnd() && !this.listPaginator.isEmpty()"
        >
        ¯\_(ツ)_/¯
      </div>
    </ion-content>
