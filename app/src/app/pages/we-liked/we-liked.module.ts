import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { WeLikedPageRoutingModule } from "./we-liked-routing.module";
import { WeLikedPage } from "./we-liked.page";
import { TranslateModule } from "@ngx-translate/core";
import { PipesModule } from "src/app/pipes/pipes.module";
import { HappyHourComponentModule } from "../../components/happy-hour/happy-hour.component.module";

@NgModule({
  imports: [
    TranslateModule.forChild(),
    CommonModule,
    FormsModule,
    IonicModule,
    WeLikedPageRoutingModule,
    PipesModule,
    HappyHourComponentModule,
  ],
  declarations: [WeLikedPage],
})
export class WeLikedPageModule {}
