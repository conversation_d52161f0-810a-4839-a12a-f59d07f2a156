import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { WeLikedPage } from "./we-liked.page";

const routes: Routes = [
  {
    path: "",
    component: WeLikedPage,
  },
];

import { TranslateModule } from "@ngx-translate/core";
@NgModule({
  imports: [TranslateModule.forChild(), RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class WeLikedPageRoutingModule {}
