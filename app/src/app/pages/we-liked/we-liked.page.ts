import { Component } from "@angular/core";
import { Debug } from "src/app/helpers/debug";
import { ListPaginator } from "src/app/services/lists/list-paginator";
import { PaginatorHelperService } from "src/app/services/paginator-helper.service";
import { UnreadCountService } from "src/app/services/unread-count.service";
import { ProfileService } from "../../services/profile.service";

@Component({
  selector: "app-we-liked",
  templateUrl: "./we-liked.page.html",
  styleUrls: ["./we-liked.page.scss"],
  standalone: false,
})
export class WeLikedPage {
  public listPaginator: ListPaginator;

  constructor(
    public profileService: ProfileService,
    private paginatorHelper: PaginatorHelperService,
    private debug: Debug,
    public unreadCountService: UnreadCountService,
  ) {
    this.listPaginator = this.paginatorHelper.weLikedPaginator;
  }

  ionViewDidEnter() {
    if (!this.listPaginator.didLoad()) {
      this.listPaginator.next();
    }
  }
}
