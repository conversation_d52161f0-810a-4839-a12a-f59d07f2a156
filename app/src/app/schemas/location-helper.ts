import * as yup from "yup";
import { Utils } from "../helpers/utils";

export enum LocationMissing {
  "city",
  "zip",
  "country",
}
export enum LocationWhatToShow {
  "country_select",
  "city_select",
  "zip_input",
}

export namespace LocationHelper {
  // Schema.
  export let schema = yup.object({
    selected_country: yup.string().default(""),
    selected_city: yup.string().default(""),
    selected_zip: yup.string().default(""),
    missing: yup
      .array()
      .of(yup.string().oneOf(Utils.enumValues(LocationMissing))),
    whatToShow: yup.array(),
    // .of(yup.string().oneOf(Utils.enumValues(LocationWhatToShow))),
    city_list: yup.object(),
    country_list: yup.object(),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
