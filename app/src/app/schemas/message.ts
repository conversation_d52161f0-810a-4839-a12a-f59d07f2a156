import * as yup from "yup";
import { GalleryImage } from "./gallery-image";

export namespace Message {
  // Schema.
  export let schema = yup.object({
    id: yup.number().default(0),
    thread_id: yup.number().default(0),
    ts: yup.number().default(0),
    body: yup.string().default(""),
    user_id: yup.number().default(0),
    target_username: yup.string().default(""),
    attachments: yup.array().of(GalleryImage.schema),
    system_msg: yup.boolean().default(false),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace MessageChatSecret {
  // Schema.
  export let schema = yup.object({
    username: yup.string().default(""),
    secret: yup.string().default(""),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace MessageUnreadCount {
  // Schema.
  export let schema = yup.object({
    user_id: yup.number().default(0),
    last_read_ts: yup.number().default(0),
    count: yup.number().default(0),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace MessageGetReadTs {
  // Schema.
  export let schema = yup.object({
    read_ts: yup.number().default(0),
    received_ts: yup.number().default(0),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
