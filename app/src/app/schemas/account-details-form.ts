import * as yup from "yup";

export namespace Answer {
  // Schema.
  export let schema = yup.object({
    key: yup.number(),
    data: yup.string(),
    checked: yup.boolean(),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace Question {
  // Schema.
  export let schema = yup.object({
    key: yup.string().default(""),
    answers: yup.array().of(Answer.schema),
    type: yup.string().default(""),
    value_min: yup.string().default(""),
    value_max: yup.string().default(""),
    multiselect: yup.mixed(),
    question_name: yup.string().default(""),
    question_name_profile: yup.string().default(""),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace DetailsForm {
  // Schema.
  export let schema = yup.object({
    group_id: yup.string().default(""),
    group_name: yup.string().default(""),
    group_name_profile: yup.string().default(""),
    group_description: yup.string().default(""),
    group_text: yup.string().default(""),
    tags: yup.mixed().default([]),
    questions: yup.array().of(Question.schema).default(null),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace Interests {
  // Schema.
  export let schema = yup.object({
    group_id: yup.string().default(""),
    group_name: yup.string().default(""),
    group_name_profile: yup.string().default(""),
    group_description: yup.string().default(""),
    group_text: yup.string().default(""),
    tags: yup.mixed().default([]),
    completed: yup.string().default(""),
    answers: yup.mixed().default([]),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace Description {
  // Schema.
  export let schema = yup.object({
    group_id: yup.string().default(""),
    group_name: yup.string().default(""),
    group_name_profile: yup.string().default(""),
    group_description: yup.string().default(""),
    group_text: yup.string().default(""),
    tags: yup.array().default([]),
    questions: yup.object().concat(Question.schema).default(null),
    completed: yup.boolean().default(false),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
