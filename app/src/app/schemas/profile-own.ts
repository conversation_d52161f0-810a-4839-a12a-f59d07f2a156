import * as yup from "yup";
import { ProfileBase } from "./profile-base";

export namespace ProfileOwn {
  // Schema.
  export let schema = yup
    .object({
      email: yup.string().email().default(""),
      profile_complete: yup.boolean().default(true),
      profile_progress_pct: yup.number().default(0),
      registration_ts: yup.number().default(0),
      last_access_ts: yup.number().default(0),
      valid_email: yup.boolean().default(false),
    })
    .concat(ProfileBase.schema);

  // Type.
  export type type = yup.InferType<typeof schema>;
}
