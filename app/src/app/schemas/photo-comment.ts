import * as yup from "yup";

export namespace PhotoComment {
  // Schema.
  export let schema = yup.object({
    datetime: yup.string().default(""),
    avatar_url: yup.string().default(""),
    username: yup.string().default(""),
    content: yup.string().default(""),
    comment_id: yup.number().default(0),
    first_name: yup.string().default(""),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace PhotoCommentBase {
  // Schema.
  export let schema = yup.object({
    gallery_id: yup.number().default(0),
    page_title: yup.string().default(""),
    comments_count: yup.number().default(0),
    items: yup.array().of(PhotoComment.schema),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
