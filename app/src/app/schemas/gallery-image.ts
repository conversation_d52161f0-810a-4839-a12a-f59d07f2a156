import * as yup from "yup";

export namespace GalleryImage {
  // Schema.
  export let schema = yup.object({
    gallery_id: yup.number().default(0),
    image_id: yup.number().default(0),
    image_url: yup.string().default(""),
    thumb_url: yup.string().default(""),
    main_photo: yup.boolean().default(true),
    approved: yup.boolean().default(false),
    comments: yup.number().default(0),
    liked: yup.boolean().default(false),
    likes: yup.number().default(0),
    access: yup.number().default(0),
    has_access: yup.boolean().default(false),
    comment: yup.string().default(""),
    blurhash: yup.string().default("U3ONB[xuoft700WBt7WB~qayM{ofM{ayWBj["), // default blurhash gray box
    selected: yup.boolean().default(false),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
