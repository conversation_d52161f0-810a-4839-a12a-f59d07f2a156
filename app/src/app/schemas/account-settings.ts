import * as yup from "yup";

export namespace AccountSettings {
  // Schema.
  export let schema = yup.object({
    privacy_show_online_status: yup.boolean().default(true),
    privacy_show_distance: yup.boolean().default(true),
    is_online: yup.boolean().default(true),
    push_notify_new_matches: yup.boolean().default(true),
    push_notify_messages: yup.boolean().default(true),
    push_notify_super_likes: yup.boolean().default(true),
    push_notify_likes: yup.boolean().default(true),
    push_notify_suggestions: yup.boolean().default(true),
    push_notify_reminder: yup.boolean().default(true),
    push_notify_campaigns: yup.boolean().default(true),
    push_new_flirt: yup.boolean().default(true),
    push_chat_request: yup.boolean().default(true),
    push_photo_uploaded: yup.boolean().default(true),
    push_profile_viewed: yup.boolean().default(true),
    email_notify_new_matches: yup.boolean().default(true),
    email_notify_messages: yup.boolean().default(true),
    email_notify_super_likes: yup.boolean().default(true),
    email_notify_likes: yup.boolean().default(true),
    email_promo: yup.boolean().default(true),
    email_add_friend: yup.boolean().default(true),
    email_profile_visits: yup.boolean().default(true),
    email_newsletter: yup.boolean().default(true),
    email_first_photo: yup.boolean().default(true),
    email_online_user: yup.boolean().default(true),
    email_msg_read: yup.boolean().default(true),
    auto_update_location: yup.boolean().default(true),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
