import * as yup from "yup";

export namespace BillingItem {
  // Schema.
  export let schema = yup.object({
    item_id: yup.number(),
    svc_title: yup.string().default(""),
    svc_description: yup.string().default(""),
    rebill_title: yup.string().default(""),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace BillingHistoryItem {
  // Schema.
  export let schema = yup.object({
    id: yup.number(),
    status: yup.string().default(""),
    type: yup.string().default(""),
    amount: yup.number().default(0.0),
    currency: yup.string().default(""),
    descriptor: yup.string().default(""),
    cctype: yup.string().default(""),
    ccnum: yup.string().default(""),
    processor_name: yup.string().default(""),
    country: yup.string().default(""),
    tran_items: yup.array().of(yup.number()).default([]),
    is_rebill: yup.boolean().default(false),
    items: yup.array().of(BillingItem.schema),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
