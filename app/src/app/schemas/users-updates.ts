import * as yup from "yup";

export namespace UsersUpdates {
  // Schema.
  export let schema = yup.object({
    update_id: yup.string().default(""),
    username: yup.string().default(""),
    ts: yup.string().default(""),
    new: yup.boolean().default(false),
    name: yup.string().default(""),
    img: yup.string().default(""),
    msg: yup.string().default(""),
    action_id: yup.string().default(""),
    param1: yup.string().default(""),
    param2: yup.string().default(""),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
