import * as yup from "yup";

export type MatchPrefDistances = 0 | 10 | 20 | 30 | 50 | 100 | 250; // @todo - check this

export namespace MatchPref {
  // Schema.
  export let schema = yup.object({
    age_from: yup.number().default(0),
    age_to: yup.number().default(0), // Age from if both 0 that means ALL ages
    distance: yup.number().oneOf([0, 10, 20, 30, 50, 100, 250]).default(0),
    genders: yup.array().default([]),
    gender_id: yup.number().default(1),
    looking_id: yup.number().default(1),
    anybody: yup.boolean().default(false),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
