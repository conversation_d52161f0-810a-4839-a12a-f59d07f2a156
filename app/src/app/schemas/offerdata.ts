import * as yup from "yup";

export namespace OfferData {
  // Schema.
  export let schema = yup.object({
    uid: yup.string(),
    title: yup.string(),
    subtitle: yup.string(),
    description: yup.string(),
    imageUrl: yup.string(),
    action: yup.string(),
    buttonText: yup.string(),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace PromoDataType {
  // Schema.
  export let schema = yup.object({
    premium: yup.string().default("premium"),
    featured: yup.string().default("featured"),
    happyhour: yup.string().default("happyhour"),
    freemessage: yup.string().default("freemessage"),
    nocode: yup.string().default("nocode"),
    error: yup.string().default("error"),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace PromoData {
  // Schema.
  export let schema = yup.object({
    display_message: yup.boolean(),
    message: yup.string(),
    type: yup.mixed().oneOf([PromoDataType, yup.string()]), // @todo - check this
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
