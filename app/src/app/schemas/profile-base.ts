import * as yup from "yup";
import { GalleryImage } from "./gallery-image";
import { Utils } from "../helpers/utils";
import { ProfileBaseDetails } from "./profile-base-details";

export enum OnlineStatus {
  "online",
  "offline",
}

export namespace ProfileBase {
  // Schema.
  export let schema = yup.object({
    access: yup.number().default(0),
    user_id: yup.number().default(0),
    username: yup.string().default(""),
    country: yup.string().default(""),
    state: yup.string().default(""),
    city: yup.string().default(""),
    zip: yup.string().default(""),
    gender_id: yup.number().default(0),
    looking_id: yup.number().default(0),
    age: yup.number().default(0),
    birthdate: yup.string().default(""),
    avatar_url: yup.string().default("assets/img/avatar.svg"),
    avatar_blurhash: yup
      .string()
      .default("U3ONB[xuoft700WBt7WB~qayM{ofM{ayWBj["), // default blurhash gray box
    description: yup.string().default(""),
    title: yup.string().default(""),
    first_name: yup.string().default(""),
    online_status: yup
      .string()
      //.oneOf(Utils.enumValues(OnlineStatus))
      .default("offline"),
    interests: yup.array().of(yup.string()).default([]),
    is_premium: yup.boolean().default(false),
    is_featured: yup.boolean().default(false),
    photos: yup.lazy(() => yup.array().of(GalleryImage.schema).default([])),
    is_active: yup.boolean().default(false),
    do_hide: yup.boolean().default(false),
    details: yup.array().of(
      yup
        .object({
          title: yup.string().default(""),
          data: yup
            .array()
            .of(yup.object().concat(ProfileBaseDetails.schema).default(null)),
        })
        .default(null),
    ),
    mobile_registration: yup.boolean().default(true),
    profile_type: yup.string().default("profile"),
    image: yup.string().default(""),
    distance: yup.number().nullable().default(null),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;

  /**
   * get the picture urls only
   */
  export function getPictureUrls(profile: ProfileBase.type): string[] {
    if (!profile) {
      return [];
    }

    let photos: GalleryImage.type[] = [];
    let mainPhoto: GalleryImage.type = null;

    profile.photos.forEach((photo) => {
      if (!photo.has_access) return;

      if (photo.main_photo) {
        mainPhoto = photo;
      } else {
        photos.push(photo);
      }
    });

    if (mainPhoto) {
      photos.unshift(mainPhoto);
    }

    if (!photos.length) {
      return [profile.avatar_url];
    }

    // return only strings
    return photos.map((img) => img.image_url);
  }
}
