import * as yup from "yup";

export namespace Ads {
  // Schema.
  export let schema = yup.object({
    width: yup.number().default(100),
    height: yup.number().default(80),
    type: yup.string().default("text"),
    title: yup.string().default(""),
    description: yup.string().default(""),
    image: yup.string().default(""),
    iframe: yup.string().default(""),
    redirect: yup.string().default(""),
    _debug_cpc: yup.string().default(""),
    redirect_label: yup.string().default(""),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
