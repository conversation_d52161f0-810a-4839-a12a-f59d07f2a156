import * as yup from "yup";

export namespace Restriction {
  // Schema.
  export let schema = yup.object({
    renew_credits: yup.number().default(0),
    renew_time: yup.number().default(0),
    credits: yup.number().default(0),
    max_credits: yup.number().default(0),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace RestrictionAll {
  // Schema.
  export let schema = yup.object({
    can_hide_online_status: yup.boolean().default(true),
    can_choose_premium_match: yup.boolean().default(true),
    can_change_location: yup.boolean().default(true),
    can_hide_ads: yup.boolean().default(true),
    can_use_undo: yup.boolean().default(true),
    swipe_restriction: yup.boolean().default(true),
    superlike_reset: Restriction.schema,
    like_reset: Restriction.schema,
    test_count: Restriction.schema,
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace RestrictionCheck {
  // Schema.
  export let schema = yup.object({
    allowed: yup.boolean().default(false),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
