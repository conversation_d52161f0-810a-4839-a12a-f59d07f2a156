import * as yup from "yup";
import { ProfileBase } from "./profile-base";
import { Utils } from "../helpers/utils";

export enum SuperLikeStatus {
  "" = "",
  "superliked_you" = "superliked_you",
  "you_superliked" = "you_superliked",
}

/**
 * UserRelationshipType descriptions:
 *
 * likes     - If logged in user liked given user(from input list of users) - "weLiked"
 * liked     - If given user liked logged in user - "likedUs"
 * dislikes  - If logged in user disliked given user(from input list of users) - "not in contacts list"
 * disliked  - If given user disliked logged in user - in this case, we have 2 options:
 *                  1. the profile is not in the contacts list,
 *                  2. the profile is in the likedUs list
 * superlike - If given or logged in user superliked the other - "matches"
 * match     - If both logged in and given user liked eachother - "matches"
 * ''        - No status is presented with empty string - "not in contacts list"
 */

export enum UserRelationshipType {
  "" = "",
  "superlike" = "superlike",
  "likes" = "likes",
  "dislikes" = "dislikes",
  "liked" = "liked",
  "disliked" = "disliked",
  "match" = "match",
}

export namespace ProfileUser {
  // Schema.
  export let schema = yup
    .object({
      last_read_ts: yup.number().default(0),
      unread_count: yup.number().default(0),
      distance: yup.string().default(""),
      format: yup.number().default(0),
      superlike_status: yup.string(),
      //.oneOf(Utils.enumValues(SuperLikeStatus)),
      // .default(""),
      created_ts: yup.string().default("0"),
      relationship: yup.string(),
      //.oneOf(Utils.enumValues(UserRelationshipType))
    })
    .concat(ProfileBase.schema);

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace ProfileList {
  // Schema.
  export let schema = yup.object({
    items: yup.array().of(ProfileUser.schema),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace ProfileListTotal {
  // Schema.
  export let schema = yup.object({
    items: yup.array().of(ProfileUser.schema),
    total: yup.number().default(0),
    next: yup.number().default(0),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
