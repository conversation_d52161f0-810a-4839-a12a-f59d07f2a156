import * as yup from "yup";

export namespace PreRegister {
  export type UserStatus = "USER_EXISTS_N_LOGGED" | "USER_NOT_EXISTS";

  // Schema.
  export let schema = yup.object({
    user_status: yup.string() /* 'USER_EXISTS_N_LOGGED' | 'USER_NOT_EXISTS'; */,
    jwt: yup.string(),
    pre_reg_data: yup.lazy(() => yup.object().concat(PreRegData.schema)),
    provider_data: yup.mixed(),
    user: yup
      .object({
        username: yup.string(),
        email: yup.string(),
      })
      .notRequired(),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace PreRegData {
  // Schema.
  export let schema = yup.object({
    email: yup.string().notRequired(),
    username: yup.string().notRequired(),
    first_name: yup.string().notRequired(),
    gender_id: yup.number().notRequired(),
    birthdate: yup.string().notRequired(),
    country: yup.string().notRequired(),
    state: yup.string().notRequired(),
    city: yup.string().notRequired(),
    latituce: yup.number().notRequired(),
    lingitude: yup.number().notRequired(),
    looking_id: yup.number().notRequired(),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
