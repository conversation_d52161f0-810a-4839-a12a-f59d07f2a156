import * as yup from "yup";

export namespace InboxThreadItem {
  // Schema.
  export let schema = yup.object({
    id: yup.number().default(0),
    ts: yup.number().default(0),
    subject: yup.string().default(""),
    msg_count: yup.number().default(0),
    username: yup.string().default(""),
    user_id: yup.number().default(0),
    avatar_url: yup.string().default(""),
    last_message: yup.string().default(""),
    thread_status: yup.number().default(0),
    is_archived: yup.boolean().default(false),
    is_read: yup.boolean().default(false),
    type_name: yup.string().default(""),
    type_index: yup.number().default(0),
    appended_data: yup.object(InboxThreadItemAppendedData),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace InboxThreadItemAppendedData {
  // Schema.
  export let schema = yup.object({
    first_name: yup.string(),
    age: yup.number(),
    distance: yup.string(),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
