import * as yup from "yup";

export namespace AccountProduct {
  // Schema.
  export let schema = yup.object({
    id: yup.string(),
    alias: yup.string(),
    type: yup.string(),
    _pay_item_id: yup.number(),
    _preselected: yup.boolean(),
    _period_length: yup.number(),
    _period_unit: yup.string(),
    _trial_period_length: yup.number(),
    _trial_period_unit: yup.string(),
    _trial_price_percentage: yup.number(),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
