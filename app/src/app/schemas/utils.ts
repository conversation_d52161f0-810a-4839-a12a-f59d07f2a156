import * as yup from "yup";

export namespace ConsentRequirementType {
  // Schema.
  export let schema = yup.object({
    required: yup.boolean().default(false),
    offers_consent: yup.boolean().default(false),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

export namespace StaticPageType {
  // Schema.
  export let schema = yup.object({
    title: yup.string().default(""),
    content: yup.string().default(""),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}

/*
"data": {
  "locId": 5296802,
  "name": "United States",
  "country": "US",
  "region": "AZ",
  "city": "Green Valley",
  "postalCode": "85614",
  "latitude": 31.854700000000001,
  "longitude": -111.0087,
  "metroCode": 789,
  "host": "*************",
  "source": "GeoIP2",
  "provider": "GeoIP2",
  "city_locale": false,
  "country_name": "United States",
  "ip": "*************"
}
*/

export namespace ResolveIpData {
  export let schema = yup.object({
    name: yup.string().default(""),
    country: yup.string().default(""),
    region: yup.string().default(""),
    city: yup.string().default(""),
    postalCode: yup.string().default(""),
    latitude: yup.number().default(null),
    longitude: yup.number().default(null),
    country_name: yup.string().default(""),
    ip: yup.string().default(""),
  });
}

export namespace UtilsConfigData {
  // Schema.
  export let schema = yup.object({
    REFRESHER: yup.boolean().default(false),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
