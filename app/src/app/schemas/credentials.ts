import * as yup from "yup";
import { Utils } from "../helpers/utils";

export namespace CredentialData {
  export enum CredType {
    "Facebook" = "facebook",
    "Email" = "email",
    "Username" = "username",
    "Apple" = "apple",
    "URLSession" = "url_session",
  }

  // Schema.
  export let schema = yup.object({
    identifier: yup.string(), // username , email, etc
    secret: yup.string(), // password, token, etc
    type: yup.string(), //.oneOf(Utils.enumValues(CredType)), // cred type
    data: yup.object().notRequired().nullable(), // use for misc data
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
