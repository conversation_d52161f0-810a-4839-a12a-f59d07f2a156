import * as yup from "yup";

export namespace RejectedPhoto {
  // Schema.
  export let schema = yup.object({
    gallery_id: yup.number().default(0),
    image_id: yup.number().default(0),
    comment: yup.string().default(""),
    access: yup.number().default(0),
    thumb_url: yup.string().default(""),
    image_url: yup.string().default(""),
    blurhash: yup.string().default("U3ONB[xuoft700WBt7WB~qayM{ofM{ayWBj["), // default blurhash gray box
    reason: yup.string().default(""),
  });

  // Type.
  export type type = yup.InferType<typeof schema>;
}
