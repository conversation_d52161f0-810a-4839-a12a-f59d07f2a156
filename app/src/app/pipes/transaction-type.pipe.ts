import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
  name: "transactionType",
  standalone: false,
})
export class TransactionTypePipe implements PipeTransform {
  transform(value: Array<string>, ...args: any[]): string {
    var ret = "";
    if (value["type"] == "sale" && value["status"] == "successful") {
      ret = "trans_status_approved";
    } else if (value["type"] == "sale" && value["status"] == "failed") {
      ret = "trans_status_declined";
    } else if (value["type"] == "auth" && value["status"] == "successful") {
      ret = "trans_status_approved_auth";
    } else if (value["type"] == "auth" && value["status"] == "failed") {
      ret = "trans_status_declined_auth";
    } else if (value["type"] == "pending") {
      ret = "trans_status_pending";
    } else if (value["type"] == "refund" && value["status"] == "successful") {
      ret = "trans_status_refunded";
    } else if (
      value["type"] == "chargeback" &&
      value["status"] == "successful"
    ) {
      ret = "trans_status_chargeback";
    } else {
      ret = "trans_status_unknown";
    }

    return ret;
  }
}
