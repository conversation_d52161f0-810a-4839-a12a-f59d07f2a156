import { Pipe, PipeTransform } from "@angular/core";
import _ from "lodash";

@Pipe({
  name: "capitalize",
  standalone: false,
})
export class CapitalizePipe implements PipeTransform {
  transform(
    value: string,
    capitalizationMethod: "allUpperCase" | "titleCase" | "sentenceCase",
    active: boolean,
  ): string {
    if (!active) return value;
    if (_.isArray(value)) value = value.toString();

    if (capitalizationMethod === "allUpperCase") {
      return value.toUpperCase();
    } else if (capitalizationMethod === "titleCase") {
      const splitString = value
        .split(" ")
        .map((s) => `${s[0].toUpperCase()}${s.slice(1)}`);
      return splitString.join(" ");
    } else if (capitalizationMethod === "sentenceCase") {
      const splitString = value.split(".").map((s) => {
        const trimmedString = s.trim();
        if (trimmedString.length > 0) {
          return `${trimmedString[0].toUpperCase()}${trimmedString.slice(1)}`;
        }
        return "";
      });
      return splitString.join(". ");
    }
    return "";
  }
}
