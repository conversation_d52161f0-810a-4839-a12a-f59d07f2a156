import { Pipe, PipeTransform } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { AccountService } from "../services/account.service";

@Pipe({
  name: "distance",
  standalone: false,
})
export class DistancePipe implements PipeTransform {
  /**
   * Suffix 'miles away'.
   *
   * @type {string}
   */
  private suffix: string = "";

  /**
   * Is country metric or not
   *
   * @param countryCode
   * @returns true if country is metric
   */
  private isMetricCountry(countryCode: string): boolean {
    return ["US", "UK", "GB"].indexOf(countryCode.toUpperCase()) < 0;
  }

  constructor(
    private translate: TranslateService,
    private accountService: AccountService,
  ) {
    let countryCode = this.accountService.account.country ?? "US";

    let distance = !this.isMetricCountry(countryCode)
      ? "miles_distance"
      : "kilometer_distance";
    this.suffix = this.translate.instant(distance);
  }

  transform(value: any, ...args: string[]): unknown {
    let distance: number = parseFloat(value);
    let result: string = "";

    switch (true) {
      case distance === 99999:
      case isNaN(distance):
        return "";
      case distance > 250:
        result =
          "> " + String(this.suffix === "mi" ? 250 : Math.round(250 * 1.6));
        break;
      case distance < 2:
        result = "< " + String(this.suffix === "mi" ? 2 : Math.round(2 * 1.6));
        break;
      case distance % 1 === 0:
        result = String(
          this.suffix === "mi" ? distance : Math.round(distance * 1.6),
        );
        break;
      default:
        result = String(
          this.suffix === "mi"
            ? distance.toFixed(1)
            : Math.round(distance * 1.6),
        );
        break;
    }

    return result + " " + this.suffix;
  }
}
