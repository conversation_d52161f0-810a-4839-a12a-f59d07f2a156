import { CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { TestBed, waitForAsync } from "@angular/core/testing";

import { MenuController, Platform } from "@ionic/angular";

import { AppComponent } from "./app.component";
import { RouterTestingModule } from "@angular/router/testing";
import { LoaderService } from "./services/loader.service";
import { StorageService } from "./services/storage.service";
import { HttpClient } from "@angular/common/http";
import { Observable } from "rxjs";
import { SessionService } from "./services/session.service";

describe("AppComponent", () => {
  let statusBarSpy;
  let splashScreenSpy;
  let platformReadySpy;
  let platformSpy;
  let loaderSpy;
  let storageSpy;
  let httpSpy: HttpClient;
  let menuSpy: MenuController;
  let sessionSpy: SessionService;

  beforeEach(
    waitForAsync(() => {
      platformReadySpy = Promise.resolve();
      platformSpy = jasmine.createSpyObj("Platform", {
        ready: platformReadySpy,
      });
      loaderSpy = jasmine.createSpyObj("LoaderService", ["storeLoaderStatus"]);
      storageSpy = jasmine.createSpyObj("StorageService", ["get", "set"]);
      httpSpy = jasmine.createSpyObj("HttpClient", {
        request: new Observable<any>(),
      });
      menuSpy = jasmine.createSpyObj(MenuController, {
        enable: Promise.resolve(),
        open: Promise.resolve(),
        isEnabled: Promise.resolve(),
        isOpen: Promise.resolve(),
        close: Promise.resolve(),
      });
      sessionSpy = jasmine.createSpyObj(SessionService, {
        restoreSession: Promise.resolve(),
      });

      TestBed.configureTestingModule({
        declarations: [AppComponent],
        schemas: [CUSTOM_ELEMENTS_SCHEMA],
        imports: [RouterTestingModule],
        providers: [
          { provide: Platform, useValue: platformSpy },
          { provide: LoaderService, useValue: loaderSpy },
          { provide: StorageService, useValue: storageSpy },
          { provide: HttpClient, useValue: httpSpy },
          { provide: MenuController, useValue: menuSpy },
          { provide: SessionService, useValue: sessionSpy },
        ],
      }).compileComponents();
    })
  );

  it("should create the app", () => {
    let spy = spyOn(AppComponent.prototype, "initializeApp");
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.debugElement.componentInstance;
    expect(app).toBeTruthy();
    expect(spy).toHaveBeenCalledTimes(1);
  });

  it("should initialize the app", async () => {
    TestBed.createComponent(AppComponent);
    expect(platformSpy.ready).toHaveBeenCalled();
    await platformReadySpy;
    expect(statusBarSpy.styleDefault).toHaveBeenCalled();
    expect(splashScreenSpy.hide).toHaveBeenCalled();
  });

  // TODO: add more tests!
});
