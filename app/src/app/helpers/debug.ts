import { Injectable } from "@angular/core";
import { environment as Env } from "../../environments/environment";
import { <PERSON><PERSON><PERSON>roll<PERSON>, Platform } from "@ionic/angular";
// import { Utils } from "./utils";
// import * as Sentry from "@sentry/angular-ivy";
import * as _ from "lodash";

type LogSeverity = "info" | "warning" | "error" | "critical" | "log";

export type IDebut_log_args = {
  message?: string;
  data?: any;
  severity?: LogSeverity;
  timestamp?: number;
  doAddStack?: boolean;
  displayAsTable?: boolean;
  doClone?: boolean;
};

/**
 * Log entry object
 */
export class LogEntry {
  /**
   * Constructor
   *
   * @param {string} message
   * @param data
   * @param {string} severity
   * @param {number} timestamp
   */
  constructor(
    message: string = "",
    data: any = null,
    severity: LogSeverity = "info",
    timestamp: number = 0,
  ) {
    this.message = message;
    this.data = data;
    this.severity = severity;
    if (timestamp == 0) {
      this.timestamp = _.now();
    }
  }

  public message: string = "";
  public timestamp: number = 0;
  public severity: LogSeverity = "info";
  public data: any = null;
  public stack: string = "";

  /**
   * Add stack to the entry object
   */
  public addStack() {
    this.stack = new Error().stack;
  }
}

@Injectable({
  providedIn: "root",
})
export class Debug {
  private logContainer: LogEntry[] = [];
  public isDebug: boolean = false;
  public testPhoto = "";

  private tsStart = Date.now();
  private tsPrevious = Date.now();

  constructor(public platform: Platform) {
    this.isDebug = Env.IS_DEBUG;
    if (this.isDebug) {
      this.getTestPhoto();
    }
  }

  /**
   * Override env debug status
   *
   * @param {boolean} isDebug
   */
  public isDebugEnv(isDebug: boolean) {
    this.isDebug = isDebug;
  }

  public clear(): void {
    this.logContainer = [];
  }

  public get(): LogEntry[] {
    return this.logContainer;
  }

  /**
   * Simple logging function
   *
   * @param {string} message
   * @param data
   * @param {string} severity
   * @param {number} timestamp
   * @param {boolean} doAddStack
   */
  public log({
    message = "",
    data = null,
    severity = "info",
    timestamp = 0,
    doAddStack = false,
    displayAsTable = false,
    doClone = false,
  }: IDebut_log_args) {
    this.addLogEntry(
      new LogEntry(message, data, severity, timestamp),
      doAddStack,
      displayAsTable,
      doClone,
    );
  }

  /**
   * Mark a moment in app to measure
   *
   * @param label write label
   */
  public tsMarker(label: string = "") {
    let tsCurrent = Date.now() - this.tsStart;
    let tsDuration = Date.now() - this.tsPrevious;

    console.log("TIME MARKER " + label);
    console.log("Uptime(ms): " + tsCurrent);
    console.log("Last marker(ms): " + tsDuration);

    this.tsPrevious = Date.now();
  }

  /**
   * Simple function for loggin only data and message
   * for more control use log(...) function
   *
   * @param message
   * @param data
   */
  public l(message: string, data: any = null, doClone: boolean = false) {
    this.log({
      message: message,
      data: data,
    });
  }

  /**
   * Simple function for loggin only data and message
   * for more control use log(...) function
   *
   * @param message
   * @param data
   */
  public le(error: string, data: any = null, doClone: boolean = false) {
    this.log({
      message: error,
      data: data,
      severity: "error",
    });
  }

  /**
   * Logging function for adding a log entry with LogEntry object
   *
   * @param {LogEntry} entry
   * @param {boolean} doAddStack
   */
  public addLogEntry(
    entry: LogEntry,
    doAddStack: boolean = false,
    asTable: boolean = false,
    doClone: boolean = false,
  ): void {
    if (doAddStack) {
      entry.addStack();
    }

    // clone the data so we have a copy of the object if the same is logged multiple times
    if (typeof entry.data === "object") {
      entry.data = doClone ? _.cloneDeep(entry.data) : entry.data;
    }

    if (this.isDebug || ["error", "critical"].indexOf(entry.severity) > -1) {
      this.logContainer.unshift(entry);
    }

    if (["error", "warning", "log"].indexOf(entry.severity) > -1) {
      // Sentry.captureMessage(entry.severity + ": " + entry.message, {
      //   extra: { data: entry },
      // });
    }

    this.displayLog(entry, asTable);
  }

  private cl(text, data = null, asTable = false) {
    if (!Env._verbose) return;

    if (asTable) {
      console.log(text);
      console.table(data);
    } else {
      console.log(text, data);
    }
  }

  /**
   * Stringify only 1 level of the object to avoid circular reference problems on native device
   *
   * @param obj
   */
  private stringifyLimited(obj) {
    let _result = "";

    if (_.isObject(obj)) {
      Object.keys(obj).forEach((e) => {
        _result += e + " : " + _.toString(obj[e]) + " ,\n";
      });
    } else {
      let _result = _.toString(obj);
    }

    return _result;
  }

  /**
   * Display error log
   * @param entry LogEntry
   */
  private displayLog(entry: LogEntry, asTable: boolean = false) {
    if (
      !this.isDebug &&
      ["error", "critical", "warning"].indexOf(entry.severity) < 0
    )
      return;

    // @todo remove this - we don't use cordova - detect it differently
    if (this.platform && this.platform.is("cordova")) {
      // if mobile app ( we don't add data, because it can cause infinite loop when stringifying )
      this.cl(
        entry.severity + ": " + entry.message,
        this.stringifyLimited(entry.data),
      );
      return;
    }

    // if browser
    this.cl(
      entry.severity + ": " + entry.message,
      asTable ? entry.message : entry,
      asTable,
    );
  }

  private getTestPhoto() {
    this.toDataURL("assets/img/debug_image.png", (data) => {
      this.testPhoto = data.replace("data:image/png;base64,", "", data);
    });
  }

  private toDataURL(url, callback) {
    var httpRequest = new XMLHttpRequest();
    httpRequest.onload = function () {
      var fileReader = new FileReader();
      fileReader.onloadend = function () {
        callback(fileReader.result);
      };
      fileReader.readAsDataURL(httpRequest.response);
    };
    httpRequest.open("GET", url);
    httpRequest.responseType = "blob";
    httpRequest.send();
  }
}
