import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgModule } from "@angular/core";
import { Debug, LogEntry } from "./debug";
//import * as Sentry from "@sentry/angular-ivy";
//import { environment as Env } from "../../environments/environment";
import { AnalyticsService } from "../services/analytics.service";
import _ from "lodash";

/**
 * An error handler to inject our stuff on top of ionic error handler
 */
@NgModule({ providers: [Debug] })
export class AppErrorHandler extends ErrorHandler {
  /**
   * Initialize sentry service
   */
  // public initializeSentry() {
  //   Sentry.init({
  //     dsn: Env.SENTRY_DSN,
  //     release:
  //       Env.APP_NAME + " [" + Env.IS_DEBUG
  //         ? "debug"
  //         : "production" + "] " + Env.APP_VERSION,
  //   });
  // }

  constructor(
    private debug: Debug,
    private analytics: AnalyticsService,
  ) {
    // call super
    super();

    // initialize sentry
    // this.initializeSentry();
  }

  /**
   * Add automatic error handling
   * @param error
   */
  public handleError(error: any): void {
    // do our stuff
    if (this.debug) {
      this.debug.addLogEntry(
        new LogEntry("EXCEPTION", error, "critical"),
        true,
      );
    }

    // log error
    if (this.analytics) {
      this.analytics.logError(
        _.isObject(error) ? error["message"] : error ? error : "error",
        "console",
        true,
      );
    }

    // hide splash

    setTimeout(() => {
      // hide splash with delay maybe on error
      window.document.getElementById("global_animated_splash").style.display =
        "none";
    }, 5000);

    // Send it to sentry
    // Sentry.captureException(error);

    // call ionic error handler
    super.handleError(error);
  }
}
