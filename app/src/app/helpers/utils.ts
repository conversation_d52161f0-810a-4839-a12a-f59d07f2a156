import { environment as Env } from "../../environments/environment";
import * as _ from "lodash";
import { TranslateService } from "@ngx-translate/core";

/**
 * General purpose class for utils
 */
export class Utils {
  /**
   * See if the url is an app url for logged in users
   * @param url
   */
  public static isAppUrl(url: string) {
    return url.indexOf("app/") == 0 || url.indexOf("/app/") == 0;
  }

  /**
   *  This function takes an object as an argument and returns an
   *  array of its values. If the argument is already an array, it
   *  returns the same array. If the argument is not an object, it
   *  returns the argument itself.
   *
   * @param object
   * @returns
   */
  public static indexedObjectToArray(object) {
    // if it's an array - return
    if (_.isArray(object)) {
      return object;
    }

    // if it's not object - return
    if (!_.isObject(object)) {
      return object;
    }

    // this will return only the values as array
    return Object.values(object);
  }

  /**
   * Wait for a condition to be met within a specified timeout.
   *
   * @param conditionFn - A function that returns a boolean indicating if the condition is met.
   * @param intervalMs - The interval in milliseconds to check the condition.
   * @param timeoutMs - The maximum time in milliseconds to wait for the condition to be met.
   *
   * @returns A promise that resolves if the condition is met within the timeout, or rejects if the timeout is reached.
   */
  public static waitForCondition(
    conditionFn: () => boolean,
    intervalMs: number,
    timeoutMs: number,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const interval = setInterval(() => {
        if (conditionFn()) {
          clearInterval(interval);
          clearTimeout(timeout);
          resolve();
        }
      }, intervalMs);

      const timeout = setTimeout(() => {
        clearInterval(interval);
        reject(new Error("Condition not met within timeout"));
      }, timeoutMs);
    });
  }

  /**
   * Retry until condition is met
   *
   * @param condition
   * @param callback
   * @param maxRetries
   * @param retryInterval
   */
  public static retryUntil(
    condition: () => boolean,
    callback: () => void,
    maxRetries: number = 10,
    retryInterval: number = 1000,
  ) {
    if (condition()) {
      callback();
    } else {
      if (maxRetries >= 0) {
        setTimeout(() => {
          Utils.retryUntil(condition, callback, maxRetries - 1, retryInterval);
        }, retryInterval);
      }
    }
  }

  /**
   * Get days ago string from timestamp
   * @param ts
   * @returns
   */
  public static getTimeAgoString(ts: any): string {
    if (ts >= _.now()) {
      return "Same day";
    }

    let sec = Math.floor(_.now() / 1000 - parseInt(ts));
    let days = Math.floor(sec / 60 / 60 / 24);

    if (days <= 0) {
      return "Same day";
    } else if (days == 1) {
      return "1 day ago";
    } else if (days == 2) {
      return "2 days ago";
    } else if (days >= 3 && days < 8) {
      return "3-7 days ago";
    } else if (days >= 8 && days < 15) {
      return "8-14 days ago";
    } else if (days >= 15 && days < 31) {
      return "15-30 days ago";
    } else if (days >= 31 && days < 61) {
      return "31-60 days ago";
    } else if (days >= 61 && days < 181) {
      return "61-180 days ago";
    } else if (days >= 181 && days < 366) {
      return "181-365 days ago";
    }

    return "Year+ ago";
  }

  /**
   * check checkUrl param against currentUrl - if the current url begins with check url then it's true
   * @param currentUrl
   * @param checkUrl
   * @returns
   */
  public static checkUrl(currentUrl: string, checkUrl: string): boolean {
    return (
      currentUrl.indexOf(checkUrl + "/") == 0 ||
      currentUrl.indexOf("/" + checkUrl + "/") == 0 ||
      currentUrl.indexOf("/" + checkUrl) == 0
    );
  }

  /**
   * See if an url is upgrade page
   * @param url
   * @returns
   */
  public static isUpgradeUrl(url: string) {
    return url.indexOf("/go-premium") > 0;
  }

  /**
   * See if the url is an error url for logged in users
   * @param url
   */
  public static isErrorUrl(url: string) {
    return url.indexOf("/error") == 0 || url.indexOf("error") == 0;
  }

  public static isOlderBy(seconds: number, ts: number) {
    return ts + seconds < Utils.now();
  }

  /**
   * Return seconds / millis
   * @param doReturnMillis
   * @returns
   */
  public static now(doReturnMillis: boolean = false) {
    return doReturnMillis ? _.now() : Math.round(_.now() / 1000);
  }

  /**
   * helper function to get numbers from the inbox retry error message
   * example error 'Please wait 53 seconds to send this message'
   *
   */
  public static getCountdownFromError(errorText) {
    let regex = /[0-9]+/;
    let res = regex.exec(errorText) || [];
    if (res.length > 0) {
      return parseInt(res[0]) || 0;
    }
    return 0;
  }

  public static plural(
    translationService: TranslateService,
    val: number,
    translationKey: string,
  ) {
    if (!!!translationKey) {
      console.log("Empty translation key!");
      return;
    }

    let pluralKey = translationKey + "_plural";

    let singular = translationService.instant(translationKey);
    let plural = translationService.instant(translationKey + "_plural");

    // if we have no plural translation we return singular translation
    if (plural == pluralKey) {
      plural = singular;
    }

    return Math.abs(val) == 1 ? singular : plural;
  }

  /**
   * Returns a regexp for validating emails
   */
  public static getEmailRegexp() {
    return /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/i;
  }

  /**
   * Return regexp for email or username
   */
  public static getEmailOrUsernameRegexp() {
    return /^(((([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))|([a-z\p{L}0-9_\.\-]{6,}))$/iu;
  }

  /**
   * Returns the actual username regex string
   */
  public static getUsernameRegexString() {
    return "[a-zA-Zp{L}0-9_.-]*";
  }

  /**
   * Remove emojis from text
   * @param text cleaned text
   */
  public static removeEmojis(text: String) {
    let ranges = [
      "\ud83c[\udf00-\udfff]", // U+1F300 to U+1F3FF
      "\ud83d[\udc00-\ude4f]", // U+1F400 to U+1F64F
      "\ud83d[\ude80-\udeff]", // U+1F680 to U+1F6FF
    ];

    return text.replace(new RegExp(ranges.join("|"), "g"), "");
  }

  /**
   * add escape characters to regex search term
   * @param val string value
   */
  public static escapeRegex(val: string): string {
    return val.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&");
  }

  /**
   * Html entity decode
   *
   * @param html
   * @return {string}
   */
  public static decodeHtml(html) {
    let txt = document.createElement("textarea");
    txt.innerHTML = html;
    return txt.value;
  }

  /**
   * generate a descriptive app id for code use from app name and env
   *
   * @param {string} prefix
   * @return {string}
   */
  public static getAppId(prefix: string = ""): string {
    return (
      prefix +
      Env.PROJECT_ID +
      "_" +
      (!Env.IS_DEBUG ? "production" : "development")
    )
      .replace(" ", "_")
      .toLowerCase();
  }

  /**
   * convert url to base64
   */
  public static imageToBase64(url: string): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      let img = new Image();
      img.crossOrigin = "Anonymous";

      img.onload = () => {
        let canvas = document.createElement("canvas");
        canvas.height = img.naturalHeight;
        canvas.width = img.naturalWidth;
        canvas.getContext("2d").drawImage(img, 0, 0);

        let dataUrl = canvas
          .toDataURL("image/jpeg")
          .replace(/^data:image\/(png|jpg|jpeg);base64,/, "");

        canvas = null;
        resolve(dataUrl);
      };

      img.src = url;
    });
  }

  /**
   *
   * @param accessLevelNumber
   * @returns
   */
  public static getAccessObject(accessLevelNumber: any, key: string = null) {
    const accessLevels: { id: number; name: string; is_premium: boolean }[] = [
      {
        id: 10,
        name: "free",
        is_premium: false,
      },
      {
        id: 20,
        name: "premium",
        is_premium: true,
      },
      {
        id: 30,
        name: "premiumplus",
        is_premium: true,
      },
    ];

    let res = _.find(accessLevels, ["id", parseInt(accessLevelNumber)]);
    return !res ? null : key ? res[key] : res;
  }

  /**
   * Returns gender name by id
   * @param id
   * @returns
   */
  public static getGenderNameById(id: number) {
    if (!id) {
      return null;
    }
    let res = null;

    Env.APP_GENDERS.forEach((itm) => {
      if (itm.id == id) {
        res = itm;
      }
    });

    return res ? res["name"] : null;
  }

  /**
   * return gender object based on gender string
   * returns null if no pair found
   */
  public static getGenderIdByName(name: string) {
    let res = null;

    Env.APP_GENDERS.forEach((itm) => {
      if (itm.name == name) {
        res = itm;
      }
    });

    return res;
  }

  /**
   * string html
   * @param html string
   */
  public static stripHtml(html) {
    var tmp = document.createElement("DIV");
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || "";
  }

  /**
   * Return valid params from enum.
   *
   * @param params
   * @param doReturnNumbers
   */
  public static enumValues(params: object, doReturnNumbers: boolean = false) {
    return _.values(params).filter((val) =>
      doReturnNumbers ? _.isNumber(val) : !_.isNumber(val),
    );
  }

  /**
   * Helper to call function check if defined
   * @param functionToCall
   */
  public static call(functionToCall: Function = () => {}) {
    if (functionToCall) {
      functionToCall();
    } else {
      console.log("function call failed", functionToCall);
    }
  }

  public static btoaUnicode(str) {
    // First, we convert the string to a sequence of bytes using TextEncoder
    const uint8Array = new TextEncoder().encode(str);
    // Then, we convert these bytes to a string of Latin1 characters
    let binary = "";
    uint8Array.forEach((byte) => {
      binary += String.fromCharCode(byte);
    });
    // Finally, we use btoa to encode the string in base64
    return btoa(binary);
  }

  public static atobUnicode(str) {
    // Decode the base64 string into a Latin1 string
    const binary = atob(str);
    // Convert the Latin1 string to a Uint8Array
    const uint8Array = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      uint8Array[i] = binary.charCodeAt(i);
    }
    // Decode the UTF-8 byte array back into a string
    return new TextDecoder().decode(uint8Array);
  }
}
