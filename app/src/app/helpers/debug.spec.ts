import { TestBed, waitForAsync } from "@angular/core/testing";
import { Debug, IDebut_log_args, LogEntry } from "./debug";
import { Utils } from "./utils";

describe("LogEntry", () => {
  it("should be created", () => {
    let logEntry: LogEntry = new LogEntry("test", {}, "log", 12345);

    expect(logEntry.severity).toEqual("log");
  });

  it("should add stack to the entry object", () => {
    let logEntry: LogEntry = new LogEntry();

    logEntry.addStack();

    expect(logEntry.stack).toContain("Error:");
  });
});

describe("Debug", () => {
  let service: Debug;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({});

    service = TestBed.inject(Debug);

    service.isDebugEnv(true);
  }));

  it("should be created", () => {
    expect(service).toBeTruthy();
  });

  it("isDebugEnv - should override env debug status", () => {
    service.isDebug = true;

    service.isDebugEnv(false);

    expect(service.isDebug).toBeFalse();
  });

  it("clear - should clear log container", () => {
    let log: LogEntry = new LogEntry();

    service.addLogEntry(log);

    expect(service.get().length).toBeCloseTo(1);

    service.clear();

    expect(service.get().length).toBeCloseTo(0);
  });

  describe("log", () => {
    it("should log data", () => {
      let params: IDebut_log_args = {};

      let spy1 = spyOn(LogEntry.prototype, "addStack");
      let spy2 = spyOn(Utils, "clone");
      let spy3 = spyOn(service["logContainer"], "unshift");

      service.log(params);

      expect(spy1).toHaveBeenCalledTimes(0);
      expect(spy2).toHaveBeenCalledTimes(1);
      expect(spy3).toHaveBeenCalledTimes(1);
    });

    it("should log data and add to the stack", () => {
      let params: IDebut_log_args = {
        message: "test",
        data: { a: 1 },
        severity: "error",
        doAddStack: true,
      };

      let spy = spyOn(LogEntry.prototype, "addStack");

      service.log(params);

      expect(spy).toHaveBeenCalledTimes(1);
    });
  });

  describe("addLogEntry", () => {
    it("should log entry", () => {
      let log: LogEntry = new LogEntry();
      log.severity = "critical";
      service.isDebugEnv(false);

      let spy1 = spyOn(log, "addStack");
      let spy2 = spyOn(Utils, "clone");
      let spy3 = spyOn(service["logContainer"], "unshift");

      service.addLogEntry(log);

      expect(spy1).not.toHaveBeenCalled();
      expect(spy2).toHaveBeenCalledTimes(1);
      expect(spy3).toHaveBeenCalledTimes(1);
    });

    it("should log entry and add to the stack", () => {
      let log: LogEntry = new LogEntry();
      log.data = "";
      log.severity = "error";
      service.isDebugEnv(true);

      let spy1 = spyOn(log, "addStack");
      let spy2 = spyOn(Utils, "clone");
      let spy3 = spyOn(service["logContainer"], "unshift");
      let spy4 = spyOn(console, "log");

      service.addLogEntry(log, true);

      expect(spy1).toHaveBeenCalledTimes(1);
      expect(spy2).not.toHaveBeenCalled();
      expect(spy3).toHaveBeenCalledTimes(1);
      expect(spy4).toHaveBeenCalledTimes(1);
    });

    it("should log entry and should not add it to the log container", () => {
      let log: LogEntry = new LogEntry();
      log.data = "";
      log.severity = "info";
      service.isDebugEnv(false);

      let spy1 = spyOn(log, "addStack");
      let spy2 = spyOn(Utils, "clone");
      let spy3 = spyOn(service["logContainer"], "unshift");
      let spy4 = spyOn(console, "log");

      service.addLogEntry(log, true);

      expect(spy1).toHaveBeenCalledTimes(1);
      expect(spy2).not.toHaveBeenCalled();
      expect(spy3).not.toHaveBeenCalled();
      expect(spy4).not.toHaveBeenCalled();
    });
  });

  describe("displayLog", () => {
    it("should not display log", () => {
      let params: LogEntry = new LogEntry();

      service.isDebugEnv(false);

      let spy1 = spyOn(console, "log");

      service["displayLog"](params);

      expect(spy1).not.toHaveBeenCalled();
    });

    it("should display [info] log entry for mobile", () => {
      let params: LogEntry = new LogEntry();
      params.message = "test";
      params.severity = "info";

      let spy1 = spyOn(console, "log");
      let spy2 = spyOn(service["platform"], "is").and.returnValue(true);

      service["displayLog"](params);

      expect(spy1).toHaveBeenCalledTimes(1);
    });

    it("should display [info] log entry for browser", () => {
      let params: LogEntry = new LogEntry();
      params.severity = "info";

      let spy1 = spyOn(console, "log");

      service["displayLog"](params);

      expect(spy1).toHaveBeenCalledTimes(1);
      expect(spy1).toHaveBeenCalledWith("info: ", params);
    });

    it("should display [error] log entry for browser", () => {
      let params: LogEntry = new LogEntry();
      params.severity = "error";

      let spy1 = spyOn(console, "log");

      service["displayLog"](params);

      expect(spy1).toHaveBeenCalledTimes(1);
      expect(spy1).toHaveBeenCalledWith("error: ", params);
    });
  });
});
