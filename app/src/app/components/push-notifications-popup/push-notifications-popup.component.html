<div class="push-notifications-wrapper" [class.hidden]="!doShow">
  <span class="arrow"></span>
  <ion-card class="ion-text-center">
    <ion-card-header>
      <span class="header-icons">
        <ion-icon name="mail" color="primary"></ion-icon>
        <span class="bubble-container">
          <ion-icon name="chatbubble" color="primary"></ion-icon>
          <ion-icon name="chatbubble"></ion-icon>
          <ion-text color="primary">1</ion-text>
        </span>
      </span>
      <ion-card-title>{{
        "push_notifications_header_title" | translate
      }}</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-card-subtitle>{{
        "push_notifications_header_subtitle" | translate
      }}</ion-card-subtitle>
      <ion-row> </ion-row>
      <ion-text color="primary">
        <h2 class="card-content-title ion-padding">
          {{ "push_notifications_content_title" | translate }}
        </h2>
      </ion-text>
      <ion-row class="ion-text-center">
        <ion-text class="card-content-text">{{
          "push_notifications_content_subtitle" | translate
        }}</ion-text>
      </ion-row>
      <ion-row class="ion-justify-content-center">
        <ion-button class="ion-margin-top okButton" (click)="ok()">{{
          "push_notifications_button_ok" | translate | uppercase
        }}</ion-button>
      </ion-row>
      <ion-row class="ion-justify-content-center">
        <ion-button fill="clear" (click)="cancel()">{{
          "push_notifications_button_cancel" | translate
        }}</ion-button>
      </ion-row>
    </ion-card-content>
  </ion-card>
</div>
