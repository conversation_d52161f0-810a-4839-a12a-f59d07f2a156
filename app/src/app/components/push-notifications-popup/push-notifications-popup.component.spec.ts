import { ComponentFixture, TestBed, waitForAsync } from "@angular/core/testing";
import { IonicModule } from "@ionic/angular";

import { PushNotificationsPopupComponent } from "./push-notifications-popup.component";

describe("PushNotificationsPopupComponent", () => {
  let component: PushNotificationsPopupComponent;
  let fixture: ComponentFixture<PushNotificationsPopupComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [PushNotificationsPopupComponent],
      imports: [IonicModule.forRoot()],
    }).compileComponents();

    fixture = TestBed.createComponent(PushNotificationsPopupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
