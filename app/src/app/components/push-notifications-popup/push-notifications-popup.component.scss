.push-notifications-wrapper {
  width: 100%;
  height: 100%;
  position: fixed;
  background-color: #00000075;
  z-index: 999999;
  padding: 20px;

  .arrow {
    width: 0;
    height: 0;
    border-bottom: 35px solid #fff;
    border-left: 35px solid transparent;
    transform: skewX(-30deg);
    position: absolute;
    left: 50%;
    top: 6px;
    right: auto;
    margin-left: 30%;
  }

  ion-card {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    margin: 20px 16px;
    overflow: visible;
    box-shadow: none;

    ion-card-header {
      flex: 2;
      text-align: center;
      flex-direction: column;
      padding: 24px 24px 0;

      .bubble-container {
        position: absolute;
        left: 50%;
        top: 20px;

        ion-icon  {
          font-size: 2.3em;
          color: #fff;

          &[color="primary"] {
            position: absolute;
            left: 0;
            top: 0;
            font-size: 2.75em;
            margin-left: -3px;
            margin-top: -3px;
          }
        }

        ion-text {
          position: absolute;
          width: 100%;
          text-align: center;
          left: 0;
          top: 5px;
          font-size: 1.3em;
          font-weight: 700;
        }
      }

      ion-icon[name="mail"] {
        font-size: 5.5em;
      }

      ion-card-title {
        font-size: 20px;
        font-weight: 800;
        text-wrap: balance;
        margin: 8px 0 16px;
      }

    }

    ion-card-content {
      flex: 5;

      ion-card-subtitle {
        color: #000;
        border-bottom: 1px solid #c9c9c9;
        font-size: 16px;
        font-weight: 400;
        line-height: 1.4;
        text-wrap: pretty;
        text-transform: none;
        margin: 0;
        padding-bottom: 20px;
      }

      .card-content-title {
        font-weight: 700;
      }

      .card-content-text {
        color: #000;
      }

      .okButton {
        width: 100%;

        @media screen and (min-width: 720px) {
          max-width: 320px;
        }
      }
    }
  }
}
