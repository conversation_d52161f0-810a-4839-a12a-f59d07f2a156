import { Component, EventEmitter, Input, Output } from "@angular/core";
import { AnimationController } from "@ionic/angular";

@Component({
  selector: "app-push-notifications-popup",
  templateUrl: "./push-notifications-popup.component.html",
  styleUrls: ["./push-notifications-popup.component.scss"],
  standalone: false,
})
export class PushNotificationsPopupComponent {
  @Input() doShow: boolean = false;
  @Output() doShowChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Output() onSuccess: EventEmitter<void> = new EventEmitter();
  @Output() onCancel: EventEmitter<void> = new EventEmitter();

  constructor(private animationCtrl: AnimationController) {}

  public async ok() {
    await this.animate().play();
    this.doShow = false;
    setTimeout(() => this.onSuccess.emit(), 100);
  }

  public async cancel() {
    await this.animate().play();
    this.doShow = false;
    setTimeout(() => this.onCancel.emit(), 100);
  }

  private animate() {
    return this.animationCtrl
      .create()
      .addElement(document.querySelector(".push-notifications-wrapper"))
      .duration(400)
      .keyframes([
        { offset: 1, opacity: "0.5", transform: "translateY(100%)" },
      ]);
  }
}
