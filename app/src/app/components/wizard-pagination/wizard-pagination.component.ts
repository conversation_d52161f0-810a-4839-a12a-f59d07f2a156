import { Component, Input, OnInit } from "@angular/core";
import { IPaginator } from "src/app/services/wizard.service";
import * as _ from "lodash";

@Component({
  selector: "app-wizard-pagination",
  templateUrl: "./wizard-pagination.component.html",
  styleUrls: ["./wizard-pagination.component.scss"],
  standalone: false,
})
export class WizardPaginationComponent implements OnInit {
  @Input()
  public items: IPaginator[];
  public percentage: number = 0.0;

  constructor() {}

  ngOnInit() {
    // calculate percentage ( 0...1 )

    let itmCnt = this.items?.length || 1;

    this.percentage =
      (_.findIndex(this.items || [], "isActive") + 1) * (1 / itmCnt);
  }
}
