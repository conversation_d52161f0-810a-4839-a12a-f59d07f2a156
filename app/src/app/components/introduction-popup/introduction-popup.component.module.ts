import { CommonModule } from "@angular/common";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { IntroductionPopupComponent } from "./introduction-popup.component";
import { CountdownComponentModule } from "../countdown/countdown.component.module";
import { TranslateModule } from "@ngx-translate/core";
import { PipesModule } from "src/app/pipes/pipes.module";

@NgModule({
  imports: [CommonModule, TranslateModule, PipesModule],
  declarations: [IntroductionPopupComponent],
  exports: [IntroductionPopupComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class IntroductionPopupComponentModule {}
