import {
  Component,
  EventEmitter,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  Output,
} from "@angular/core";
import _ from "lodash";
import { ApiParams } from "../../services/api/api.params";
import { ReminderIntroductionService } from "../../services/reminder-introduction.service";
import { Platform } from "@ionic/angular";

@Component({
  selector: "app-introduction-popup",
  templateUrl: "./introduction-popup.component.html",
  styleUrls: ["./introduction-popup.component.scss"],
  standalone: false,
})
export class IntroductionPopupComponent implements OnInit, OnDestroy {
  @Input() doShow: boolean = false;
  @Output() doShowChange: EventEmitter<boolean> = new EventEmitter();
  @Input() isDebug: boolean = false;
  @Input() isValentines: boolean = false;

  /**
   * Default data set.
   *
   * @type {Introduction.type}
   */
  public introductionData: ApiParams.Introduction.type = {
    distance_under: 0,
    subject: "",
    message: "",
    cont_optin: true,
  };

  /**
   * Can send introduction data?
   */
  public canSend: boolean = false;

  private sub;

  constructor(
    public reminderIntroduction: ReminderIntroductionService,
    private platform: Platform,
  ) {}

  ngOnInit() {
    this.sub = this.platform.backButton.subscribeWithPriority(
      999999,
      async () => {
        this.dismissModal();
      },
    );
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  public dismissModal(): void {
    this.doShow = false;
    this.doShowChange.emit(this.doShow);

    if (this.isDebug) return;

    this.reminderIntroduction.postpone();
  }

  /**
   * Close modal.
   */
  public add(): void {
    this.doShow = false;
    this.doShowChange.emit(this.doShow);

    if (this.isDebug) return;

    this.reminderIntroduction.send(this.introductionData);
  }

  /**
   * Validate introduction data.
   *
   * @params {type} string 'subejct' | 'message'
   */
  public checkIntroductionParams(e, type): void {
    switch (type) {
      case "subject":
        this.introductionData.subject = e.detail.value;
        break;
      case "message":
        this.introductionData.message = e.detail.value;
        break;
    }
    this.canSend =
      this.introductionData.subject.length >= 3 &&
      this.introductionData.message.length >= 6;
  }
}
