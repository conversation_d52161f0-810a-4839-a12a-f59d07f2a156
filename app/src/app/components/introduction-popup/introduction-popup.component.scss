ion-modal#introduction-popup-modal {
  --width: calc(100% - 16px);
  --height: fit-content;
  --border-radius: 16px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  ion-card {

    ion-icon {
      padding: 12px 12px 0;
    }

    ion-card-header {
      padding: 0 24px;

      h2 {
        font-weight: 700;
        margin: 0;

        @media screen and (max-width: 400px) {
          font-size: 1.4rem;
        }
      }
    }

    ion-card-content {
      padding: 24px;

      .list-md {
        padding-bottom: 0;
      }

      ion-item-divider {
        --background: transparent;
      }

      ion-item {
        --padding-start: 0;
        --inner-padding-end: 0px;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 8px;
        }

        &.md:last-child {
          margin-bottom: 0;
        }
      }

      ion-input,
      ion-select,
      ion-textarea {
        --font-size: 15px;
      }

      .field-title {
        font-weight: 700;
        text-transform: uppercase;
        margin-right: 0;
        color: #444;

        &.label-stacked.sc-ion-label-md-h {
          font-size: 14px;
          transform: none;
          margin-top: 8px;
        }
      }

      .tip {
        color: var(--ion-color-medium-tint);
        font-weight: 400;
        text-transform: none;
        position: absolute;
        right: 0;
      }
      
      .btn {
        width: 100%;
      }

      .fix-opacity {
        opacity: 1 !important;
      }

      .fix-opacity-light {
        opacity: 0.6 !important;
      }
    }
  }

  ion-checkbox span {
    color: var(--ion-color-white-contrast);
    line-height: 1.4;
    display: inline-block;
    font-size: .9rem;
  }
}
