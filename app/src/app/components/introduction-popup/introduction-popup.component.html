<ion-modal
  id="introduction-popup-modal"
  [showBackdrop]="true"
  [backdropDismiss]="true"
  (willDismiss)="dismissModal()"
  [isOpen]="doShow"
  >
  <ion-card class="ion-no-margin">
    <ion-icon
      size="large"
      name="close-outline"
      (click)="dismissModal()"
      class="ion-hide-lg-up"
    ></ion-icon>
    <ion-card-header class="ion-text-center">
      @if (!isValentines) {
        <ion-card-title>
          <h2 [innerHTML]="'introduction_subtitle' | translate"></h2>
        </ion-card-title>
      }
      @if (isValentines) {
        <ion-card-title>
          <h2 [innerHTML]="'introduction_subtitle_valentines_1' | translate"></h2>
          <ion-text color="primary">
            <h2
              [innerHTML]="'introduction_subtitle_valentines_2' | translate"
            ></h2>
          </ion-text>
          <h5
            class="ion-no-margin"
            [innerHTML]="'introduction_subtitle_valentines_3' | translate"
          ></h5>
        </ion-card-title>
      }
    </ion-card-header>

    <ion-card-content class="ion-text-center ion-padding-top">
      <!-- introduction service content -->
      <ion-list>
        <ion-item-group>
          <!-- DISTANCE -->
          <ion-item-divider class="ion-no-padding">
            <ion-label
              position="stacked"
              [innerHTML]="'introduction_distance' | translate"
              class="field-title ion-text-start"
            ></ion-label>
          </ion-item-divider>
          <ion-item>
            <ion-select
              [placeholder]="'any_distance' | translate"
              [value]="0"
              (ionChange)="
                introductionData.distance_under = $event.detail.value
              "
              interface="popover"
              >
              <ion-select-option
                [value]="0"
                [innerHTML]="'any_distance' | translate"
              ></ion-select-option>
              <ion-select-option [value]="20">20 {{ 'miles_distance' | translate }} / {{ 20*1.6 }} {{ 'kilometer_distance' | translate }} </ion-select-option>
              <ion-select-option [value]="30">30 {{ 'miles_distance' | translate }} / {{ 30*1.6 }} {{ 'kilometer_distance' | translate }} </ion-select-option>
              <ion-select-option [value]="50">50 {{ 'miles_distance' | translate }} / {{ 50*1.6 }} {{ 'kilometer_distance' | translate }} </ion-select-option>
              <ion-select-option [value]="80">80 {{ 'miles_distance' | translate }} / {{ 80*1.6 }} {{ 'kilometer_distance' | translate }} </ion-select-option>
              <ion-select-option [value]="160">160 {{ 'miles_distance' | translate }} / {{ 160*1.6 }} {{ 'kilometer_distance' | translate }} </ion-select-option>
              <ion-select-option [value]="400">400 {{ 'miles_distance' | translate }} / {{ 400*1.6 }} {{ 'kilometer_distance' | translate }} </ion-select-option>
            </ion-select>
          </ion-item>

          <!-- SUBJECT -->
          <ion-item-divider class="ion-no-padding">
            <ion-label
              class="field-title ion-text-start"
              position="stacked"
              id="introduction_subject"
              >{{
              (isValentines
              ? "introduction_subject_valentines"
              : "introduction_subject"
              ) | translate
              }}</ion-label
              >
            </ion-item-divider>
            <ion-item>
              <ion-input
                aria-labelledby="introduction_subject"
                [aria-label]="'introduction_subject' | translate"
              [placeholder]="
                (isValentines
                  ? 'introduction_subject_placeholder_valentines'
                  : 'introduction_subject_placeholder'
                ) | translate
              "
                name="subject"
                (ionInput)="checkIntroductionParams($event, 'subject')"
              ></ion-input>
            </ion-item>

            <!-- MESSAGE -->
            <ion-item-divider class="ion-no-padding">
              <ion-label class="field-title ion-text-start" position="stacked"
                >{{ "introduction_message" | translate }}
              </ion-label>
              <span class="tip">{{
                "introduction_message_tip" | translate
              }}</span>
            </ion-item-divider>
            <ion-item>
              <ion-textarea
                [ariaLabel]="'introduction_message' | translate"
              [placeholder]="
                (isValentines
                  ? 'introduction_message_placeholder_valentines'
                  : 'introduction_message_placeholder'
                ) | translate
              "
                rows="1"
                name="message"
                (ionInput)="checkIntroductionParams($event, 'message')"
              ></ion-textarea>
            </ion-item>

            <ion-item lines="none">
              <ion-checkbox
                (ionChange)="introductionData.cont_optin = $event.detail.checked"
                [checked]="true"
                label-placement="end"
                >
                <span class="ion-text-wrap">
                  {{ "introduction_cont_optin" | translate }}
                </span>
              </ion-checkbox>
            </ion-item>

            <ion-item lines="none">
              <ion-button
                size="default"
                expand="block"
                [disabled]="!canSend"
                (click)="add()"
              [innerHTML]="
                (isValentines
                  ? 'introduction_button_valentines'
                  : 'introduction_step6_button_text'
                ) | translate
              "
                class="btn"
              ></ion-button>
            </ion-item>
          </ion-item-group>
        </ion-list>
      </ion-card-content>
    </ion-card>
  </ion-modal>
