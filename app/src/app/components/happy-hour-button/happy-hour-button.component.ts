import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { HappyHourService } from "../../services/happy-hour.service";

@Component({
  selector: "app-happy-hour-button",
  templateUrl: "./happy-hour-button.component.html",
  styleUrls: ["./happy-hour-button.component.scss"],
  standalone: false,
})
export class HappyHourButtonComponent {
  /**
   * Button disabled?
   *
   * @type {boolean}
   */
  @Input("disabled")
  disabled: boolean = false;

  /**
   * Button background.
   *
   * @type {string}
   */
  @Input("bg")
  bg: string = "primary";

  /**
   * Button color.
   *
   * @type {string}
   */
  @Input("color")
  color: string = "light";

  @Input("clear")
  clear: boolean = false;

  /**
   * Clean button (without box-shadow).
   *
   * @type {boolean}
   */
  @Input("clean")
  clean: boolean = false;

  /**
   * On click.
   *
   * @type {EventEmitter<void>}
   */
  @Output("onClick")
  buttonClick: EventEmitter<void> = new EventEmitter<void>();

  /**
   * Constructor.
   */
  constructor(public hhService: HappyHourService) {}

  /**
   * On click.
   */
  public onClick() {
    this.buttonClick.emit();
  }
}
