import { CommonModule } from "@angular/common";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { HappyHourButtonComponent } from "./happy-hour-button.component";
import { TranslateModule } from "@ngx-translate/core";
import { CountdownComponentModule } from "../countdown/countdown.component.module";

@NgModule({
  imports: [CommonModule, TranslateModule, CountdownComponentModule],
  declarations: [HappyHourButtonComponent],
  exports: [HappyHourButtonComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class HappyHourButtonComponentModule {}
