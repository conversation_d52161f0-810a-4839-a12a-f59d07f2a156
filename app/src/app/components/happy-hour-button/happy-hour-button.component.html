@if (hhService.showHH() && hhService.data) {
  <ion-grid
    class="ion-text-center hhGrid ion-margin-vertical"
    >
    <ion-row>
      <ion-col>
        @if (hhService.data.start > 0) {
          <h4 class="ion-no-margin">
            {{ "starts_in" | translate }}
          </h4>
        }
        @if (hhService.data.start == 0 && hhService.data.end > 0) {
          <h4
            class="ion-no-margin"
            >
            {{ "happy_hour_ends_in" | translate }}
          </h4>
        }
        <h1 class="ion-margin-vertical">
          <app-countdown
            [time]="hhService.displayTime"
            class="hh-countdown"
          ></app-countdown>
        </h1>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-button
          expand="block"
          id="hh_footer_button"
          (click)="onClick()"
          [disabled]="disabled"
          [color]="bg"
          [class.shadow]="!clean"
          [clear]="clear"
          >
          {{ "send_message" | translate }} {{ "free" | translate }}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
}
