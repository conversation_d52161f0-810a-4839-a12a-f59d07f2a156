import {
  Component,
  HostListener,
  Input,
  OnInit,
  Output,
  EventEmitter,
} from "@angular/core";
import { <PERSON><PERSON>anitizer, SafeHtml } from "@angular/platform-browser";
import { TranslateService } from "@ngx-translate/core";
import { DeeplinkService } from "src/app/services/deeplink.service";
import { environment as Env } from "../../../environments/environment";

@Component({
  selector: "app-external-content",
  templateUrl: "./external-content.component.html",
  styleUrls: ["./external-content.component.scss"],
  standalone: false,
})
export class ExternalContentComponent implements OnInit {
  @Input()
  public content: string;

  @Input()
  public doRemoveAnchor: boolean = false;

  @Input()
  public doOverwriteUtmMedium: boolean = true;

  @Output()
  public linkClick = new EventEmitter<any>();

  constructor(
    private sanitizer: DomSanitizer,
    private deeplink: DeeplinkService,
    private translateService: TranslateService,
  ) {}

  public renderedContent: SafeHtml = "";

  ngOnInit() {
    this.renderedContent = this.transform(this.content);
  }

  /**
   * append params
   *
   * @param match
   * @param g1
   * @param g2
   */
  private appendParams(match, g1, g2) {
    // if external content uses relative url's we prepend the website url
    if (g1.indexOf("http") < 0) {
      g1 = "https://" + Env.APP_WEBSITE + g1;
    }

    // generate url object
    let _urlObj = new URL(g1);

    // append tracking value to every link so dating can track
    if (this.doOverwriteUtmMedium) {
      _urlObj.searchParams.set(Env.URL_TRACKING_KEY, Env.URL_TRACKING_VALUE);
    }

    return (
      '<a href="' + _urlObj.toString() + "\" target='_blank'>" + g2 + "</a>"
    );
  }

  // IAB Filter - replace href with a function call
  public transform(text: string) {
    // clean up the text

    text = text.replace(
      "{{please_open_message_in_browser}}",
      "<b>" + this.translateService.instant("open_message_in_browser") + "</b>",
    );
    text = text.replace(
      "{{open_message_in_browser}}",
      "<b>" + this.translateService.instant("open_message_in_browser") + "</b>",
    );

    text = text.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim, "");
    text = text.replace(/<style[^>]*>([\S\s]*?)<\/style>/gim, "");
    text = text.replace(/<link[^>]*>/gim, "");

    if (this.doRemoveAnchor)
      text = text.replace(/<a[^>]*>([\S\s]*?)<\/a>/gim, function (match, p1) {
        return p1;
      });

    let re = /<a[^>]*href=\\?\"([^\"]*)\\?\"[^>]*>(.*?)<\/a>/gi;

    return this.sanitizer.bypassSecurityTrustHtml(
      text.replace(re, (match, g1, g2) => this.appendParams(match, g1, g2)),
    );
  }

  // hack for A href to work from static content external files

  @HostListener("click", ["$event"])
  public onClick(event: MouseEvent) {
    let _t = event.target as HTMLElement;
    let _found = false;

    // If we don't have an anchor tag, we don't need to do anything.
    if (_t instanceof HTMLAnchorElement !== false) {
      _found = true;
    } else if (_t.parentElement instanceof HTMLAnchorElement !== false) {
      _t = _t.parentElement;
      _found = true;
    }

    // Prevent page from reloading
    event.preventDefault();
    let target = <HTMLAnchorElement>_t;

    this.deeplink.setLinkData(target.getAttribute("href"), false).handle();

    // emmit event with deeplink reference
    this.linkClick.emit(this.deeplink);
  }
}
