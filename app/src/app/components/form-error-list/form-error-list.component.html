<div [class.hidden]="!this.errorList">
  <ul class="error-list">
    @for (errorItem of this.errorList; track errorItem) {
      <li>
        <ion-text color="danger" class="error-msg">
          <div class="icon-text-wrapper">
            <ion-icon size="large" name="alert-circle-outline"></ion-icon>
            <span class="error-text" [innerHtml]="this.t(errorItem)"></span>
          </div>
        </ion-text>
      </li>
    }
  </ul>
</div>