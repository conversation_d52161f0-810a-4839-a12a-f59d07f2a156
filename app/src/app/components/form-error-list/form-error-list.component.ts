import { Component, HostListener, Input, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { TranslateService } from "@ngx-translate/core";

@Component({
  selector: "app-form-error-list",
  templateUrl: "./form-error-list.component.html",
  styleUrls: ["./form-error-list.component.scss"],
  standalone: false,
})
export class FormErrorListComponent implements OnInit {
  @Input()
  errorList: string[];

  constructor(
    private translate: TranslateService,
    private router: Router,
  ) {}

  /**
   * translate value
   * @param val
   * @returns
   */
  public t(val: string) {
    return this.translate.instant(val);
  }

  ngOnInit() {}

  // hack for A href to work from translation files

  @HostListener("click", ["$event"])
  public onClick(event: MouseEvent) {
    // If we don't have an anchor tag, we don't need to do anything.
    if (event.target instanceof HTMLAnchorElement === false) {
      return;
    }

    // Prevent page from reloading
    event.preventDefault();
    let target = <HTMLAnchorElement>event.target;

    // Navigate to the path in the link
    // we'll use # in language files, so the link don't break
    // the application if it's not properly added
    this.router.navigate([target.hash.replace("#", "/")]);
  }
}
