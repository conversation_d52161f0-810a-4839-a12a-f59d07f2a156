import { ComponentFixture, TestBed, waitForAsync } from "@angular/core/testing";
import { IonicModule } from "@ionic/angular";

import { WizardSubmitButtonComponent } from "./wizard-submit-button.component";

describe("WizardSubmitButtonComponent", () => {
  let component: WizardSubmitButtonComponent;
  let fixture: ComponentFixture<WizardSubmitButtonComponent>;

  beforeEach(
    waitForAsync(() => {
      TestBed.configureTestingModule({
        declarations: [WizardSubmitButtonComponent],
        imports: [IonicModule.forRoot()],
      }).compileComponents();

      fixture = TestBed.createComponent(WizardSubmitButtonComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
    })
  );

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
