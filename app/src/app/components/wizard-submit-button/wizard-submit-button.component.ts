import { Component, Input, OnInit } from "@angular/core";

@Component({
  selector: "app-wizard-submit-button",
  templateUrl: "./wizard-submit-button.component.html",
  styleUrls: ["./wizard-submit-button.component.scss"],
  standalone: false,
})
export class WizardSubmitButtonComponent implements OnInit {
  @Input()
  public text: string = "";

  @Input()
  public isProgress: boolean = false;

  @Input()
  public disabled: boolean = false;

  constructor() {}

  ngOnInit() {}
}
