<ion-modal
  id="rules-popup-modal"
  [showBackdrop]="true"
  [backdropDismiss]="false"
  (willDismiss)="dismissModal()"
  [isOpen]="doShow"
>
  <ion-card class="ion-no-margin">
    <ion-icon
      (click)="dismissModal()"
      size="large"
      name="close-outline"
      class="ion-hide-lg-up"
    ></ion-icon>
    <ion-card-header class="ion-text-center">
      <ion-card-title>
        <ion-text color="dark" class="header">
          <p class="ion-no-margin">
            {{ text_1 }}
            {{ text_2 }}
          </p>
        </ion-text>
      </ion-card-title>
      <ion-card-subtitle>
        <ion-text color="medium">
          <p class="text-transform-none font-weight-normal">
            {{ text_3 }}
          </p>
        </ion-text>
      </ion-card-subtitle>
    </ion-card-header>

    <ion-card-content class="ion-no-padding ion-padding-bottom">
      <ion-row class="ion-align-items-center ion-padding-end">
        <ion-col size="8" class="ion-no-padding">
          <ion-item lines="none" class="no-click">
            <ion-checkbox
              label-placement="end"
              class="checkbox"
              slot="start"
              [checked]="errorValidEmailAddress"
              [class.fix-opacity]="!errorValidEmailAddress"
              [class.fix-opacity-light]="errorValidEmailAddress"
              ><span class="checkbox-label">{{
                "rules_popup_error_valid_email_address" | translate
              }}</span></ion-checkbox
            >
          </ion-item>
        </ion-col>
        <ion-col size="4" class="ion-no-padding">
          <ion-button
            size="small"
            expand="block"
            [class.hidden]="errorValidEmailAddress"
            (click)="navigate('app/manage-menu')"
            >{{ "update" | translate }}</ion-button
          >
        </ion-col>
      </ion-row>

      <ion-row class="ion-align-items-center ion-padding-end">
        <ion-col size="8" class="ion-no-padding">
          <ion-item lines="none" class="no-click">
            <ion-checkbox
              label-placement="end"
              class="checkbox"
              justify="start"
              slot="start"
              [checked]="errorPhotoApproved"
              [class.fix-opacity]="!errorPhotoApproved"
              [class.fix-opacity-light]="errorPhotoApproved"
              ><span class="checkbox-label">{{
                "rules_popup_error_photo_approved" | translate
              }}</span></ion-checkbox
            >
          </ion-item>
        </ion-col>
        <ion-col size="4" class="ion-no-padding">
          <ion-button
            class="ion-text-wrap"
            size="small"
            expand="block"
            [class.hidden]="errorPhotoApproved"
            (click)="navigate('/app/my-profile-edit')"
            >{{ "upload_photo" | translate }}</ion-button
          >
        </ion-col>
      </ion-row>

      <ion-row
        class="ion-align-items-center ion-padding-end ion-padding-bottom"
      >
        <ion-col size="8" class="ion-no-padding">
          <ion-item lines="none" class="no-click">
            <ion-checkbox
              label-placement="end"
              class="checkbox"
              slot="start"
              [checked]="errorProfileHeadline"
              [class.fix-opacity]="!errorProfileHeadline"
              [class.fix-opacity-light]="errorProfileHeadline"
              ><span class="checkbox-label">{{
                "rules_popup_error_profile_headline" | translate
              }}</span></ion-checkbox
            >
          </ion-item>
        </ion-col>
        <ion-col size="4" class="ion-no-padding">
          <ion-button
            class="ion-text-wrap"
            size="small"
            expand="block"
            [class.hidden]="errorProfileHeadline"
            (click)="navigate('/app/my-profile-edit')"
            >{{ "update" | translate }}</ion-button
          >
        </ion-col>
      </ion-row>
    </ion-card-content>
  </ion-card>
</ion-modal>
