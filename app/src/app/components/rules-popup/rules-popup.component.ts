import {
  Component,
  EventEmitter,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  Output,
} from "@angular/core";
import { ProfileOwn } from "../../schemas/profile-own";
import _ from "lodash";
import { Router } from "@angular/router";
import { NavController, Platform } from "@ionic/angular";
import { AccountService } from "../../services/account.service";

@Component({
  selector: "app-rules-popup",
  templateUrl: "./rules-popup.component.html",
  styleUrls: ["./rules-popup.component.scss"],
  standalone: false,
})
export class RulesPopupComponent implements OnInit, OnDestroy {
  /*
  'error_force_approved_photo' => 'You need to have an approved photo. '
  'error_force_profile_details_update' => 'You need to have profile headline and description filled out. ',
  'error_force_email_update' => 'You need to have valid email address. '
  */

  @Input() profile: ProfileOwn.type = null;

  @Input() text_1: string = null;
  @Input() text_2: string = null;
  @Input() text_3: string = null;

  @Input() doShow: boolean = false;
  @Output() doShowChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  public errorValidEmailAddress: boolean = true;
  public errorPhotoApproved: boolean = true;
  public errorProfileHeadline: boolean = true;

  private sub;

  constructor(
    public router: Router,
    private platform: Platform,
    private navController: NavController,
    public accountService: AccountService,
  ) {}

  ngOnInit() {
    // disable back button for the view
    this.sub = this.platform.backButton.subscribeWithPriority(999, async () =>
      this.dismissModal(),
    );

    this.errorProfileHeadline = this.accountService.hasProfileHeadline();
    this.errorValidEmailAddress = this.accountService.hasValidEmail();
    this.errorPhotoApproved = this.accountService.hasApprovedPhotos();
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  public navigate(link = null): void {
    this.doShow = false;
    setTimeout(() => {
      if (link) this.router.navigate([link]);
    }, 100);
  }

  public dismissModal(): void {
    this.doShow = false;
    this.doShowChange.emit(this.doShow);
  }
}
