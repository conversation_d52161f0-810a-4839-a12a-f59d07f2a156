import { CommonModule } from "@angular/common";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { RulesPopupComponent } from "./rules-popup.component";
import { CountdownComponentModule } from "../countdown/countdown.component.module";
import { TranslateModule } from "@ngx-translate/core";

@NgModule({
  imports: [CommonModule, TranslateModule],
  declarations: [RulesPopupComponent],
  exports: [RulesPopupComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class RulesPopupComponentModule {}
