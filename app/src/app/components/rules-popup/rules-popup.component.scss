ion-modal#rules-popup-modal {
  --width: 90%;
  --height: fit-content;
  --border-radius: 16px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  top: -35%;

  ion-card {
    ion-icon {
        padding: 10px;
    }

    ion-card-header {
        flex-direction: column;
        padding: 0 10px;

        ion-card-title {
            ion-text.header {
                font-size: 16px;
            }
        }

        ion-card-subtitle {
            .text-transform-none {
                text-transform: none;
            }
            .font-weight-normal {
                font-weight: normal;
            }
        }
    }

    ion-card-content {

        ion-item {
            --min-height: auto;
            --background-focused-opacity: 0;
        }
        .no-click {
            pointer-events: none;
        }

        .checkbox {
            --border-color: #333;
            --size: 20px;
            color: #000;
            line-height: 1.2;
            margin-right: 0;
            pointer-events: none;

            .checkbox-label {
                font-size: 14px;
                white-space: normal;
            }
        }

        ion-button {
            font-size: 11px;
            min-height: 35px;

            &.ios {
              font-size: 13px;
            }
        }

        .fix-opacity {
            opacity: 1 !important;
        }

        .fix-opacity-light {
            opacity: .6 !important;
        }
    }
  }
}