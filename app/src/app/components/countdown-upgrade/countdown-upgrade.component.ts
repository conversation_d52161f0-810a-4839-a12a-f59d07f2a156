import { Component, Input, OnInit } from "@angular/core";
import $ from "jquery";

@Component({
  selector: "app-countdown-upgrade",
  templateUrl: "./countdown-upgrade.component.html",
  styleUrls: ["./countdown-upgrade.component.scss"],
  standalone: false,
})
export class CountdownUpgradeComponent implements OnInit {
  @Input() renewal: number = 0;

  /**
   * For assigning random ID in HTML => #cdComponent_1234
   * otherwise it will mess with the same html and most likely it won't work.
   */
  public counter: number = Math.floor(Math.random() * 1000);

  constructor() {}

  ngOnInit() {
    // Wait for component to render.
    setTimeout(() => {
      $(`#cdComponent_${this.counter}`).countdown(
        new Date(this.renewal * 1000),
        (event) => {
          $(`#cdHours_${this.counter}`).html(event.strftime("%H"));
          $(`#cdMins_${this.counter}`).html(event.strftime("%M"));
          $(`#cdSecs_${this.counter}`).html(event.strftime("%S"));
        },
      );
    }, 100);
  }
}
