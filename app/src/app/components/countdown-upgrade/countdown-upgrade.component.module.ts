import { CommonModule } from "@angular/common";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { CountdownUpgradeComponent } from "./countdown-upgrade.component";
import { TranslateModule } from "@ngx-translate/core";
import { CountdownComponentModule } from "../countdown/countdown.component.module";

@NgModule({
  imports: [CommonModule, TranslateModule, CountdownComponentModule],
  declarations: [CountdownUpgradeComponent],
  exports: [CountdownUpgradeComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CountdownUpgradeComponentModule {}
