import {
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { ProfileBase } from "src/app/schemas/profile-base";
import { EventEmitter } from "@angular/core";
import { Debug } from "src/app/helpers/debug";

@Component({
  selector: "card-base",
  templateUrl: "./card-base.component.html",
  styleUrls: ["./card-base.component.scss"],
  standalone: false,
})
export class CardBaseComponent implements AfterViewInit {
  @ViewChild("card")
  public cardRef: ElementRef;

  @ViewChild("likeStatus")
  public likeStatusRef: ElementRef;

  @ViewChild("passStatus")
  public passStatusRef: ElementRef;

  @ViewChild("superLikeStatus")
  public superLikeStatusRef: ElementRef;

  @ViewChild("laterStatus")
  public laterStatusRef: ElementRef;

  @ViewChild("visitStatus")
  public visitStatusRef: ElementRef;

  @ViewChild("dismissStatus")
  public dismissStatusRef: ElementRef;

  @Input()
  public profile: ProfileBase.type;

  @Input()
  public offset: number = 3;

  @Input()
  public ads: boolean = false;

  @Input()
  public set state(
    state: "like" | "pass" | "superlike" | "later" | "visit" | "dismiss" | "",
  ) {
    if (this.likeStatusRef) {
      this.likeStatusRef.nativeElement.style.opacity =
        state == "like" ? "1" : "0";
      this.passStatusRef.nativeElement.style.opacity =
        state == "pass" ? "1" : "0";
      this.superLikeStatusRef.nativeElement.style.opacity =
        state == "superlike" ? "1" : "0";
      this.laterStatusRef.nativeElement.style.opacity =
        state == "later" ? "1" : "0";
      this.laterStatusRef.nativeElement.style.opacity =
        state == "visit" ? "1" : "0";
      this.laterStatusRef.nativeElement.style.opacity =
        state == "dismiss" ? "1" : "0";
    }
  }

  @Output()
  onLike = new EventEmitter<ProfileBase.type>();

  @Output()
  onDislike = new EventEmitter<ProfileBase.type>();

  @Output()
  onUndo = new EventEmitter<ProfileBase.type>();

  @Output()
  onSuperlike = new EventEmitter<ProfileBase.type>();

  @Output()
  onMenu = new EventEmitter<ProfileBase.type>();

  @Output()
  onOpenProfile = new EventEmitter<ProfileBase.type>();

  @Output()
  onOpenGallery = new EventEmitter<ProfileBase.type>();

  @Output()
  onDismiss = new EventEmitter();

  @Output()
  onVisit = new EventEmitter();

  constructor(public debug: Debug) {}

  public openGallery() {
    this.onOpenGallery.emit(this.profile);
  }

  public openProfile() {
    this.onOpenProfile.emit(this.profile);
  }

  public undo() {
    this.onUndo.emit(this.profile);
  }

  public like() {
    this.onLike.emit(this.profile);
  }

  public dislike() {
    this.onDislike.emit(this.profile);
  }

  public superlike() {
    this.onSuperlike.emit(this.profile);
  }

  public menu() {
    this.onMenu.emit(this.profile);
  }

  ngAfterViewInit() {
    this.cardRef.nativeElement.style.marginTop = this.offset + "%";
  }

  public dismiss() {
    this.debug.l("dismiss ads");
    this.onDismiss.emit();
  }

  public async visit() {
    this.debug.l("visit ads");
    this.onVisit.emit();
  }
}
