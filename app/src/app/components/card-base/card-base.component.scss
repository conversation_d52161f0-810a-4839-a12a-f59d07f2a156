ion-card {
  border-radius: 0;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  margin: 0 !important;
  position: absolute;
  display: flex;
  flex-direction: column;
  background: var(--ion-color-primary-contrast);
  box-shadow: 0 6px 12px rgba(0,0,0,.04);

  .card-base-wrapper {
    display: inherit;
  }

  ion-card-content {
    padding: 20px 16px;
  }

  /* Status: Indicator */

  .status-icon {
    position: absolute;
    font-size: 36vw;
    top: 16px;
    opacity: 0;
    pointer-events: none;
  }

  .iLike {
    color: var(--ion-color-danger);
    top: 16px;
    left: 16px;
  }

  .iPass {
    color: black;
    right: 16px;
  }

  .iLater {
    color: white;
    font-size: 28vw;
    top: 36%;
    left: 50%;
    transform: translateX(-50%);
  }

  .iSuperlike {
    color: yellow;
    top: 36%;
    left: 50%;
    transform: translateX(-50%);
  }

  /* User: Image */

  .image-wrapper {
    display: flex;
    flex-grow: 1;
  }

  ion-img {
    flex-grow: 1;
    flex-basis: auto;
    object-fit: cover; 

    img {
      -webkit-touch-callout: none;
    }
  }

  .skeleton-image {
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: auto;
    object-fit: cover;

    ion-skeleton-text {
      height: 100%;
      margin: 0;
    }
  }

  .second-row {
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: auto;
  }

  /* User: Info */

  .user-info {
    margin-bottom: 20px;
  }

  .user-info-username-age {
    width: 100%;
    color: var(--ion-color-white);
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 4px;
    margin-bottom: 2px;
  }

  .user-info-location {
    width: 100%;
    color: var(--ion-color-white);
    font-size: 18px;
    opacity: .8;
  }

  .username-age {
    font-weight: 600;
    margin-bottom: 2px;

    h2 {
      font-size: 24px;
    }
  }

  .user-action-ads {
    color: var(--ion-color-white);
  }

  /* Buttons */

  .user-action {
    width: 100%;
    background: linear-gradient(0deg, rgba(2,0,36,0.25) 0%, rgba(0,0,0,0.25) 75%, rgba(0,0,0,0) 75%);
    position: absolute;
    bottom: 0;

    .user-action-buttons-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;

      ion-fab-button {

        &.btn--fs {
          height: 74px;
          width: 74px;
          margin: 8px;

          ion-icon {
            font-size: 40px;
          }
        }

        &.small {
          width: 62px;
          height: 62px;
        }

        &.dislike {
          ion-icon {
            color: #D85848;
          }
        }

        &.superlike {
          ion-icon {
            color: #EFC941;
          }
        }

        &.like {
          ion-icon {
            color: #99C369;
          }
        }
      }
    }
  }

  .menu-icon {
    font-size: 20px;
    position: absolute;
    top: 0;
    right: 0;
    padding: 16px;
  }

  .btn-info {
    font-size: 20px;
    margin-left: 6px;
  }

  &:not(:last-child) {
    left: 10px;
    right: 10px;
  }

  .ad-sign {
    font-weight: 500;
    color: var(--ion-color-light);
    background: var(--ion-color-dark);
    padding: 3px;
    border-radius: 5px;
  }
}
