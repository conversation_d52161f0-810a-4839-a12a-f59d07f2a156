<ion-card #card [hidden]="!profile" class="card-base card-base-container gl-box-shadow">
  <!-- card-base-ads -->
  @if (profile && profile.profile_type === 'ads') {
    <div class="card-base-wrapper">
      <span class="image-wrapper">
        <ion-img
          style="pointer-events: none;"
          [src]="profile?.image"
          onerror="this.onerror=null;this.src='assets/placeholder.svg';"
          [hidden]="!profile?.image"
        ></ion-img>
      </span>
      <div class="skeleton-image" [hidden]="profile?.image">
        <ion-skeleton-text animated style="width: 100%"></ion-skeleton-text>
      </div>
      <ion-icon #visitStatus class="status-icon iLike" name="heart"></ion-icon>
      <ion-icon #dismissStatus class="status-icon iPass" name="close"></ion-icon>
    </div>
  }

  @if (profile && profile.profile_type === 'ads') {
    <ion-card-header class="user-action user-action-ads ion-no-padding">
      <ion-grid class="ion-no-padding">
        <ion-row class="user-action-buttons ion-text-center">
          <ion-col class="user-action-buttons-wrapper ion-no-padding">
            <ion-fab-button color="white" class="dislike btn--fs" (click)="dismiss()">
              <ion-icon #dismissStatus name="close"></ion-icon>
            </ion-fab-button>
            <ion-fab-button color="white" class="like btn--fs" (click)="visit()">
              <ion-icon #visitStatus name="heart"></ion-icon>
            </ion-fab-button>
          </ion-col>
        </ion-row>
        @if (profile) {
          <ion-row class="ion-text-center">
            <ion-col class="ion-no-padding">
              <h2>
                {{ profile?.title }}
              </h2>
              <ion-text>
                <span color="white" class="ad-sign">{{ "ad_prefix" | translate }}</span>
                {{ profile?.description }}
              </ion-text>
            </ion-col>
          </ion-row>
        }
        <ion-row class="dismiss-visit-buttons ion-text-center ion-padding-top">
          <ion-col class="ion-no-padding">
            <ion-button #dismissStatus color="medium" class="dislike btn--fs" (click)="dismiss()">
              {{ 'dismiss' | translate }}
            </ion-button>
            <ion-button #visitStatus color="primary" class="like btn--fs" (click)="visit()">
              {{ 'visit_site' | translate }}
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card-header>
  }
  <!-- card-base-ads -->

  <!-- card-base -->
  @if (profile && profile.profile_type === 'profile') {
    <div class="card-base-wrapper">
      <span (click)="openGallery()" class="image-wrapper">
        <ion-img
          style="pointer-events: none"
          [src]="profile?.avatar_url"
          onerror="this.onerror=null;this.src='assets/placeholder.svg';"
          [hidden]="!profile?.avatar_url"
        ></ion-img>
      </span>
      <div class="skeleton-image" [hidden]="profile?.avatar_url">
        <ion-skeleton-text animated style="width: 100%"></ion-skeleton-text>
      </div>
      <ion-icon #likeStatus class="status-icon iLike" name="heart"></ion-icon>
      <ion-icon #passStatus class="status-icon iPass" name="close"></ion-icon>
      <ion-icon
        #superLikeStatus
        class="status-icon iSuperlike"
        name="star"
      ></ion-icon>
      <ion-icon
        #laterStatus
        class="status-icon iLater"
        name="hourglass-outline"
      ></ion-icon>
    </div>
  }

  @if (profile && profile.profile_type === 'profile') {
    <ion-card-header class="user-action ion-no-padding">
      <ion-grid class="ion-no-padding">
        <ion-row class="user-action-buttons ion-text-center">
          <ion-col class="user-action-buttons-wrapper ion-no-padding">
            <ion-fab-button
              color="white"
              class="dislike btn--fs"
              (click)="dislike()"
              >
              <ion-icon name="close"></ion-icon>
            </ion-fab-button>
            <ion-fab-button
              color="white"
              class="superlike btn--fs"
              (click)="superlike()"
              >
              <ion-icon name="star"></ion-icon>
            </ion-fab-button>
            <ion-fab-button color="white" class="like btn--fs" (click)="like()">
              <ion-icon name="heart"></ion-icon>
            </ion-fab-button>
          </ion-col>
        </ion-row>
        <ion-row class="user-info ion-text-center" (click)="openProfile()">
          <ion-col class="ion-no-padding">
            <h2 class="user-info-username-age">
              {{ profile?.first_name || profile?.username }}, {{ profile?.age }}
              <ion-icon
                class="btn-info"
                name="information-circle-outline"
              ></ion-icon>
            </h2>
            @if (profile) {
              <ion-text class="user-info-location">
                {{ profile.city }},
                {{ profile.country === "US" ? profile.state : profile.country }}
              </ion-text>
            }
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card-header>
  }
  <!-- card-base -->
</ion-card>
