import { Component, Input } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { ActionSheetButton, ActionSheetController } from "@ionic/angular";
import { ProfileService } from "src/app/services/profile.service";
import { ProfileUser } from "src/app/schemas/profile-user";
import { ProfileBase } from "src/app/schemas/profile-base";
import { DialogHelperService } from "src/app/services/dialog-helper.service";
import { firstValueFrom } from "rxjs";
import { AlertController } from "@ionic/angular";

@Component({
  selector: "app-profile-menu",
  templateUrl: "./profile-menu.component.html",
  styleUrls: ["./profile-menu.component.css"],
  standalone: false,
})
export class ProfileMenuComponent {
  @Input() profileData: ProfileUser.type;

  private profileReport: { reason: string; comment: string } = {
    reason: "block",
    comment: "",
  };

  constructor(
    public profileService: ProfileService,
    private translateService: TranslateService,
    private actionSheetController: ActionSheetController,
    private dialog: DialogHelperService,
    private alertController: AlertController,
  ) {}

  /**
   * Open profile menu ...
   */
  public async openProfileMenu() {
    let profile = this.profileData;
    let buttons: Array<ActionSheetButton> = [];
    buttons.push({
      text: this.translateService.instant("report"),
      role: "destructive",
      handler: async () => await this.reportProfile(),
    });

    if (this.profileService.listBlocked.haveUsername(profile.username)) {
      buttons.push({
        text: this.translateService.instant("unblock"),
        role: "destructive",
        handler: () => this.unblockProfile(profile.username),
      });
    } else {
      buttons.push({
        text: this.translateService.instant("block"),
        role: "destructive",
        handler: () => this.blockProfile(profile),
      });
    }

    buttons.push({
      text: this.translateService.instant("cancel"),
      role: "cancel",
    });

    const actionSheet = await this.actionSheetController.create({
      header: this.translateService.instant("profile_actions"),
      buttons: buttons,
    });

    await actionSheet.present();
  }

  private blockProfile(profile: ProfileBase.type): void {
    this.dialog.confirm("block", "sure_to_block", () => {
      firstValueFrom(this.profileService.block(profile))
        .then(() => {
          this.dialog.showToast("", "blocking_profile");
        })
        .catch((e) => {
          this.dialog.showToastBaseOnErrorResponse(e);
        });
    });
  }

  private unblockProfile(username: string): void {
    this.dialog.confirm("unblock", "sure_to_unblock", () => {
      firstValueFrom(this.profileService.unblock(username))
        .then(() => {
          this.dialog.showToast("", "unblocking_profile");
        })
        .catch((e) => {
          this.dialog.showToastBaseOnErrorResponse(e);
        });
    });
  }

  private reportProfile(): Promise<void> {
    return new Promise(async (resolve) => {
      resolve();
      // display prompt dialog (abuse/spam)
      await this.displayReportProfilePromptDialog();
      // display alert with textarea for comment
      await this.displayReportProfileCommentDialog();
      // display confirm dialog
      this.displayReportProfileConfirmDialog();
    });
  }

  private async displayReportProfilePromptDialog(): Promise<void> {
    return new Promise(async (resolve) => {
      const alert = await this.alertController.create({
        header:
          this.translateService.instant("report") +
          " " +
          this.profileData.username,
        buttons: [
          {
            text: this.translateService.instant("ok"),
            role: "confirm",
            handler: async (res) => {
              this.profileReport.reason = res ?? this.profileReport.reason;
              resolve();
            },
          },
        ],
        inputs: [
          {
            label: "Spam",
            type: "radio",
            value: "spam",
          },
          {
            label: "Abuse",
            type: "radio",
            value: "abuse",
          },
        ],
      });

      await alert.present();
    });
  }

  private displayReportProfileCommentDialog(): Promise<void> {
    return new Promise(async (resolve) => {
      const alert = await this.alertController.create({
        header:
          this.translateService.instant("report") +
          " " +
          this.profileData.username,
        buttons: [
          {
            text: this.translateService.instant("ok"),
            role: "confirm",
            handler: (data) => {
              this.profileReport.comment =
                data[0] ?? "*profile have been reported*";
              resolve();
            },
          },
        ],
        inputs: [
          {
            type: "textarea",
            placeholder: this.translateService.instant("enter_comment"),
          },
        ],
      });

      await alert.present();
    });
  }

  private displayReportProfileConfirmDialog(): void {
    this.dialog.confirm("report", "sure_to_report", () => {
      firstValueFrom(
        this.profileService.report({
          comment: this.profileReport.comment,
          reason: this.profileReport.reason,
          user_id: this.profileData.user_id,
          username: this.profileData.username,
        }),
      )
        .then(() => this.dialog.showToast("", "report_in_progress", "loading"))
        .catch((err) => this.dialog.showToastBaseOnErrorResponse(err));
    });
  }
}
