import { NgModule } from "@angular/core";
import { WpgFormComponent } from "./wpg-form";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { TranslateModule } from "@ngx-translate/core";
import AutoFocusModule from "src/app/auto-focus.module";
import { CapitalizePipe } from "../../pipes/capitalize.pipe";

@NgModule({
  imports: [
    IonicModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    AutoFocusModule,
  ],
  declarations: [WpgFormComponent, CapitalizePipe],
  exports: [WpgFormComponent],
})
export class WpgFormComponentModule {}
