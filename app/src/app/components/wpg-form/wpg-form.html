@if (show && !loading) {
  <div>
    <form [formGroup]="fg">
      <!-- CHECK - SELECT -->
      @if (data.answers && (data.type === 'check' || data.type === 'select')) {
        <ion-grid
          class="ion-text-center"
          >
          <ion-row>
            <ion-col [class.hidden]="!data.question_name">
              <h4>{{ data.question_name }}</h4>
            </ion-col>
          </ion-row>
          <ion-row class="ion-justify-content-center">
            <ion-col>
              @for (answer of data.answers; track answer) {
                <ion-button
                  color="{{ answer['checked'] ? 'primary' : 'medium' }}"
                  fill="{{ answer['checked'] ? 'solid' : 'outline' }}"
                  [class.checked]="answer['checked']"
                  (click)="toggleCheckbox(data._key, answer, data);"
                  size="small"
                  >{{ answer['data'] }}
                </ion-button>
              }
            </ion-col>
          </ion-row>
        </ion-grid>
      }
      <!-- CHAR -->
      @if ((!data.answers || !data.answers.length) && data.type === 'char') {
        <ion-grid
          >
          <ion-row>
            <ion-col size="12" [class.hidden]="!data.question_name">
              <h4>{{ data.question_name }}</h4>
            </ion-col>
            <ion-col>
              <ion-item class="ion-no-padding ion-no-margin">
                <ion-input
                  [name]="data._key"
                  [ngModel]="data['values'] | capitalize:'sentenceCase':capitalizeFirstLetter"
                  (ngModelChange)="data['values']=$event"
                  [formControlName]="data._key"
                  [min]="data.value_min"
                  [minlength]="data.value_min"
                  [max]="data.value_max ? data.value_max : null"
                  [maxlength]="data.value_max ? data.value_max : null"
                  (ionInput)="onChange(data)"
                  appAutoFocus
                ></ion-input>
              </ion-item>
            </ion-col>
          </ion-row>
        </ion-grid>
      }
      <!-- TEXT -->
      @if ((!data.answers || !data.answers.length) && data.type === 'text') {
        <ion-grid
          >
          <ion-row>
            <ion-col size="12" [class.hidden]="!data.question_name">
              <h4>{{ data.question_name }}</h4>
            </ion-col>
            <ion-col>
              <ion-item class="ion-no-padding ion-no-margin">
                <ion-textarea
                  label="{{ 'write_message_here' | translate }}"
                  labelPlacement="floating"
                  [value]="data['values']"
                  [formControlName]="data._key"
                  [minlength]="data.value_min"
                  [attr.min]="data.value_min"
                  [attr.max]="data.value_max ? data.value_max : null"
                  rows="4"
                  (ionInput)="onChange(data)"
                  appAutoFocus
                ></ion-textarea>
              </ion-item>
            </ion-col>
          </ion-row>
        </ion-grid>
      }
      <!-- TEXT -->
      @if (data.type === 'slider') {
        <ion-grid>
          <ion-row>
            <ion-col [class.hidden]="!data.question_name">
              <h4>{{ data.question_name }}</h4>
            </ion-col>
          </ion-row>
          <ion-row>
            <!--
            <ion-col size="12">
              <ion-text color="medium"
                >{{ ( data['callback'] || 'set_range' ) | translate }}</ion-text
                >
              </ion-col>
              -->
              <!--
              will hide this for now because added pin on range
              <ion-col size="12" class="ion-no-padding">
                <ion-text color="medium"
                  ><small>
                  {{ data['sliderValue']['lower'] }} - {{
                  data['sliderValue']['upper'] }}
                </small></ion-text
                >
              </ion-col>
              -->
              <ion-col size="12">
                <ion-item lines="none" class="text-field age-range">
                  <ion-label slot="start"
                    >{{ data['sliderValue']['lower'] }}</ion-label
                    >
                    <ion-label slot="end">{{ data['sliderValue']['upper'] }}</ion-label>
                    <ion-range
                      label=""
                      id="dual-range"
                      color="dark"
                      [dualKnobs]="isMultiselect()"
                      [min]="data.value_min"
                      [max]="data.value_max"
                      step="1"
                      [(ngModel)]="data['slider']"
                      [formControlName]="data._key"
                      (ionInput)="updateSelected($event)"
                      [snaps]="hasAnswers()"
                    ></ion-range>
                  </ion-item>
                </ion-col>
              </ion-row>
            </ion-grid>
          }
        </form>
      </div>
    }
