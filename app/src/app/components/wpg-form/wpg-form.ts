import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  Output,
} from "@angular/core";
import { Debug } from "../../helpers/debug";
import { DialogHelperService } from "../../services/dialog-helper.service";
import { WelcomePageData } from "src/app/pages/welcome/welcome-page-data.class";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import _ from "lodash";

@Component({
  selector: "wpg-form",
  templateUrl: "wpg-form.html",
  styleUrls: ["wpg-form.scss"],
  standalone: false,
})
export class WpgFormComponent implements AfterViewInit {
  @Input()
  data: WelcomePageData = null;

  @Input()
  show: boolean = false;

  @Input()
  capitalizeFirstLetter: boolean = false;

  @Output()
  toggleGroup: EventEmitter<WelcomePageData> =
    new EventEmitter<WelcomePageData>();

  @Output()
  onWpgChange: EventEmitter<WelcomePageData> =
    new EventEmitter<WelcomePageData>();

  /**
   * FormGroup.
   */
  public fg: FormGroup;

  /**
   * Loading in progress.
   *
   * @type {boolean}
   */
  public loading: boolean = true;

  /**
   * Did value changed?
   *
   * @type {boolean}
   */
  private _didValueChange: boolean = false;

  /**
   * Constructor.
   */
  constructor(
    public debug: Debug,
    public fBuilder: FormBuilder,
    public dlg: DialogHelperService,
  ) {
    this.fg = fBuilder.group({});
  }

  /**
   * AfterViewInit.
   */
  ngAfterViewInit() {
    this.normalize()
      .then(() => this.prepareForm())
      .then(() => this.prepareData())
      .then(() => this.checkForSliderType())
      .then(() => (this.loading = false))
      .catch((err) => this.debug.le("error prepareForm", err))
      .then(() => (this._didValueChange = false));
  }

  /**
   * Did value changed?
   *
   * @return {boolean}
   */
  public didValueChanged(): boolean {
    return this._didValueChange;
  }

  /**
   * on change
   */
  public onChange(_el: WelcomePageData): void {
    // Set values [ TEXT, CHAR ]
    if (_el.type !== "check" && _el.type !== "select") {
      this.data.values = this.fg.value[this.data._key];
    }

    this.onWpgChange.emit(_el);
    this._didValueChange = true;
  }

  /**
   * Toggle checkbox.
   *
   * @param {string} name
   * @param answer
   * @param {any} q
   */
  public toggleCheckbox(name: string, answer: any, q: any): void {
    if (!this.isMultiselect()) {
      this.data.answers.forEach(
        (val: any) => (val.checked = val.key == answer.key),
      );

      this.data.values = this.data.answers.some(
        (val: any) => val.key == answer.key,
      )
        ? answer.key
        : "";
    } else {
      answer.checked = !answer.checked;

      if (answer.checked) {
        if (this.inValues(answer.key)) return;

        this.data.values.push(answer.key);
      } else {
        if (this.inValues(answer.key)) {
          this.data.values = this.data.values.filter(
            (val: any) => val != answer.key,
          );
        }
      }
    }

    this.onChange(q);
  }

  /**
   * Normalize answers.
   *
   * from {123: answer1} to [{checked:false, key:123, data: asdf1}]
   */
  private async normalize(): Promise<void> {
    if (!this.hasAnswers()) return;

    if (Array.isArray(this.data.answers)) {
      this.data.answers.forEach((answer) => {
        answer["checked"] = Array.isArray(this.data.values)
          ? this.inValues(answer["key"])
          : this.data.values == answer["key"];
      });

      return;
    }

    let tmp: any[] = [];

    Object.keys(this.data.answers).forEach((aKey) => {
      let val: any = {};

      val["checked"] = Array.isArray(this.data.values)
        ? this.inValues(aKey)
        : this.data.values == aKey;

      val["key"] = aKey;
      val["data"] = this.data.answers[aKey];
      tmp.push(val);
    });

    this.data.answers = tmp;
  }

  /**
   * Prepare form.
   *
   * @return {Promise<void>}
   */
  private async prepareForm(): Promise<void> {
    if (!this.data) return;

    return this.fg.addControl(this.data._key, new FormControl());
  }

  /**
   * Prepare data.
   *
   * @return {Promise<void>}
   */
  private async prepareData(): Promise<void> {
    if (!this.data) return;

    if (this.isMultiselect()) {
      if (this.data.values === undefined) this.data.values = [];
    } else {
      if (!this.data.answers) return;
      if (this.data.values === undefined) this.data.values = "";
    }
  }

  /**
   * Prepare data (slider) type.
   *
   * @return {Promise<void>}
   */
  private async checkForSliderType(): Promise<void> {
    if (this.data.type !== "slider") return;

    this.data["slider"] = [];
    this.data["sliderValue"] = [];

    this.setSliderMinValue();
    this.setSliderMaxValue();

    this.setSliderDefaultValues();
  }

  /**
   * Set slider default values.
   */
  private setSliderDefaultValues(): void {
    if (this.isMultiselect()) {
      if (!this.hasAnswers()) {
        if (this.data["defaults"].length) {
          this.data["slider"]["lower"] = this.data["defaults"][0];
          this.data["sliderValue"]["lower"] = this.data["defaults"][0];
          this.data["slider"]["upper"] = this.data["defaults"][1];
          this.data["sliderValue"]["upper"] = this.data["defaults"][1];
        }
      }
    }
  }

  /**
   * Set min value.
   */
  private setSliderMinValue(): void {
    if (this.hasAnswers()) {
      let index = [];
      this.data["answers"].forEach((answer, i) => {
        if (answer["checked"]) index.push(i);
      });

      let checkedAnswers = this.data["answers"].filter(
        (answer) => answer["checked"],
      );
      if (checkedAnswers.length) {
        this.data["slider"]["lower"] = index[0];
        this.data["sliderValue"]["lower"] = checkedAnswers[0]["data"];
      } else {
        this.data["slider"]["lower"] = 0;
        this.data["sliderValue"]["lower"] = this.data.answers[0]["data"];
      }
    } else {
      this.data["slider"]["lower"] = this.data.value_min;
      this.data["sliderValue"]["lower"] = this.data.value_min;
    }
  }

  /**
   * Set max value.
   */
  private setSliderMaxValue(): void {
    if (this.isMultiselect()) {
      if (this.hasAnswers()) {
        this.data.value_max = this.data.answers.length - 1;

        let index = [];
        this.data["answers"].forEach((answer, i) => {
          if (answer["checked"]) index.push(i);
        });

        let checkedAnswers = this.data["answers"].filter(
          (answer) => answer["checked"],
        );

        if (checkedAnswers.length) {
          if (checkedAnswers.length > 1) {
            this.data["slider"]["upper"] = index[1];
            this.data["sliderValue"]["upper"] = checkedAnswers[1]["data"];
          } else {
            this.data["slider"]["upper"] = index[0];
            this.data["sliderValue"]["upper"] = checkedAnswers[0]["data"];
          }
        } else {
          this.data["slider"]["upper"] = this.data.answers.length - 1;
          this.data["sliderValue"]["upper"] =
            this.data.answers[this.data.answers.length - 1]["data"];
        }
      } else {
        this.data["slider"]["upper"] = this.data.value_max;
        this.data["sliderValue"]["upper"] = this.data.value_max;
      }
    } else {
      if (this.data.value_max <= 0 && this.hasAnswers()) {
        this.data.value_max = this.data.answers.length - 1;
      }
    }
  }

  /**
   * Is multiselect?
   */
  public isMultiselect(): boolean {
    if (this.data.multiselect !== undefined) {
      return this.data.multiselect == 1;
    }

    return false;
  }

  /**
   * Values contains data?
   *
   * @param data any
   */
  private inValues(data: any): boolean {
    return this.data.values.some((val: any) => val == data);
  }

  /**
   * Has answers?
   */
  public hasAnswers(): boolean {
    // if undefined
    if (this.data.answers === undefined) return false;
    // if empty []
    if (Array.isArray(this.data.answers) && !this.data.answers.length)
      return false;
    // if empty {}
    return !!Object.keys(this.data.answers).length;
  }

  /**
   * Update selected.
   *
   * @param e
   */
  public updateSelected(e): void {
    if (this.hasAnswers()) {
      this.data.values = this.isMultiselect()
        ? [
            this.data.answers[e.value.lower]["key"],
            this.data.answers[e.value.upper]["key"],
          ]
        : [this.data.answers[e.value]["key"]];
      if (this.isMultiselect()) {
        this.data["sliderValue"]["lower"] =
          this.data.answers[e.value.lower]["data"];
        this.data["sliderValue"]["upper"] =
          this.data.answers[e.value.upper]["data"];
      } else {
        this.data["sliderValue"]["lower"] = this.data.answers[e.value]["data"];
      }
    } else {
      if (this.isMultiselect()) {
        this.data["sliderValue"]["lower"] = e.detail.value.lower;
        this.data["sliderValue"]["upper"] = e.detail.value.upper;
      } else {
        this.data["sliderValue"]["lower"] = e.value;
      }

      this.data.values = this.isMultiselect()
        ? [e.detail.value.lower, e.detail.value.upper]
        : [e.value];
    }
  }
}
