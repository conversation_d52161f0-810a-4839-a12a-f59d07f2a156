import { CommonModule } from "@angular/common";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { HappyHourComponent } from "./happy-hour.component";
import { TranslateModule } from "@ngx-translate/core";
import { CountdownComponentModule } from "../countdown/countdown.component.module";

@NgModule({
  imports: [CommonModule, TranslateModule, CountdownComponentModule],
  declarations: [HappyHourComponent],
  exports: [HappyHourComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class HappyHourComponentModule {}
