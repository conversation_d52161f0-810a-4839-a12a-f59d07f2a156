@if (hhService.showHH() && hhService.data) {
  <ion-grid
    no-margin
    class="hh-top-container gl-box-shadow ion-margin ion-no-padding"
    >
    <ion-row>
      <ion-col class="hh-icon-col ion-no-padding ion-padding" size="auto">
        <div class="hh-icon">
          <ion-icon name="alarm"></ion-icon>
        </div>
      </ion-col>
      <ion-col
        class="hh-text-container ion-no-padding ion-padding-vertical ion-padding-end"
        >
        <div class="hh-top-text-container">
          <h4>{{ "happy_hour" | translate }}</h4>
          <span>{{ "send_message_for_free" | translate }}</span>
        </div>
      </ion-col>
      <ion-col class="hh-time-col ion-no-padding ion-padding" size="auto">
        <div class="hh-top-time-container">
          @if (hhService.data.start > 0) {
            <span class="hh-time-title">{{
              "starts_in" | translate
            }}</span>
          }
          @if (hhService.data.start == 0 && hhService.data.end > 0) {
            <span
              class="hh-time-title"
              >{{ "ends_in" | translate }}</span
              >
            }
            <app-countdown [time]="hhService.displayTime"></app-countdown>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  }
