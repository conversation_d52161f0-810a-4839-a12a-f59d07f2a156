.hh-top-container {
  background: #64a52f;
  background-image:
    linear-gradient(
      to right,
      hsl(101.12, 37.87%, 46.08%) 0%,
      hsl(101.05, 38.04%, 45.91%) 8.1%,
      hsl(100.83, 38.51%, 45.43%) 15.5%,
      hsl(100.48, 39.29%, 44.69%) 22.5%,
      hsl(100.01, 40.39%, 43.7%) 29%,
      hsl(99.41, 41.83%, 42.5%) 35.3%,
      hsl(98.7, 43.66%, 41.11%) 41.2%,
      hsl(97.86, 45.95%, 39.57%) 47.1%,
      hsl(96.91, 48.76%, 37.91%) 52.9%,
      hsl(95.84, 52.21%, 36.15%) 58.8%,
      hsl(94.66, 56.42%, 34.33%) 64.7%,
      hsl(93.38, 61.5%, 32.49%) 71%,
      hsl(92.01, 67.56%, 30.68%) 77.5%,
      hsl(90.61, 74.53%, 28.97%) 84.5%,
      hsl(89.34, 81.61%, 27.53%) 91.9%,
      hsl(88.72, 85.4%, 26.86%) 100%
    );
  border-radius: 8px;
  box-shadow: 0 6px 12px rgba(255,255,255,.24);

  h4 {
    color: var(--ion-color-charcoal);
    text-shadow: 2px 2px 0 rgb(255, 255, 255, 0.1);
    line-height: 1.1;
    margin: 0;
  }

  span {
    font-size: 14px;
  }

  .hh-icon-col {
    width: 44px;

    .hh-icon {
      background-color: #FFF;
      width: 44px;
      height: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      animation: lullaby 4s infinite;
    }

    ion-icon {
      font-size: 28px;
    }
  }

  .hh-text-container {
    color: var(--ion-color-white);
  }

  .hh-time-col {
    &:before {
      background-image:
        linear-gradient(
          53deg,
          hsl(0, 0%, 100%) 0%,
          hsla(0, 0%, 100%, 0.987) 8.1%,
          hsla(0, 0%, 100%, 0.951) 15.5%,
          hsla(0, 0%, 100%, 0.896) 22.5%,
          hsla(0, 0%, 100%, 0.825) 29%,
          hsla(0, 0%, 100%, 0.741) 35.3%,
          hsla(0, 0%, 100%, 0.648) 41.2%,
          hsla(0, 0%, 100%, 0.55) 47.1%,
          hsla(0, 0%, 100%, 0.45) 52.9%,
          hsla(0, 0%, 100%, 0.352) 58.8%,
          hsla(0, 0%, 100%, 0.259) 64.7%,
          hsla(0, 0%, 100%, 0.175) 71%,
          hsla(0, 0%, 100%, 0.104) 77.5%,
          hsla(0, 0%, 100%, 0.049) 84.5%,
          hsla(0, 0%, 100%, 0.013) 91.9%,
          hsla(0, 0%, 100%, 0) 100%
        );
      width: 200px;
      height: 200px;
      border-radius: 48% 52% 58% 42% / 53% 73% 27% 47%;
      position: absolute;
      content: "";
      top: -124%;
      right: -102px;
      opacity: 0.2;
      transform: rotate(45deg);
    }
  }

  .hh-top-time-container {
    color: var(--ion-color-white);
    position: relative;
    text-align: center;
  }

  .hh-time-title {
    color: var(--ion-color-charcoal);
    font-weight: 500;
    text-shadow: 2px 2px 0 rgb(255, 255, 255, 0.1);
  }
}

@keyframes lullaby {
  0% {transform: rotate(-20deg);}
  50% {transform: rotate(20deg);}
  100% {transform: rotate(-20deg);}
}
