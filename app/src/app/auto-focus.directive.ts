import { Directive, ElementRef, Ng<PERSON><PERSON> } from "@angular/core";
import { firstValueFrom, timer } from "rxjs";

@Directive({
  selector: "[appAutoFocus]",
  standalone: false,
})
export class AutoFocusDirective {
  private checked = false;

  /**
   * Constructor.
   */
  constructor(
    private elementRef: ElementRef,
    private zone: NgZone,
  ) {}

  /**
   * ngAfterViewInit
   */
  ngAfterViewInit() {
    this.setFocus();
  }

  /**
   * Set el focus.
   */
  private setFocus(): void {
    // invoke shadow dom
    setTimeout(async () => {
      let element =
        this.elementRef.nativeElement.getElementsByClassName("native-input")[0];

      if (!element) {
        element =
          this.elementRef.nativeElement.getElementsByClassName(
            "native-textarea",
          )[0];
      }

      if (!element && !this.checked) {
        this.checked = true;
        await firstValueFrom(timer(2000));
        return this.setFocus();
      }

      element.focus();

      this.zone.run(() => {});
    }, 700);
  }
}
