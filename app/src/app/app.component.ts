import { Component, ViewChild } from "@angular/core";
import { IonRouterOutlet, MenuController, Platform } from "@ionic/angular";
import { Router } from "@angular/router";
import { LoaderService } from "./services/loader.service";
import { Debug } from "./helpers/debug";
import { SessionService } from "./services/session.service";
import { StorageService } from "./services/storage.service";
import { AuthService } from "./services/auth.service";
import { LanguageService } from "./services/language.service";
import { AccountService } from "./services/account.service";
import { environment as Env } from "../environments/environment";
import { DeviceInfoService } from "./services/device-info.service";
import { DialogHelperService } from "./services/dialog-helper.service";
import { BootService } from "./services/boot.service";
import { QueueService } from "./services/queue.service";
import { SplashScreen } from "@capacitor/splash-screen";
import { MenuHelperService } from "./services/menu-helper.service";
import { ApiService } from "./services/api/api.service";
import { AnalyticsService } from "./services/analytics.service";
import { AnalyticsHelperService } from "./services/analytics-helper.service";
import { Title } from "@angular/platform-browser";
import { AppsflyerHelperService } from "./services/appsflyer-helper.service";
import { PurchaseService } from "./services/purchase.service";
import { UnreadCountService } from "./services/unread-count.service";
import { ApiSpyService } from "./services/api-spy.service";
import { PromoService } from "./services/promo.service";
import { DeeplinkService } from "./services/deeplink.service";
import { PushNotificationService } from "./services/push-notification.service";
import { TrackingService } from "./services/tracking.service";
import { ProfileService } from "./services/profile.service";
import { register } from "swiper/element/bundle";
import { UtilsService } from "./services/utils.service";

register();
@Component({
  selector: "app-root",
  templateUrl: "app.component.html",
  styleUrls: ["app.component.scss"],
  standalone: false,
})
export class AppComponent {
  @ViewChild(IonRouterOutlet)
  public set ionRouterOutlet(data: IonRouterOutlet) {
    // set ion router outlet here
    this.menuHelper.routerOutlet = data;
  }

  public showDebugButton: boolean = true;

  constructor(
    private platform: Platform,
    public router: Router,
    public storage: StorageService,
    public menu: MenuController,
    public loader: LoaderService,
    private debug: Debug,
    public sessionService: SessionService,
    public auth: AuthService,
    private language: LanguageService,
    public accountService: AccountService,
    private dialog: DialogHelperService,
    private deviceInfo: DeviceInfoService,
    private bootService: BootService,
    private queueService: QueueService,
    public menuHelper: MenuHelperService,
    private api: ApiService,
    private analytics: AnalyticsService,
    private analyticsHelper: AnalyticsHelperService,
    private title: Title,
    private appsflyer: AppsflyerHelperService,
    private purchaseService: PurchaseService,
    public unreadCountService: UnreadCountService,
    private apiSpyService: ApiSpyService,
    private promoService: PromoService,
    private deeplinkService: DeeplinkService,
    private pushNotificationService: PushNotificationService,
    private trackingService: TrackingService,
    public profileService: ProfileService,
    private utilsService: UtilsService,
  ) {
    // initialize app
    this.initializeApp();
  }

  /**
   *  Is curren user premium
   * @returns bool
   */
  public isPremium() {
    return this.accountService?.account?.is_premium;
  }

  public openUpgrade() {
    this.closeMenu();
    this.purchaseService.openUpgradeDialog((error) => {
      this.dialog.showToastBaseOnErrorResponse(error, null, false);
    });
  }

  /**
   * analytics event listener
   * @param event
   * @param params
   */
  public _eventLogListener(event, params) {
    // send upgrade errors to server

    if (event == "upgrade_error") {
      this.analytics.addMarker("upgrade_error", params);

      var _logData = {
        event: event,
        params: params,
        markers: this.analytics.getMarkers(),
      };

      this.utilsService.log(_logData);
      this.analytics.clearMarkers();
    }
  }

  public initializeApp() {
    // splash show
    this.dialog.showSplash();

    setTimeout(() => {
      SplashScreen.hide();
    }, 100);

    // ready?
    this.platform.ready().then(async () => {
      // init api spy
      this.apiSpyService.init();

      // add analytics overrider
      this.analytics.logEventCallback = (event, params) => {
        this._eventLogListener(event, params);
      };

      // set page title
      this.title.setTitle(Env.APP_NAME);

      // see if we need to loop the animation
      if (!Env.DO_LOOP_SPLASH) {
        window.document
          .getElementById("lottie_player_el")
          .removeAttribute("loop");
      }

      // init stuff
      this.menuHelper.init("main");

      // init main services

      await this.storage.init();
      await this.sessionService.quickFetchToken();
      await this.loader.init();
      await this.deviceInfo.init();
      await this.queueService.init();

      await this.utilsService.getConfig();

      // initialize lang ( only if we're not running e2e tests )
      if (Env.ENVIRONMENT !== "testing") {
        await this.language.initialize();
      }

      // initialize stuff that's independent from boot sequencer and session service
      this.accountService.getConsentRequirements().subscribe();

      // initialize device info

      if (!Env.IS_DEBUG) {
        this.showDebugButton = false;
      }

      this.debug.l("initialize analytics class");

      // initialize analytics
      await this.analytics.init();

      this.debug.l("initialize appsflyer class");
      // init appsflyer
      await this.appsflyer.init();

      this.debug.l("initialize tracking class");
      // initialize tracking service
      await this.trackingService.init();

      this.debug.l("initialize analytics helper class");
      // init analytic helper
      await this.analyticsHelper.init();

      this.debug.l("tracking inid done");

      // pre-register push notification service for push session handler
      await this.pushNotificationService.preInit();

      // pre init done
      this.pushNotificationService.setPreInitDone();

      // see if we have session data saved, and restore it if possible

      try {
        // @todo test if we get bad session, can we resoter
        // @todo check if we get bad session, and we cannot restore ( that data is currupt as well )

        await this.sessionService.initialSessionRestore();

        // wait for main stuff with boot init
        await this.bootService.startInitialBoot();

        // test instant open
      } catch (error) {
        this.analytics.addMarker("session restore error");

        // we cannot restore session - goto welcome page
        this.debug.le("Session error", error);

        // initialize deeplink for login page
        this.deeplinkService.init(false);

        // callback when we have no session
        this.deeplinkService.CallbackNoSession = async (data) => {
          this.deeplinkSessionRestoreIfNoSession(data);
        };

        this.pushNotificationService.CallbackNoSession = async (data) => {
          this.pushHashSessionRestoreIfNoSession(data);
        };

        if (!this.api.wasNetworkError) {
          this.debug.l("was no network error, deleting session");

          // delete session only if there was no network error
          await this.sessionService.deleteSession();
          await this.router.navigate(["welcome"], { replaceUrl: true });
        }

        this.dialog.hideSplash(200);
      }
    });
  }

  /**
   * Callback to handle no session
   * @param hash
   */
  private async pushHashSessionRestoreIfNoSession(hash) {
    if (!this.pushNotificationService.sessionHash) {
      // no hash set
      return;
    }

    try {
      // restore session?
      await this.sessionService.initialSessionRestore();

      // wait for main stuff with boot init
      await this.bootService.startInitialBoot();
    } catch (error) {
      this.analytics.addMarker("session restore error push");

      // we cannot restore session - goto welcome page
      this.debug.le("Session restore error from push", error);
    }
  }

  /**
   * If we have no session, we try to restore sess from
   * deeplink url's h= param
   * @param data
   */
  private async deeplinkSessionRestoreIfNoSession(data) {
    // we need to try and restore the session if we have h= param
    let _url = data?.url || "";

    console.log("URL!");

    if (_url.indexOf("&h=") > 0 || _url.indexOf("?h=") > 0) {
      console.log("!", _url);
      // if we have session defined
      try {
        // restore session?
        await this.sessionService.initialSessionRestore(_url);

        // wait for main stuff with boot init
        await this.bootService.startInitialBoot();

        // set deeplink data
        this.deeplinkService.setLinkData(_url).handle();
      } catch (error) {
        this.analytics.addMarker("session restore error deeplink");

        // we cannot restore session - goto welcome page
        this.debug.le("Session error", error);
      }
    }
  }

  /**
   * Open menu.
   * @param menuId
   * @todo [DEBUG]
   */
  async openMenu(menuId: string) {
    // await this.toggleMenus();
    await this.menu.open(menuId);
  }

  /**
   * Toggle menu.
   * @todo [DEBUG]
   */
  private async toggleMenus() {
    if (await this.menu.isEnabled("custom")) {
      await this.menu.enable(false, "custom");
      await this.menu.enable(true, "main");
    } else {
      await this.menu.enable(true, "custom");
      await this.menu.enable(false, "main");
    }
  }

  /**
   * Close menu.
   * @param menuId
   * @todo [DEBUG]
   */
  public async closeMenu(menuId: string = "main") {
    if (await this.menu.isOpen(menuId)) {
      await this.menu.close(menuId);
    }
  }

  public logout() {
    this.sessionService.logoutAndRestart();
  }

  public navigateTo(path: string) {
    this.closeMenu("custom");
    return this.router.navigate([path]);
  }

  public openMyLists() {
    this.closeMenu();

    if (this.accountService.account.is_premium) {
      this.navigateTo("app/my-lists/viewed-me");
    } else {
      this.purchaseService.openViewedMeList((error) => {
        this.dialog.showToastBaseOnErrorResponse(error, null, false);
      });
    }
  }
}
