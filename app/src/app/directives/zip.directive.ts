import { Directive, ElementRef, HostListener, Renderer2 } from "@angular/core";

@Directive({
  selector: "[inputZip]",
  standalone: false,
})
export class ZipDirective {
  private get _type(): string {
    return this._sourceElementRef.nativeElement.type || "text";
  }

  /**
   * Keep the value of input element in a cache.
   *
   * @type {string}
   * @private
   */
  private _value: string;

  // Source services to modify elements.
  private _sourceRenderer: Renderer2;
  private _sourceElementRef: ElementRef;

  /**
   * Updates the value on the input event.
   */
  @HostListener("input", ["$event.type", "$event.target.value"])
  onInput(event: string, value: string): void {
    this.updateValue(event, value);
  }

  constructor(renderer: Renderer2, elementRef: ElementRef) {
    this._sourceRenderer = renderer;
    this._sourceElementRef = elementRef;
  }

  /**
   * Writes a new value to the element based on the type of input element.
   *
   * @param {any} value - new value
   */
  public writeValue(value: any): void {
    this._value = value === "" ? "" : value || null;

    this._sourceRenderer.setProperty(
      this._sourceElementRef.nativeElement,
      "value",
      this._value,
    );

    if (this._type !== "text") {
      this._sourceRenderer.setAttribute(
        this._sourceElementRef.nativeElement,
        "value",
        this._value,
      );
    }
  }

  /**
   * Remove chars not used in zip from the input box
   *
   * @param {string} value - input value
   * @param {string} event - input event
   */
  private updateValue(event: string, value: string): void {
    const previous = this._value;

    // only valid characters allowed
    value = value.replace(/[^0-9a-zA-Z\-\ ]/gm, "");

    // write value to the element.
    this.writeValue(value);
  }
}
