import { Directive, ElementRef, EventEmitter, Output } from "@angular/core";
import { G<PERSON><PERSON>, GestureController, IonImg } from "@ionic/angular";

@Directive({
  selector: "[longPress]",
  standalone: false,
})
export class LongPressDirective {
  @Output() onLongPressEnd: EventEmitter<any> = new EventEmitter();

  public longPressActive: boolean = false;
  private timer: number = 0;
  private end: number = 400;

  constructor(
    private el: ElementRef,
    private gestureCtrl: GestureController,
  ) {
    this.setupGesture(this.el);
  }

  /**
   * Setup gesture.
   *
   * @param el {ElementRef}
   */
  private setupGesture(el: ElementRef): void {
    const gesture: Gesture = this.gestureCtrl.create(
      {
        el: el.nativeElement,
        threshold: 0,
        blurOnStart: true,
        gestureName: "long-press",
        onStart: (ev) => {
          this.longPressActive = true;
          this.handle();
        },
        onEnd: (ev) => {
          this.longPressActive = false;
          this.timer = 0;
        },
      },
      true,
    );
    gesture.enable(true);
  }

  /**
   * Handle timer.
   */
  private handle(): void {
    setTimeout(() => {
      if (this.timer >= this.end) {
        this.setActive(this.el);
        return;
      }

      if (this.longPressActive) {
        this.timer += 100;
        this.handle();
      }
    }, 100);
  }

  /**
   * Emit event.
   *
   * @param img {ElementRef}
   */
  private setActive(img: ElementRef): void {
    this.onLongPressEnd.emit(img.nativeElement.id);
  }
}
