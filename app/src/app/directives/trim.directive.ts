import {
  Directive,
  ElementRef,
  HostListener,
  Input,
  Renderer2,
} from "@angular/core";

@Directive({
  selector: "[inputTrim]",
  standalone: false,
})
export class TrimDirective {
  private get _type(): string {
    return this._sourceElementRef.nativeElement.type || "text";
  }

  // Get a value of the trim attribute if it was set.
  @Input() trim: string;

  /**
   * Keep the value of input element in a cache.
   *
   * @type {string}
   * @private
   */
  private _value: string;

  // Source services to modify elements.
  private _sourceRenderer: Renderer2;
  private _sourceElementRef: ElementRef;

  /**
   * Updates the value on the input event.
   */
  @HostListener("input", ["$event.type", "$event.target.value"])
  onInput(event: string, value: string): void {
    this.updateValue(event, value.trim());
  }

  constructor(renderer: Renderer2, elementRef: ElementRef) {
    this._sourceRenderer = renderer;
    this._sourceElementRef = elementRef;
  }

  /**
   * Writes a new value to the element based on the type of input element.
   *
   * @param {any} value - new value
   */
  public writeValue(value: any): void {
    this._value = value === "" ? "" : value || null;

    this._sourceRenderer.setProperty(
      this._sourceElementRef.nativeElement,
      "value",
      this._value,
    );

    if (this._type !== "text") {
      this._sourceRenderer.setAttribute(
        this._sourceElementRef.nativeElement,
        "value",
        this._value,
      );
    }
  }

  /**
   * Trims an input value, and sets it to the model and element.
   *
   * @param {string} value - input value
   * @param {string} event - input event
   */
  private updateValue(event: string, value: string): void {
    value = this.trim !== "" && event !== this.trim ? value : value.trim();

    const previous = this._value;

    // write value to the element.
    this.writeValue(value);

    if ((this._value || previous) && this._value.trim() !== previous) {
      this.onChange(this._value);
    }

    // check that non-null value is being changed
    const hasTypedSymbol = value && previous && value !== previous;
  }

  private onChange = (_: any) => {};
}
