import { Injectable } from "@angular/core";
import { ReminderServiceBase } from "./reminder-base";
import { Debug } from "../helpers/debug";
import { DialogHelperService } from "./dialog-helper.service";
import { NavController } from "@ionic/angular";
import { Router } from "@angular/router";
import { IntroductionService } from "./introduction.service";
import { AccountService } from "./account.service";
import { ApiParams } from "./api/api.params";
import { StorageService } from "./storage.service";
import { environment as Env } from "../../environments/environment";

export const REMINDER_STORAGE_KEY_BASE = "reminderService_";

@Injectable({
  providedIn: "root",
})
export class ReminderIntroductionService extends ReminderServiceBase {
  public isValentines: boolean = false;
  /**
   * Constructor
   */
  public constructor(
    public storage: StorageService,
    public dbg: Debug,
    public dlg: DialogHelperService,
    public router: Router,
    public introduction: IntroductionService,
    public accountService: AccountService,
  ) {
    // call parent constructor
    super(storage, dbg, dlg);
  }

  /**
   * is the reminder relevant?
   * @note child classes should overwrite this function and implement logic to
   * check if we need to remind the user at all
   *
   * @return promise resolves if relevant, rejects if not
   */
  public checkIfRelevant(): Promise<any> {
    return new Promise<any>(async (resolve, reject) => {
      let data = await this.storage.get(this.storageKey);
      if (data.closed_count > 3) return reject();

      if (this.checkIsValentines()) {
        return resolve(true);
      }

      this.introduction
        .isAvailable()
        .then(({ data }) => {
          if (data.available && this.accountService.account) {
            return resolve(true);
          }
          this.postpone();
          reject();
        })
        .catch((err) => {
          this.dbg.le("error - introduction not available", err);
          this.postpone();
          reject();
        });
    });
  }

  public postpone() {
    this.postponeReminder()
      .then(async () => {
        this.dbg.l("introduction postponed");
        // Set closed_count.
        let data = await this.storage.get(this.storageKey);
        !data["closed_count"]
          ? (data["closed_count"] = 1)
          : data["closed_count"]++;
        this.storage.set(this.storageKey, data);
        this.introduction.doShow = false;
      })
      .catch((e) => {
        this.dbg.le("introduction postpone - error", e);
      });
  }

  private checkIsValentines(): boolean {
    const dateObj = new Date();
    const month = dateObj.getUTCMonth() + 1;
    const day = dateObj.getUTCDate();
    const year = dateObj.getUTCFullYear();

    const start: number = Env.VALENTINES_START;
    const end: number = Env.VALENTINES_END;

    if (month != 2) return;

    return (this.isValentines = !!(day >= start && day <= end));
  }

  /**
   * Execute reminder content
   *
   * @note child classes should overwrite this function to implement logic,
   * we should show dialogs, here, or whatever
   * @note child classes should call this parent function
   *
   * @param forceExecute
   */
  public async execute(forceExecute: boolean = false): Promise<any> {
    this.storageKey =
      REMINDER_STORAGE_KEY_BASE +
      "introduction_" +
      this.accountService.account.user_id;
    await this.init();

    return new Promise((resolve, reject) => {
      super
        .execute(forceExecute)
        .then(() => {
          this.postponeReminder()
            .then(() => {
              // show notification dialog
              // this.router.navigate(['/app/introduction']);
              this.introduction.doShow = true;
            })
            .catch((err) => {
              this.dbg.le("postpone on execute", err);
            });

          resolve(true);
        })
        .catch((err) => {
          this.dbg.le("catch", err);
          reject(err);
        });
    });
  }

  public send(data: ApiParams.Introduction.type): void {
    this.introduction
      .addIntroduction(data)
      .then(() => {
        this.postpone();
        this.dlg.showToast("", "introduction_step7_subtitle3");
      })
      .catch((err) => {
        this.dlg.showToastBaseOnErrorResponse(err);
      });
  }
}
