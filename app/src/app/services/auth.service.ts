import { Injectable } from "@angular/core";
import { firstValueFrom, Observable, Subject } from "rxjs";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { environment as Env } from "../../environments/environment";
import { ApiCommands } from "./api/api.commands";
import { CredentialData } from "../schemas/credentials";
import { map, tap } from "rxjs/operators";
import { JwtData } from "../schemas/jwt";
import { JwtHelperService } from "@auth0/angular-jwt";
import _ from "lodash";
import { Utils } from "../helpers/utils";
import { DeviceInfoService } from "./device-info.service";
import { PreRegister } from "../schemas/pre-register";
import { Router } from "@angular/router";
import { Debug } from "../helpers/debug";
import { UtilsService } from "./utils.service";
import { DialogHelperService } from "./dialog-helper.service";
import {
  SignInWithApple,
  SignInWithAppleResponse,
  SignInWithAppleOptions,
} from "@capacitor-community/apple-sign-in";
import { AppleSignupWizardService } from "./apple-signup-wizard.service";
import { TrackingService } from "./tracking.service";

export type UserStatus = "USER_EXISTS_N_LOGGED" | "USER_NOT_EXISTS";

export type AuthEvent = {
  credentialData: CredentialData.type;
  jwtData: JwtData.type;
};

export type RegisterEvents = {
  method: CredentialData.CredType;
  data: any;
};

export type AuthConsentParams = {
  receive_offers?: boolean;
  privacy_policy?: boolean;
  terms_of_service?: boolean;
};

@Injectable({
  providedIn: "root",
})
export class AuthService {
  constructor(
    private api: ApiService,
    private deviceInfo: DeviceInfoService,
    private router: Router,
    private debug: Debug,
    private utilsService: UtilsService,
    private dialog: DialogHelperService,
    private appleSignupWizard: AppleSignupWizardService,
    private trackingService: TrackingService
  ) {
    this.registerEvents$.subscribe({
      next: (data) => {
        // save if the session was started with a registration
        this._wasRegistration = true;
        this._regType = data.method;
      },
    });
  }

  private _wasRegistration: boolean = false;
  private _regType: CredentialData.CredType | "" = "";

  /**
   * keys for cleanup
   * @returns
   */
  private getKeysForCleanup() {
    return [
      "aff_id",
      "aff_pg",
      "aff_cp",
      "aff_kw",
      "aff_src",
      "aff_adg",
      "track_id",
    ];
  }

  /**
   * Was there a registration before auth?
   */
  get wasRegistration() {
    return this._wasRegistration;
  }

  /**
   * If was registration this will return the type
   */
  get regType(): CredentialData.CredType | "" {
    // return CredentialData.CredType.Apple; // debug
    return this._regType;
  }

  // only for testing
  private DO_TEST_FORCE_REGISTER = false;

  private jwtHelper = new JwtHelperService();

  private _authEvents$: Subject<AuthEvent> = new Subject<AuthEvent>();

  get authEvents$(): Observable<AuthEvent> {
    return this._authEvents$.asObservable();
  }

  private _registerEvents$: Subject<RegisterEvents> =
    new Subject<RegisterEvents>();

  get registerEvents$(): Observable<RegisterEvents> {
    return this._registerEvents$.asObservable();
  }

  /**
   * Is app register or pre-register in progress?
   * @returns
   */
  public isAppRegInProgress() {
    return (
      this.api.isInEventList({ command: ApiCommands.AppPreRegister.name }) ||
      this.api.isInEventList({ command: ApiCommands.AppRegister.name })
    );
  }

  /**
   * Is simple auth call in progress?
   * @returns
   */
  public isSimpleInProgress() {
    return this.api.isInEventList({
      command: ApiCommands.AuthSimple.name,
    });
  }

  /**
   * Get errors for simple auth
   * @returns
   */
  public getSimpleErrors() {
    return this.api.getFromErrorList(
      {
        command: ApiCommands.AuthSimple.name,
      },
      true
    );
  }

  /**
   * Helper for simple ( email, username ) auth
   * @param identifier
   * @param password
   * @returns
   */
  public simple(
    identifier: string,
    password: string
  ): Observable<ApiResponses.SimpleAuthResponse.type> {
    if (Utils.getEmailRegexp().test(identifier)) {
      return this.withEmail(identifier, password);
    }
    return this.withUsername(identifier, password);
  }

  /**
   * Extract hash value from url ( usually deeplink activation url )
   * @param activationUrl
   * @returns
   */
  public extractSessionIdFromUrl(activationUrl: string): string {
    let sessionId: string | null = null;

    if (activationUrl) {
      let url = new URL(activationUrl);
      sessionId = url.searchParams.get("h");
    }

    return sessionId;
  }

  /**
   * Case when we have session slug in deeplink url, and we auth the user with that
   * @param url
   */
  public async withUrlSessionId(sessionId: string): Promise<JwtData.type> {
    let restoredJwt = null;

    if (sessionId) {
      // try session restore
      restoredJwt = await this.urlSessionRestore(sessionId);
    }

    // no success restoring jwt from url session variable
    if (!restoredJwt) {
      return Promise.reject("restoredJwt is empty");
    }

    let decoded = this.jwtHelper.decodeToken(restoredJwt);
    let new_hash_array = (decoded?.jti || "").split(".");
    let new_hash = new_hash_array.length <= 1 ? sessionId : new_hash_array[1];

    // we save the new autologin hash for session recovery
    let _cred: CredentialData.type = {
      //identifier: sessionId,
      identifier: new_hash,
      secret: "",
      type: CredentialData.CredType.URLSession,
      data: null,
    };

    let _jwt: JwtData.type = {
      token: restoredJwt,
      payload: decoded,
    };

    this._authEvents$.next({ credentialData: _cred, jwtData: _jwt });

    return Promise.resolve(_jwt);
  }

  /**
   * Restore session from url
   * @param session_id
   * @returns jwt if success, null if fails
   */
  public urlSessionRestore(session_id: string = ""): Promise<String> {
    return firstValueFrom(
      this.api
        .call<ApiResponses.SimpleAuthResponse.type>({
          command: ApiCommands.CheckSession.name,
          method: "get",
          params: {
            session_id: session_id,
          },
          useToken: false,
          commandVersion: "2",
          callType: ["ignore_401"],
        })
        .pipe(
          map((data) => {
            return data?.data?.jwt ?? null;
          })
        )
    );
  }

  /**
   * Authenticate with email
   */
  public withEmail(
    email: string,
    password: string
  ): Observable<ApiResponses.SimpleAuthResponse.type> {
    return this.api
      .call<ApiResponses.SimpleAuthResponse.type>({
        command: ApiCommands.AuthSimple.name,
        method: "post",
        params: <ApiParams.AuthSimpleEmail.type>{
          email: email,
          password: password,
          system_id: Env.API_SYSTEM_ID,
        },
        useToken: false,
        callType: ["ignore_401"],
      })
      .pipe(
        tap((data: ApiResponses.SimpleAuthResponse.type) => {
          // send data to subscribers

          let _cred: CredentialData.type = {
            identifier: email,
            secret: password,
            type: CredentialData.CredType.Email,
            data: null,
          };

          let _jwt: JwtData.type = {
            token: data.data.jwt,
            payload: this.jwtHelper.decodeToken(data.data.jwt),
          };

          this._authEvents$.next({ credentialData: _cred, jwtData: _jwt });
        })
      );
  }

  /**
   * Authenticate with username
   */
  public withUsername(
    username: string,
    password: string
  ): Observable<ApiResponses.SimpleAuthResponse.type> {
    return this.api
      .call<ApiResponses.SimpleAuthResponse.type>({
        command: ApiCommands.AuthSimple.name,
        method: "post",
        params: <ApiParams.AuthSimpleUsername.type>{
          username: username,
          password: password,
          system_id: Env.API_SYSTEM_ID,
        },
        useToken: false,
        callType: ["ignore_401"],
      })
      .pipe(
        tap((data: ApiResponses.SimpleAuthResponse.type) => {
          // send data to subscribers

          let _cred: CredentialData.type = {
            identifier: username,
            secret: password,
            type: CredentialData.CredType.Username,
            data: null,
          };

          let _jwt: JwtData.type = {
            token: data.data.jwt,
            payload: this.jwtHelper.decodeToken(data.data.jwt),
          };

          this._authEvents$.next({ credentialData: _cred, jwtData: _jwt });
        })
      );
  }

  /**
   * Pre-register helper function
   */
  private appPreRegister(
    params: ApiParams.AppPreRegisterParams.type
  ): Observable<ApiResponses.AppPreRegisterResponse.type> {
    // make sure that force register param is not present
    if (!params["force_register"]) {
      delete params["force_register"];
    }

    // send tracking data

    params["aff_id"] = this.trackingService.compiledData?.aff_id || "";
    params["aff_pg"] = this.trackingService.compiledData?.aff_pg || "";
    params["aff_cp"] = this.trackingService.compiledData?.aff_cp || "";
    params["aff_kw"] = this.trackingService.compiledData?.aff_kw || "";
    params["aff_src"] = this.trackingService.compiledData?.aff_src || "";
    params["aff_adg"] = this.trackingService.compiledData?.aff_adg || "";
    params["track_id"] = this.trackingService.compiledData?.track_id || "";

    // remove empty keys from data

    for (let key of this.getKeysForCleanup()) {
      if (!params?.[key]) {
        delete params[key];
      }
    }

    // api call

    return this.api.call<ApiResponses.AppPreRegisterResponse.type>({
      command: ApiCommands.AppPreRegister.name,
      method: "post",
      useToken: false,
      params: params,
      callType: ["ignore_401"],
    });
  }

  /**
   * Register helper function
   */
  private appRegister(
    params: ApiParams.AppRegisterParams.type
  ): Observable<ApiResponses.AppRegisterResponse.type> {
    // make sure that force register param is not present
    if (!params["force_register"]) {
      delete params["force_register"];
    }

    // send tracking data

    params["aff_id"] = this.trackingService.compiledData?.aff_id || "";
    params["aff_pg"] = this.trackingService.compiledData?.aff_pg || "";
    params["aff_cp"] = this.trackingService.compiledData?.aff_cp || "";
    params["aff_kw"] = this.trackingService.compiledData?.aff_kw || "";
    params["aff_src"] = this.trackingService.compiledData?.aff_src || "";
    params["aff_adg"] = this.trackingService.compiledData?.aff_adg || "";
    params["track_id"] = this.trackingService.compiledData?.track_id || "";

    // remove empty keys from data

    for (let key of this.getKeysForCleanup()) {
      if (!params?.[key]) {
        delete params[key];
      }
    }

    // api call

    return this.api.call<ApiResponses.AppRegisterResponse.type>({
      command: ApiCommands.AppRegister.name,
      method: "post",
      useToken: false,
      params: params,
      callType: ["ignore_401"],
    });
  }

  /**
   * Emmit auth observable subject data for apple + save token and failsafe data to local storage
   *
   * @param jwt
   * @param token
   */
  private async appleHandleCredentials(
    jwt: string,
    token: string,
    username: string,
    provider_response: any
  ) {
    this.debug.log({
      message: "creds test",
      data: [jwt, token, username],
    });

    let _cred: CredentialData.type = {
      identifier: username,
      secret: token,
      type: CredentialData.CredType.Apple,
      data: provider_response,
    };

    let _jwt: JwtData.type = {
      token: jwt,
      payload: this.jwtHelper.decodeToken(jwt),
    };

    this._authEvents$.next({ credentialData: _cred, jwtData: _jwt });
  }

  /**
   * Initialize and open data entry wizard for apple sign in
   */
  private async openAppleSignupWizard(data) {
    this.appleSignupWizard.initialize();

    let prdata = data["pre_reg_data"];

    this.appleSignupWizard.setStoredDataByKey(
      "first_name",
      this._applePluginDataCache["response"]["givenName"] ||
        prdata["first_name"]
    );

    // do we have email? fill it in, remove step

    if (prdata["email"] && prdata["email"].length > 3) {
      this.appleSignupWizard.setStoredDataByKey("email", prdata["email"]);
      this.appleSignupWizard.removeStepByUrl("email");
    }

    // add mandatory apple data
    this.appleSignupWizard.setStoredDataByKey(
      "provider_data",
      data["provider_data"]
    );

    // add location data ( if not obtained from the device )
    // @todo add location data from geolocation plugin if possible
    // -- or do we need to use this, and ask it for later

    this.appleSignupWizard.setStoredDataByKey("latitude", prdata["latitude"]);

    this.appleSignupWizard.setStoredDataByKey("longitude", prdata["longitude"]);

    this.appleSignupWizard.setFinishCallback(async (data) => {
      try {
        await this.finishAppleRegistration(data);
      } catch (err) {
        this.dialog.showErrorAlert(
          "error",
          this.utilsService.figureOutTheErrorMessage(err),
          this.utilsService.figureOutTheErrorKey(err)
        );
      }
    });

    // open wizard
    this.router.navigateByUrl(Env.APPLE_SIGNUP_URL_PREFIX);
  }

  /**
   * Finish apple registration - send app reg api call
   */
  protected async finishAppleRegistration(data) {
    this.debug.log({
      message: "Finish apple registraion data",
      data: data,
    });

    let sendData = {
      username: data["username"] || "",
      email: data["email"],
      gender_id: data["genderId"] || "",
      looking_id: data["lookingId"] || "",
      first_name: data["first_name"] || "",
      age: data["age"] || "",
      birthdate: data["birthdate"] || "",
      latitude: data["latitude"] || "",
      longitude: data["longitude"] || "",

      consent_offers: data["special"] || "",
      consent_privacy: data["privacy"] || "",
      consent_terms: data["terms"] || "",
    };

    let result = await firstValueFrom(
      this.appRegister({
        data: sendData,
        force_register: this.DO_TEST_FORCE_REGISTER,
        platform: this.deviceInfo.platform,
        provider_data: data["provider_data"],
        system_id: Env.API_SYSTEM_ID,
      })
    );

    this.debug.log({
      message: "apple registration result",
      data: result,
    });

    // propagate data to subject
    this._registerEvents$.next({
      method: CredentialData.CredType.Apple,
      data: result?.data?.account || {},
    });

    // finish auth, handle credentials
    await this.appleHandleCredentials(
      result.data.jwt,
      data["provider_data"]["access_token"]["access_token"],
      sendData.email,
      this._applePluginDataCache
    );
  }

  /**
   * See if we have credentials login in progress ( meaning any recovery methog)
   * @returns
   */
  public isAuthInProgress() {
    // if either simple or 3th party login api call is in progress, then we have a login in progress
    return this.isSimpleInProgress() || this.isAppRegInProgress();
  }

  /**
   * Log user in with credentials object ( recovery )
   * @param credentials
   * @returns
   */
  public async withCredentials(credentials: CredentialData.type): Promise<any> {
    switch (credentials.type) {
      case CredentialData.CredType.Username:
        return firstValueFrom(
          this.withUsername(credentials.identifier, credentials.secret)
        );

      case CredentialData.CredType.Email:
        return firstValueFrom(
          this.withEmail(credentials.identifier, credentials.secret)
        );

      case CredentialData.CredType.Apple:
        // @todo NOT TESTED !!! test this
        return firstValueFrom(this.restoreApple(credentials));

      case CredentialData.CredType.Facebook:
        // @todo NOT TESTED !!! test this
        return firstValueFrom(this.restoreFacebook(credentials));

      case CredentialData.CredType.URLSession:
        // @todo test this
        return this.withUrlSessionId(
          this.extractSessionIdFromUrl(credentials.identifier)
        );

      default:
        return Promise.resolve();
    }
  }

  /**
   * Try and restore facebook login
   */
  private restoreFacebook(
    cred: CredentialData.type
  ): Observable<ApiResponses.AppPreRegisterResponse.type> {
    return this.appPreRegister({
      force_register: false,
      provider: ApiParams.AppLoginProviderFacebook,
      platform: this.deviceInfo.platform,
      provider_response: cred.data,
      refresh_token: 1,
      system_id: Env.API_SYSTEM_ID,
      token: cred.secret,
    }).pipe(
      tap(async (data) => {
        // what did we get here?
        this.debug.l("Facebook restore get", data);

        let _jwt: JwtData.type = {
          token: data.data.jwt,
          payload: this.jwtHelper.decodeToken(data.data.jwt),
        };

        this._authEvents$.next({ credentialData: cred, jwtData: _jwt });
      })
    );
  }

  /**
   * Try and restore apple login
   */

  private restoreApple(
    cred: CredentialData.type
  ): Observable<ApiResponses.AppPreRegisterResponse.type> {
    return this.appPreRegister({
      force_register: false,
      provider: ApiParams.AppLoginProviderApple,
      platform: this.deviceInfo.platform,
      provider_response: cred.data,
      refresh_token: 1,
      system_id: Env.API_SYSTEM_ID,
      token: cred.secret,
    }).pipe(
      tap((data) => {
        // what did we get here?
        this.debug.l("Apple restore get", data);

        let _jwt: JwtData.type = {
          token: data.data.jwt,
          payload: this.jwtHelper.decodeToken(data.data.jwt),
        };

        this._authEvents$.next({ credentialData: cred, jwtData: _jwt });
      })
    );
  }

  private _appleDataCachePreRegister: PreRegister.type = null;
  private _applePluginDataCache: any = null;

  /**
   * Get apple pre-register cached data
   */
  get appleDataCachePreRegister(): PreRegister.type {
    return this.appleDataCachePreRegister;
  }

  /**
   * Get apple plugin cache data
   */
  get applePluginDataCache(): any {
    return this._applePluginDataCache;
  }

  /**
   * Initialize apple sign in proccess, get prereg data, call native login, etc
   */
  private async appleInit() {
    this._appleDataCachePreRegister = null;

    let params: ApiParams.AppPreRegisterParams.type =
      await this.initAppleAuth();

    params["system_id"] = Env.API_SYSTEM_ID;

    let preRegResult = await firstValueFrom(this.appPreRegister(params));

    this._appleDataCachePreRegister = preRegResult.data;

    this.debug.l("apple prereg data", this._appleDataCachePreRegister);

    return Promise.resolve(preRegResult.data);
  }

  /**
   * Init apple authentication from native plugin, get data, generate prereg data
   */
  private async initAppleAuth() {
    this._applePluginDataCache = null;

    let options: SignInWithAppleOptions = {
      clientId: Env.APP_STORE_ID,

      // we do not use this yet, but we need to set up app redirect once we will
      redirectURI: `https://${Env.APP_WEBSITE}/connect/apple`,

      scopes: "email name",

      // nonce: "nonce",
      // state:"12345"
    };

    let data: SignInWithAppleResponse = await SignInWithApple.authorize(
      options
    );

    this._applePluginDataCache = data;

    if (_.isEmpty(data)) {
      // we have no result
      throw new Error("login_error");
    }

    let result: ApiParams.AppPreRegisterParams.type = {
      force_register: this.DO_TEST_FORCE_REGISTER,
      platform: this.deviceInfo.platform,
      provider: ApiParams.AppLoginProviderApple,
      provider_response: data,
      system_id: Env.API_SYSTEM_ID,
      token: data["response"]["authorizationCode"],
      refresh_token: 0,
    };

    return result;
  }

  /**
   * Sign in with apple
   *
   * @param params
   */
  public async apple() {
    let data = await this.appleInit();

    if (data.user_status == <UserStatus>"USER_EXISTS_N_LOGGED") {
      // auth user with the data we got

      this.debug.log({
        message: "existing apple user",
        data: data,
      });

      // handle credentials
      await this.appleHandleCredentials(
        data.jwt,
        this._applePluginDataCache["response"]["authorizationCode"],
        data.user.username,
        this._applePluginDataCache
      );
    } else {
      // pre populate signup facebook wizard and open it

      this.openAppleSignupWizard(data);
      return Promise.resolve(data);
    }
    /*
    try
    {
    }
    catch(err)
    {
      this.debug.log({
        message: "apple error",
        data: this.utilsService.figureOutTheErrorMessage(err),
        severity: "error",
      });
    }
    */
  }

  /**
   * Verify if username is valid and will be accepted by the site
   * @param username
   * @returns
   */
  public verifyUsername(
    username: string
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api.call<ApiResponses.NoDataResponse.type>({
      command: ApiCommands.AuthVerifyUsername.name,
      method: "post",
      useToken: false,
      params: <ApiParams.AuthVerifyUsername.type>{
        system_id: Env.API_SYSTEM_ID,
        username: username,
      },
    });
  }

  /**
   * Verify if email is valid and will be accepted by the site
   * @param email
   * @returns
   */
  public verifyEmail(
    email: string
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api.call<ApiResponses.NoDataResponse.type>({
      command: ApiCommands.AuthVerifyEmail.name,
      method: "post",
      useToken: false,
      params: <ApiParams.AuthVerifyEmail.type>{
        system_id: Env.API_SYSTEM_ID,
        email: email,
      },
    });
  }

  /**
   * Is user authenticated?
   *
   * @returns {Promise<ApiResponses.ResponseBase>}
   */
  public check(): Observable<ApiResponses.ResponseBase.type> {
    return this.api.call<ApiResponses.ResponseBase.type>({
      command: ApiCommands.AuthCheck.name,
      method: "get",
      useToken: true,
      callType: ["background"],
    });
  }

  /**
   * Register a user account with manual data
   * @param params
   * @returns
   */
  public register(params: ApiParams.Registration.type) {
    // append affiliate param

    params["aff_id"] = this.trackingService.compiledData?.aff_id || "";
    params["aff_pg"] = this.trackingService.compiledData?.aff_pg || "";
    params["aff_cp"] = this.trackingService.compiledData?.aff_cp || "";
    params["aff_kw"] = this.trackingService.compiledData?.aff_kw || "";
    params["aff_src"] = this.trackingService.compiledData?.aff_src || "";
    params["aff_adg"] = this.trackingService.compiledData?.aff_adg || "";
    params["track_id"] = this.trackingService.compiledData?.track_id || "";

    // remove empty keys from data

    for (let key of this.getKeysForCleanup()) {
      if (!params?.[key]) {
        delete params[key];
      }
    }

    // api call

    return this.api
      .call<ApiResponses.RegistrationResponse.type>({
        command: ApiCommands.AuthRegister.name,
        method: "post",
        useToken: false,
        params: params,
      })
      .pipe(
        tap((data) => {
          this._registerEvents$.next({
            method: CredentialData.CredType.Username,
            data: data?.data?.account || {},
          });

          // send data to subscribers

          let _cred: CredentialData.type = {
            identifier: params.username,
            secret: params.password,
            type: CredentialData.CredType.Username,
            data: null,
          };

          let _jwt: JwtData.type = {
            token: data.data.jwt,
            payload: this.jwtHelper.decodeToken(data.data.jwt),
          };

          this._authEvents$.next({ credentialData: _cred, jwtData: _jwt });
        })
      );
  }

  /**
   * Reset password
   * @param email
   * @param system_id
   * @returns
   */
  public resetPassword(email: string) {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.AuthResetPassword.name,
        method: "post",
        useToken: false,
        params: <ApiParams.AuthResetPassword.type>{
          email: email,
          system_id: Env.API_SYSTEM_ID,
        },
      })
    );
  }
}
