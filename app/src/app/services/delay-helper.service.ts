import { Injectable, NgZone } from "@angular/core";
import { Debug } from "../helpers/debug";
import { Utils } from "../helpers/utils";
import { ApiService } from "./api/api.service";
import _ from "lodash";

@Injectable({
  providedIn: "root",
})
export class DelayHelperService {
  constructor(
    private api: ApiService,
    private dbg: Debug,
    private ngZone: NgZone
  ) {
    this.initializeMessageDelayListener();
  }

  get isActive() {
    return this._messageDelayCount > 0;
  }

  get delay() {
    return this._messageDelayCount;
  }

  /**
   * start delay interval
   */
  private startDelayInterval() {
    if (!this._messageDelayCountInterval) {
      this._messageDelayCountInterval = setInterval(() => {
        this.ngZone.run(() => {
          this.messageDelayTick();
        });
      }, 1100);
    }
  }

  /**
   * clear delay interval
   */
  public clearDelayInterval() {
    this._messageDelayCount = 0;

    if (this._messageDelayCountInterval) {
      clearInterval(this._messageDelayCountInterval);
      this._messageDelayCountInterval = null;
    }
  }

  /**
   * if we get message delay, we have to track it across app and display countdown
   */
  private initializeMessageDelayListener() {
    this.api.errorStream$.subscribe({
      next: (event) => {
        if (
          _.isArray(event?.data?.error?.errors) &&
          event?.data?.error?.errors[0]?.key == "wait_to_send"
        ) {
          this._messageDelayCount =
            Utils.getCountdownFromError(event?.data?.error?.errors[0]?.error) ??
            0;
          this.startDelayInterval();
        }
      },
    });
  }

  private messageDelayTick() {
    if (this._messageDelayCount > 0) {
      this._messageDelayCount--;
      return;
    }

    this.clearDelayInterval();
  }

  private _messageDelayCountInterval: any = null;
  private _messageDelayCount: number = 0;
}
