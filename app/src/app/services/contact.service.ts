import { Injectable } from "@angular/core";
import { ApiService } from "./api/api.service";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiCommands } from "./api/api.commands";
import { firstValueFrom } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class ContactService {
  constructor(private api: ApiService) {}

  /**
   * Api call for contacting the support.
   *
   * @param {ApiParams.ContactAnonymous.type} params
   * @returns {Promise<ApiResponses.NoDataResponse.type>}
   */
  public anonymous(
    params: ApiParams.ContactAnonymous.type
  ): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.ContactAnonymous.name,
        method: "post",
        useToken: false,
        params,
        commandVersion: "2",
      })
    );
  }

  /**
   * Api call for contacting the support.
   *
   * @param {ApiParams.ContactAdd.type} params
   * @returns {Promise<ApiResponses.NoDataResponse.type>}
   */
  public add(
    params: ApiParams.ContactAdd.type
  ): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.ContactAdd.name,
        method: "post",
        useToken: true,
        params,
      })
    );
  }
}
