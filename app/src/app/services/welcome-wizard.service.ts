import { Injectable } from "@angular/core";
import { WelcomePageData } from "../pages/welcome/welcome-page-data.class";
import { AccountService } from "./account.service";
import { Debug } from "../helpers/debug";
import { ApiParams } from "./api/api.params";
import WELCOME_STATUS_FLAG = ApiParams.WELCOME_STATUS_FLAG;
import { WelcomeProfileService } from "./welcome-profile.service";
import { MatchPrefService } from "./match-pref.service";
import { Router } from "@angular/router";
import { QueueCommandService } from "./queue/queue-command.service";
import { NavController, Platform } from "@ionic/angular";
import { environment as Env } from "src/environments/environment";
import { PurchaseService } from "./purchase.service";
import { Observable, Subject } from "rxjs";
import { AnalyticsService } from "./analytics.service";
import { TME_welcome_form_completed } from "../classes/gtm-event.class";
import _ from "lodash";

export class WelcomeWizardPageItem {
  public pageType: string;
  public index: number = -1;
  public pageData: WelcomePageData;

  constructor(pageType: string, pageData: WelcomePageData) {
    this.pageType = pageType;
    this.pageData = pageData;
  }
}

@Injectable({
  providedIn: "root",
})
export class WelcomeWizardService {
  /**
   * Current page index.
   *
   * @type {number}
   */
  public _currentIndex: number = 0;

  /**
   * Constructor.
   */
  constructor(
    public ownProfile: AccountService,
    public debug: Debug,
    public welcomeProfileService: WelcomeProfileService,
    public matchPref: MatchPrefService,
    public router: Router,
    public queueCommand: QueueCommandService,
    private purchaseService: PurchaseService,
    private analytics: AnalyticsService,
  ) {
    // this.firstname = this.ownProfile?.account?.first_name ?? this.ownProfile?.account?.username ?? '';
  }

  private _isClosed = false;

  /**
   * Set indicator if wizard is closed or not ( needed for a bug where progress is not updated when user uses backbutton )
   * @param isClosed
   */
  private setClosed(isClosed) {
    this._isClosed = isClosed;

    if (isClosed) {
      if (this._interval) {
        clearInterval(this._interval);
        this._interval = null;
      }
    } else {
      if (!this._interval) {
        this._interval = setInterval(() => {
          if (!this._isClosed) {
            this._realIndex = this.getIndexFromUrl();
          }
        }, 500);
      }
    }
  }

  private _interval = null;

  private _realIndex = 0;

  public getRealIndex(): number {
    return this._realIndex;
  }

  public getIndexFromUrl() {
    return parseInt(this.router.url.split("/")[3]) ?? this.currentIndex;
  }

  /**
   * - wizard page should be and interface that makes sure it has all the required stuff
   * - all the wizard data should be kept in wizard class
   * - page navigation should only happen through wizard class
   * - all page should have the wizard class - so it can pass / retrieve data , navigate
   */

  /**
   * wizard pages in order
   */
  public pages: WelcomeWizardPageItem[] = [];

  /**
   * Do we need to hide back button on first page ( do we want to allow the user to exit without competing the wizard ? )
   */
  public doHideFirstBackButton: boolean = true;

  public firstname: string;

  /**
   * reset controller
   */
  public reset() {
    this.pages = [];
    this.index = 0;
  }

  private index: number = 0;

  public getPageListFromData(apiData) {
    let data = apiData; //["result"]["data"];

    let resultArray = [];

    data.forEach((item) => {
      item["_key"] = item["name"];
      item["type"] = item["question_type"];

      if (item["name"] == "question") {
        item["_id"] = item["question_id"];
        item["_type"] = "WpgDataPage";
        item["values"] = item["answered"].length
          ? item["answered"].split(",")
          : [];

        if (!item["values"].length && item["defaults"].length) {
          item["values"] = item["defaults"];
        }

        item["_type"] = "wpg-data";
      } else {
        item["_id"] = -1;

        switch (item["name"]) {
          case "photo":
            item["_type"] = "wpg-photo";
            break;

          case "end":
          case "info":
            item["_type"] = "wpg-info";
            break;

          default:
            item["_type"] = "wpg-data";
        }

        item["values"] = item["answered"].length
          ? item["answered"].split(",")
          : [];

        if (item["name"] == "photo") {
          item["values"] = []; // photos
        } else {
          item["values"] = item["answered"].length
            ? item["answered"].split(",")
            : [];
        }
      }

      if (this.validatePageData(item)) {
        resultArray.push(item);
      }
    });

    // generate page list

    let pageList = [];

    resultArray.forEach((pg) => {
      let _newPage = new WelcomeWizardPageItem(pg._type, pg);
      pageList.push(_newPage);
    });

    return pageList;
  }

  /**
   * Add multimple pages
   * @param pageList WelcomeWizardPageItem[]
   */
  public addPageList(pageList: WelcomeWizardPageItem[]) {
    pageList.forEach((pg) => {
      let _doSkip = false;

      if (Env.DO_SKIP_INFO_WELCOME_PAGE && pg.pageType == "wpg-info") {
        _doSkip = true;
      }

      if (!_doSkip) {
        this.addPage(pg);
      }
    });
  }

  private _closed$: Subject<any> = new Subject();

  get closed$(): Observable<any> {
    return this._closed$.asObservable();
  }

  /**
   * Add page to the wizard
   * @param page WelcomeWizardPageItem
   */
  public addPage(page: WelcomeWizardPageItem) {
    page.index = this.index;
    this.pages.push(page);

    this.index++;
  }

  private closePromise: Promise<any>;
  private _resolve: Function;

  public async closed() {
    await this.router.navigate([Env.HOME_PATH], { replaceUrl: true });

    // emmit event
    this._closed$.next(true);

    this.setClosed(true);

    this.analytics.logEvent(
      "welcome_form_completed",
      <TME_welcome_form_completed>{},
    );

    // open promo dialog

    if (this.ownProfile.isPremium() || !Env.DO_SHOW_OFFER_AFTER_WELCOME) {
      this.debug.l(
        "Skipping promo dialog, user is premium or offer is disabled",
      );
      // user is premium, continue
      this.queueCommand.gotoNextItem();
      return;
    } else {
      this.debug.l("Display promo offer after welcome wizard");

      this.purchaseService.openDiscountedDialog(
        () => {
          this.queueCommand.gotoNextItem();
        },
        () => {
          this.queueCommand.gotoNextItem();
        },
      );

      /*
      this.purchaseService.setPromoDialogType("welcome").openCampaignDialog(
        "welcomeWizardOffer",
        () => {
          console.log("Promo queue continue 1");
          this.queueCommand.gotoNextItem();
        },
        () => {
          console.log("Promo queue continue 2");
          this.queueCommand.gotoNextItem();
        }
      );
      */
    }

    this._resolve();
  }

  public open(index: number = 0): Promise<any> {
    this.setClosed(false);

    this.closePromise = new Promise((resolve) => {
      this._resolve = resolve;
    });

    if (this.pages.length == 0) {
      this.debug.log({
        message: "no pages added to welcome wizard",
        severity: "error",
      });
      this.closed();
      return;
    }

    if (!(index > -1 && index < this.pages.length)) {
      this.debug.log({ message: "page index out of bounds" });
      this.closed();
      return;
    }

    let url = "/app/" + this.pages[index].pageType;
    url +=
      this.pages[index].pageType === "wpg-data"
        ? "/" + index + "/" + this.pages[index].pageData.name
        : "";
    this.router.navigate([url], {
      state: this.pages[index],
    });

    this.currentIndex = index;
    this._realIndex = index;

    return this.closePromise;
  }

  /**
   *
   */
  public closeWizard(saveFlag: boolean = false) {
    if (saveFlag) {
      // save flag that user have completed the wizard
      this.welcomeProfileService
        .saveStatus({
          value: WELCOME_STATUS_FLAG.Complete,
        })
        .subscribe(
          (res) => {
            this.debug.l("welcome profile - saved status");
          },
          (error) => {
            this.debug.le("welcome profile - save status error", error);
          },
        );
    }

    // reload match pref

    this.matchPref
      .getMatchPref()
      .then(() => {
        // this.events.publish(AppEvents.MatchPrefChanged);
      })
      .catch((err) => {
        this.debug.log({
          message: "matchpref refresh error welcome wizard",
          data: err,
          severity: "error",
        });
      });

    this.closed();
  }

  /**
   * Set page index.
   *
   * @return {number}
   */
  set currentIndex(val: number) {
    this._currentIndex = val;
  }

  /**
   * Get current page index.
   *
   * @return {number}
   */
  get currentIndex(): number {
    return this._currentIndex;
  }

  /**
   * Validate answers.
   *
   * @returns {boolean}
   */
  private validatePageData(data): boolean {
    if (!data) return false;

    if (data.type === "check" || data.type === "select") {
      if (_.isArray(data)) {
        return !!data["answers"]?.length;
      } else {
        return !!Object.keys(data.answers).length;
      }
    }

    return true;
  }
}
