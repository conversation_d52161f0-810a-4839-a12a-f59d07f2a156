import { Injectable } from "@angular/core";
import { DialogHelperService } from "./dialog-helper.service";
import { Debug } from "../helpers/debug";
import { RestrictionAll } from "../schemas/restriction";
import { RestrictionService } from "./restriction.service";
import { IBootAction } from "./boot.service";
import { AccountService } from "./account.service";
import { Router } from "@angular/router";
import { StateService } from "./state.service";
import { ProfileService } from "./profile.service";
import { Refreshable } from "./refresher.service";

export interface CreditKeyObject {
  key: string;
  type: string;
}

@Injectable({
  providedIn: "root",
})
export class AclService implements IBootAction, Refreshable {
  /**
   * Restriction data.
   *
   * @type {RestrictionAll}
   */
  private restriction: RestrictionAll.type; // @todo - get restrictions before time runs out.

  /**
   * Loaded data time.
   */
  private loadedTs: number;

  /**
   * Type (like, pass, skip, superlike).
   */
  private type: string;

  /**
   * Action (like_reset, superlike_reset).
   */
  private action: string;

  constructor(
    private restrictionService: RestrictionService,
    private dbg: Debug,
    private dlg: DialogHelperService,
    private accountService: AccountService,
    private router: Router,
    private stateService: StateService,
    private profileService: ProfileService,
  ) {
    this.profileService.wasError.subscribe({
      error: (e) => this.getRestrictions(),
    });
  }

  /**
   * Boot.
   * @returns {Promise<void>}
   */
  public boot(): Promise<void> {
    return this.getRestrictions();
  }

  public async refreshData(): Promise<any> {
    return await this.boot();
  }

  /**
   * Init ACL.
   *
   * @returns {Promise<boolean>}
   */
  public init(action: string): Promise<boolean> {
    this.type = action;
    this.setAction(this.type);

    return this.handle();
  }

  /**
   * Handle action.
   *
   * @returns {Promise<boolean>}
   */
  private handle(): Promise<boolean> {
    return new Promise(async (resolve, reject) => {
      if (this.timeToRefreshData()) {
        await this.getRestrictions();
      }

      if (this.isFreeUserAndHasNoCredits()) {
        this.openFreeCountdownPage();
        return reject(false);
      }

      return this.decreaseCredits()
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  /**
   * Get restriction data.
   *
   * @returns {RestrictionAll}
   */
  public data(): RestrictionAll.type {
    return this.restriction;
  }

  /**
   * Fetch account restrictions.
   *
   * @returns {Promise<void>}
   */
  public getRestrictions(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.restrictionService
        .all()
        .then(({ data }) => {
          this.restriction = data;
          this.restriction["like_reset"]["renewal"] =
            this.restriction["like_reset"].renew_time + this.time();
          this.restriction["superlike_reset"]["renewal"] =
            this.restriction["superlike_reset"].renew_time + this.time();
          resolve();
        })
        .catch((error) => reject(error));
    });
  }

  /**
   * Decrease credits.
   *
   * @returns {Promise<boolean>}
   */
  public decreaseCredits(): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      if (this.hasInfiniteCredits()) {
        return resolve(true);
      }

      if (this.hasCredits()) {
        this.restriction[this.action].credits--;
        return resolve(true);
      }

      if (!this.timeToRefreshData()) {
        this.openCountdownPage();
        return resolve(false);
      }

      // @note you can assume that the user has credits, and you can resolve the promise but refresh data in background

      resolve(true);

      this.getRestrictions()
        .then(() => this.decreaseCredits())
        .catch((error) => this.dbg.le("get restrictions", error));
    });
  }

  /**
   * Can user hide online status?
   *
   * @returns {boolean}
   */
  public canHideOnlineStatus(): boolean {
    return this.restriction.can_hide_online_status;
  }

  /**
   * Can select to use premium match only?
   *
   * @returns {boolean}
   */
  public canChoosePremiumMatch(): boolean {
    return this.restriction.can_choose_premium_match;
  }

  /**
   * Can user change location?
   *
   * @returns {boolean}
   */
  public canChangeLocation(): boolean {
    return this.restriction.can_change_location;
  }

  /**
   * Can user hide ads?
   *
   * @returns {boolean}
   */
  public canHideAds(): boolean {
    return this.restriction.can_hide_ads;
  }

  /**
   * Can user use undo?
   *
   * @returns {boolean}
   */
  public canUseUndo(): boolean {
    return this.restriction.can_use_undo;
  }

  /**
   * Is swipe active - meaning if (true) is both like and dislike counted as swipe and limited or only
   * like is counted and limited (false)
   *
   * @returns {boolean}
   */
  public isSwipeActive(): boolean {
    return this.restriction.swipe_restriction;
  }

  /**
   * Check if user has credits.
   *
   * @returns {boolean}
   */
  private hasCredits(): boolean {
    return this.restriction[this.action].credits > 0;
  }

  /**
   * Check if user has an infinite number of credits.
   *
   * @return {boolean}
   */
  private hasInfiniteCredits(): boolean {
    return this.restriction[this.action].credits === -1;
  }

  /**
   * See if time has come for refresh.
   *
   * @returns {boolean}
   */
  private timeToRefreshData(): boolean {
    return this.restriction[this.action].renewal < this.time();
  }

  /**
   * Get current timestamp.
   *
   * @returns {number}
   */
  private time(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * Check if user is free, and has no right to use (like or pass) action?
   *
   * @return {boolean}
   */
  private isFreeUserAndHasNoCredits(): boolean {
    return !this.accountService.isPremium() && !this.hasCredits();
  }

  /**
   * Open free countdown page.
   */
  private openFreeCountdownPage(): void {
    this.setDataForCountdownPage();
    this.router.navigate(["/app/modals/free-countdown"]);
  }

  /**
   * Open countdown page.
   */
  private openCountdownPage(): void {
    this.setDataForCountdownPage();
    this.router.navigate(["/app/modals/countdown"]);
  }

  /**
   * Set data.
   */
  private setDataForCountdownPage(): void {
    this.stateService.set("countdown", this.accountService.account.username, {
      type: this.type,
      renewal: this.restriction[this.action]["renewal"] + 2,
    });
  }

  /**
   * Set action (like_reset, superlike_reset...).
   */
  private setAction(action): void {
    switch (this.type) {
      case "superlike":
        this.action = action + "_reset";
        break;
      case "like":
      case "pass":
      case "skip":
        this.action = "like_reset";
        break;
    }
  }
}
