import { Injectable } from "@angular/core";
import { ApiParams } from "./api/api.params";
import { Observable } from "rxjs";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { ApiCommands } from "./api/api.commands";
import { Debug } from "../helpers/debug";
import { tap } from "rxjs/operators";
import { ChatMessageService } from "./chat-message.service";
import { AccountService } from "./account.service";

@Injectable({
  providedIn: "root",
})
export class PictureService {
  /**
   * Is subscribed to upload event?
   */
  private isSubscribe = false;

  /**
   * Is upload in progress?
   */
  public isInProgress: boolean = false;

  /**
   * Progress upload percentage.
   * From 0 to 100
   */
  public percentage: number = 0;

  constructor(
    private api: ApiService,
    public debug: Debug,
    public chatMessagesService: ChatMessageService,
    private accountService: AccountService,
  ) {
    this.uploadInProgress();
  }

  /**
   * Rotate picture
   *
   * @param {ApiParams.PictureRotate} params
   * @returns {Promise<ApiResponses.GalleryImageResponse>}
   */
  public rotate(
    params: ApiParams.PictureRotate.type,
  ): Observable<ApiResponses.ResponseBase.type> {
    return this.api.call<ApiResponses.ResponseBase.type>({
      command: ApiCommands.PictureRotate.name,
      useToken: true,
      method: "post",
      params: params,
    });
  }

  /**
   * Crop picture
   *
   * @param {ApiParams.PictureCrop} params
   * @returns {Promise<ApiResponses.GalleryImageResponse>}
   */
  public crop(
    params: ApiParams.PictureCrop.type,
  ): Observable<ApiResponses.ResponseBase.type> {
    return this.api.call<ApiResponses.ResponseBase.type>({
      command: ApiCommands.PictureCrop.name,
      useToken: true,
      method: "post",
      params: params,
    });
  }

  public isPhotoUploading(): boolean {
    return (
      this.api.isInEventList({
        command: ApiCommands.PictureUpload.name,
      }) ||
      this.api.isInEventList({
        command: ApiCommands.PictureUploadAttachment.name,
      })
    );
  }

  /**
   * Upload a picture to user’s gallery.
   *
   * @param {ApiParams.PictureUpload} params
   * @returns {Promise<ApiResponses.GalleryImageResponse>}
   */
  public upload(
    params: ApiParams.PictureUpload.type,
  ): Observable<ApiResponses.GalleryImageResponse.type> {
    return this.api.call<ApiResponses.GalleryImageResponse.type>({
      command: ApiCommands.PictureUpload.name,
      useToken: true,
      method: "post",
      params: params,
      reportProgress: true,
    });
  }

  /**
   * Upload a picture to user’s attachments.
   *
   * @param {ApiParams.PictureUploadAttachment} params
   * @returns {Promise<ApiResponses.GalleryImageResponse>}
   */
  public uploadAttachment(
    params: ApiParams.PictureUploadAttachment.type,
  ): Observable<ApiResponses.GalleryImageResponse.type> {
    return this.api
      .call<ApiResponses.GalleryImageResponse.type>({
        command: ApiCommands.PictureUploadAttachment.name,
        useToken: true,
        method: "post",
        params: params,
        reportProgress: true,
      })
      .pipe(
        tap(async (data) =>
          this.chatMessagesService.attachImages.push(data.data.photo),
        ),
      );
  }

  /**
   * Delete a picture from user’s gallery.
   *
   * @param {ApiParams.PictureDelete} params
   * @returns {Promise<ApiResponses.NoDataResponse>}
   */
  public delete(
    params: ApiParams.PictureDelete.type,
  ): Observable<ApiResponses.PictureDeleteResponse.type> {
    return this.api.call<ApiResponses.PictureDeleteResponse.type>({
      command: ApiCommands.PictureDelete.name,
      useToken: true,
      method: "delete",
      params: params,
      commandVersion: "2",
    });
  }

  /**
   * Delete a picture from user’s attachment.
   *
   * @param {ApiParams.PictureDeleteAttachment} params
   * @returns {Promise<ApiResponses.NoDataResponse>}
   */
  public deleteAttachment(
    params: ApiParams.PictureDeleteAttachment.type,
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api.call<ApiResponses.NoDataResponse.type>({
      command: ApiCommands.PictureDeleteAttachment.name,
      useToken: true,
      method: "delete",
      params: params,
    });
  }

  /**
   * Set user’s avatar.
   *
   * @param {ApiParams.PictureSetAvatar} params
   * @returns {Promise<ApiResponses.NoDataResponse>}
   */
  public setAvatar(
    params: ApiParams.PictureSetAvatar.type,
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api
      .call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.PictureSetAvatar.name,
        useToken: true,
        method: "post",
        params: params,
      })
      .pipe(
        tap((data) => {
          this.accountService.account.photos.forEach((photo, i) => {
            photo.main_photo = photo.image_id === params.image_id;
            if (photo.main_photo) {
              this.accountService.account.photos[i].main_photo = true;
              this.accountService.account.avatar_url = photo.image_url;
            } else {
              this.accountService.account.photos[i].main_photo = false;
            }
          });
        }),
      );
  }

  /**
   * Set access.
   *
   * @param {ApiParams.PictureSetAccessParams} params
   * @return {Promise<ApiResponses.NoDataResponse>}
   */
  public setAccess(
    params: ApiParams.PictureSetAccessParams.type,
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api.call<ApiResponses.NoDataResponse.type>({
      command: ApiCommands.PictureSetAccess.name,
      useToken: true,
      method: "post",
      params: params,
    });
  }

  /**
   * Subscribe to upload progress event.
   */
  public uploadInProgress() {
    if (this.isSubscribe) return;

    this.api.eventStream$.subscribe({
      next: (data) => {
        if (
          data.eventType == "UploadProgress" &&
          data.method === "post" &&
          (data.command === ApiCommands.PictureUpload.name ||
            data.command === ApiCommands.PictureUploadAttachment.name)
        ) {
          this.debug.l("progress", data.data);
          this.percentage = data?.data["percentage"] || 0;
          this.isInProgress = this.percentage > 0 && this.percentage < 100;
        }
      },
    });

    this.isSubscribe = true;
  }
}
