import { TestBed } from "@angular/core/testing";

import { AuthService } from "./auth.service";
import { ApiService } from "./api/api.service";

describe("AuthService", () => {
  let service: AuthService;
  let apiSpy: ApiService;

  beforeEach(() => {
    apiSpy = jasmine.createSpyObj("ApiService", ["call"]);

    TestBed.configureTestingModule({
      providers: [{ provide: ApiService, useValue: apiSpy }],
    });
    service = TestBed.inject(AuthService);
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });
});
