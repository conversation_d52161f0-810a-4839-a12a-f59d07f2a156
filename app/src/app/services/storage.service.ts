import { Injectable } from "@angular/core";
import { Drivers, Storage } from "@ionic/storage";
import { Utils } from "../helpers/utils";

@Injectable({
  providedIn: "root",
})
export class StorageService {
  /**
   * Storage.
   */
  private _storage: Storage | null = null;

  /**
   * Constructor.
   */
  constructor(private storage: Storage) {}

  /**
   * Init storage.
   */
  async init() {
    const storageObject = new Storage({
      name: Utils.getAppId("db_"),
      driverOrder: [Drivers.IndexedDB, Drivers.LocalStorage],
      storeName: Utils.getAppId("db_"),
    });

    this._storage = await storageObject.create();
  }

  /**
   * Set storage.
   *
   * @param key
   * @param value
   */
  public set(key: string, value: any): Promise<any> {
    return this._storage.set(key, value);
  }

  /**
   * Get storage.
   *
   * @param key
   */
  public get(key: string): Promise<any> {
    return this._storage.get(key);
  }

  /**
   * Remove from storage.
   *
   * @param key
   */
  public remove(key: string): Promise<any> {
    return this._storage.remove(key);
  }
}
