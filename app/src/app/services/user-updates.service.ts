import { Injectable } from "@angular/core";
import { firstValueFrom, Observable } from "rxjs";
import { tap } from "rxjs/operators";
import { Debug } from "../helpers/debug";
import { ApiCommands } from "./api/api.commands";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { UserUpdatesServiceItemList } from "./lists/user-updates.service.list";

@Injectable({
  providedIn: "root",
})
export class UserUpdatesService {
  // @note right now it seems that limit param is ignored for user updates
  //       so it's paginated to 10 items always

  constructor(private api: ApiService, private debug: Debug) {}

  // update list
  public listUpdates: UserUpdatesServiceItemList =
    new UserUpdatesServiceItemList(
      this.api,
      ApiCommands.UsersUpdatesGetUpdates,
      this.debug
    );

  /**
   * Mark all updates as read
   */
  public markAllUpdatesRead(): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.UsersUpdatesMarkAllUpdateRead.name,
        method: "get",
        useToken: true,
      })
    );
  }
}
