import { TestBed } from "@angular/core/testing";

import { ProfileService } from "./profile.service";
import { ApiService } from "./api/api.service";

describe("ProfileService", () => {
  let service: ProfileService;
  let apiSpy: ApiService;

  beforeEach(() => {
    apiSpy = jasmine.createSpyObj("ApiService", ["call"]);

    TestBed.configureTestingModule({
      providers: [{ provide: ApiService, useValue: apiSpy }],
    });
    service = TestBed.inject(ProfileService);
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });
});
