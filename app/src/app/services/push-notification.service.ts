import { Injectable, <PERSON><PERSON>one } from "@angular/core";
import { Router } from "@angular/router";
import { App } from "@capacitor/app";
import { Capacitor } from "@capacitor/core";
import {
  ActionPerformed,
  PushNotifications,
  PushNotificationSchema,
  Token,
} from "@capacitor/push-notifications";
import _ from "lodash";
import { TME_push_notification_open } from "../classes/gtm-event.class";
import { Debug } from "../helpers/debug";
import { AccountService } from "./account.service";
import { AnalyticsService } from "./analytics.service";
import { IBootAction } from "./boot.service";
import { DialogHelperService } from "./dialog-helper.service";
import { MiscService } from "./misc.service";
import { UtilsService } from "./utils.service";
import { PurchaseService } from "./purchase.service";
import {
  AndroidSettings,
  IOSSettings,
  NativeSettings,
} from "capacitor-native-settings";
import { StorageService } from "./storage.service";
import { BehaviorSubject, Observable } from "rxjs";
import { IntroductionService } from "./introduction.service";

export interface PushNotificationData {
  value?: string; // depending on the type, if route, then it's a redirection route, if custom, then we have custom handler ( like we do something )
  type?: "route" | "custom"; // these are custom handled commands, not routes
}

@Injectable({
  providedIn: "root",
})
export class PushNotificationService implements IBootAction {
  constructor(
    private debug: Debug,
    private account: AccountService,
    private utilsService: UtilsService,
    private router: Router,
    private zone: NgZone,
    private dialog: DialogHelperService,
    private misc: MiscService,
    private analytics: AnalyticsService,
    private purchaseService: PurchaseService,
    private storage: StorageService,
    private introduction: IntroductionService,
  ) {}

  public boot(): Promise<any> {
    // just initialize for now, and return promise resolve - we don't need to do anything else
    this.init();

    return Promise.resolve(null);
  }

  public text: string = "";

  /**
   * PushNotifications observable.
   */
  private _wasPushNotification$: BehaviorSubject<boolean> = new BehaviorSubject(
    null,
  );

  public get wasPushNotification$(): Observable<boolean> {
    return this._wasPushNotification$.asObservable();
  }

  public LatestPushData: PushNotificationSchema = null;

  /**
   * Handle notification
   * @param notification
   */
  public handleNotification(notification: PushNotificationSchema) {
    this.analytics.addMarker("push handler", notification);

    this.LatestPushData = notification;

    this._wasPushNotification$.next(true);
    let data = notification?.data;

    this.debug.l("Handle notification", [notification, data]);

    // send analytics data when push notification clicked

    let statsTitle = data && (data["notification_type"] || data["value"]);
    statsTitle = statsTitle || notification["title"] || "N/A";

    this.analytics.logEvent("push_notification_open", <
      TME_push_notification_open
    >{
      message_title: statsTitle,
    });

    // execute action

    // @note type custom variable HAS to be defined in DatingVIP template
    //      otherwise it will not work, because it will send the click_action param
    //      and that crashes the app ( basically it tells the app which activity to open )
    //      in order for that to work, you need to add it to manifest file, but we don't need that

    switch (data?.type) {
      // route navigation with notification data

      case "route":
        if (data?.value) {
          // @todo see if we can open this right away, or we have to delay this if the bootstrap is not done
          //       see what's happening if the user is not logged in and we run bootstrap after login - or session recovery
          // @todo on iphone the page is not opened, we need a way to delay this
          //       probably put it in a value, and then execute it when app is ready

          this.debug.l("navigate");

          this.misc.executeWhenAppActive(
            () => {
              // we need the zone refresh, because otherwise some bindings don't work

              this.zone.run(async () => {
                this.introduction.doShow = false;
                //await this.router.navigate(['/app']);
                await this.router.navigate([data?.value]).catch((error) => {
                  this.debug.le("error navigating from notification", error);
                });
              });

              // we need the delay in execution because on older phones angular bindings get fucked up
              // preventing chat page from working properly 😶
            },
            1000,
            500,
          );

          /*
            setTimeout(()=>{
              this.router
                .navigate([data?.value])
                .catch((error) => {
                  this.debug.le("error navigating from notification", error);
                })
                .then((data) => {
                  this.debug.l("push notif navigation done", data);
                });
            }, 500);
            */
        }

        // @todo should we remove this?
        // should we reset badge count?
        PushNotifications.removeAllDeliveredNotifications();

        break;

      // @todo: add custom handlers, for example upgrade page

      case "custom":
        this.introduction.doShow = false;
        if (data?.value == "promo") {
          // Promo offer
          // {"type":"custom", "value":"promo", "campaign":"campaign"}

          let campaign_name = data?.campaign || "none";

          this.purchaseService
            .setPromoDialogType("default")
            .openCampaignDialog(campaign_name, this.errorOccured, () => {
              this.debug.l("try premium cancelled");
            });
        }

        if (data?.value == "discounted") {
          // {"type":"custom", "value":"discounted", "campaign":"campaign"}

          let campaign_name = data?.campaign || "none";

          this.purchaseService.openDiscountedDialog(
            () => {},
            () => {},
          );
        }

        break;
    }
  }

  private async errorOccured(error) {
    await this.dialog.hideLoading();

    if (error?.["doDisplay"] !== false) {
      this.dialog.showToastBaseOnErrorResponse(
        "purchase_error",
        this.utilsService.figureOutTheErrorMessage(error),
      );
    }
  }

  private _endpoint: string;
  private _isRegistered: boolean = false;

  /**
   * On push entity registration
   * @param data
   */
  private onRegistration(data: Token) {
    this.debug.l("Push notification registration", data);

    // set data endpoint for later use

    // save endpoint
    this._endpoint = data?.value ?? "";

    // update endpoint when account data is loaded
    this.account.accountChanges$.subscribe({
      next: () => {
        this.updateEndpoint();
      },
    });

    /*
    this.account.updatePushEndpoint(data.value).catch((error) => {
      this.debug.le("Push token update failed", error);
    });
    */
  }

  public updateEndpoint() {
    // if we don't have the necessare data, then don't send it to the server
    if (
      !this._endpoint ||
      !this?.account?.account?.user_id ||
      this._isRegistered
    ) {
      this.debug.l("Push notification: no endpoint or user id");
      return;
    }

    this.account
      .updatePushFirebaseEndpoint(
        this._endpoint,
        this?.account?.account?.user_id,
      )
      .then((data) => {
        this._isRegistered = true;
        this.debug.l("Push endpoint updated", data);
      })
      .catch((error) => {
        this.debug.le("Push token update failed", error);
      });
  }

  public async onPushNotificationReceived(
    notification: PushNotificationSchema,
  ) {
    // note: it can happen that the phone will display notification, but
    //       the callback will not register since the app is not running

    console.log("received", notification);

    let id = notification?.data?.id;

    if (id) {
      this.utilsService
        .notificationDisplayed(id, notification)
        .catch((error) => {
          this.debug.le("Error sending notif received event", error);
        });
    } else {
      this.debug.le("Push notification received, but no id", notification);
    }

    let { isActive } = await App.getState();

    if (isActive) {
      // display notification in app
      this.dialog.clickableToast(
        notification.title,
        notification.body,
        null,
        "💌",
        () => {
          let action: ActionPerformed = {
            actionId: "in_app_click",
            notification: notification,
          };

          this.onPushNotificationAction(action);
        },
      );
    }
  }

  public onPushNotificationAction(action: ActionPerformed) {
    // todo: check what's happening if the app is not active and we click? will the app get any
    //       info about the notification?

    console.log("action", action);

    let id = action?.notification?.data?.id;

    if (id) {
      this.utilsService.notificationClicked(id, action).catch((error) => {
        this.debug.le("Error sending notif clicked event", error);
      });
    } else {
      this.debug.le("Push notification action, but no id", action);
    }

    // open link if there's any

    this.handleNotification(action.notification);
  }

  private async setListeners() {
    // remove listeners if they're set
    await PushNotifications.removeAllListeners();

    // add push notification listeners

    await PushNotifications.addListener("registration", (data) => {
      this.onRegistration(data);
    });

    await PushNotifications.addListener(
      "pushNotificationReceived",
      (notification) => {
        this.onPushNotificationReceived(notification);
      },
    );

    await PushNotifications.addListener(
      "pushNotificationActionPerformed",
      (action) => {
        this.onPushNotificationAction(action);
      },
    );

    await PushNotifications.addListener("registrationError", (error) => {
      this.debug.le("Push registration error", error);
    });

    if (this.actionPerformedCache) {
      // if we had a cached version, then handle it
      this.handleNotification(_.clone(this.actionPerformedCache.notification));
      this.actionPerformedCache = null;
    }
  }

  private _isInit: boolean = false;

  public actionPerformedCache: ActionPerformed = null;

  private _sessionHash: string = "";

  public get sessionHash() {
    return this._sessionHash;
  }

  private preInitDone: boolean = false;

  /**
   * Get initialization state
   *
   * yes          - can/should be initialized
   * need_prompt  - user will get prompt when initializing
   * no           - cannot be initialized ( probably declined )
   *
   * @returns
   */
  public async canInitialize(): Promise<"yes" | "need_prompt" | "no"> {
    if (!Capacitor.isNativePlatform()) {
      // it's not native platform
      return Promise.resolve("no");
    }

    let _result = await PushNotifications.checkPermissions();

    if (_result.receive == "granted") {
      return Promise.resolve("yes");
    }

    if (
      _result.receive == "prompt" ||
      _result.receive == "prompt-with-rationale"
    ) {
      return Promise.resolve("need_prompt");
    }

    return Promise.resolve("no");
  }

  public setPreInitDone() {
    this.preInitDone = true;
  }

  public async preInit() {
    // cannot be initialized, no permission to push
    if ((await this.canInitialize()) != "yes") {
      return;
    }

    // check if we have permission for push
    // if yes then add a listener here
    // if listener has a hash, then set that hash in auth class for session restore

    try {
      let data = await PushNotifications.requestPermissions();

      if (data.receive == "granted") {
        // permission granted, we're good, let's register the device
        let data = await PushNotifications.register();
        this.debug.l("Push register done", data);

        await PushNotifications.addListener(
          "pushNotificationActionPerformed",
          (nData) => {
            this.actionPerformedCache = nData;

            // if it's initialized don't do it
            if (this._isInit) {
              console.log("! preinit registration ignored");
              return;
            }

            // save session hash for later use
            if (nData.notification.data["h"]) {
              this._sessionHash = nData.notification.data["h"];
              this.debug.l(
                "Set push notification session hash",
                this._sessionHash,
              );
            }

            if (this.preInitDone) {
              // the user clicked on push, but the app is on login page ( so no initial boot )
              // so the user is not logged in - in this case user clicked on push
              // but we have no session - we need to initialize login
              this.CallbackNoSession(this.sessionHash);
            }
          },
        );
      }
    } catch (error) {
      this.debug.le("pre init - Push notification registration error", error);
    }

    // delayed resolve, we give some time to set the session restore hash, otherwise the auth will
    // unfortunately there's no better way because we don't know if we get a push
    // untile the upper pushNotificationActionPerformed event is fired

    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, 300);
    });
  }

  public CallbackNoSession: Function = () => {};

  public async init() {
    this._isInit = true;

    if (!Capacitor.isNativePlatform()) {
      // if we're running this in a borwer, we want to avoid error messages, so ignore init

      this.debug.log({
        message: "Push notification disabled, not a native platform",
        severity: "warning",
      });

      return;
    }

    // @todo check if we have permission, if not open the dialog and ask user
    //       if user dismisses, then save flag, and don't show it again

    // set listener functions
    await this.setListeners();

    try {
      let data = await PushNotifications.requestPermissions();
      this.analytics.logEvent("opt_in_popup", { trigger: "init" });
      this.debug.l("push registration", data);

      /*
      granted: Every permission in this alias has been granted by the end user (or prompting is not necessary).
      denied: One or more permissions in this alias have been denied by the end user.
      prompt: The end user should be prompted for permission, because it has neither been granted nor denied.
      prompt-with-rationale: The end user has denied permission before, but has not blocked the prompt yet.
      */

      // 'prompt' | 'prompt-with-rationale' | 'granted' | 'denied'
      // @todo see which on is which
      // data.receive == ""

      if (data.receive == "granted") {
        // permission granted, we're good, let's register the device
        let data = await PushNotifications.register();
        this.analytics.logEvent("opt_in_accept", { trigger: "init" });
        this.debug.l("Push register done", data);
      } else {
        // permission denied
        // - see if user told us not to nag
        // - if not, show a dialog to user explain why we need notif
        //   and explain how to re-enable - button to open settings
      }
    } catch (error) {
      this.debug.le("Push notification registration error", error);
    }
  }

  public showNotificationPopup: boolean = false;

  /**
   * Helper function for conditional initialization
   *
   * @returns
   */
  public async conditionalInitialization() {
    // Show only on iOS devices.

    // debug
    // this.showNotificationPopup = true;

    if (!this.misc.isNativePlatform()) return;

    if (this.misc.devicePlatform !== "ios") {
      // initialize push for android
      this.init();
      return;
    }

    // we have permission on ios, no need to show the popup
    if ((await this.canInitialize()) == "yes") {
      this.init();
      return;
    }

    // see if we need to display popup for ios
    this.storage
      .get("pushNotificationPopup")
      .then((res) => {
        if (!res) return (this.showNotificationPopup = true);
        if (res.action === "cancel" && this.checkTime(res.time))
          this.showNotificationPopup = true;
      })
      .catch((err) => this.debug.le(" get from storage error", err));
  }

  /**
   * Popover onCancel action.
   */
  public onDialogCancel(): void {
    this.storage.set("pushNotificationPopup", {
      action: "cancel",
      time: this.time(),
    });
  }

  /**
   * Popover onSuccess action.
   */
  public async onDialogSuccess() {
    this.storage.set("pushNotificationPopup", {
      action: "ok",
      time: this.time(),
    });

    // if we need prompt then we open it ( initialize and ask for permission with init )
    if ((await this.canInitialize()) == "need_prompt") {
      this.init();
    }

    // if user did not grant permission, then we open settings
    if ((await this.canInitialize()) == "no") {
      NativeSettings.open({
        optionAndroid: AndroidSettings.ApplicationDetails,
        optionIOS: IOSSettings.App,
      });
    }
  }

  /**
   * Get current timestamp.
   *
   * @returns {number}
   */
  private time(): number {
    return Date.now();
  }

  /**
   * Time to show popup dialog?
   *
   * @returns {boolean}
   */
  private checkTime(time): boolean {
    let date = new Date(time);
    return this.time() >= date.setDate(date.getDate() + 7);
  }
}
