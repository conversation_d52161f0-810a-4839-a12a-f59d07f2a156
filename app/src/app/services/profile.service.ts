import { Injectable } from "@angular/core";
import { Observable, of, throwError } from "rxjs";
import { catchError, map, tap } from "rxjs/operators";
import {
  ProfileUser,
  SuperLikeStatus,
  UserRelationshipType,
} from "../schemas/profile-user";
import { ApiCommandItem, ApiCommands } from "./api/api.commands";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { ProfileServiceItemList } from "./lists/profile.service.item-list";
import { ProfileServiceBrowseItemList } from "./lists/profile.service.browse.item-list";
import { Debug } from "../helpers/debug";
import { Router } from "@angular/router";
import { StateService } from "./state.service";
import _ from "lodash";
import { ProfileBase } from "../schemas/profile-base";
import { BehaviorSubject } from "rxjs";
import { DialogHelperService } from "./dialog-helper.service";
import { TranslateService } from "@ngx-translate/core";
import { AnalyticsService } from "./analytics.service";
import { TME_rules_popup_display } from "../classes/gtm-event.class";
import { StorageService } from "./storage.service";

export const VIEWED_ME = "viewed_me_";

@Injectable({
  providedIn: "root",
})
export class ProfileService {
  public tmpUserProfileData: ProfileUser.type;
  public listWeLiked: ProfileServiceItemList = new ProfileServiceItemList(
    this.api,
    ApiCommands.ProfileWeLiked,
    this.debug,
  );

  // lists
  public listLikedUs: ProfileServiceItemList = new ProfileServiceItemList(
    this.api,
    ApiCommands.ProfileLikedUs,
    this.debug,
  );

  public listMatches: ProfileServiceItemList = new ProfileServiceItemList(
    this.api,
    ApiCommands.ProfileMatches,
    this.debug,
  );

  public listBlocked: ProfileServiceItemList = new ProfileServiceItemList(
    this.api,
    ApiCommands.ProfileWeBlocked,
    this.debug,
  );

  public listIViewed: ProfileServiceItemList = new ProfileServiceItemList(
    this.api,
    ApiCommands.ListIViewed,
    this.debug,
  );

  public listViewedMe: ProfileServiceItemList = new ProfileServiceItemList(
    this.api,
    ApiCommands.ListViewedMe,
    this.debug,
  );

  // @todo add list here
  public listSuggestions = null;

  /*
  public listSuggestions: ProfileServiceSuggestionsItemList =
    new ProfileServiceSuggestionsItemList(
      this.api,
      ApiCommands.ProfileSuggestions,
      this.debug
    );
    */

  public listBrowse: ProfileServiceBrowseItemList =
    new ProfileServiceBrowseItemList(
      this.api,
      ApiCommands.BrowseNearby,
      this.debug,
    );

  public doShowRulesPopup: boolean = false;

  public viewedMeBadge: number = 0;
  public viewedMeBadgeTotal: number = 0;

  constructor(
    private api: ApiService,
    private debug: Debug,
    public router: Router,
    public stateService: StateService,
    private dialog: DialogHelperService,
    private translationSrvice: TranslateService,
    private analytics: AnalyticsService,
    private storage: StorageService,
  ) {}

  /**
   * Tells if a user is a system user ( user without a profile )
   * @note we have another function in profile-base.ts which does the same thing, but we don't have the
   *       id here, so we need one that can determine the same thing with username
   * @param username username or user id
   */
  public isKnownSystemUser(username: string) {
    let knownSystemUsers = ["match_maker", "100", "system"];
    return knownSystemUsers.indexOf(username) > -1;
  }

  /**
   * Pass profile ( dislike )
   */
  public simpleUnlike(
    username: string,
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api
      .call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.ProfileUnLikeSimple.name,
        method: "post",
        params: <ApiParams.ProfileMark.type>{ username: username },
        useToken: true,
        identifier: username,
        commandVersion: "2",
      })
      .pipe(
        tap(() => {
          // remove from every list
          this.removeFromLists(username, [
            this.listLikedUs,
            this.listWeLiked,
            this.listMatches,
          ]);
        }),
        catchError(({ error }) => {
          this.handleError(error);
          //do your processing here
          return throwError(error);
        }),
      );
  }

  /**
   * Like profile
   */
  public simpleLike(
    username: string,
  ): Observable<ApiResponses.ProfileLikeResponse.type> {
    return this.api
      .call<ApiResponses.ProfileLikeResponse.type>({
        command: ApiCommands.ProfileLikeSimple.name,
        method: "post",
        params: <ApiParams.ProfileMark.type>{ username: username },
        useToken: true,
        identifier: username,
        commandVersion: "2",
      })
      .pipe(
        tap((response: ApiResponses.ProfileLikeResponse.type) => {
          this.processProfile(username, true, false);
        }),
        catchError(({ error }) => {
          console.log("ERR!", error);

          if (
            error?.errors &&
            error?.errors[0]?.key == "error_show_likes_rules_dialog"
          ) {
            // send analytics
            this.analytics.logEvent("rules_popup_display", <
              TME_rules_popup_display
            >{
              popup_type: "like_dialog",
            });

            // @todo display proper dialog
            // - when added don't forget to add proper analytics events for interaction
            error.errors = error.errors?.filter(
              (err) => err.key !== "error_show_likes_rules_dialog",
            );
            this.doShowRulesPopup = true;

            return;
          }

          // error_show_likes_rules_dialog

          this.handleError(error);
          //do your processing here
          return throwError(error);
        }),
      );
  }

  /**
   * Like profile
   */
  public like(
    username: string,
  ): Observable<ApiResponses.ProfileLikeResponse.type> {
    return this.api
      .call<ApiResponses.ProfileLikeResponse.type>({
        command: ApiCommands.ProfileLike.name,
        method: "post",
        params: <ApiParams.ProfileMark.type>{ username: username },
        useToken: true,
        identifier: username,
      })
      .pipe(
        tap((response: ApiResponses.ProfileLikeResponse.type) => {
          let wasMatch = !!response?.data?.was_match;

          this.processProfile(username, true, wasMatch);

          // todo fetch full profile here, and add it to
          // a) liked list
          // b) if there was isMatch=true in the result, add to match list
          // c) remove it from other lists
          // d) somehow broadcast that there was a new match -- where do this belong?
        }),
        catchError(({ error }) => {
          this.handleError(error);
          //do your processing here
          return throwError(error);
        }),
      );
  }

  /**
   * Superlike profile
   */
  public superLike(
    username: string,
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api
      .call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.ProfileSuperlike.name,
        method: "post",
        params: <ApiParams.ProfileMark.type>{ username: username },
        useToken: true,
        identifier: username,
      })
      .pipe(
        tap((res) => {
          // add stuff to lists
          this.processProfile(username, true, true);
        }),
        catchError(({ error }) => {
          this.handleError(error);
          //do your processing here
          return throwError(error);
        }),
      );
  }

  private _wasError$: BehaviorSubject<any> = new BehaviorSubject(null);

  get wasError(): Observable<any> {
    return this._wasError$.asObservable();
  }

  private handleError(error) {
    if (!error.errors.length) return;

    this._wasError$.error(error);
  }

  // add broswe function here
  // @todo
  // BROWSE SERVICE ( old one ) GOES HERE !!!
  // add logic, because we have one list here, but use multiple api calls, so
  // maybe add another param, or make it so we can pass a list of api calls, something
  // public listBrowse: ProfileServiceItemList = new ProfileServiceItemList(this.api, ApiCommands.BrowseAll);

  /**
   * Pass profile ( dislike )
   */
  public pass(username: string): Observable<ApiResponses.NoDataResponse.type> {
    return this.api
      .call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.ProfilePass.name,
        method: "post",
        params: <ApiParams.ProfileMark.type>{ username: username },
        useToken: true,
        identifier: username,
      })
      .pipe(
        tap(() => {
          // remove from every list
          this.removeFromLists(username, [
            this.listLikedUs,
            this.listWeLiked,
            this.listMatches,
          ]);
        }),
        catchError(({ error }) => {
          this.handleError(error);
          //do your processing here
          return throwError(error);
        }),
      );
  }

  /**
   * Skip giver profile
   * @param username
   */
  public skip(username: string): Observable<ApiResponses.NoDataResponse.type> {
    return this._removeAction(
      { username: username },
      ApiCommands.ProfileSkip,
    ).pipe(
      catchError(({ error }) => {
        this.handleError(error);
        //do your processing here
        return throwError(error);
      }),
    );
  }

  /**
   * Unmatch given profile
   * @param username
   */
  public unmatch(
    username: string,
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this._removeAction(
      { username: username },
      ApiCommands.ProfileUnmatch,
    );
  }

  /**
   * Block given profile
   * @param username
   */
  public block(
    profile: ProfileBase.type,
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api
      .call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.ProfileBlock.name,
        method: "post",
        params: <ApiParams.ProfileGet.type>{
          username: profile.username,
        },
        useToken: true,
        identifier: profile.username,
      })
      .pipe(
        tap(() => {
          this.listBlocked.addItem(profile);
        }),
      );
  }

  /**
   * Unblock given profile
   * @param username
   */
  public unblock(
    username: string,
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api
      .call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.ProfileUnblock.name,
        method: "post",
        params: <ApiParams.ProfileGet.type>{
          username: username,
        },
        useToken: true,
        identifier: username,
        commandVersion: "2",
      })
      .pipe(
        tap(() => {
          this.listBlocked.removeItems({ username: username });
        }),
      );
  }

  /**
   * Report given profile
   * @param params ApiParams.ProfileReport.type
   */
  public report(
    params: ApiParams.ProfileReport.type,
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this._removeAction(params, ApiCommands.ProfileReport);
  }

  /**
   * Is user fetch in progress with givern username?
   * @param username
   * @returns
   */
  public isGetInProgress(username: string) {
    return this.api.isInEventList({
      command: ApiCommands.ProfileGet.name,
      identifier: username,
    });
  }

  /**
   * Was error loading a specific user profile?
   * @param username
   * @returns
   */
  public wasGetError(username: string) {
    return this.api.isInErrorList({
      command: ApiCommands.ProfileGet.name,
      identifier: username,
    });
  }

  /**
   * Get profile data
   *
   * @param username username to get
   * @param overWriteSystem object, overwrites system user the result partially if needed
   */
  public get(
    username: string,
    overWriteSystem: object = null,
    track_visits: number = 0,
  ): Observable<ApiResponses.ProfileBaseResponse.type> {
    // return a fulfilled observable if it's a known system user
    if (this.isKnownSystemUser(username)) {
      let response = ApiResponses.ProfileBaseResponse.schema.cast(
        {},
        {
          assert: false,
        },
      );
      response.data.username = username;
      response.data.user_id = 100;

      if (overWriteSystem) {
        response.data = _.merge(response.data, overWriteSystem);
      }

      return of(response);
    }
    if (track_visits != 0 && track_visits != 1) {
      track_visits = 0;
    }

    // get it from server if not
    return this.api.call<ApiResponses.ProfileBaseResponse.type>({
      command: ApiCommands.ProfileGet.name,
      method: "get",
      params: <ApiParams.ProfileGet.type>{
        username: username,
        "track-visit": track_visits,
      },
      useToken: true,
      identifier: username,
    });
  }

  /**
   * Get full main photo url from photos array
   * @param profile
   * @returns
   */
  public getMainPhoto(profile: ProfileBase.type) {
    let _img = profile.avatar_url;

    profile.photos.forEach((photo) => {
      if (photo.main_photo) {
        _img = photo.image_url;
      }
    });

    return _img;
  }

  /**
   * Request photo from user
   */
  public requestPhoto(
    username: string,
  ): Observable<ApiResponses.ResponseBase.type> {
    return this.api.call<ApiResponses.ResponseBase.type>({
      command: ApiCommands.ProfilePhotoRequest.name,
      method: "post",
      useToken: true,
      params: <ApiParams.ProfilePhotoRequest.type>{
        username: username,
      },
    });
  }

  /**
   * Progress check (pc)
   * helper function to check if a command is in progress
   */
  public pc(username: string, command: ApiCommandItem) {
    return this.api.isInEventList({
      identifier: username,
      command: command.name,
    });
  }

  /**
   * Remove profiles from all lists based on a filter
   * @param filter
   */
  private removeFromLists(username: string, lists: ProfileServiceItemList[]) {
    lists.forEach((l) => {
      l.removeItems({ username: username });
    });
  }

  /**
   * Fetch profile, and add it to appropriate lists
   *
   * @param username
   * @param doAddToTop
   * @param doForceMatch
   */
  public processProfile(
    username: string,
    doAddToTop: boolean = false,
    doForceMatch: boolean = false,
    doOpenMatchPage: boolean = true,
  ) {
    this.get(username).subscribe({
      next: async (result) => {
        let profile = result.data;

        if (doForceMatch) {
          profile.relationship = UserRelationshipType.match;
        }

        /**
         * UserRelationshipType descriptions:
         *
         * likes     - If logged in user liked given user(from input list of users) - "weLiked"
         * liked     - If given user liked logged in user - "likedUs"
         * dislikes  - If logged in user disliked given user(from input list of users) - "not in contacts list"
         * disliked  - If given user disliked logged in user - in this case, we have 2 options:
         *                  1. the profile is not in the contacts list,
         *                  2. the profile is in the likedUs list
         * superlike - If given or logged in user superliked the other - "matches"
         * match     - If both logged in and given user liked eachother - "matches"
         * ''        - No status is presented with empty string - "not in contacts list"
         */

        // export enum UserRelationshipType {"", "superlike", "likes", "dislikes", "liked", "disliked", "match"}
        // if(['likes'] profile.relationship)

        // if liked then goes to we liked list
        // if likes then goes to liked us

        if (
          [
            UserRelationshipType.disliked,
            UserRelationshipType.dislikes,
            "",
          ].indexOf(<UserRelationshipType>profile.relationship) >= 0
        ) {
          // remove profile from all the lists
          this.removeFromLists(profile.username, [
            this.listLikedUs,
            this.listWeLiked,
            this.listMatches,
          ]);
        }

        if (profile.relationship == UserRelationshipType.liked) {
          // add user to likedUs list, remove from other lists
          this.removeFromLists(profile.username, [
            this.listWeLiked,
            this.listMatches,
          ]);

          this.listLikedUs.addItem(profile, doAddToTop);
        }

        if (profile.relationship == UserRelationshipType.likes) {
          // add user to we liked list, remove from other lists
          this.removeFromLists(profile.username, [
            this.listLikedUs,
            this.listMatches,
          ]);

          this.listWeLiked.addItem(profile, doAddToTop);
        }

        if (
          [UserRelationshipType.match, UserRelationshipType.superlike].indexOf(
            <UserRelationshipType>profile.relationship,
          ) >= 0
        ) {
          // add user to matches then ...
          this.listMatches.addItem(profile, doAddToTop);

          // if we have superlike status, then we handle it differently

          if (
            [SuperLikeStatus.superliked_you, ""].indexOf(
              profile.superlike_status,
            ) >= 0
          ) {
            // if we superliked the user, and we're a match then we add the user to we like list as well
            // (or if there was no superlike, because then it will be added to both lists )
            this.listLikedUs.addItem(profile, doAddToTop);
          } else {
            this.listLikedUs.removeItems({ username: profile.username });
          }

          if (
            [SuperLikeStatus.you_superliked, ""].indexOf(
              profile.superlike_status,
            ) >= 0
          ) {
            // if the user liked us, and we're a match then we add the user to liked us list as well
            // (or if there was no superlike, because then it will be added to both lists )
            this.listWeLiked.addItem(profile, doAddToTop);
          } else {
            this.listWeLiked.removeItems({ username: profile.username });
          }

          if (
            UserRelationshipType.match == profile.relationship &&
            doOpenMatchPage
          ) {
            this.stateService.set("match", profile.username, profile);
            await this.router.navigate(["/app/modals/match", profile.username]);
          }
        }

        this.debug.l("Profile data", [profile, this.listWeLiked["_storage"]]);
      },
    });
  }

  /**
   * Common implementation for remove actions
   */
  private _removeAction(
    params: object,
    apiCommand: ApiCommandItem,
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api
      .call<ApiResponses.NoDataResponse.type>({
        command: apiCommand.name,
        method: "post",
        params: params,
        useToken: true,
        identifier: params["username"],
      })
      .pipe(
        tap(() => {
          // remove from every list
          this.removeFromLists(params["username"], [
            this.listLikedUs,
            this.listWeLiked,
            this.listMatches,
          ]);
        }),
      );
  }

  public setViewedMeBadge(accountId: number): void {
    this.listViewedMe.list$.subscribe({
      next: (list) => {
        this.viewedMeBadgeTotal = list.length;

        this.storage
          .get(VIEWED_ME + accountId)
          .then((res) => {
            if (list.length >= res) this.viewedMeBadge = list.length - res;
          })
          .catch((err) => this.debug.le("viewed me not set", err));
      },
    });
  }

  public addToListIViewed(profile: ProfileUser.type): void {
    if (this.listIViewed.haveUsername(profile.username)) return;

    this.listIViewed.addItem(profile, true);
  }
}
