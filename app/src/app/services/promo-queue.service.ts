import { Injectable } from "@angular/core";
import { Debug } from "../helpers/debug";
import { PromoService } from "./promo.service";
import { QueueCommandService } from "./queue/queue-command.service";
import { QueueItem } from "./queue/queue-item.class";

@Injectable({
  providedIn: "root",
})
export class PromoQueueService extends QueueItem {
  constructor(
    private promoService: PromoService,
    private queueCommand: QueueCommandService,
    private dbg: Debug
  ) {
    super();
  }

  /**
   * Queue action
   */
  protected queueAction(): void {
    this.dbg.l("promo queue exec");

    this.promoService
      .loadPromoData()
      .then((data) => {
        this.dbg.l("promo queue exec 2", data);

        this.promoService
          .clearPromoData()
          .then(() => {
            this.dbg.l("promo queue exec 3", data);

            if (data?.app_promo_code) {
              this.promoService.handleAppsflyerPromo(data);
            } else {
              this.queueCommand.gotoNextItem();
            }
          })
          .catch((error) => {
            this.dbg.le("promo queue error", error);
            this.queueCommand.gotoNextItem();
          });
      })
      .catch((error) => {
        this.dbg.le("promo queue error", error);
        this.queueCommand.gotoNextItem();
      });
  }
}
