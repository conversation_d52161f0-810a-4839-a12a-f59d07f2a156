import { Injectable } from "@angular/core";
import { Debug } from "../helpers/debug";
import { AccountService } from "./account.service";
import { DeviceInfoService } from "./device-info.service";
import { DialogHelperService } from "./dialog-helper.service";
import { environment as Env } from "../../environments/environment";
import _ from "lodash";
import { TranslateService } from "@ngx-translate/core";
import { Utils } from "../helpers/utils";
import { PaymentService } from "./payment.service";
import { MiscService } from "./misc.service";
import { Router } from "@angular/router";
import { AnalyticsService } from "./analytics.service";
import { AppsflyerHelperService } from "./appsflyer-helper.service";
import {
  TME_purchase,
  TME_submit_upgrade_form,
  TME_upgrade_error,
} from "../classes/gtm-event.class";
import { UtilsService } from "./utils.service";
import { IBootAction } from "./boot.service";
import { IapItem } from "./iap-item.interface";
import { PromoDialogType, UpgradeDialogType } from "./upgrade-dialog.type";
import { IapPluginService } from "../plugins/iap-plugin.service";
import { RefresherService } from "./refresher.service";
import { StorageService } from "./storage.service";

export const DB_KEY_F_SUBSCRIPTION_LIST =
  "purchase_formatted_subscription_list";
export const DB_KEY_F_OFFER_LIST = "purchase_formatted_offer_list";
export const DB_KEY_F_CAMPAIGN_LIST = "purchase_formatted_campaign_list";
export const DB_KEY_F_DISCOUNT_LIST = "purchase_formatted_discount_list";

export const DB_KEY_SUBSCRIPTION_LIST = "purchase_subscription_list";
export const DB_KEY_OFFER_LIST = "purchase_offer_list";
export const DB_KEY_CAMPAIGN_LIST = "purchase_campaign_list";
export const DB_KEY_DISCOUNT_LIST = "purchase_discount_list";

export const DB_KEY_PURCHASE_DATA_APP_VERSION = "purchase_data_app_version";

@Injectable({
  providedIn: "root",
})
export class PurchaseService implements IBootAction {
  constructor(
    private accountService: AccountService,
    private deviceInfo: DeviceInfoService,
    private dialog: DialogHelperService,
    private debug: Debug,
    private iap: IapPluginService,
    private translateService: TranslateService,
    private payment: PaymentService,
    private misc: MiscService,
    private router: Router,
    private analytics: AnalyticsService,
    private appsflyer: AppsflyerHelperService,
    private utilsService: UtilsService,
    private refresherService: RefresherService,
    private storage: StorageService,
  ) {}

  /**
   * Boot interface implementation
   */
  public async boot(): Promise<void> {
    this.analytics.addMarker("purchase_boot");

    return new Promise(async (resolve, reject) => {
      try {
        let purchase_cache_app_version = await this.storage.get(
          DB_KEY_PURCHASE_DATA_APP_VERSION,
        );

        // Do we have cached data?

        // to test problems
        // if (purchase_cache_app_version == Env.APP_VERSION && false) {
        if (purchase_cache_app_version == Env.APP_VERSION) {
          this.analytics.addMarker("purchase_local_storate_load");

          // Set purchase data from cache.
          await this.setDataFromLocalStorage();

          this.analytics.addMarker("purchase_local_storage_data", [
            this.offers.length,
            this.campaigns.length,
            this.subscriptions.length,
            this.discounts.length,
          ]);

          // Resolve immediately after setting data and do IAP initialization in the background.
          resolve();
        }

        this.analytics.addMarker("purchase_default_load");

        // Initialize the in-app purchase system
        await this.iap.initialize();

        // Retrieve various types of products from the in-app purchase system
        // Store the list of upgrade products
        this.subscriptionList = this.iap.getProductsByCategory("upgrade");

        // Store the list of campaign products
        this.campaignList = this.iap.getProductsByCategory("campaign");

        // Store the list of offer products
        this.offerList = this.iap.getProductsByCategory("offer");

        // Store the list of discount products
        this.discountList = this.iap.getProductsByCategory("discount");

        // Format the retrieved data in a required way
        await this.formatData();

        this.analytics.addMarker("purchase_default_data", [
          this.offers.length,
          this.campaigns.length,
          this.subscriptions.length,
          this.discounts.length,
        ]);

        // Cache data.
        this.cacheData();
        resolve();
      } catch (error) {
        // Error handling block is executed if any error occurs in the try block

        // Log error about global purchase initialization problem to debug service
        this.debug.le("global purchase init error", error);

        // Log the error in analytics under "upgrade_error" event
        this.analytics.logEvent("upgrade_error", <TME_upgrade_error>{
          message: "purchase_init_error",
        });

        // TODO: Add error handling for purchase stats across the board
        // Call general error handler with the occurred error
        this.callErrorHandler(error);
        resolve();
      }
    });
  }

  /**
   * Cache data.
   */
  private cacheData(): void {
    this.storage.set(DB_KEY_CAMPAIGN_LIST, this.campaignList);
    this.storage.set(DB_KEY_DISCOUNT_LIST, this.discountList);
    this.storage.set(DB_KEY_OFFER_LIST, this.offerList);
    this.storage.set(DB_KEY_SUBSCRIPTION_LIST, this.subscriptionList);

    this.storage.set(DB_KEY_F_CAMPAIGN_LIST, this.formattedCampaignList);
    this.storage.set(DB_KEY_F_DISCOUNT_LIST, this.formattedDiscountList);
    this.storage.set(DB_KEY_F_OFFER_LIST, this.formattedOfferList);
    this.storage.set(
      DB_KEY_F_SUBSCRIPTION_LIST,
      this.formattedSubscriptionList,
    );

    this.storage.set(DB_KEY_PURCHASE_DATA_APP_VERSION, Env.APP_VERSION);
  }

  /**
   * Set data from cache.
   */
  private async setDataFromLocalStorage() {
    this.subscriptionList = await this.storage.get(DB_KEY_SUBSCRIPTION_LIST);
    this.offerList = await this.storage.get(DB_KEY_OFFER_LIST);
    this.campaignList = await this.storage.get(DB_KEY_CAMPAIGN_LIST);
    this.discountList = await this.storage.get(DB_KEY_DISCOUNT_LIST);

    this.formattedSubscriptionList = await this.storage.get(
      DB_KEY_F_SUBSCRIPTION_LIST,
    );
    this.formattedOfferList = await this.storage.get(DB_KEY_F_OFFER_LIST);
    this.formattedCampaignList = await this.storage.get(DB_KEY_F_CAMPAIGN_LIST);
    this.formattedDiscountList = await this.storage.get(DB_KEY_F_DISCOUNT_LIST);
  }

  // flag to see if user initiated purchse

  // falg to see if we should show loading spinner etc
  private _inProgress: boolean = false;

  /**
   * Wrapper for progress indicator
   * @returns
   */
  public inProgress() {
    return this._inProgress;
  }

  /**
   * Setter for progress indicator
   * @param p
   */
  private setProgress(p: boolean) {
    if (!p) {
      this.dialog.hideLoading();
    }

    this._inProgress = p;

    // force template update
    this.misc.ngForceTemplateUpdate();
  }

  // cache
  private subscriptionList = [];
  private offerList = [];
  private discountList = [];
  private campaignList = [];

  public isCampaign: boolean = false;

  // selected campaign
  private selectedCampaignItem: IapItem = null;

  // proper formatted data
  private formattedSubscriptionList: IapItem[] = [];
  private formattedOfferList: IapItem[] = [];
  private formattedDiscountList: IapItem[] = [];
  private formattedCampaignList: IapItem[] = [];

  // determine which dialog to open for campaign link
  public campaignDialogType: string = "";

  public areProductsReady(): boolean {
    return (
      this.offers.length > 0 &&
      this.discounts.length > 0 &&
      this.campaigns.length > 0 &&
      this.subscriptions.length > 0
    );
  }

  // Define a getter for the offers property
  get offers() {
    // When the offers property is accessed, return the value of the formattedOfferList property
    return this.formattedOfferList;
  }

  // Define a getter for the discounts property
  get discounts() {
    // When the discounts property is accessed, return the value of the formattedDiscountList property
    return this.formattedDiscountList;
  }

  // Define a getter for the campaigns property
  get campaigns() {
    // When the campaigns property is accessed, return the value of the formattedCampaignList property
    return this.formattedCampaignList;
  }

  // Define a getter for the subscriptions property
  get subscriptions() {
    // When the subscriptions property is accessed, return the value of the formattedSubscriptionList property
    return this.formattedSubscriptionList;
  }

  // Define a getter for the selectedCampaign property
  get selectedCampaign() {
    // When the selectedCampaign property is accessed, return the value of the selectedCampaignItem property
    return this.selectedCampaignItem;
  }

  /**
   * Initialize in app purchase item, calculate per item values and add human readable
   * stuff to be displayed on upgrade pages
   *
   * @param data
   * @param isOffer
   * @returns
   */
  private initializeIapItem(data, isOffer: boolean = false) {
    this.debug.l("format data we got", data);

    let item: IapItem = {
      trialCallToAction: "upgrade_now",
      promoCallToAction: "upgrade_now",
      promoSubscriptionDetails: "",
      trialPriceText: "",
      promoPriceText: "",
      originalProductObject: null,
      transactionData: null,
      wasApproved: false,
      canPurchase: data.canPurchase,
      store_id: data.id,
      default: data._preselected,
      storeTitle: data.title,
      storeDescription: data.description,
      id: data._pay_item_id,
      price: data.price,
      currency: data.currency,
      priceAsDecimal: data.priceMicros / 1000000,
      billingLength: data.billingPeriod,
      billingPeriod: (data?.billingPeriodUnit || "").toLowerCase(),
      billingPeriodText: Utils.plural(
        this.translateService,
        data.billingPeriod,
        (data?.billingPeriodUnit || "").toLowerCase(),
      ),

      hasTrial: !!data.introPricePaymentMode,
      hasFreeTrial: !!data.introPricePaymentMode && data.introPriceMicros == 0,

      trialPeriod:
        data.introPriceSubscriptionPeriod || data.introPricePeriodUnit,
      trialLength: data.introPriceNumberOfPeriods || data.introPricePeriod,
      trialPrice: data.introPriceMicros == 0 ? "" : data.introPrice,

      periodTextSimple: this.translateService.instant(
        `billed every ${data.billingPeriod} ${(
          data?.billingPeriodUnit || ""
        ).toLowerCase()}`,
      ),

      periodText: this.translateService.instant("billed_periodically", {
        periodText: this.translateService.instant(
          `billed every ${data.billingPeriod} ${(
            data?.billingPeriodUnit || ""
          ).toLowerCase()}`,
        ),
      }),

      // will be added later

      savingsInPercentage: null,
      isBestOption: false,

      // generate now

      pricePerDay: null,
      description: null,
      pricePerDayText: null,
      ribbonText: null,
      titleText: null,
    };

    // add price per day
    item.pricePerDay = this.calcPricePerDay(item);

    // add texts
    item.pricePerDayText = this.translateService.instant("price_per_day", item);

    // add description
    if (item.hasTrial) {
      item.trialPeriodText = Utils.plural(
        this.translateService,
        item?.trialLength || 0,
        item?.trialPeriod?.toLowerCase() || "?",
      );

      item.titleText = this.translateService.instant("trial_short_text", {
        length: item.trialLength,
        period: item.trialPeriodText,
      });

      if (item.trialPrice != "") {
        // paid trial

        item.description = this.translateService.instant(
          isOffer ? "try_description_paid_trial" : "sub_description_paid_trial",
          {
            billing_details: item.price + " " + item.periodText,
            trial_length: item.trialLength,
            trial_period: item.trialPeriodText,
            trial_price: item.trialPrice,
          },
        );

        item.promoPriceText = this.translateService.instant(
          "val_promo_with_trial_style",
          {
            length: item.trialLength,
            period: item.trialPeriodText,
            price: item.trialPrice,
            rebillDetails: item.price + " " + item.periodText,
          },
        );

        item.trialPriceText = this.translateService.instant(
          "val_try_with_trial_style",
          {
            length: item.trialLength,
            period: item.trialPeriodText,
            price: item.trialPrice,
            rebillDetails: item.price + " " + item.periodText,
          },
        );

        item.promoSubscriptionDetails = this.translateService.instant(
          "val_promo_details_trial",
          {
            length: item.trialLength,
            period: item.trialPeriodText,
            price: item.trialPrice,
            rebillPrice: item.price,
            rebillPeriod: item.periodTextSimple,
          },
        );

        item.promoCallToAction = item.trialCallToAction = "upgrade_now_trial";
      } else {
        // free trial
        item.ribbonText = this.translateService.instant("free");

        item.description = this.translateService.instant(
          isOffer ? "try_description_free_trial" : "sub_description_free_trial",
          {
            billing_details: item.price + " " + item.periodText,
            trial_length: item.trialLength,
            trial_period: item.trialPeriodText,
          },
        );

        item.trialPriceText = this.translateService.instant(
          "val_try_free_trial_style",
          {
            length: item.trialLength,
            period: item.trialPeriodText,
            price: item.trialPrice,
            rebillDetails: item.price + " " + item.periodText,
          },
        );

        item.promoPriceText = this.translateService.instant(
          "val_promo_free_trial_style",
          {
            length: item.trialLength,
            period: item.trialPeriodText,
            price: item.trialPrice,
            rebillDetails: item.price + " " + item.periodText,
          },
        );

        item.promoSubscriptionDetails = this.translateService.instant(
          "val_promo_details_trial",
          {
            length: item.trialLength,
            period: item.trialPeriodText,
            price: item.trialPrice,
            rebillPrice: item.price,
            rebillPeriod: item.periodTextSimple,
          },
        );

        item.promoCallToAction = item.trialCallToAction =
          "upgrade_now_free_trial";
      }
    } else {
      // no trial
      item.description = this.translateService.instant(
        isOffer ? "try_description_regular" : "sub_description_regular",
        {
          billing_details: item.price + " " + item.periodText,
        },
      );

      item.trialPriceText = this.translateService.instant(
        "val_try_no_trial_style",
        {
          length: item.trialLength,
          period: item.trialPeriodText,
          price: item.trialPrice,
          rebillDetails: item.price + " " + item.periodText,
        },
      );

      item.promoPriceText = this.translateService.instant(
        "val_promo_no_trial_style",
        {
          length: item.trialLength,
          period: item.trialPeriodText,
          price: item.trialPrice,
          rebillDetails: item.price + " " + item.periodText,
        },
      );

      item.promoSubscriptionDetails = item.description;
      item.ribbonText = "" + item.billingLength;
      item.titleText = item.billingPeriodText;
    }

    return item;
  }

  /**
   * Calculate price per day.
   *
   * @param data
   * @return {string}
   */
  private calcPricePerDay(data: IapItem): number {
    let days: number = 0;

    // day, week, month, year
    for (let i = 1; i <= data.billingLength; i++) {
      switch ((data?.billingPeriod || "").toLowerCase()) {
        case "year":
          days = 365;
          break;
        case "month":
          days += this.getDaysForMonth(i);
          break;
        case "week":
          days = 7 * data.billingLength;
          break;
        case "day":
          days = data.billingLength;
          break;
      }
    }

    let n: Number = data.priceAsDecimal / days;

    return parseFloat(n.toFixed(2));
  }

  /**
   *
   * @param {number} month
   * @return {number}
   */
  private getDaysForMonth(month: number): number {
    let now = new Date();
    return new Date(now.getFullYear(), now.getMonth() + month, 0).getDate();
  }

  /**
   * Sorts subscription list and calculates the best option plus percentage
   */
  private sortAndCalculateBestOption() {
    if (this.formattedSubscriptionList.length < 2) {
      return false;
    }

    let cheapestItemIndex = 1;
    let expencieveItemIndex = -1;
    let cheapestPrice = this.formattedSubscriptionList[1].pricePerDay;
    let expencievePrice = -1;

    this.formattedSubscriptionList.forEach((e, i) => {
      if (expencievePrice < 0 || expencievePrice < e.pricePerDay) {
        expencievePrice = e.pricePerDay;
        expencieveItemIndex = i;
      }
    });

    // set percentage, and best option

    let percentage = (100 * cheapestPrice) / expencievePrice;
    percentage = Math.ceil(100 - percentage);
    this.formattedSubscriptionList[cheapestItemIndex].savingsInPercentage =
      percentage;
    this.formattedSubscriptionList[cheapestItemIndex].isBestOption = true;
  }

  /**
   * Format the iap data, add what needs to be added etc
   */
  private formatData(): Promise<any> {
    // format data for display and proper usage, only what's required should be in the object
    // maybe generate some readable stuff separately

    this.formattedSubscriptionList = [];
    this.formattedOfferList = [];
    this.formattedCampaignList = [];
    this.formattedDiscountList = [];

    this.debug.l("sublist", this.subscriptionList);
    this.debug.l("offerlist", this.offerList);
    this.debug.l("discountList", this.discountList);
    this.debug.l("campaignlist", this.campaignList);

    this.subscriptionList.forEach((e) => {
      let item = this.initializeIapItem(e, false);
      this.formattedSubscriptionList.push(item);
    });

    this.offerList.forEach((e) => {
      let item = this.initializeIapItem(e, true);
      this.formattedOfferList.push(item);
    });

    this.discountList.forEach((e) => {
      let item = this.initializeIapItem(e, true);
      this.formattedDiscountList.push(item);
    });

    this.campaignList.forEach((e) => {
      let item = this.initializeIapItem(e, true);
      this.formattedCampaignList.push(item);
    });

    this.sortAndCalculateBestOption();

    this.debug.l("formatted stuff", {
      offers: this.formattedOfferList,
      subscriptions: this.formattedSubscriptionList,
    });

    return Promise.resolve();
  }

  /**
   * Set campaign variable from environment
   */
  private setCampaign() {
    // set campaign if any
    this.selectedCampaignItem = null;

    if (!!this.campaignProductId) {
      let selectedCampaignIndex = _.findIndex(this.formattedCampaignList, [
        "id",
        this.campaignProductId,
      ]);

      // use selected or first in array as fallback
      selectedCampaignIndex =
        selectedCampaignIndex >= 0 ? selectedCampaignIndex : 0;

      this.selectedCampaignItem =
        this.formattedCampaignList[selectedCampaignIndex];
    }
  }

  // mock for init - so we can work on desktop without real device
  public async init_MOCK() {
    this.subscriptionList = [
      {
        id: "m1_14_201_premium",
        alias: "quarterly premium",
        type: "paid subscription",
        _pay_item_id: 201,
        _preselected: true,
        _period_length: 1,
        _period_unit: "month",
        group: "default",
        state: "valid",
        title: "Quarterly Subscription",
        description: "Premium Membership Subscription",
        priceMicros: 32990000,
        price: "$32.99",
        currency: "USD",
        countryCode: null,
        loaded: true,
        canPurchase: true,
        owned: false,
        introPrice: "",
        introPriceMicros: "",
        introPricePeriod: null,
        introPriceNumberOfPeriods: null,
        introPricePeriodUnit: null,
        introPriceSubscriptionPeriod: null,
        introPricePaymentMode: null,
        ineligibleForIntroPrice: null,
        discounts: [],
        downloading: false,
        downloaded: false,
        additionalData: null,
        transaction: null,
        trialPeriod: null,
        trialPeriodUnit: null,
        billingPeriod: 1,
        billingPeriodUnit: "Month",
        valid: true,
      },
      {
        id: "m1_14_203_premium",
        alias: "quarterly premium",
        type: "paid subscription",
        _pay_item_id: 203,
        _preselected: true,
        _period_length: 3,
        _period_unit: "month",
        group: "default",
        state: "valid",
        title: "Quarterly Subscription",
        description: "Premium Membership Subscription",
        priceMicros: 44990000,
        price: "$44.99",
        currency: "USD",
        countryCode: null,
        loaded: true,
        canPurchase: true,
        owned: false,
        introPrice: "",
        introPriceMicros: "",
        introPricePeriod: null,
        introPriceNumberOfPeriods: null,
        introPricePeriodUnit: null,
        introPriceSubscriptionPeriod: null,
        introPricePaymentMode: null,
        ineligibleForIntroPrice: null,
        discounts: [],
        downloading: false,
        downloaded: false,
        additionalData: null,
        transaction: null,
        trialPeriod: null,
        trialPeriodUnit: null,
        billingPeriod: 3,
        billingPeriodUnit: "Month",
        valid: true,
      },
      {
        id: "m1_14_202_premium",
        alias: "yearly premium",
        type: "paid subscription",
        _pay_item_id: 202,
        _preselected: true,
        _period_length: 1,
        _period_unit: "year",
        group: "default",
        state: "valid",
        title: "Yearly Subscription",
        description: "Premium Membership Subscription",
        priceMicros: 89990000,
        price: "$89.99",
        currency: "USD",
        countryCode: null,
        loaded: true,
        canPurchase: true,
        owned: false,
        introPrice: "",
        introPriceMicros: "",
        introPricePeriod: null,
        introPriceNumberOfPeriods: null,
        introPricePeriodUnit: null,
        introPriceSubscriptionPeriod: null,
        introPricePaymentMode: null,
        ineligibleForIntroPrice: null,
        discounts: [],
        downloading: false,
        downloaded: false,
        additionalData: null,
        transaction: null,
        trialPeriod: null,
        trialPeriodUnit: null,
        billingPeriod: 1,
        billingPeriodUnit: "Year",
        valid: true,
      },
    ];

    this.campaignList = [
      {
        id: "m1_14_206_premium",
        alias: "Monthly trial",
        type: "paid subscription",
        _pay_item_id: 206,
        _preselected: true,
        _period_length: 1,
        _period_unit: "month",
        _trial_period_length: 1,
        _trial_period_unit: "month",
        _trial_price_percentage: 3.00090936647,
        group: "default",
        state: "valid",
        title: "1 month trial, rebills monthly",
        description: "Premium Membership Subscription",
        priceMicros: 32990000,
        price: "$32.99",
        currency: "USD",
        countryCode: null,
        loaded: true,
        canPurchase: true,
        owned: false,
        introPrice: "$0.99",
        introPriceMicros: 990000,
        introPricePeriod: 1,
        introPriceNumberOfPeriods: 1,
        introPricePeriodUnit: "Month",
        introPriceSubscriptionPeriod: "Month",
        introPricePaymentMode: "UpFront",
        ineligibleForIntroPrice: null,
        discounts: [],
        downloading: false,
        downloaded: false,
        additionalData: null,
        transaction: null,
        trialPeriod: null,
        trialPeriodUnit: null,
        billingPeriod: 1,
        billingPeriodUnit: "Month",
        valid: true,
      },
    ];

    this.offerList = [
      {
        id: "m1_14_201_premium",
        alias: "Monthly free trial",
        type: "paid subscription",
        _pay_item_id: 201,
        _preselected: false,
        _period_length: 1,
        _period_unit: "month",
        group: "default",
        state: "valid",
        title: "22.99 offer welcome",
        description: "Premium Membership Subscription",
        priceMicros: 2299000000,
        price: "$22.99",
        currency: "USD",
        countryCode: null,
        loaded: true,
        canPurchase: true,
        owned: false,
        introPrice: "",
        introPriceMicros: "",
        introPricePeriod: null,
        introPriceNumberOfPeriods: null,
        introPricePeriodUnit: null,
        introPriceSubscriptionPeriod: null,
        introPricePaymentMode: null,
        ineligibleForIntroPrice: null,
        discounts: [],
        downloading: false,
        downloaded: false,
        additionalData: null,
        transaction: null,
        trialPeriod: null,
        trialPeriodUnit: null,
        billingPeriod: 1,
        billingPeriodUnit: "Month",
        valid: true,
      },
      {
        id: "m1_14_203_premium",
        alias: "quarterly premium",
        type: "paid subscription",
        _pay_item_id: 203,
        _preselected: true,
        _period_length: 3,
        _period_unit: "month",
        group: "default",
        state: "valid",
        title: "Quarterly Subscription",
        description: "Premium Membership Subscription",
        priceMicros: 30990000,
        price: "$30.99",
        currency: "USD",
        countryCode: null,
        loaded: true,
        canPurchase: true,
        owned: false,
        introPrice: "",
        introPriceMicros: "",
        introPricePeriod: null,
        introPriceNumberOfPeriods: null,
        introPricePeriodUnit: null,
        introPriceSubscriptionPeriod: null,
        introPricePaymentMode: null,
        ineligibleForIntroPrice: null,
        discounts: [],
        downloading: false,
        downloaded: false,
        additionalData: null,
        transaction: null,
        trialPeriod: null,
        trialPeriodUnit: null,
        billingPeriod: 3,
        billingPeriodUnit: "Month",
        valid: true,
      },
    ];

    this.discountList = [
      {
        id: "m1_14_201_premium",
        alias: "Monthly free trial",
        type: "paid subscription",
        _pay_item_id: 201,
        _preselected: false,
        _period_length: 1,
        _period_unit: "month",
        group: "default",
        state: "valid",
        title: "22.99 offer welcome",
        description: "Premium Membership Subscription",
        priceMicros: 2299000000,
        price: "$22.99",
        currency: "USD",
        countryCode: null,
        loaded: true,
        canPurchase: true,
        owned: false,
        introPrice: "",
        introPriceMicros: "",
        introPricePeriod: null,
        introPriceNumberOfPeriods: null,
        introPricePeriodUnit: null,
        introPriceSubscriptionPeriod: null,
        introPricePaymentMode: null,
        ineligibleForIntroPrice: null,
        discounts: [],
        downloading: false,
        downloaded: false,
        additionalData: null,
        transaction: null,
        trialPeriod: null,
        trialPeriodUnit: null,
        billingPeriod: 1,
        billingPeriodUnit: "Month",
        valid: true,
      },
      {
        id: "m1_14_203_premium",
        alias: "quarterly premium",
        type: "paid subscription",
        _pay_item_id: 203,
        _preselected: true,
        _period_length: 3,
        _period_unit: "month",
        group: "default",
        state: "valid",
        title: "Quarterly Subscription",
        description: "Premium Membership Subscription",
        priceMicros: 30990000,
        price: "$30.99",
        currency: "USD",
        countryCode: null,
        loaded: true,
        canPurchase: true,
        owned: false,
        introPrice: "",
        introPriceMicros: "",
        introPricePeriod: null,
        introPriceNumberOfPeriods: null,
        introPricePeriodUnit: null,
        introPriceSubscriptionPeriod: null,
        introPricePaymentMode: null,
        ineligibleForIntroPrice: null,
        discounts: [],
        downloading: false,
        downloaded: false,
        additionalData: null,
        transaction: null,
        trialPeriod: null,
        trialPeriodUnit: null,
        billingPeriod: 3,
        billingPeriodUnit: "Month",
        valid: true,
      },
    ];

    // format data
    this.formatData();

    // selected campaign set
    this.selectedCampaignItem = this.formattedCampaignList[0];
  }

  /**
   * We have an error, do defaults, and pass it back
   * @param error
   */
  private callErrorHandler(error) {
    this.setProgress(false);

    if (this._errorHandler) {
      this._errorHandler(error);
    }
  }

  private _errorHandler: Function = () => {};

  /**
   * On close handler setter
   * @param closeFunction function to call
   */
  public setOnCloseHandler(closeFunction: Function) {
    this._onClose = closeFunction;
  }

  /**
   * Close callback for pages
   */
  public executeCloseCallback() {
    this._onClose();
  }

  private _onClose: Function = () => {};

  /**
   * return which dialog to show to the user
   * @returns
   */
  private getCampaignDialogLink() {
    return (this.campaignDialogType ?? "").toLocaleLowerCase() == "b"
      ? "app/modals/try-premium"
      : "app/modals/go-premium-promo";
  }

  private _dialogType: UpgradeDialogType;

  /**
   * Dialog Type getter
   */
  get dialogType(): string {
    return this._dialogType;
  }

  /**
   * dialog type for display
   * @param dialogType
   */
  public setDialogType(dialogType: UpgradeDialogType) {
    this.setPurchaseExtraInfo(
      "initiate_upgrade_title",
      dialogType || "default",
    );
    this._dialogType = dialogType;
    return this;
  }

  /**
   * Open / navigate to an upgrade dialog of given type
   *
   * @param errorHandler
   * @param onClose
   * @param forceOpen
   * @returns
   */
  public async openUpgradeDialog(
    errorHandler: Function,
    onClose: Function = () => {},
    forceOpen: boolean = false,
  ) {
    this.isCampaign = false;

    this.analytics.addMarker("open upgrade dialog");

    this.setPurchaseExtraInfo("initiate_upgrade_category", "upgrade_page");

    // @todo check and open only if user is not premium yet

    if (this.accountService.account.is_premium && !forceOpen) {
      this.dialog.showAlert("hey", "already_premium");
      this.dialog.hideLoading();
      return;
    }

    if (this.checkIfOffer24hIsActive() == true) {
      return this.openDiscounted24hDialog(errorHandler, onClose, forceOpen);
    }

    // debug purposes only

    if (!this.misc.isNativePlatform()) {
      this.router.navigate(["app/modals/go-premium"]);
      return;
    }

    // real device usage

    this._onClose = onClose;
    this._errorHandler = errorHandler;

    if (this.subscriptions.length == 0) {
      // @todo maybe in each case when we have empty array try to re-initialize plugin?
      this.dialog.showErrorAlert("sorry", "no_active_subscriptions_error");

      this.analytics.logEvent("upgrade_error", <TME_upgrade_error>{
        message: "no_active_subscriptions_error",
      });

      this.dialog.hideLoading();
    } else {
      this.setTargetPage("upgrade");
      this.openModal();
      return Promise.resolve();
    }
  }

  /**
   * Helper funtion to set target page based on dialog type
   * @param openDialog
   */
  private setTargetPage(openDialog: string) {
    switch (openDialog) {
      case "upgrade":
        this.targetPage = "app/modals/go-premium";
        break;
      case "discounted":
        this.targetPage = "app/modals/go-premium-discounted";
        break;
      case "discounted24h":
        this.targetPage = "app/modals/go-premium-discounted-24h";
        break;
      case "downloaded":
        this.targetPage = "app/modals/go-premium-downloaded";
        break;
      default:
        this.targetPage = "app/modals/try-premium";
        break;
    }

    if (openDialog === "my-lists") {
      this.targetPage = "app/my-lists/viewed-me";
    }

    if (this.isCampaign) {
      this.targetPage = this.getCampaignDialogLink();
    }
  }

  /**
   * Set dialog type for promo
   * @param dialogType
   * @returns
   */
  public setPromoDialogType(dialogType: PromoDialogType) {
    this._promoDialogType = dialogType;
    return this;
  }

  /**
   * Get promo dialog type
   */
  public get promoDialogType(): PromoDialogType {
    return this._promoDialogType;
  }

  private _promoDialogType: PromoDialogType = "default";

  /**
   * Open campaign dialog
   *
   * @param campaignId
   * @param errorHandler
   * @param onClose
   * @param forceOpen
   * @returns
   */
  public async openCampaignDialog(
    campaignId: string,
    errorHandler: Function,
    onClose: Function = () => {},
    forceOpen: boolean = false,
  ) {
    this.isCampaign = true;

    this.analytics.addMarker("open campaign dialog");

    this.setPurchaseExtraInfo("initiate_upgrade_category", "campaign_offer");

    this.campaignProductId = "";
    this.campaignId = campaignId;

    this.setPurchaseExtraInfo(
      "initiate_upgrade_title",
      this.campaignId || "default",
    );

    if (campaignId) {
      // fetch campaign product
      let res = await this.accountService.getCampaignProduct({
        campaign_id: this.campaignId,
        store_type: this.deviceInfo.platform,
      });

      this.campaignProductId = "" + res["_pay_item_id"];

      this.debug.l("Using campaign " + this.campaignId);
      this.debug.l("Using productId " + this.campaignProductId);
    }

    // @todo check and open only if the user is not premium yet

    if (this.accountService.account.is_premium && !forceOpen) {
      this.dialog.showAlert("hey", "already_premium");
      this.dialog.hideLoading();
      return;
    }

    // debug purposes only

    if (!this.misc.isNativePlatform()) {
      this.router.navigate([this.getCampaignDialogLink()]);
      return;
    }

    // real device usage
    this._onClose = onClose;
    this._errorHandler = errorHandler;

    if (this.campaigns.length == 0) {
      this.debug.l("SHIT Happened campaign");
      this.dialog.showErrorAlert("Sorry...", "no_active_campaign_error");
      this.analytics.logEvent("upgrade_error", <TME_upgrade_error>{
        message: "no_active_campaign_error",
      });
      this.dialog.hideLoading();
    } else {
      // set campaign
      this.setCampaign();

      this.setTargetPage("trial");
      this.openModal();
      return Promise.resolve();
    }
  }

  /**
   * Open discount dialog
   *
   * @param errorHandler
   * @param onClose
   * @param forceOpen
   * @returns
   */
  public async openDiscountedDialog(
    errorHandler: Function,
    onClose: Function = () => {},
    forceOpen: boolean = false,
  ) {
    this.isCampaign = false;

    this.analytics.addMarker("open discounted dialog");

    // @todo check and open only if the user is not premium yet

    if (this.accountService.account.is_premium && !forceOpen) {
      this.dialog.showAlert("hey", "already_premium");
      this.dialog.hideLoading();
      return;
    }

    // debug purposes only

    if (!this.misc.isNativePlatform()) {
      this.router.navigate(["app/modals/go-premium-discounted"]);
      return;
    }

    // real device usage
    this._onClose = onClose;
    this._errorHandler = errorHandler;

    if (this.discounts.length == 0) {
      this.debug.l("SHIT Happened discounted");
      this.dialog.showErrorAlert("sorry", "no_active_offers_error");
      this.analytics.logEvent("upgrade_error", <TME_upgrade_error>{
        message: "no_active_offers_error_discounted",
      });
      this.dialog.hideLoading();
    } else {
      this.setTargetPage("discounted");
      this.openModal();
      return Promise.resolve();
    }
  }

  /**
   * Open discount 24h dialog
   *
   * @param errorHandler
   * @param onClose
   * @param forceOpen
   * @returns
   */
  public async openDiscounted24hDialog(
    errorHandler: Function,
    onClose: Function = () => {},
    forceOpen: boolean = false,
  ) {
    this.isCampaign = false;

    // @todo check and open only if the user is not premium yet

    this.analytics.addMarker("open discounted24 dialog");

    if (this.accountService.account.is_premium && !forceOpen) {
      this.dialog.showAlert("hey", "already_premium");
      this.dialog.hideLoading();
      return;
    }

    if (!this.checkIfOffer24hIsActive()) {
      return this.openUpgradeDialog((error) => {
        this.dialog.showToastBaseOnErrorResponse(error, null, false);
      });
    }
    // debug purposes only

    if (!this.misc.isNativePlatform()) {
      this.router.navigate(["app/modals/go-premium-discounted-24h"]);
      return;
    }

    // real device usage
    this._onClose = onClose;
    this._errorHandler = errorHandler;

    if (this.discounts.length == 0) {
      this.dialog.showErrorAlert("sorry", "no_active_offers_error");
      this.analytics.logEvent("upgrade_error", <TME_upgrade_error>{
        message: "no_active_offers_error_discounted_24h",
      });
      this.dialog.hideLoading();
    } else {
      this.setTargetPage("discounted24h");
      this.openModal();
      return Promise.resolve();
    }
  }

  public checkIfOffer24hIsActive(): boolean {
    if (this.accountService.account.is_premium) {
      return false;
    }

    if (!this.utilsService?.config["24offer"]) {
      return false;
    }

    if (
      this.accountService.account.registration_ts <
      Math.floor(Date.now() / 1000) - 24 * 60 * 60
    ) {
      return false;
    }

    return true;
  }

  /**
   * Open trial dialog
   *
   * @param errorHandler
   * @param onClose
   * @param forceOpen
   * @returns
   */
  public async openTrialDialog(
    errorHandler: Function,
    onClose: Function = () => {},
    forceOpen: boolean = false,
  ) {
    this.isCampaign = false;

    this.analytics.addMarker("open trial dialog");

    // @todo check and open only if the user is not premium yet

    if (this.accountService.account.is_premium && !forceOpen) {
      this.dialog.showAlert("hey", "already_premium");
      this.dialog.hideLoading();
      return;
    }

    // debug purposes only

    if (!this.misc.isNativePlatform()) {
      this.router.navigate(["app/modals/try-premium"]);
      return;
    }

    // real device usage
    this._onClose = onClose;
    this._errorHandler = errorHandler;

    if (this.offers.length == 0) {
      this.debug.l("SHIT Happened trial");
      this.dialog.showErrorAlert("sorry", "no_active_offers_error");
      this.analytics.logEvent("upgrade_error", <TME_upgrade_error>{
        message: "no_active_offers_error_trial",
      });
      this.dialog.hideLoading();
    } else {
      this.setTargetPage("trial");
      this.openModal();
      return Promise.resolve();
    }
  }

  private targetPage = "";

  /**
   * Generic function to open the currently set modal
   * @returns
   */
  private async openModal() {
    this.formatData();

    this.setProgress(false);

    // @todo see if the user is already premium, and don't open the dialog
    // ( if it's not debug mode )

    if (!Env.IS_DEBUG && this.accountService.isPremium()) {
      this.dialog.showAlert("whoa", "already_premium");
      return Promise.reject();
    }

    return this.router.navigate([this.targetPage]);
  }

  private campaignProductId = "";
  private campaignId = "";

  /**
   * Initiate subscription purchase
   * @param id
   */
  public async buySubscription(id: string) {
    // log event
    this.analytics.logEvent("submit_upgrade_form", <TME_submit_upgrade_form>{});

    this.setProgress(true);

    try {
      let result = await this.iap.purchase(id);
      this.debug.l("Purchase success on page", result);
      this.finalizePurchaseAndRestart(result);
    } catch (error) {
      this.debug.le("iap order error", error);
      this.callErrorHandler(error);
    }
  }

  private _trackingData: any = {};
  private _trackingDataCompiled: any = {};

  /**
   * This method is used to set purchase information for analytics purposes.
   *
   * @param {string} key - The key indicating which information to set.
   * @param {string} value - The information to set.
   * @returns {void}
   */
  public setPurchaseExtraInfo(
    key: "initiate_upgrade_category" | "initiate_upgrade_title",
    value: string,
  ): void {
    this.statsPurchaseInfo[key] = value;
    this.analytics.setPurchaseExtraInfo(key, value);
  }

  private statsPurchaseInfo = {
    initiate_upgrade_category: "",
    initiate_upgrade_title: "",
  };

  /**
   * Set tracking data used in analytics
   * @param data
   */
  public setTrackingData(data: any) {
    this.debug.l("purchase set tracking data", data);
    this._trackingData = data;
    this._trackingDataCompiled = data?.COMPITLED;
  }

  /**
   * This is a private method that is used to finalize a purchase and restart the app.
   * It sets analytics data based on the data received from the purchase and logs the purchase in analytics.
   *
   * @param {any} data - The purchase data received from the payment provider.
   * @returns {void}
   */
  private finalizePurchaseAndRestart(data: any): void {
    // Show a dialog and restart the app

    // Log the received data
    this.debug.l("finalize restart data", data);

    // Determine if the purchase has a trial, and set the price accordingly
    let hasTrial = !!data?.introPricePeriodUnit;
    let price = hasTrial
      ? data?.introPriceMicros / 1000000
      : data?.priceMicros / 1000000;

    let purchaseAnalyticsInfo = <TME_purchase>{
      transaction_id: data?.transaction?.transactionId,
      affiliation: this.appsflyer.getAffiliateString(), // add affiliate link or campaign link here
      currency: data?.currency,
      value: price, // Total Revenue
      initiate_upgrade_category:
        this.statsPurchaseInfo.initiate_upgrade_category,
      initiate_upgrade_title: this.statsPurchaseInfo.initiate_upgrade_title,
    };

    // log to see on staging
    this.debug.l("IAP PurchaseAnalyticsInfo", purchaseAnalyticsInfo);

    // Check that the purchase is not in sandbox mode
    if (!this.iap.wasSandboxPurchase) {
      // Log the purchase information in the analytics
      this.analytics.logEvent("purchase_info", purchaseAnalyticsInfo);

      // Log the purchase in the AppsFlyer analytics
      /*
      this.appsflyer.logEvent("purchase", {
        af_revenue: price,
        af_receipt_id: data?.transaction?.["transactionId"],
        af_currency: data?.currency,
      });*/

      // Check if the user came from a push notification and send the data to the server if it matches the right conditions
      if (
        this._trackingDataCompiled?.source == "PUSH" &&
        this._trackingData?.PUSH?.id
      ) {
        this.utilsService.notificationUpgrade(
          this._trackingData?.PUSH?.id,
          this._trackingData?.PUSH || {},
        );
      }
    } else {
      // For sandbox purchases, log a message indicating that it is not being logged to analytics
      this.debug.l("sandbox purchase, not logging to analytics");
    }

    // Show a message and restart the app only if the user is not already subscribed
    this.dialog.showAlert("thank_you", "upgrade_thanks", "ok", async () => {
      // make sure we delete close callback, since it's not applicable when we do purchase
      this._onClose = () => {};

      // Restart the app
      await this.refresherService.refresh();
    });

    // Turn off the blocking spinner
    this.setProgress(false);
  }

  /**
   * Open ViewedMe list upgrade dialog
   * @param errorHandler
   * @param onClose
   * @param forceOpen
   * @returns
   */
  public async openViewedMeList(
    errorHandler: Function,
    onClose: Function = () => {},
    forceOpen: boolean = false,
  ) {
    this.isCampaign = false;

    // @todo check and open only if user is not premium yet

    if (this.accountService.account.is_premium && !forceOpen) {
      this.dialog.showAlert("hey", "already_premium");
      this.dialog.hideLoading();
      return;
    }

    // debug purposes only

    if (!this.misc.isNativePlatform()) {
      this.router.navigate(["app/my-lists/viewed-me"]);
      return;
    }

    // real device usage

    this._onClose = onClose;
    this._errorHandler = errorHandler;

    if (this.subscriptions.length == 0) {
      this.debug.l("SHIT Happened");

      // @todo report error to analytics
      // @todo on every place like this display message to user that subscriptions not available
      //       or campaing expired, or something like that !!!!

      this.dialog.showErrorAlert("sorry", "no_active_subscription_error");
      this.analytics.logEvent("upgrade_error", <TME_upgrade_error>{
        message: "no_active_subscription_error",
      });
      this.dialog.hideLoading();
      //return await this.init("my-lists");
    } else {
      this.setTargetPage("my-lists");
      this.openModal();
      return Promise.resolve();
    }
  }

  public async openGoPremiumDownloaded(
    errorHandler: Function,
    onClose: Function = () => {},
    forceOpen: boolean = false,
  ) {
    this.isCampaign = false;

    // @todo check and open only if the user is not premium yet

    if (this.accountService.account.is_premium && !forceOpen) {
      this.dialog.showAlert("hey", "already_premium");
      this.dialog.hideLoading();
      return;
    }

    // debug purposes only

    if (!this.misc.isNativePlatform()) {
      this.router.navigate(["app/modals/go-premium-downloaded"]);
      return;
    }

    // real device usage
    this._onClose = onClose;
    this._errorHandler = errorHandler;

    if (this.discounts.length == 0) {
      this.dialog.showErrorAlert("sorry", "no_active_offers_error");
      this.analytics.logEvent("upgrade_error", <TME_upgrade_error>{
        message: "no_active_offers_error_downloaded",
      });
      this.dialog.hideLoading();
    } else {
      this.setTargetPage("downloaded");
      this.openModal();
      return Promise.resolve();
    }
  }
}

/*

data examples

  /*

  // subscription data example
    "offers": [
      {
        "id": "m1_14_201_premium",
        "alias": "Monthly free trial",
        "type": "paid subscription",
        "_pay_item_id": 201,
        "_preselected": false,
        "_period_length": 1,
        "_period_unit": "month",
        "_trial_period_length": 7,
        "_trial_period_unit": "day",
        "_trial_price_percentage": 0,
        "group": "default",
        "state": "valid",
        "title": "7 days free trial , rebills monthly",
        "description": "Premium Membership Subscription",
        "priceMicros": 3699000000,
        "price": "RSD 3,699",
        "currency": "RSD",
        "countryCode": null,
        "loaded": true,
        "canPurchase": true,
        "owned": false,
        "introPrice": "",
        "introPriceMicros": "",
        "introPricePeriod": 1,
        "introPriceNumberOfPeriods": 1,
        "introPricePeriodUnit": "Week",
        "introPriceSubscriptionPeriod": "Week",
        "introPricePaymentMode": "FreeTrial",
        "ineligibleForIntroPrice": null,
        "discounts": [],
        "downloading": false,
        "downloaded": false,
        "additionalData": null,
        "transaction": null,
        "trialPeriod": null,
        "trialPeriodUnit": null,
        "billingPeriod": 1,
        "billingPeriodUnit": "Month",
        "valid": true
      }
    ],
    "subscriptions": [
      {
        "id": "m1_14_202_premium",
        "alias": "7 days paid trial, rebills monthly",
        "type": "paid subscription",
        "_pay_item_id": 202,
        "_preselected": false,
        "_period_length": 1,
        "_period_unit": "month",
        "_trial_period_length": 7,
        "_trial_period_unit": "day",
        "_trial_price_percentage": 22.3074358119,
        "group": "default",
        "state": "valid",
        "title": "7 days trial, rebills monthly",
        "description": "Premium Membership Subscription",
        "priceMicros": 3699000000,
        "price": "RSD 3,699",
        "currency": "RSD",
        "countryCode": null,
        "loaded": true,
        "canPurchase": true,
        "owned": false,
        "introPrice": "RSD 849",
        "introPriceMicros": 849000000,
        "introPricePeriod": 1,
        "introPriceNumberOfPeriods": 1,
        "introPricePeriodUnit": "Week",
        "introPriceSubscriptionPeriod": "Week",
        "introPricePaymentMode": "UpFront",
        "ineligibleForIntroPrice": null,
        "discounts": [],
        "downloading": false,
        "downloaded": false,
        "additionalData": null,
        "transaction": null,
        "trialPeriod": null,
        "trialPeriodUnit": null,
        "billingPeriod": 1,
        "billingPeriodUnit": "Month",
        "valid": true
      },
      {
        "id": "m1_14_200_premium",
        "alias": "quarterly premium",
        "type": "paid subscription",
        "_pay_item_id": 200,
        "_preselected": true,
        "_period_length": 3,
        "_period_unit": "month",
        "group": "default",
        "state": "valid",
        "title": "Quarterly Subscription",
        "description": "Premium Membership Subscription",
        "priceMicros": 4999000000,
        "price": "RSD 4,999",
        "currency": "RSD",
        "countryCode": null,
        "loaded": true,
        "canPurchase": true,
        "owned": false,
        "introPrice": "",
        "introPriceMicros": "",
        "introPricePeriod": null,
        "introPriceNumberOfPeriods": null,
        "introPricePeriodUnit": null,
        "introPriceSubscriptionPeriod": null,
        "introPricePaymentMode": null,
        "ineligibleForIntroPrice": null,
        "discounts": [],
        "downloading": false,
        "downloaded": false,
        "additionalData": null,
        "transaction": null,
        "trialPeriod": null,
        "trialPeriodUnit": null,
        "billingPeriod": 3,
        "billingPeriodUnit": "Month",
        "valid": true
      },
      {
        "id": "m1_14_199_premium",
        "alias": "monthly premium",
        "type": "paid subscription",
        "_pay_item_id": 199,
        "_preselected": false,
        "_period_length": 1,
        "_period_unit": "month",
        "group": "default",
        "state": "valid",
        "title": "Monthly Subscription",
        "description": "Premium membership subscription",
        "priceMicros": 3699000000,
        "price": "RSD 3,699",
        "currency": "RSD",
        "countryCode": null,
        "loaded": true,
        "canPurchase": true,
        "owned": false,
        "introPrice": "",
        "introPriceMicros": "",
        "introPricePeriod": null,
        "introPriceNumberOfPeriods": null,
        "introPricePeriodUnit": null,
        "introPriceSubscriptionPeriod": null,
        "introPricePaymentMode": null,
        "ineligibleForIntroPrice": null,
        "discounts": [],
        "downloading": false,
        "downloaded": false,
        "additionalData": null,
        "transaction": null,
        "trialPeriod": null,
        "trialPeriodUnit": null,
        "billingPeriod": 1,
        "billingPeriodUnit": "Month",
        "valid": true
      }
    ]

    @example_data_success

  {
        "id": "m1_14_197_premium",
        "alias": "1 month for $1 trial, rebills $32.99 monthly",
        "type": "paid subscription",
        "categories": [
            "campaign",
            "discount"
        ],
        "_pay_item_id": 197,
        "_preselected": false,
        "_period_length": 1,
        "_period_unit": "month",
        "_trial_period_length": 1,
        "_trial_period_unit": "month",
        "_trial_price_percentage": 0,
        "group": "default",
        "state": "valid",
        "title": "Premium Free trial  / 24.99 monthly",
        "description": "premium with 5 day free trial",
        "priceMicros": 3099000000,
        "price": "RSD 3,099",
        "currency": "RSD",
        "countryCode": null,
        "loaded": true,
        "canPurchase": true,
        "owned": false,

        // we don't have this if there's not trial

        "introPrice": "Free",
        "introPriceMicros": 0,
        "introPricePeriod": 5,
        "introPriceNumberOfPeriods": 1,
        "introPricePeriodUnit": "Day",
        "introPriceSubscriptionPeriod": "Day",
        "introPricePaymentMode": "FreeTrial",

        "ineligibleForIntroPrice": null,
        "discounts": [],
        "downloading": false,
        "downloaded": false,
        "additionalData": null,
        "transaction": null,
        "trialPeriod": null,
        "trialPeriodUnit": null,
        "billingPeriod": 1,
        "billingPeriodUnit": "Month",
        "valid": true,

        "transaction": {
            "className": "Transaction",
            "transactionId": "GPA.3327-7455-9225-45725",
            "state": "finished",
            "products": [
                {
                    "id": "m1_14_200_premium"
                }
            ],
            "platform": "android-playstore",
            "nativePurchase": {
                "orderId": "GPA.3327-7455-9225-45725",
                "packageName": "com.onlineconnections.seniornextDev",
                "productId": "m1_14_200_premium",
                "purchaseTime": *************,
                "purchaseState": 0,
                "purchaseToken": "ocklioocbmkidopmcnjcljka.AO-J1OwDuvJNCr-kqyK0HuXliiTqhBfZtm2DqgMpg5u8E-P1PRwiodDKAbbI3bKL7Z_nRgQhJ3KPLou_dhWrH2IkGe3bAaYr84gs0rlWSyDsB3Ze777HJXM",
                "obfuscatedAccountId": "8febc4779d4b348f8198d111c775213b",
                "quantity": 1,
                "autoRenewing": true,
                "acknowledged": false,
                "productIds": [
                    "m1_14_200_premium"
                ],
                "getPurchaseState": 1,
                "developerPayload": "",
                "accountId": "8febc4779d4b348f8198d111c775213b",
                "profileId": "",
                "signature": "...",
                "receipt": "{\"orderId\":\"GPA.3327-7455-9225-45725\",\"packageName\":\"com.onlineconnections.seniornextDev\",\"productId\":\"m1_14_200_premium\",\"purchaseTime\":*************,\"purchaseState\":0,\"purchaseToken\":\"ocklioocbmkidopmcnjcljka.AO-J1OwDuvJNCr-kqyK0HuXliiTqhBfZtm2DqgMpg5u8E-P1PRwiodDKAbbI3bKL7Z_nRgQhJ3KPLou_dhWrH2IkGe3bAaYr84gs0rlWSyDsB3Ze777HJXM\",\"obfuscatedAccountId\":\"8febc4779d4b348f8198d111c775213b\",\"quantity\":1,\"autoRenewing\":true,\"acknowledged\":false}"
            },
            "purchaseId": "ocklioocbmkidopmcnjcljka.AO-J1OwDuvJNCr-kqyK0HuXliiTqhBfZtm2DqgMpg5u8E-P1PRwiodDKAbbI3bKL7Z_nRgQhJ3KPLou_dhWrH2IkGe3bAaYr84gs0rlWSyDsB3Ze777HJXM",
            "purchaseDate": "2023-09-19T15:00:17.091Z",
            "isPending": false,
            "isAcknowledged": false,
            "renewalIntent": "Renew"
        }


    }

*/
