import { Injectable, NgZone } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { Platform } from "@ionic/angular";
import { Router } from "@angular/router";
import { Utils } from "../helpers/utils";
import { Device } from "@capacitor/device";
import { App } from "@capacitor/app";

@Injectable({
  providedIn: "root",
})
export class MiscService {
  public devicePlatform: string;

  constructor(
    private translate: TranslateService,
    private platform: Platform,
    private router: Router,
    private ngZone: NgZone
  ) {
    this.getPlatform();
  }

  /**
   * Execute the fuction as soon as the application is in the foreground
   */
  public async executeWhenAppActive(
    callback: Function,
    checkFrequency: number = 1000,
    execDelay: number = 0
  ): Promise<any> {
    // check if we're active right now to avoid the delay

    let { isActive } = await App.getState();

    if (isActive) {
      return Promise.resolve(callback());
    }

    // if not check in 1000 sec

    return new Promise((resolve) => {
      let interval = setInterval(async () => {
        let { isActive } = await App.getState();

        if (isActive) {
          clearInterval(interval);

          // if we have exec delay
          if (execDelay) {
            setTimeout(() => {
              resolve(callback());
            }, execDelay);
          } else {
            resolve(callback());
          }
        }
      }, checkFrequency);
    });
  }

  /**
   * Helper function to re-run angular template renerer
   * !!! USE IT ONLY WHEN ABSOLUTELY NECESSARY !!!
   */
  public async ngForceTemplateUpdate() {
    this.ngZone.run(() => {
      return Promise.resolve();
    });
  }

  private async _restartApp() {
    await this.router.navigate(["/"], { replaceUrl: true });
    // @todo show splash
    window.location.reload();
  }

  /**
   * Restart application
   */
  public async restartApplication() {
    await this._restartApp();
  }

  /**
   * See if it has translation or not
   *
   * @param key
   * @returns
   */
  public hasTranslation(key: string) {
    return this.translate.instant(key) != key;
  }

  /**
   * check if the current url is url
   * @param url
   */
  public checkUrl(url: string) {
    return Utils.checkUrl(this.router.url, url);
  }

  public isAppUrl() {
    return Utils.isAppUrl(this.router.url);
  }

  public isErrorUrl() {
    return Utils.isErrorUrl(this.router.url);
  }

  public gotoErrorUrl() {
    if (!this.isErrorUrl()) {
      this.router.navigate(this.isAppUrl() ? ["/app/error"] : ["/error"], {
        replaceUrl: true,
      });
    }
  }

  /**
   * Check if cordova is active ( or we run stuff in browser)
   */
  public isNativePlatform() {
    return this.platform.is("cordova") || this.platform.is("capacitor");
  }

  /**
   * Get proper platform names
   */
  public async getPlatform() {
    if (this.isNativePlatform()) {
      let info = await Device.getInfo();
      this.devicePlatform = info.platform.toLowerCase();

      return;
    }

    this.devicePlatform = this.platform.is("ios") ? "ios" : "android";
  }

  /**
   * Helper to get error message from standard api error response safely,
   * with using key translation if available, and with a fallback to default
   * if object cannot be parsed for any reason
   *
   * @param errorResult
   * @param defaultKey
   */
  public getErrorText(
    errorResult: any,
    defaultKey: string = "default_error_message"
  ) {
    if (!!!errorResult?.errors && !!errorResult?.error) {
      errorResult = errorResult.error;
    }

    if (!defaultKey) {
      defaultKey = "default_error_message";
    }

    // is valid error object
    if (
      errorResult &&
      errorResult.errors &&
      errorResult.errors[0] &&
      errorResult.errors[0].error
    ) {
      let _error = errorResult.errors[0].error;

      // do we have translation? translate and return
      if (!!_error && this.hasTranslation(_error)) {
        return this.translate.instant(_error);
      }

      // we don't have translation, we return error got from server
      return _error;
    }

    // we return default error when no valid data from server
    return this.translate.instant(defaultKey);
  }

  /**
   * Helper to return error key if exists from error object
   * @param errorResult
   * @param defaultKey
   * @returns
   */
  public getErrorKey(
    errorResult: any,
    defaultKey: string = "default_error_message"
  ) {
    if (!!!errorResult?.errors && !!errorResult?.error) {
      errorResult = errorResult.error;
    }

    // is valid error object
    if (
      errorResult &&
      errorResult &&
      errorResult.errors &&
      errorResult.errors[0] &&
      errorResult.errors[0].key
    ) {
      let _key = errorResult.errors[0].key;

      // have error key?
      if (!!_key) {
        return _key;
      }
    }

    return defaultKey;
  }
}
