import { Injectable } from "@angular/core";
import {
  <PERSON><PERSON>,
  DeviceId,
  DeviceInfo,
  GetLanguageCodeResult,
} from "@capacitor/device";

@Injectable({
  providedIn: "root",
})
export class DeviceInfoService {
  private info: DeviceInfo;
  private id: DeviceId;
  private languageId: GetLanguageCodeResult;
  private infoFilteredObj: Object;

  public async init() {
    this.info = await Device.getInfo();
    this.id = await Device.getId();
    this.languageId = await Device.getLanguageCode();

    let _fi = {};
    let _addOnly = [
      "model",
      "platform",
      "osVersion",
      "manufacturer",
      "isVirtual",
      "webViewVersion",
    ];

    Object.keys(this.info).forEach((key) => {
      if (_addOnly.indexOf(key) >= 0) {
        _fi[key] = this.info[key];
      }
    });

    this.infoFilteredObj = _fi;
  }

  /**
   * Get device platform ( android or ios )
   */
  get platform(): "android" | "ios" {
    // we limit the platform only for ios or android
    return this.info?.operatingSystem == "ios" ? "ios" : "android";
  }

  /**
   * Get device uuid
   */
  get uuid(): string {
    return this.id.identifier;
  }

  /**
   * Get language id
   */
  get lngId(): string {
    return this.languageId.value;
  }

  /**
   * Get the whole device info object
   */
  get infoObject(): DeviceInfo {
    return this.info;
  }

  /**
   * Return device info as a joined string
   */
  get infoFilteredObject(): Object {
    return this.infoFilteredObj;
  }
}
