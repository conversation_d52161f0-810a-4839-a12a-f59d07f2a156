import { Injectable } from "@angular/core";
import {
  ActionSheetController,
  AlertController,
  PopoverController,
  ModalController,
} from "@ionic/angular";
import { Debug } from "../helpers/debug";

@Injectable({
  providedIn: "root",
})
export class AutocloseOverlaysService {
  constructor(
    private dbg: Debug,
    private actionSheetCtrl: ActionSheetController,
    private popoverCtrl: PopoverController,
    private modalCtrl: ModalController,
    private alertCtrl: AlertController
  ) {}

  async trigger(): Promise<boolean> {
    // close action sheet
    try {
      const element = await this.actionSheetCtrl.getTop();
      if (element) {
        element.dismiss();
        return Promise.resolve(true);
      }
    } catch (error) {
      this.dbg.le("close action sheet", error);
      return Promise.reject();
    }

    // close popover
    try {
      const element = await this.popoverCtrl.getTop();
      if (element) {
        element.dismiss();
        return Promise.resolve(true);
      }
    } catch (error) {
      this.dbg.le("close popover", error);
      return Promise.reject();
    }

    // close modal
    try {
      const element = await this.modalCtrl.getTop();
      if (element) {
        element.dismiss();
        return Promise.resolve(true);
      }
    } catch (error) {
      this.dbg.le("close modal", error);
      return Promise.reject();
    }

    // close alert
    try {
      const element = await this.alertCtrl.getTop();
      if (element) {
        element.dismiss();
        return Promise.resolve(true);
      }
    } catch (error) {
      this.dbg.le("close alert", error);
      return Promise.reject();
    }
  }
}
