import { Injectable } from "@angular/core";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { ApiCommands } from "src/app/services/api/api.commands";
import { map, tap } from "rxjs/operators";
import { firstValueFrom, Observable, Subject } from "rxjs";
import { GalleryImage } from "../schemas/gallery-image";
import { ApiParams } from "src/app/services/api/api.params";
import {
  Message,
  MessageGetReadTs,
  MessageUnreadCount,
} from "../schemas/message";
import { ChatIncomingService } from "./chat-incoming.service";
import _ from "lodash";
import { AccountService } from "./account.service";
import { ChatMessage } from "./chat-message.interface";
import { Utils } from "../helpers/utils";

@Injectable({
  providedIn: "root",
})
export class ChatMessageService {
  public attachImages: GalleryImage.type[] = [];
  public selectedAttachImages: GalleryImage.type[] = [];

  /**
   * Photo requests.
   *
   * @type {Array}
   */
  public photoRequests: number[] = [];

  constructor(
    private api: ApiService,
    private chatIncomming: ChatIncomingService,
    private account: AccountService,
  ) {}

  public async init(): Promise<void> {
    await this.getPhotoRequests();
  }

  /**
   * Get image list for images that we can attach
   */
  public getAttachableImages(): Promise<GalleryImage.type[]> {
    return firstValueFrom(
      this.api
        .call<ApiResponses.MessagesImageResponse.type>({
          command: ApiCommands.MessagesGetImages.name,
          method: "get",
          useToken: true,
        })
        .pipe(
          map((data) => {
            return (this.attachImages = data?.data?.items || []);
          }),
        ),
    );
  }

  public getSelectedAttachableImages(): GalleryImage.type[] {
    return (this.selectedAttachImages = <GalleryImage.type[]>(
      _.filter(this.attachImages, (img) => img["selected"])
    ));
  }

  public removeFromSelectedImages(img): GalleryImage.type[] {
    return (this.selectedAttachImages = _.filter(
      this.selectedAttachImages,
      (data) => data !== img,
    ));
  }

  public clearAttachableImages(): void {
    this.attachImages = [];
    this.selectedAttachImages = [];
  }

  /**
   * Get messages with a specific user
   */
  public getMessages(
    params: ApiParams.MessagesGet.type | any,
  ): Observable<ChatMessage[]> {
    params["get_attachments"] = 1;

    return this.api
      .call<ApiResponses.MessagesResponse.type>({
        command: ApiCommands.MessagesGet.name,
        method: "get",
        useToken: true,
        params: params,
        identifier: "" + params.user_id,
      })
      .pipe(
        map((data) => {
          let _data: ChatMessage[] = _.map(data?.data?.items || [], (item) => {
            return this.converToChatMessage(item);
          });

          return _data;
        }),
      );
  }

  /**
   * Is tehre a get command in progress?
   * @returns
   */
  public isInProgress() {
    return this.api.isInEventList({
      //identifier: "" + user_id,
      command: ApiCommands.MessagesGet.name,
    });
  }

  public isCanSendInProgress() {
    return this.api.isInEventList({
      //identifier: "" + user_id,
      command: ApiCommands.CanSendMessage.name,
    });
  }

  /**
   * Is there a get error
   * @returns
   */
  public wasGetError(user_id: number) {
    return this.api.isInErrorList({
      command: ApiCommands.MessagesGet.name,
      identifier: "" + user_id,
    });
  }

  /**
   * Return unread count list for giver user ids
   * @param user_id_list
   * @returns
   */
  public getUnreadCountList(
    user_id_list: number[],
  ): Observable<MessageUnreadCount.type[]> {
    let params: ApiParams.MessagesUnreadCount.type = {
      "user_ids[]": user_id_list,
    };

    return this.api
      .call<ApiResponses.MessagesUnreadCountResponse.type>({
        command: ApiCommands.MessagesUnreadCount.name,
        useToken: true,
        method: "get",
        params: params,
      })
      .pipe(
        map((data) => {
          return data?.data?.items || [];
        }),
      );
  }

  /**
   * See when did we read a message with a user, or when did they read our messages (last time)
   * @param user_id
   * @param doGetReverse
   * @returns
   */
  public getReadTs(
    user_id: number,
    doGetReverse: boolean = false,
  ): Observable<MessageGetReadTs.type> {
    let params: ApiParams.MessagesGetReadTs.type = {
      user_id: user_id,
      revers: doGetReverse ? 1 : 0,
    };

    return this.api
      .call<ApiResponses.MessagesGetReadTsResponse.type>({
        command: ApiCommands.MessagesGetReadTs.name,
        useToken: true,
        params: params,
        method: "get",
      })
      .pipe(
        map((data) => {
          return data?.data;
        }),
      );
  }

  /**
   * Set read TS with a user to now
   * @param user_id
   * @returns
   */
  public setReadTs(user_id: number): Promise<ApiResponses.NoDataResponse.type> {
    let params = <ApiParams.MessagesPostReadTs.type>{
      user_id: user_id,
    };

    // call api
    return firstValueFrom(
      this.api
        .call<ApiResponses.NoDataResponse.type>({
          command: ApiCommands.MessagesPostReadTs.name,
          method: "post",
          useToken: true,
          params: params,
        })
        .pipe(
          tap({
            next: () => {
              // emmit read event
              this._markReadEmiter$.next(user_id);
            },
          }),
        ),
    );
  }

  /**
   * Mark thread list as read
   *
   * @param thread_ids
   * @returns
   */
  public async markAsRead(
    thread_ids: number[],
  ): Promise<ApiResponses.NoDataResponse.type> {
    return await firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.InboxMarkAsReadCommand.name,
        method: "post",
        useToken: true,
        params: {
          threads: thread_ids,
        },
      }),
    );
  }

  /**
   * Mark thread list as unread
   *
   * @param thread_ids
   * @returns
   */
  public async markAsUnread(
    thread_ids: number[],
  ): Promise<ApiResponses.NoDataResponse.type> {
    return await firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.InboxMarkAsUnreadCommand.name,
        method: "post",
        useToken: true,
        params: {
          threads: thread_ids,
        },
      }),
    );
  }

  public async deleteThreads(
    thread_ids: number[],
  ): Promise<ApiResponses.NoDataResponse.type> {
    return await firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.InboxDeleteThreads.name,
        method: "delete",
        useToken: true,
        params: {
          threads: thread_ids,
        },
      }),
    );
  }

  /**
   * Subject to emmit when we mark a conversation as read
   * emmits user id
   */
  private _markReadEmiter$ = new Subject<number>();

  /**
   * Subject to emmit when we mark a conversation as read
   * emmits user id
   */
  get markReadEmiter$(): Observable<number> {
    return this._markReadEmiter$.asObservable();
  }

  /**
   * Set received TS with a user to now
   * @param user_id
   * @returns
   */
  public setReceivedTs(
    user_id: number,
  ): Promise<ApiResponses.NoDataResponse.type> {
    let params = <ApiParams.MessagesReceivedTs.type>{
      user_id: user_id,
    };

    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.MessagesReceivedTs.name,
        method: "post",
        useToken: true,
        params: params,
      }),
    );
  }

  /**
   * Convert server result to ChatMessage
   * @param msg
   * @returns
   */
  public converToChatMessage(msg: Message.type): ChatMessage {
    let _new = <ChatMessage>{
      id: msg.id || "0",
      isOwn: msg?.user_id && msg?.user_id == this?.account?.account?.user_id,
      text: msg.body,
      time: msg.ts,
      attachments: msg.attachments,
      userId: msg.user_id,
      originalData: msg,
      system_msg: msg.system_msg ?? false,
      cometData: undefined,
    };

    return _new;
    //return _.merge(msg, _new);
  }

  /**
   * Is message send in progress?
   * @returns
   */
  public isSending() {
    return this.api.isInEventList({
      command: ApiCommands.SendMessage.name,
      method: "post",
    });
  }

  /**
   * Send message to a user
   * @param params
   * @returns
   */
  public sendMessage(
    params: ApiParams.SendMessage.type,
    attachments: GalleryImage.type[] = [],
  ): Promise<ApiResponses.PostMessageResponse.type> {
    let _params: any = params;

    // this param is not implemented yet on server, we don't send it
    delete _params["delivery_notify"];

    // emmit message sent

    return firstValueFrom(
      this.api
        .call<ApiResponses.PostMessageResponse.type>({
          command: ApiCommands.SendMessage.name,
          method: "post",
          useToken: true,
          params: _params,
        })
        .pipe(
          tap((data) => {
            // emmit message

            let msg: Message.type = {
              attachments: attachments,
              body: params.body,
              id: parseInt(data.data.msg_id),
              thread_id: parseInt(data.data.thread_id),
              ts: Utils.now(),
              user_id: this.account.account.user_id,
              target_username: params.username,
            };

            this.chatIncomming.emmitMessage(this.converToChatMessage(msg));
          }),
        ),
    );
  }

  /**
   * determines if user can send message to a thread
   * promise resolves if it can, reject if not
   * @param thread_id if null then we'll see if user can send in general
   */
  public canSendMessage(thread_id: string = null): Promise<any> {
    let params = thread_id ? { thread_id: thread_id } : {};

    return firstValueFrom(
      this.api.call<any>({
        command: ApiCommands.CanSendMessage.name,
        method: "get",
        useToken: true,
        params: params,
      }),
    );
  }

  /**
   * Tells if there's a happy hour, and returns data about duration if the promise resolves
   * NOTE: if it's not happy hour does not mean that the user cannot send message,
   * it only means that it's not because of the happy hour
   *
   * so use this function to check if we need to display happy hour message, or something like that !!!
   *
   * NOTE: when you get the result, if start > 0 that means that the happy hour did not begin yet,
   * if start == 0 and end >0 that means that it has begun
   *
   * if start >0 display the dialog on when will it begin, if start==0 display the dialog on when will it end
   *
   */
  public checkHappyHour(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.canSendMessage()
        .then((data) => {
          // if data.data.free_message.end >0 then it's happy hour
          if (data?.data?.free_message) {
            resolve(data.data.free_messages);
          } else {
            reject();
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  /**
   * Can user send free message.
   *
   * @returns {ApiResponses.MessagesFreeResponse.type}
   */
  public free(): Promise<ApiResponses.MessagesFreeResponse.type> {
    return new Promise<ApiResponses.MessagesFreeResponse.type>(
      (resolve, reject) => {
        firstValueFrom(
          this.api.call<ApiResponses.MessagesFreeResponse.type>({
            command: ApiCommands.FreeMessage.name,
            method: "get",
            useToken: true,
          }),
        )
          .then((data) => {
            this.api.clearErrorList();
            resolve(data);
          })
          .catch((error) => {
            this.api.clearErrorList();
            reject(error);
          });
      },
    );
  }

  /**
   * Return a list of user ID's that received photo requests by logged in user.
   *
   * @return {Promise<ApiResponses.InboxGetPhotoRequestsResponse>}
   */
  public getPhotoRequests() {
    return firstValueFrom(
      this.api
        .call<ApiResponses.InboxGetPhotoRequestsResponse.type>({
          command: ApiCommands.InboxPhotoRequests.name,
          method: "get",
          useToken: true,
        })
        .pipe(
          tap(async (data) => {
            // keep data
            this.photoRequests = data.data.items;
          }),
        ),
    );
  }
}
