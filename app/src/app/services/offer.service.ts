import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { Debug } from "../helpers/debug";
import { ApiCommands } from "./api/api.commands";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { StorageService } from "./storage.service";

@Injectable({
  providedIn: "root",
})
export class OfferService {
  constructor(private api: ApiService) {}

  public get(): Observable<ApiResponses.OffersResponse.type> {
    return this.api.call<ApiResponses.OffersResponse.type>({
      command: ApiCommands.Offers.name,
      method: "get",
      useToken: true,
      callType: "background",
    });
  }
}
