import { Injectable } from "@angular/core";
import { IapItemArray } from "./iap-item.interface";

export type IapItemTypes =
  | "subscriptions"
  | "offers"
  | "campaigns"
  | "discontinued";

/**
 * Helper service to work with in app purchases, goal would be to
 * have an uniform layer to work with independent from the real
 * plugins, so we can test it properly, and if we want to use a new plugin
 * we'll not need to re-write this part
 */
@Injectable({
  providedIn: "root",
})
export class IapHelperService {
  /**
   * Container for IAP items
   */
  private items = {
    subscriptions: <IapItemArray>[],
    offers: <IapItemArray>[],
    campaigns: <IapItemArray>[],
    discontinued: <IapItemArray>[],
  };

  /**
   * Set item list with specific key
   *
   * @param key
   * @param items
   */
  public setItemList(key: IapItemTypes, items: IapItemArray) {
    items[key] = items;
  }

  /**
   * Draft plan of what we want:
   *
   * - we need a way to inject products to this plugin
   * - we need a way to open dialogs from this plugin
   * - we need a way from this plugin to initiate purchase ( reapl purchase ) a sort of callback
   * - all analytics should be handled by this service
   * - this plugin should accept a uniformized data format, and when we connect with real plugin that should adopt to it
   *
   * Data types:
   *
   * - we have upgrade page - when user can choose subscription
   * - we have promo page - when user clicks on an offer from deeplink or something
   * - we have trial offer page - when user gets an offer on welcome ( try premium )
   *
   * Would be good:
   *
   * - to ask server to give user premium if user has a subscription in app,
   *   but does not have premium status ... this can be problematic, because
   *   banned users will have this situation ... or will they? @todo ask this!
   *
   * - instead of restarting application on upgrade, we should just update all the services
   *   for this every relevant service would need an interface with a refresh function
   *   and we'd put the services to an array into misc class, which would refresh them
   *   all - would just iterate through them and call the refresh function after upgrade
   */

  constructor() {}
}
