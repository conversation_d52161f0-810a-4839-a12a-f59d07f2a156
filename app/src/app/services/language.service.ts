import { Injectable } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { environment as Env } from "../../environments/environment";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { ApiCommands } from "./api/api.commands";
import { Debug } from "../helpers/debug";
import { StorageService } from "./storage.service";
import { Device } from "@capacitor/device";
import { MiscService } from "./misc.service";
import { AlertController, AlertInput } from "@ionic/angular";
import { DialogHelperService } from "./dialog-helper.service";
import _ from "lodash";
import { firstValueFrom } from "rxjs";

export const APP_SUPPORTED_LANGUAGES: string = "app_supported_languages";
export const APP_TRANSLATION: string = "app_translation";
export const APP_LANGUAGE: string = "app_language";

@Injectable({
  providedIn: "root",
})
export class LanguageService {
  /**
   * App language.
   */
  public appLanguage: string = Env.DEFAULT_LANGUAGE;

  /**
   * Supported languages.
   */
  public supportedLanguages: Object = Env.SUPPORTED_LANGUAGES;

  constructor(
    public translate: TranslateService,
    private api: ApiService,
    private dbg: Debug,
    private storage: StorageService,
    private misc: MiscService,
    public alertController: AlertController,
    public dlg: DialogHelperService,
  ) {}

  /**
   * Init.
   */
  public initialize(): Promise<void> {
    return this.getSupportedLanguages()
      .then(() => this.storage.get(APP_LANGUAGE))
      .then((data) => {
        // we don't have stored language, use local translation
        if (!data || !data.code || !data.translation) {
          this.useLocalTranslation();
          // invalid data
          this.dbg.l("stored test language is invalid", data);
          throw new Error("stored test language is invalid");
        }

        // set fresh data
        this.changeLanguage(data.code);
      })
      .catch((err) => {
        // no stored language, change it to app default
        this.setPrefferedLanguage();
      });
  }

  /**
   * Show language select dialog.
   * @return {Promise<void>}
   */
  public async showLanguageSelectDialog(): Promise<void> {
    let inputs: AlertInput[] = await (<AlertInput[]>Object.keys(
      this.supportedLanguages,
    ).map((val) => {
      return {
        name: "lang",
        type: "radio",
        label: this.supportedLanguages[val],
        value: val,
        handler: () => this.dbg.l("language select", val),
      };
    }));

    inputs.forEach((val) => {
      if (val.value === this.getAppLanguageId()) val["checked"] = true;
    });

    // sort languages by name
    inputs = _.sortBy(inputs, ["label"]);

    const alert = await this.alertController.create({
      cssClass: "my-custom-class",
      header: this.translate.instant("select_language"),
      inputs,
      buttons: [
        {
          text: this.translate.instant("default_language"),
          handler: async () => {
            this.dbg.l("auto select language");
            this.storage.remove(APP_LANGUAGE);
            this.storage.remove(APP_TRANSLATION);
            await this.dlg.showLoading();
            await this.changeLanguage(Env.DEFAULT_LANGUAGE);
            await this.dlg.hideLoading();
          },
        },
        {
          text: this.translate.instant("cancel"),
          role: "cancel",
          cssClass: "secondary",
          handler: () => {},
        },
        {
          text: this.translate.instant("ok"),
          handler: async (data) => {
            await this.dlg.showLoading();
            await this.changeLanguage(data);
            await this.dlg.hideLoading();
          },
        },
      ],
    });

    await alert.present();
  }

  /**
   * Get supported languages.
   * @return {Promise<void>}
   */
  private getSupportedLanguages(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.all()
        .then((result) => {
          this.dbg.l("Get all supported languages", result.data.items);

          // set supported languages only if we get valid value from the server
          // otherwise keep the default
          if (result.data && result.data.items) {
            this._setData("all", result.data.items);
            this.supportedLanguages = result.data.items;
          }

          resolve();
        })
        .catch((error) => {
          this.dbg.le("Get all supported languages error", error);
          this._setData("all", Env.SUPPORTED_LANGUAGES);
          this.supportedLanguages = Env.SUPPORTED_LANGUAGES;
          this.useLocalTranslation();

          reject();
        });
    });
  }

  /**
   * Return an array of all supported languages.
   *
   * @return {Promise<LanguageAllResponse>}
   */
  private all(): Promise<ApiResponses.LanguageAllResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.LanguageAllResponse.type>({
        command: ApiCommands.LanguageAll.name,
        method: "get",
        params: { system_id: Env.API_SYSTEM_ID },
        useToken: false,
        commandVersion: "2",
      }),
    );
  }

  /**
   * App langauge set wrapper
   * @param lngId
   * @return {void}
   */
  private setAppLanguageId(lngId: string): void {
    if (!this.supportedLanguages[lngId]) {
      this.appLanguage = Env.DEFAULT_LANGUAGE;
      this.dbg.le("Unsupported language selected! Set language to default!");
    } else {
      this.appLanguage = lngId;
    }

    this.api.setAppLanguage(this.appLanguage);
  }

  /**
   * Set preffered language
   * @return {Promise<void>}
   */
  private async setPrefferedLanguage(): Promise<void> {
    if (this.misc.isNativePlatform()) {
      this.changeLanguage(await this.getDeviceLanguage()).catch((e) =>
        this.dbg.le("lng change error", e),
      );
    } else {
      // if no cordova, set default language.
      this.changeLanguage(Env.DEFAULT_LANGUAGE).catch((e) => {
        this.dbg.le("lng change error", e);
      });
    }
  }

  /**
   * Helper function to change device language, set locale, save test storage data if necessary
   *
   * @param lang
   * @return {Promise<any>}
   */
  private changeLanguage(lang): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.storage
        .get(APP_SUPPORTED_LANGUAGES)
        .then(async (data) => {
          if (data && Object.keys(data).indexOf(lang) > -1) {
            try {
              if (Env.USE_LOCAL_LANGUAGE_FILES) {
                this.dbg.l("Use local translation");
                this.useLocalTranslation(lang);
              } else {
                this.dbg.l("Get fresh translation");
                this.getTranslation(lang).catch(() => {});
              }
              resolve(true);
            } catch (e) {
              this.dbg.le("set translation error", e);
              reject();
            }
          } else {
            reject();
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * Return an object, translation strings for specific language (input param is country code).
   *
   * @param country
   * @return {Promise<LanguageTranslationResponse>}
   */
  private translation(
    country: string,
  ): Promise<ApiResponses.LanguageTranslationResponse.type> {
    this.setAppLanguageId(country);
    return firstValueFrom(
      this.api.call<ApiResponses.LanguageTranslationResponse.type>({
        command: ApiCommands.LanguageTranslation.name,
        method: "get",
        params: {
          system_id: Env.API_SYSTEM_ID,
          country,
        },
        useToken: false,
        commandVersion: "2",
      }),
    );
  }

  /**
   * Get translation.
   *
   * @param country
   * @return {Promise<any>}
   */
  private getTranslation(code: string = Env.DEFAULT_LANGUAGE): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.translation(code)
        .then((result) => {
          if (!result.data) {
            this.useLocalTranslation(code);
            this.dbg.l("get translation error", result);
            return reject();
          }

          this.dbg.l("Get translation", result);
          this.setAppLanguageId(code);
          this.translate.setTranslation(code, result.data.items);
          this.translate.use(code);

          this._setData("translation", result.data.items);
          this._setData("test", { code, translation: result.data.items });

          resolve(result.data.items);
        })
        .catch((error) => {
          this.dbg.le("Get translation error", error);
          this.useLocalTranslation(code);

          reject();
        });
    });
  }

  /**
   * Get app language safely with supported language lookup
   * @returns {string}
   */
  public getAppLanguageId(): string {
    if (Object.keys(this.supportedLanguages).indexOf(this.appLanguage) < 0) {
      return Env.DEFAULT_LANGUAGE;
    }

    return this.appLanguage;
  }

  /**
   * Set data locally
   *
   * @param {string} key
   * @param {any} data
   * @private
   * @return {void}
   */
  private _setData(key: string, data: any): void {
    // save to cache
    switch (key) {
      case "all":
        this.storage.set(APP_SUPPORTED_LANGUAGES, data);
        break;
      case "translation":
        this.storage.set(APP_TRANSLATION, data);
        break;
      case "test":
        this.storage.set(APP_LANGUAGE, data);
        break;
    }
  }

  /**
   * Use local translation when we don't have saved translation.
   * @return {Promise<void>}
   */
  private async useLocalTranslation(code: string = null): Promise<void> {
    this.setAppLanguageId(code ?? (await this.getDeviceLanguage()));

    this.translate.reloadLang(this.appLanguage).subscribe({
      next: (res) => {
        this._setData("translation", res);
        this._setData("test", { code, translation: res });
      },
      error: (err) => console.error("Translation load failed", err),
    });
  }

  /**
   * Get device language code.
   * @return {Promise<string>}
   */
  private async getDeviceLanguage(): Promise<string> {
    let lang = await Device.getLanguageCode();
    return lang.value.split("-")[0];
  }
}
