import { TranslateService } from "@ngx-translate/core";
import { Injectable } from "@angular/core";
import { FormGroup, ValidationErrors } from "@angular/forms";
import { environment as Env } from "../../environments/environment";

@Injectable()
export class ErrorHelper {
  constructor(public translate: TranslateService) {}

  /**
   * Get error string list from formgroup
   *
   * @param _fg formGroup
   */
  public getFormValidationErrors(_fg: FormGroup) {
    let errors: string[] = [];
    Object.keys(_fg.controls).forEach((key) => {
      let translateKey: string = "form_error_" + key;
      const controlErrors: ValidationErrors = _fg.get(key).errors;
      if (controlErrors != null) {
        let message: string = "";
        Object.keys(controlErrors).forEach((keyError) => {
          if (key === "age" && keyError === "min") {
            message = this.translate.instant(
              translateKey + "_" + keyError + "_1"
            );
            message += " " + Env.APP_VALID_AGE + " ";
            message += this.translate.instant(
              translateKey + "_" + keyError + "_2"
            );
          } else {
            message = this.translate.instant(translateKey + "_" + keyError);
          }
          errors.push(message);
        });
      }
    });

    return errors;
  }

  /**
   * Get error object list from formgroup
   *
   * @param _fg formGroup
   */
  public getFormValidationErrorsWithKeys(_fg: FormGroup) {
    let errors: Object[] = [];
    Object.keys(_fg.controls).forEach((key) => {
      const controlErrors: ValidationErrors = _fg.get(key).errors;
      if (controlErrors != null) {
        Object.keys(controlErrors).forEach((keyError) => {
          errors.push({
            [key]: this.translate.instant(
              "form_error_" + key + "_" + keyError + ""
            ),
          });
        });
      }
    });

    return errors;
  }

  /**
   * do we have error for the given controller
   *
   * @param _fg form group controller
   * @param ctrlName control name
   * @returns {boolean}
   */
  public controlHasError(_fg: FormGroup, ctrlName: string): boolean {
    return _fg.get(ctrlName).errors != null;
  }
}
