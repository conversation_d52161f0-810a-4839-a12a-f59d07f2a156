import { Injectable } from "@angular/core";
import { firstValueFrom, Observable } from "rxjs";
import { Debug } from "../helpers/debug";
import { AccountService } from "./account.service";
import { ApiCommands } from "./api/api.commands";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { DialogHelperService } from "./dialog-helper.service";
import { MiscService } from "./misc.service";
import { QueueCommandService } from "./queue/queue-command.service";
import { StorageService } from "./storage.service";
import _ from "lodash";
import { AppsflyerHelperService } from "./appsflyer-helper.service";
import { StateService } from "./state.service";
import { RefresherService } from "./refresher.service";

export const KEY_PROMO_DATA = "promoData";

/**
 * Info:
 *
 * - deeplink user to upgrade page in app: site.com/upgrade
 * - deeplink user to Try permium offer page: site.com/upgrade?mobile_offer=1
 *   this will still work on desktop if user opens it there
 *
 */

@Injectable({
  providedIn: "root",
})
export class PromoService {
  constructor(
    private api: ApiService,
    private storageService: StorageService,
    private accountService: AccountService,
    private dlg: DialogHelperService,
    private misc: MiscService,
    private dbg: Debug,
    private queueCommand: QueueCommandService,
    private appsflyerService: AppsflyerHelperService,
    private stateService: StateService,
    private refresherService: RefresherService,
  ) {
    this.appsflyerService.attributionData$.subscribe({
      next: (data) => {
        // if the app queue is done, then handle promo code, if not then save it for the queue

        this.dbg.l("appsflyer attrib data sub", data);

        if (data?.app_promo_code) {
          if (
            !(
              this.accountService?.account?.user_id &&
              this.stateService.get("queueFinished", "", false)
            )
          ) {
            // if user not logged in or queue not finished
            this.storePromoData(data);
          } else {
            // if user is logged in and queue finished, handle stuff
            this.handleAppsflyerPromo(data);
          }
        }
      },
    });
  }

  private _hasDelayedData: boolean = false;

  public get hasDelayedData() {
    return this._hasDelayedData;
  }

  /**
   * Handle appsflyer promo data
   * @param data
   */
  public handleAppsflyerPromo(data: object) {
    // do we have a promo related data in appsflyer attribution stuff

    if (_.isObject(data) && data["app_promo_code"]) {
      // queue finished handle instantly and silently
      // it will not show message only if successfully
      // promoted
      this.handlePromoData(data, true, () => {
        // goto next queue if done
        this.queueCommand.gotoNextItem();
      });
    } else {
      this.queueCommand.gotoNextItem();
    }
  }

  /**
   * Helper call for manual promo code validation / handling
   * @param code
   * @returns
   */
  public handlePromoCode(code: string) {
    return this.handlePromoData({
      app_promo_code: code,
    });
  }

  /**
   * Handle promo data
   * @param data
   * @returns
   */
  public handlePromoData(
    data: object,
    isSilent: boolean = false,
    doneCallback: Function = () => {},
  ) {
    if (!data["app_promo_code"]) {
      doneCallback();
      return;
    }

    if (!isSilent) {
      this.dlg.showToast("promo_code", "checking", "loading", 500);
    }

    firstValueFrom(this.checkPromoData(data))
      .then((data) => {
        console.log(data);

        if (!data.data?.display_message) {
          if (!isSilent) {
            this.dlg.showToast("promo_code", "invalid_code", "warning");
          }
          doneCallback();
          return;
        }

        // should we restart the application?
        let _doRestart = data.data?.type == "premium";

        this.dlg.showAlert(
          "promotion",
          data.data?.message,
          _doRestart ? "activate" : "ok",
          () => {
            if (_doRestart) {
              this.refresherService.refresh();
            }
          },
        );

        // @todo if we check promo from appsflyer , do call queue next from boot?
      })
      .catch((error) => {
        this.dbg.le("Promo code error", error);

        // display error only if it's not s silent call
        if (!isSilent) {
          this.dlg.showToastBaseOnErrorResponse(error);
        }

        doneCallback();

        // @todo if we check promo from appsflyer , do call queue next from boot?
      });
  }

  /**
   * Show manual promo dialog / handle manual promo code entry
   */
  public showManualPromo() {
    this.dlg.showPrompt(
      "promo_code",
      "redeem_promo_code",
      (data) => {
        this.handlePromoCode(data?.code);
      },
      () => {},
      "enter_promo_code",
      "check",
    );
  }

  /**
   * Validate promo data with server
   *
   * @param user_id
   * @param promoData
   * @returns
   */
  public validatePromoData(
    promoData: object,
  ): Observable<ApiResponses.PromoResponse.type> {
    // debug: returns positive result on staging
    // promoData['test'] = true;

    promoData["user_id"] = this.accountService.account.user_id;

    return this.api.call<ApiResponses.PromoResponse.type>({
      command: ApiCommands.CheckPromoCode.name,
      method: "post",
      useToken: true,
      callType: ["background", "ignore_401"],
      params: promoData,
    });
  }

  /**
   * check a specific promo data
   * @param user_id
   * @param code
   * @returns
   */
  public checkPromoData(
    data: object,
  ): Observable<ApiResponses.PromoResponse.type> {
    let params = data || {};
    params["user_id"] = this.accountService.account.user_id;

    return this.api.call<ApiResponses.PromoResponse.type>({
      command: ApiCommands.CheckPromoData.name,
      method: "post",
      useToken: true,
      params: params,
    });
  }

  /**
   * check a specific promo code
   * @param user_id
   * @param code
   * @returns
   */
  public checkPromoCode(
    code: string,
  ): Observable<ApiResponses.PromoResponse.type> {
    let params: ApiParams.PromoCodeParams.type = {
      app_promo_code: code,
      user_id: this.accountService.account.user_id,
    };

    return this.api.call<ApiResponses.PromoResponse.type>({
      command: ApiCommands.CheckPromoData.name,
      method: "post",
      useToken: true,
      params: params,
    });
  }

  /**
   * load stored promo data from lodal storage
   * @returns
   */
  public loadPromoData(): Promise<any> {
    return this.storageService.get(KEY_PROMO_DATA);
  }

  /**
   * store promo data to local storage
   * @param data
   * @returns
   */
  public storePromoData(data: any): Promise<any> {
    return this.storageService.set(KEY_PROMO_DATA, data);
  }

  /**
   * Clear promo data from storage
   *
   * @returns
   */
  public clearPromoData(): Promise<any> {
    return this.storageService.remove(KEY_PROMO_DATA);
  }
}
