import { Injectable, OnInit } from "@angular/core";
import { Debug } from "../helpers/debug";
import {
  Camera,
  CameraDirection,
  CameraResultType,
  CameraSource,
  Photo,
} from "@capacitor/camera";
import { Capacitor } from "@capacitor/core";
import { SafeResourceUrl } from "@angular/platform-browser";
import { Platform } from "@ionic/angular";
import { ImageOptions } from "@capacitor/camera/dist/esm/definitions";
import { MiscService } from "./misc.service";
import { DialogHelperService } from "./dialog-helper.service";
import { LanguageService } from "./language.service";

import {
  AndroidSettings,
  IOSSettings,
  NativeSettings,
} from "capacitor-native-settings";

@Injectable({
  providedIn: "root",
})
export class CameraService implements OnInit {
  public photo: SafeResourceUrl;
  public isDesktop: boolean;

  constructor(
    public dbg: Debug,
    public platform: Platform,
    public misc: MiscService,
    public dlg: DialogHelperService,
  ) {}

  ngOnInit() {
    if (
      (this.platform.is("mobile") && this.platform.is("hybrid")) ||
      this.platform.is("desktop")
    ) {
      this.isDesktop = true;
    }
  }

  public async getPicture(
    _sourceType: CameraSource.Photos | CameraSource.Camera,
  ): Promise<Photo> {
    if (!Capacitor.isPluginAvailable("Camera")) {
      this.dbg.l("Camera plugin not available!");
      return;
    }

    return new Promise(async (resolve, reject) => {
      try {
        let imageData: Photo;

        if (_sourceType === CameraSource.Photos) {
          // pickImages() use system media browse (iOS & Android) - no need for permissions
          const images = await Camera.pickImages({
            quality: 100,
          });

          if (images.photos.length === 0) {
            return reject(new Error("No image selected"));
          }

          // take only first image
          const firstImage = images.photos[0];

          const base64String = await this.convertToBase64(firstImage.webPath);
          const imageFormat = this.getFileExtension(firstImage.webPath);

          // Photo object
          imageData = {
            base64String: base64String,
            format: imageFormat,
            webPath: firstImage.webPath,
            path: firstImage.path,
            exif: null,
            saved: false,
          };
        } else {
          const permissionStatus = await Camera.checkPermissions();

          if (
            !["granted", "prompt-with-rationale", "prompt"].includes(
              permissionStatus.camera,
            )
          ) {
            //no permissoin for camera, ask
            this.dlg.confirm(
              "camera_access_denied_title",
              "camera_access_denied_message",
              () => {
                NativeSettings.open({
                  optionAndroid: AndroidSettings.ApplicationDetails,
                  optionIOS: IOSSettings.App,
                });
              },
            );
            return;
          }

          let opt: ImageOptions = {
            quality: 100,
            resultType: CameraResultType.Base64,
            direction: CameraDirection.Front,
            saveToGallery: false,
            width: 1500,
            allowEditing: true,
            source: CameraSource.Camera,
          };

          imageData = await Camera.getPhoto(opt);
        }
        resolve(imageData);
      } catch (error) {
        reject(error);
      }
    });
  }

  private async convertToBase64(webPath?: string): Promise<string> {
    if (!webPath) {
      this.dbg.l("convertToBase64: webPath is undefined or null");
      return "";
    }

    try {
      const response = await fetch(webPath);
      if (!response.ok) {
        this.dbg.l(`Failed to fetch image: ${response.statusText}`);
        return "";
      }

      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64data = reader.result?.toString().split(",")[1];
          resolve(base64data || "");
        };
        reader.onerror = () => {
          reject(new Error("FileReader failed to convert Blob to Base64"));
        };
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      this.dbg.l("convertToBase64: Error converting image to Base64");
      return "";
    }
  }

  private getFileExtension(webPath: string): string {
    const extMatch = webPath.match(/\.([a-zA-Z0-9]+)$/);
    if (extMatch && extMatch[1]) {
      return extMatch[1].toLowerCase();
    }
    return "jpeg";
  }

  public async getPicture1(
    _sourceType: CameraSource.Photos | CameraSource.Camera,
  ): Promise<Photo> {
    if (!Capacitor.isPluginAvailable("Camera")) {
      this.dbg.l("Camera plugin not available!");
      return;
    }

    try {
      // Use Android Photo Picker for gallery pick
      if (_sourceType === CameraSource.Photos) {
        const images = await Camera.pickImages({
          quality: 90,
          limit: 1,
        });
        if (images.photos.length > 0) {
          const response = await fetch(images.photos[0].webPath);
          const blob = await response.blob();
          const reader = new FileReader();

          return new Promise((resolve) => {
            reader.onloadend = () => {
              const base64data = reader.result.toString().split(",")[1];
              resolve({
                base64String: base64data,
                webPath: images.photos[0].webPath,
              } as Photo);
            };

            reader.readAsDataURL(blob);
          });
        } else {
          throw new Error("No image selected");
        }
      }

      // Use Camera
      let opt: ImageOptions = {
        quality: 100,
        resultType: CameraResultType.Base64,
        direction: CameraDirection.Front,
        saveToGallery: false,
        width: 1500,
        allowEditing: true,
        source: _sourceType,
      };

      let _perm = await Camera.checkPermissions();
      let perm =
        String(_sourceType) === String(CameraSource.Photos)
          ? _perm.photos
          : _perm.camera;

      if (["granted", "prompt-with-rationale", "prompt"].indexOf(perm) < 0) {
        return await Camera.getPhoto(opt);
      } else {
        this.dlg.confirm(
          "camera_access_denied_title",
          "camera_access_denied_message",
          () => {
            NativeSettings.open({
              optionAndroid: AndroidSettings.ApplicationDetails,
              optionIOS: IOSSettings.App,
            });
          },
        );
        throw new Error("Permission denied");
      }
    } catch (error) {
      console.error("Error fetching photo:", error);
      throw error;
    }
  }

  public async getPicture_old(
    _sourceType: CameraSource.Photos | CameraSource.Camera,
  ): Promise<Photo> {
    if (!Capacitor.isPluginAvailable("Camera")) {
      this.dbg.l("Camera plugin not available!");
      return;
    }

    // set options

    let opt: ImageOptions = {
      quality: 100,
      resultType: CameraResultType.Base64,
      direction: CameraDirection.Front,
      saveToGallery: false,
      width: 1500,
      allowEditing: true,
      source: _sourceType,
    };

    return new Promise(async (resolve, reject) => {
      let _perm = await Camera.checkPermissions();
      let perm =
        _sourceType == CameraSource.Photos ? _perm.photos : _perm.camera;

      /*
      granted: Every permission in this alias has been granted by the end user (or prompting is not necessary).
      denied: One or more permissions in this alias have been denied by the end user.
      prompt: The end user should be prompted for permission, because it has neither been granted nor denied.
      prompt-with-rationale: The end user has denied permission before, but has not blocked the prompt yet.
      */

      // debug
      // perm = 'denied';

      // if denied, show a message to user that they need to allow camera access and open system settings
      if (["granted", "prompt-with-rationale", "prompt"].indexOf(perm) < 0) {
        this.dlg.confirm(
          "camera_access_denied_title",
          "camera_access_denied_message",
          () => {
            NativeSettings.open({
              optionAndroid: AndroidSettings.ApplicationDetails,
              optionIOS: IOSSettings.App,
            });
          },
        );
      } else {
        Camera.getPhoto(opt)
          .then((imageData) => {
            resolve(imageData);
          })
          .catch((e) => {
            reject(e);
          });
      }
    });
  }
}
