import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { AccountService } from "./account.service";
import { WizardService } from "./wizard.service";
import { environment as Env } from "../../environments/environment";
import { AuthService } from "./auth.service";
import { ApiService } from "./api/api.service";
import { DialogHelperService } from "./dialog-helper.service";
import { SavePassword } from "capacitor-ios-autofill-save-password";
import { MiscService } from "./misc.service";
import {
  TME_malicious_issue,
  TME_password_saved,
} from "../classes/gtm-event.class";
import { AnalyticsService } from "./analytics.service";
import { Debug } from "../helpers/debug";

@Injectable({
  providedIn: "root",
})
export class SignupWizardService extends WizardService {
  constructor(
    protected router: Router,
    protected accountService: AccountService,
    protected authService: AuthService,
    protected api: ApiService,
    protected dlg: DialogHelperService,
    protected misc: MiscService,
    protected analytics: AnalyticsService,
    protected debug: Debug
  ) {
    super(router, accountService);
  }

  public reset()
  {
    this.initSteps()
  }

  /**
   * Initialize steps
   */
  protected initSteps() {
    this._urlPrefix = Env.SIGNUP_URL_PREFIX;
    this.initStoredData();

    let steps = [
      {
        url: "gender",
        data: {},
        isPopulated: false,
      },
      {
        url: "age",
        data: {},
        isPopulated: false,
      },
      {
        url: "username",
        data: {
          doHidePassword: false,
        },
        isPopulated: false,
      },
      {
        url: "email",
        data: {},
        isPopulated: false,
      },
    ];

    // add consent page only if it's required

    if (this.accountService.consentRequirement.required) {
      steps.push({
        url: "consent",
        data: {},
        isPopulated: false,
      });
    }

    this.setSteps(steps);
  }

  public finishWizard() {
    /*
    Object
age: 22
doHidePassword: false
email: "<EMAIL>"
genderId: "1"
lookingId: "2"
password: "dsag"
privacy: true
special: true
terms: true
username: "sgadtwetwe"
    */
    //alert("@todo finish instalation, send api call");

    this._isProcessing = true;

    let data = this.getMergedData();

    // set default for special value
    let promoValue = data.hasOwnProperty("special") ? !!data["special"] : true;

    // send register api call

    let params = {
      age: data["age"],
      consent_offers: promoValue,
      consent_privacy: data["privacy"] ?? true,
      consent_terms: data["terms"] ?? true,
      email: data["email"],
      first_name: data["firstname"] || null,
      gender_id: data["genderId"],
      looking: data["lookingId"],

      // @todo add this data to registration if possible
      latitude: null,
      longitude: null,

      password: data["password"],
      system_id: Env.API_SYSTEM_ID,
      username: data["username"],
      ip_address: null, // debug only
    };

    this.authService.register(params).subscribe({
      next: (data) => {
        // save password

        let sPass = params["password"];
        let sUser = params["username"];

        console.log("data reg pass save", params);

        if (this.misc.devicePlatform == "ios") {
          SavePassword.promptDialog({
            username: sUser,
            password: sPass,
          })
            .then(() => {
              this.analytics.logEvent("password_saved", <TME_password_saved>{
                action: "registration",
              });
              console.log("Password save dialog success");
              this.debug.l("Password save dialog success");
            })
            .catch((err) => {
              this.analytics.logError(
                err["key"] ??
                  err["error"] ??
                  err["message"] ??
                  "password_manager_error",
                "console"
              );
              console.log("Password save dialog failure", err);
              this.debug.l("Password save dialog failure", err);
            });
        }

        // delete wizard data since we're done anyway
        this.initSteps();
        this._isProcessing = false;

        // initiate save password on ios
      },
      error: (error) => {
        this._isProcessing = false;

        // display errors from api class
        this.dlg.showErrorAlert("error_title", this.api.getAllErrorsTextOnly());
        this.api.clearErrorList();
      },
    });
  }
}
