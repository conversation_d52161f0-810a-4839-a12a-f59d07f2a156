import { Injectable } from "@angular/core";
import { environment as Env } from "../../environments/environment";
import { ApiService, IApiEvent } from "../services/api/api.service";
import { ApiCommands } from "../services/api/api.commands";
import _ from "lodash";
import { filter } from "rxjs/operators";
import { Debug } from "../helpers/debug";
import { DeviceInfoService } from "./device-info.service";
import { NavigationEnd, Router, RouterEvent } from "@angular/router";
import { AppsflyerHelperService } from "./appsflyer-helper.service";
import { GoogleTagManagerService } from "angular-google-tag-manager";
import {
  GTMEvent,
  TME_error_message,
  TME_initiate_upgrade,
  TME_login,
  TME_login_initial_app,
  TME_mutual_meet,
  TME_open_chat,
  TME_swipe,
  TME_welcome_offer_click,
  TM_app_and_user_info,
} from "../classes/gtm-event.class";
import { Md5 } from "ts-md5";
import { Utils } from "../helpers/utils";
import { ProfileOwn } from "../schemas/profile-own";
import { Platform } from "@ionic/angular";
import { ApiResponses } from "./api/api.responses";
import { firstValueFrom } from "rxjs";
import { MiscService } from "./misc.service";
import { StorageService } from "./storage.service";
import { FirebaseAnalytics } from "@capacitor-community/firebase-analytics";

export const WAS_INITIAL_LOGIN_KEY = "was_initial_login_flag";

@Injectable({
  providedIn: "root",
})
export class AnalyticsService {
  private user_id: string = "";
  private userData: ProfileOwn.type = null;
  private uuid: string = "";
  private gaClientIdFallback: string = "";

  constructor(
    private api: ApiService,
    private debug: Debug,
    private device: DeviceInfoService,
    private router: Router,
    private appsflyer: AppsflyerHelperService,
    private gtm: GoogleTagManagerService,
    private platform: Platform,
    private misc: MiscService,
    private storageService: StorageService,
  ) {}

  // markers for logging

  private markerArray = [];

  public addMarker(label: string, data = null, doAddTs = false) {
    var _d = null;

    if (!data) {
      _d = { label: label };
    } else {
      _d = { label: label, data: data };
    }

    if (doAddTs) {
      _d["ts"] = _.now();
    }

    this.markerArray.push(_d);
  }

  public getMarkers() {
    return this.markerArray;
  }

  public clearMarkers() {
    this.markerArray = [];
    this.addMarker("markers_cleared");
  }

  private firebaseLog(data) {
    if (!FirebaseAnalytics || !this.misc.isNativePlatform()) {
      this.debug.l("FirebaseLogEvent", data);
      return Promise.resolve();
    }
    return FirebaseAnalytics.logEvent(data);
  }

  private setFirebaseUserProperties(data) {
    if (!FirebaseAnalytics || !this.misc.isNativePlatform()) {
      this.debug.l("FirebaseUserProp", data);
      return Promise.resolve();
    }
    return FirebaseAnalytics.setUserProperty(data);
  }

  private setFirebaseUserId(data) {
    if (!FirebaseAnalytics || !this.misc.isNativePlatform()) {
      this.debug.l("FirebaseUserId", data);
      return Promise.resolve();
    }
    return FirebaseAnalytics.setUserId(data);
  }

  public setPurchaseExtraInfo(
    key: "initiate_upgrade_category" | "initiate_upgrade_title",
    value: string,
  ) {
    this.statsPurchaseInfo[key] = value;
  }

  private statsPurchaseInfo = {
    initiate_upgrade_category: "",
    initiate_upgrade_title: "",
  };

  private getStatsInfo(key) {
    return this.statsPurchaseInfo[key] || "default";
  }

  private setRules() {
    this.rules = [
      // upgrade possibility is open
      {
        urlType: "current",
        rule: new RegExp("^/app/modals/go-premium$", "i"),
        eventName: "initiate_upgrade",
        eventData: <TME_initiate_upgrade>{
          initiate_upgrade_category: "upgrade_page",
          initiate_upgrade_title: this.getStatsInfo("initiate_upgrade_title"),
        },
        errorName: "upgrade_error",
        errorOnly: false,
      },
      /*
    {
      urlType: "current",
      rule: new RegExp("^/app/modals/try-premium$", "i"),
      eventName: "welcome_offer_click",
      eventData: <TME_welcome_offer_click>{},
      errorName: "",
      errorOnly: false,
    },
    */
      {
        urlType: "current",
        rule: new RegExp("^/app/modals/go-premium-promo$", "i"),
        eventName: "initiate_upgrade",
        eventData: <TME_initiate_upgrade>{
          initiate_upgrade_category: "campaign_offer",
          initiate_upgrade_title: this.getStatsInfo("initiate_upgrade_title"),
        },
        errorName: "upgrade_error",
        errorOnly: false,
      },
      // swipe
      {
        urlType: "current",
        rule: new RegExp("^/app/tabs/meet.*$", "i"),
        eventName: "swipe",
        eventData: <TME_swipe>{
          swipe_type: "app swipe",
        },
        errorName: "",
        errorOnly: false,
      },
      // match modal
      {
        urlType: "current",
        rule: new RegExp("^/app/modals/match/.*$", "i"),
        eventName: "mutual_meet",
        eventData: <TME_mutual_meet>{},
        errorName: "",
        errorOnly: false,
      },
      // user leaves any upgrade point
      /*
    {
      urlType: "previous",
      rule: new RegExp("(try-premium)|(go-premium-promo)|(go-premium)", "i"),
      eventName: "abandon_upgrade",
      eventData: <TME_abandon_upgrade>{},
      errorName: null,
      errorOnly: false,
    },
    */
      // debug error test
      {
        urlType: "current",
        rule: new RegExp("^/app/debug$", "i"),
        eventName: "debut_testing",
        eventData: {},
        errorName: "debug_error",
        errorOnly: true,
      },
      // signup error
      {
        urlType: "current",
        rule: new RegExp("^/signup/.*$", "i"),
        eventName: "",
        eventData: null,
        errorName: "signup_error",
        errorOnly: true,
      },
      // welcome errors
      {
        urlType: "current",
        rule: new RegExp("^/app/wpg-.*$", "i"),
        eventName: "",
        eventData: null,
        errorName: "welcome_error",
        errorOnly: true,
      },
      // login errors
      {
        urlType: "current",
        rule: new RegExp("^(/welcome)|(/login)$", "i"),
        eventName: "",
        eventData: null,
        errorName: "login_error",
        errorOnly: true,
      },
    ];
  }

  /**
   * Path rules
   */
  private rules: {
    urlType: "current" | "previous";
    rule: RegExp;
    eventName: string;
    eventData: object;
    errorName: string | null;
    errorOnly: boolean;
  }[] = [];

  private pushToDataLayer(data: object = {}): Promise<any> {
    this.debug.l("gtm.pushData", data);

    // add app identifier flag

    data["_isApp"] = true;

    // return Promise.resolve(null);
    try {
      return this.gtm.pushTag(data);
    } catch (e) {
      this.debug.l("gtm.pushData error", e);
      return Promise.resolve(null);
    }
  }

  private _campaignName: string = "";
  private _activationSource: string = "";

  public set campaignName(name: string) {
    this._campaignName = name;
  }

  public get campaignName() {
    return this._campaignName;
  }

  public set activationSource(name: string) {
    this._activationSource = name;
  }

  private _trackingData = {};

  public setTrackingData(data) {
    this._trackingData = data;

    // push tradking to datalayer

    let _layerData = _.clone(data);
    _layerData["event"] = "_tracking";
    this.pushToDataLayer(_layerData);
  }

  /**
   * Set session data for GTM
   */
  public async setSessionData() {
    let data = <TM_app_and_user_info>{
      app_id: Env.APP_STORE_ID,
      app_version: Env.APP_VERSION,
      database_id: Env.RAW_SYSTEM_ID,
      gender: Utils.getGenderNameById(this.userData?.gender_id || 0) ?? "",
      membership: _.isEmpty(this.userData?.access)
        ? ""
        : (Utils.getAccessObject(this.userData?.access, "name") ?? ""),
      os: this.device.platform,
      user_id: this.getUserHash(),
      device_id: this.getUUIDHash(),
      time_since_reg: this.userData?.registration_ts
        ? Utils.getTimeAgoString(this.userData?.registration_ts)
        : "",
      email_domain: this.userData?.email
        ? this.userData?.email.split("@")[1]
        : "",
      website_niche: Env.NICHE,
      device_info: this.device.infoFilteredObject,
      tracking_info: this._trackingData,
    };

    this.debug.l("analytics.setSessionData", data);

    // add firebase stuff;
    await this.setFirebaseUserId({ userId: data.user_id });

    // set firebase property
    for (let _key in data) {
      await this.setFirebaseUserProperties({
        key: _key,
        value: data[_key],
      });
    }

    // add to tag manager

    data["event"] = "_setEnv";

    this.debug.l("GTM_SESSION", data);
    this.pushToDataLayer(data);
  }

  /**
   * Send params to googel through proxy
   * @param query
   * @param params
   * @returns
   */
  public forwardToGoogle(data: any = {}) {
    return firstValueFrom(
      this.api.call<ApiResponses.ResponseBase.type>({
        command: ApiCommands.GoogleAnalytics.name,
        method: "post",
        useToken: false,
        params: data,
        commandVersion: "2",
        callType: ["ignore_401", "background"],
      }),
    );
  }

  /**
   * Register message click (inbox) on server
   *
   * @param params
   * @returns
   */
  public messageClick(params: any = {}) {
    // skip if not for email

    /*

    param examples:

    https://www.seniornext.com/edit?utm_campaign=new_user_welcome_messages&utm_medium=internal_inbox&utm_source=membership_free&eid=0&h=224142g73061facc843affdf33efecbbbe9f305&eid=0
    https://www.seniornext.com/photos/public?utm_campaign=new_user_welcome_messages&utm_medium=internal_inbox&utm_source=membership_free&eid=0&h=224142g73061facc843affdf33efecbbbe9f305&eid=0
    https://www.seniornext.com/search?utm_campaign=new_user_welcome_messages&utm_medium=internal_inbox&utm_source=membership_free&eid=0&h=224142g73061facc843affdf33efecbbbe9f305&eid=0
    https://rs.gamer-dating.com/photos/public?utm_campaign=new_user_welcome_messages&utm_medium=internal_inbox&utm_source=membership_free&utm_term=dobro_dosli_na_site_name&h=470562g1c02df7c7574bb9eb6f820f17f1a80c2
    */

    // if (params?.utm_medium !== "internal_inbox") {
    //   this.debug.l("messageClick: skip - not internal_inbox click", params);
    //   return Promise.resolve({});
    // }

    delete params["campaign"];

    // send data to server

    return firstValueFrom(
      this.api.call<ApiResponses.ResponseBase.type>({
        command: ApiCommands.MessageClick.name,
        method: "post",
        useToken: false,
        params: params,
        commandVersion: "1",
        callType: ["ignore_401", "background"],
      }),
    );
  }

  /**
   * Register email click on server
   *
   * @param params
   * @returns
   */
  public emailClick(params: any = {}) {
    // skip if not for email

    if (params?.utm_medium !== "email") {
      this.debug.l("emailClick: skip - not email click", params);
      return Promise.resolve({});
    }

    delete params["campaign"];

    // send data to server

    return firstValueFrom(
      this.api.call<ApiResponses.ResponseBase.type>({
        command: ApiCommands.EmailClick.name,
        method: "post",
        useToken: false,
        params: params,
        commandVersion: "2",
        callType: ["ignore_401", "background"],
      }),
    );
  }

  public async init() {
    // set debug for TAG MANAGER
    window["__is_debug"] = Env.IS_DEBUG;

    // set uuid
    this.uuid = this.device.uuid;

    // initialize google tag manager
    this.setSessionData();

    // subscribe to api calls
    this.subscribeToApiCalls();

    // subscribe to screen name changes
    this.subscribeRouterChange();

    // subscribe to api errors
    this.subscribeToApiErrors();

    // subscribe to api errors
    this.subscribeToAggregatedData();

    // send init event
    this.logEvent("app_start");
  }

  private aggregatedEvents: {
    event_name: string;
    event_count: number;
  }[] = [];

  /**
   * Function to count occurence of an aggregated event
   *
   * @param event_name
   */
  private incAggregatedEvent(event_name: string) {
    let index = _.findIndex(this.aggregatedEvents, { event_name: event_name });

    if (index < 0) {
      index = this.aggregatedEvents.push({
        event_count: 0,
        event_name: event_name,
      });

      index--;
    }

    this.aggregatedEvents[index].event_count++;
  }

  /**
   * Send all aggregated events
   */
  public async sendAggregatedEvents() {
    return new Promise<any>(async (resolve) => {
      this.aggregatedEvents.forEach((e) => {
        this.logEvent(e.event_name, {
          count: e.event_count,
        });
      });

      // delay delete aggregated events
      setTimeout(() => {
        this.aggregatedEvents = [];
      }, 1000);

      resolve(null);
    });
  }

  /**
   * Subscriptions to send data aggregated
   */
  private subscribeToAggregatedData() {
    this.api.eventStream$
      .pipe(filter((e: IApiEvent) => e.event == "finalize"))
      .subscribe({
        next: (data: any) => {
          // swipe count

          if (
            (data?.command == ApiCommands.ProfileLike.name ||
              data?.command == ApiCommands.ProfileLikeSimple.name ||
              data?.command == ApiCommands.ProfilePass.name ||
              data?.command == ApiCommands.ProfileUnLikeSimple.name) &&
            this.currentUrl.indexOf("tabs/meet") > -1
          ) {
            // it's a swipe
            this.incAggregatedEvent("swipe_count");
          }

          // like count

          if (
            (data?.command == ApiCommands.ProfileLike.name ||
              data?.command == ApiCommands.ProfileLikeSimple.name) &&
            this.currentUrl.indexOf("tabs/meet") < 0
          ) {
            // it's a swipe
            this.incAggregatedEvent("like_count");
          }

          // private messages

          if (
            data?.command == ApiCommands.SendMessage.name &&
            data?.method == "post"
          ) {
            this.incAggregatedEvent("message_count");
          }

          // count flirts

          if (
            (data?.command == ApiCommands.FlirtSend.name ||
              data?.command == ApiCommands.FlirtWink.name) &&
            data?.method == "post"
          ) {
            this.incAggregatedEvent("flirt_count");
          }

          // pohot requests

          if (data?.command == ApiCommands.ProfilePhotoRequest.name) {
            this.incAggregatedEvent("request_photo_count");
          }
        },
      });

    // send aggregated stats when app goes to background
    this.platform.pause.subscribe({
      next: () => {
        this.debug.l("App going to bg, sending aggregated stats");
        this.sendAggregatedEvents();
      },
    });
  }

  /**
   * Subscribe to api errors
   */
  private subscribeToApiErrors() {
    // log even api errors
    this.api.errorStream$.subscribe({
      next: (data: any) => {
        let message = "unknown_error";

        if (data && data["data"]) {
          message =
            data?.data?.error?.errors[0]?.key ??
            data?.data?.error?.errors[0]?.message ??
            "unknown_error";
        }

        this.logError(message, "api", true);
      },
    });
  }

  private logEventsForAppsflyer = [
    "sign_up",
    "login_initial_app",
    "test_event",
  ];

  public logEventCallback: Function = (name, params) => {};

  /**
   * Log analytics event
   * @param name
   * @param params
   */
  public async logEvent(name: string, params: object = {}): Promise<any> {
    if (this.logEventCallback) {
      this.logEventCallback(name, params);
    }

    // push to gmt event
    // send to firebase
    await this.firebaseLog({ name: name, params: params });

    params["_isApp"] = true;

    this.debug.l("GTM", [name, params]);

    // log for appsflyer
    /*
    if (this.logEventsForAppsflyer.indexOf(name) >= 0) {
      this.appsflyer.logEvent(name, params);
    }*/

    // log for GA
    return this.pushToDataLayer(new GTMEvent<object>(name, params).get());
  }

  private lastLoggedError: {
    ts: number;
    event: string;
  } = { ts: 0, event: "" };

  /**
   * Throttle if error is reported too frequently ( with different types maybe )
   * @param event
   * @returns
   */
  private throttleError(event: string) {
    let now = Math.floor(_.now() / 1000);
    // throttle same type evey 2 seconds ( prevent duplicates from different types, and spamming )
    let res =
      this.lastLoggedError.event == event && now - this.lastLoggedError.ts <= 2;

    this.lastLoggedError.event = event;
    this.lastLoggedError.ts = now;

    return res;
  }

  /**
   * Function to modify event type based on message if required
   * @param message
   * @param event
   * @returns
   */
  private modifyErrorEventByMessage(message: string, event: string): string {
    // if the api error is register_error_rejected - it's a spam issue, set proper
    // event for analytics
    if (message == "register_error_rejected" || message == "rg_error") {
      return "malicious_issue";
    }

    return event;
  }

  /**
   * Add error that don't need to be logged here
   *
   * @param message
   * @returns
   */
  private doIgnoreError(message) {
    let ignore_errors = ["error_force_profile_details_update", "ads_error"];

    // ignore?
    if (ignore_errors.indexOf(message) >= 0) {
      this.debug.l("error ignored for analytics", message);
      return true;
    }

    return false;
  }

  /**
   * Function to handle error logging
   *
   * @param message error message
   * @param eType how did we display the error
   * @param onlyLogRules bool - only log if special case is defined , or always log
   * @returns
   */
  public logError(
    message: string | string[],
    eType: string,
    onlyLogRules: boolean = false,
  ) {
    if (_.isArray(message)) {
      message = (<string[]>message).join(" & ");
    }

    // don't log certain errors
    if (this.doIgnoreError(message)) {
      return;
    }

    let r = this.getRuleMatch(this.lastUrl, "");

    // if we have an error defined for this url, log it
    if (r?.errorName) {
      // throttle error reporting of the same type
      if (this.throttleError(r.errorName)) {
        return;
      }

      let event = this.modifyErrorEventByMessage(<string>message, r.errorName);

      this.logEvent(event, { message: message, type: eType });
      return;
    }

    // see if we want to log general problems, or only specific
    if (onlyLogRules) {
      return;
    }

    // we don't have special rule, generic case
    this.logEvent("error_message", <TME_error_message>{
      message: message,
      type: eType,
    });
  }

  private lastUrl: string = "/";
  private currentUrl: string = "/";

  /**
   * set screen name
   * @param name
   */
  public setScreenName(name: string) {
    this.lastUrl = name;

    // set screen name as url for native stuff
    this.logEvent("appScreenView", {
      screenPath: name,
    });

    // add aggregated data of profile views
    if (this.lastUrl.indexOf("/app/profile/") >= 0) {
      this.incAggregatedEvent("view_profile_count");
    }

    // add aggregated data for open chat
    /*
    // looks like we don't need this
    if (this.lastUrl.indexOf("/app/chat-messages/") >= 0) {
      this.incAggregatedEvent("open_chat");
    }
    */
  }

  /**
   * Match rule and retunr
   * @param url
   * @param previousUrl
   * @returns
   */
  private getRuleMatch(url: string, previousUrl: string) {
    this.setRules();

    let res = null;

    this.rules.forEach((r) => {
      if (!res && r.rule.test(r.urlType == "current" ? url : previousUrl)) {
        res = r;
      }
    });

    return res;
  }

  /**
   * Translate applicatoin path to special GTM events
   * @param url
   */
  private sendSpecialScreenEvent(url: string, previousUrl: string) {
    // check against rules
    let r = this.getRuleMatch(url, previousUrl);

    if (r && !r.errorOnly) {
      this.logEvent(r.eventName, r.eventData);
    }
  }

  /**
   * Subscribe to router change and update screen name for mobile
   */
  private subscribeRouterChange() {
    this.router.events
      .pipe(filter((e: any) => e instanceof NavigationEnd))
      .subscribe({
        next: (e: RouterEvent) => {
          this.currentUrl = e.url;
          this.sendSpecialScreenEvent(e.url, this.lastUrl);
          this.setScreenName(e.url);
        },
        error: (error) => {
          this.debug.le("analytics router error", error);
        },
      });
  }

  /**
   * Generate customer id for google analytics
   * to be exposed through window object to GA4
   * @param seed
   * @returns
   */
  private getCid(seed) {
    // fallback
    if (!seed) {
      return "" + _.now() / 1000;
    }

    // pseudo random number - based on seed
    var r = Math.sin(parseInt(seed, 10)) * 10000;
    r = r - Math.floor(r);
    return "1" + (Math.abs(Math.floor(r * 899999999)) + 100000000);
  }

  /**
   * Set user data
   * @param data
   */
  public setUserData(data: any) {
    this.userData = data;
    this.user_id = "" + this.userData["user_id"];
  }

  public doFireLoginEvent: boolean = false;
  public loginMethod: string = "";

  /**
   * Detect user data update, and set to analytics
   * Detect other api calls, and send proper analytics data
   */
  private subscribeToApiCalls() {
    this.api.eventStream$
      .pipe(filter((e: IApiEvent) => e.event == "next"))
      .subscribe({
        next: (data: any) => {
          // set user session data if account have changed
          if (
            data.command == ApiCommands.AccountGet.name &&
            !!data?.data?.body?.data?.user_id
          ) {
            this.setUserData(data?.data?.body?.data);

            // set google analytics client id
            this.gaClientIdFallback = this.getCid(this.user_id);

            this.debug.l("USER DATA", this.userData);
            this.debug.l("GA CID", this.gaClientIdFallback);

            let _cid_s =
              ("" + this.userData?.registration_ts).length > 5
                ? "" + this.userData?.registration_ts
                : "" + _.now() / 1000;

            // set globla default _d_cid for google tag script on ios
            // exposed through window object to TAG MANAGER
            window["_d_cid"] = this.gaClientIdFallback + "." + _cid_s;

            // set user id
            // exposed through window object to TAG MANAGER
            window["_d_uid"] = this.getUserHash();

            // set suer hash
            this.setSessionData();

            // set appsflyer user id
            //this.appsflyer.setCustomerUserId({ cuid: this.getUserHash() });

            // fire login event?
            // we had to put this here to make sure we have session
            if (this.doFireLoginEvent) {
              this.doFireLoginEvent = false;

              this.logEvent("login", <TME_login>{
                method: this.loginMethod,
                user_id: this.getUserHash(),
              });

              // send login event only 1 time for appsflyer and GA
              this.logOnlyInitialLogin();
            }
          }
        },
      });
  }

  /**
   * Helper function that logs initial login only one time per installed app
   */
  private async logOnlyInitialLogin() {
    let _was = (await this.storageService.get(WAS_INITIAL_LOGIN_KEY)) ?? false;

    if (!_was) {
      await this.storageService.set(WAS_INITIAL_LOGIN_KEY, true);

      this.logEvent("login_initial_app", <TME_login_initial_app>{
        method: this.loginMethod,
        user_id: this.getUserHash(),
      });
    }
  }

  /**
   * Get current user's hash
   */
  public getUUIDHash() {
    return !this.uuid ? "" : Md5.hashStr(this.uuid);
  }

  /**
   * Get current user's hash
   *
   * @param id if given used if not this.user_id is used
   * @returns
   */
  public getUserHash(id = null) {
    let _id = id;
    _id = id || this.user_id || "";

    return !_id ? "" : Env.RAW_SYSTEM_ID + "_" + Md5.hashStr(_id);
  }
}
