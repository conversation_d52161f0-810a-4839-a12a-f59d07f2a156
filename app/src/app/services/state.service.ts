import { Injectable } from "@angular/core";
import _ from "lodash";
import { Utils } from "../helpers/utils";

export const STATE_VALIDITY_DEFAULT_MILLIS = 60000; // default validity 1m

/**
 * service to pass state objects between pages if necessary
 */
@Injectable({
  providedIn: "root",
})
export class StateService {
  // container for data
  private _data = {};

  // container for timestamps
  private _timestamp = {};

  /**
   * Helper function to genearte key
   *
   * @param key
   * @param id
   * @returns
   */
  private getKey(key: string, id: string) {
    return key + "_" + id;
  }

  /**
   * Set state variable
   *
   * @param key
   * @param id
   * @param data
   */
  public set(key: string, id: string, data: any) {
    this._data[this.getKey(key, id)] = data;
    this._timestamp[this.getKey(key, id)] = Utils.now(true);
  }

  /**
   * Get state variable
   *
   * @param key key of the data
   * @param id id of the data
   * @param default_result return this if there's not data ( of it expired )
   * @param doNotReturnIfOlderThankMillis retrieve only if younger than this number in milliseconds
   * @returns
   */
  public get(
    key: string,
    id: string,
    default_result: any = null,
    doNotReturnIfOlderThankMillis: number = -1
  ) {
    if (
      doNotReturnIfOlderThankMillis <= 0 ||
      this.isValid(key, id, doNotReturnIfOlderThankMillis)
    ) {
      return this._data[this.getKey(key, id)] || default_result;
    }

    return default_result;
  }

  /**
   * Bet timestamp for an entry
   * @param key
   * @param id
   * @param default_result
   * @returns
   */
  public getTimestamp(key: string, id: string, default_result: any = null) {
    return this._timestamp[this.getKey(key, id)] || null;
  }

  /**
   * Tells if the entry is valid ( based on given param )
   * @param validityDurationMillis
   * @returns true if the entry timestamp is within the validity duration in milliseconds and false if not ( or does not exists )
   */
  public isValid(
    key: string,
    id: string,
    validityDurationMillis: number = STATE_VALIDITY_DEFAULT_MILLIS
  ) {
    let _created = this._timestamp[this.getKey(key, id)] || null;

    if (
      _created === null ||
      Utils.now(true) - _created > validityDurationMillis
    ) {
      return false;
    }

    return true;
  }

  /**
   * Delete data and timestamp form state service
   *
   * @param key
   * @param id
   */
  public delete(key: string, id: string) {
    this.set(key, id, null);
  }

  constructor() {}
}
