import { Injectable } from "@angular/core";
import { firstValueFrom, Observable } from "rxjs";
import { Debug } from "../helpers/debug";
import { ApiCommands } from "./api/api.commands";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { FlirtServiceItemList } from "./lists/flirt.service.item-list";

@Injectable({
  providedIn: "root",
})
export class FlirtService {
  constructor(private api: ApiService, private debug: Debug) {}

  // Flirt list.
  public flirtItems: FlirtServiceItemList = new FlirtServiceItemList(
    this.api,
    ApiCommands.FlirtGetList,
    this.debug
  );

  /**
   * Fetch list items
   */
  public fetchList(): Promise<any> {
    return firstValueFrom(this.flirtItems.get());
  }

  /**
   * Send flirt to user
   *
   * @param id flirt id
   * @param username target's username
   */
  public send(
    id: number,
    username: string
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api.call<ApiResponses.NoDataResponse.type>({
      command: ApiCommands.FlirtSend.name,
      method: "post",
      useToken: true,
      params: <ApiParams.FlirtSendParams.type>{
        id: id,
        username: username,
      },
    });
  }

  /**
   * Send wink to user
   *
   * @param username target's username
   */
  public sendWink(
    username: string
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api.call<ApiResponses.NoDataResponse.type>({
      command: ApiCommands.FlirtWink.name,
      method: "post",
      useToken: true,
      params: <ApiParams.FlirtSendWinkParams.type>{
        username: username,
      },
    });
  }
}
