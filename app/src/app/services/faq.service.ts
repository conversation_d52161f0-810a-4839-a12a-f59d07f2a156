import { Injectable } from "@angular/core";
import { Platform } from "@ionic/angular";
import { map } from "rxjs/operators";
import { Debug } from "../helpers/debug";
import { FaqItem } from "../schemas/faq";
import { ApiCommands } from "./api/api.commands";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { DeviceInfoService } from "./device-info.service";
import _ from "lodash";
import { firstValueFrom } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class FaqService {
  constructor(
    private api: ApiService,
    private deviceInfo: DeviceInfoService,
    private debug: Debug
  ) {}

  private _items = [];

  get items() {
    return this._items;
  }

  get inProgress(): boolean {
    return this.api.isInEventList({ command: ApiCommands.FaqGet.name });
  }

  /**
   * Fetch items
   */
  public get() {
    let platformArray = [this.deviceInfo.platform];

    return firstValueFrom(
      this.api
        .call<ApiResponses.FaqResponse.type>({
          command: ApiCommands.FaqGet.name,
          method: "get",
          useToken: true,
          params: {
            platforms: platformArray,
          },
          commandVersion: "2",
        })
        .pipe(map((data) => (this._items = data.data)))
    );
  }
}
