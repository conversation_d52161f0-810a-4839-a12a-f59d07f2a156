import {
  BehaviorSubject,
  MonoTypeOperatorFunction,
  Observable,
  OperatorFunction,
} from "rxjs";
import { ApiCommandItem } from "../api/api.commands";
import { ApiService } from "../api/api.service";
import * as _ from "lodash";
import { tap } from "rxjs/operators";
import { Debug } from "src/app/helpers/debug";
import { environment as Env } from "../../../environments/environment";

export type ListConfig = {
  uniqueKey: any;
  doRemoveDuplicates: boolean;
  isPaginated: boolean;
  defaultLimit: number;
  doAddToTop: boolean;
  sortBy: string;
  sortDir: "asc" | "desc" | "";
};

/**
 * Class for profile service items
 * @todo we'll see maybe we can use the same logic and class as a base for other services
 */
export class ListBase<ListItemType, ApiResponseT, ParamT> {
  // @todo add refreshable functionality ( optional ) to base class

  // @todo see how SEARCH would work with this logic - does that go to filter?
  // we'll have simple api call , user fills out textbox, presses search, we filter it
  // or solve as in current app - if we have pagination left ( next is not -1 ) then we can
  // do as user types filtering, if not then we will make api call with filter
  // in that case we need to display search button ( just as current app )

  // api service container
  protected api: ApiService;

  // command item conatiner
  protected apiCommandItem: ApiCommandItem;

  // debug
  protected debug: Debug;

  protected _config: ListConfig = {
    doRemoveDuplicates: true,
    uniqueKey: "unique_key_example",
    isPaginated: true,
    defaultLimit: Env.DEFAULT_LIST_GET_LIMIT,
    doAddToTop: false,
    sortBy: "",
    sortDir: "",
  };

  /**
   * Initialize how list should behave
   */
  protected initConfig() {
    // override to init config
  }

  /**
   * Constructor
   *
   * @param api inject api service
   * @param apiCommand inject api command item
   * @param debug Inject debugger class
   */
  public constructor(
    api: ApiService,
    apiCommand: ApiCommandItem,
    debug: Debug
  ) {
    // add auto injection

    this.api = api;
    this.apiCommandItem = apiCommand;
    this.debug = debug;

    this.init();
  }

  /**
   * Init class
   */
  public init(doClearItems: boolean = false) {
    if (doClearItems) {
      this._storage = [];
      this.next();
    }
    this.initConfig();
    this.initParams();
  }

  /**
   * get extra params before executing api call
   * @returns
   */
  public getExtraParams() {
    return {};
  }

  /**
   * Is storage empty?
   * @returns
   */
  public isStoredListEmpty() {
    return _.isEmpty(this._storage);
  }

  /**
   * Storage array for items
   */
  protected _storage: ListItemType[] = [];

  /**
   * Behavior subject for propagating list changes
   */
  protected _list$: BehaviorSubject<ListItemType[]> = new BehaviorSubject<
    ListItemType[]
  >([]);

  public listPipie(): Array<MonoTypeOperatorFunction<any>> {
    return [];
  }

  /**
   * Observable for list item changes
   * Expose _list$ as observable to avoid external next() calls
   */
  get list$(): Observable<ListItemType[]> {
    return this._list$
      .asObservable()
      .pipe(this.api.pipeFromArray(this.listPipie()));
  }

  /**
   * Get current storage values
   *
   * @param doClone if it's true function will return a deep clone of the storage, so internal storage cannot be messed with directly
   */
  public getStorage(doClone: boolean = true): ListItemType[] {
    return doClone ? _.cloneDeep(this._storage) : this._storage;
  }

  /**
   * Add a sinle item to the list externally
   */
  public addItem(item: ListItemType, doAddToTop: boolean = false) {
    this.addItems([item], doAddToTop);
  }

  /**
   * Add item array to the list externally
   */
  public addItems(items: ListItemType[], doAddToTop: boolean = false) {
    if (this._config.doRemoveDuplicates) {
      this._storage = _.uniqBy(
        doAddToTop
          ? _.concat(items, this._storage)
          : _.concat(this._storage, items),
        this._config.uniqueKey
      );
    } else {
      this._storage = doAddToTop
        ? _.concat(items, this._storage)
        : _.concat(this._storage, items);
    }

    this.next();
  }

  /**
   * Set item list from oustide
   */
  public set items(items: ListItemType[]) {
    this._storage = items;
    this.next();
  }

  /**
   * Remove items with filter.
   * @param removeFilter object any property that we want to filter out example { "username": "testuser" }
   */
  public removeItems(removeFilter: object) {
    this._storage = <ListItemType[]>(
      _.without(this._storage, _.find(this._storage, removeFilter))
    );
    this.next();
  }

  public haveItems(findFilter: object): boolean {
    let res = _.find(this._storage, findFilter);
    return res ? true : false;
  }

  protected _params: any;

  /**
   * Initialize params
   */
  protected initParams() {
    // overridable funtion to initialize params
  }

  /**
   * variable to keep the current param data
   */
  // flag to follow params changes, so we know when to reset storage list
  private _didParamsChange: boolean = false;

  /**
   * Read the params change state
   */
  private didParamsChange(doResetFlag = false) {
    let res = this._didParamsChange;

    if (doResetFlag) {
      this._didParamsChange = false;
    }

    return res;
  }

  /**
   * Function for changing params for get operation
   */
  public changeParams(params: ParamT) {
    if (!_.isEqual(this._params, params)) {
      this._params = params;
      this._didParamsChange = true;
    }
  }

  /**
   * Get params.
   */
  get params(): ParamT {
    return this._params;
  }

  /**
   * Propagate list change to the _list$ obaservable
   */
  private next() {
    this._list$.next(this._storage);
  }

  /**
   * Clear storage
   */
  public clearStorage() {
    this._storage = [];
    this.next();
  }

  /**
   * Get api command
   */
  protected getApiCommand(): ApiCommandItem {
    return this.apiCommandItem;
  }

  /**
   * Refresh items
   * @param limit how many items to read initially
   */
  public refresh(limit: number = null) {
    if (limit == null) {
      limit = this._config.defaultLimit;
    }

    this.clearStorage();
    this._didParamsChange = false;
    this.get(0, limit);
  }

  private _commandVersion: string = "1";

  public setCommandVersion(version: string) {
    this._commandVersion = version;
    return this;
  }

  /**
   * We can add some validation before we make the api call
   * See if the params are valid - if required, and return
   * false to prevent the api call from being fired
   * @returns boolean
   */
  protected validateBeforeCall(): boolean {
    return true;
  }

  /**
   * Fetch item list from server
   *
   * @param page number
   * @param limit number
   */
  public get(): Observable<ApiResponseT>;
  public get(page?: number, limit?: number): Observable<ApiResponseT>;
  public get(
    page?: number,
    limit?: number,
    params?: ParamT
  ): Observable<ApiResponseT> {
    // set optional filter
    if (!_.isNil(params)) {
      this.changeParams(params);
    }

    // set default params
    page = _.isNil(page) ? 0 : page;
    limit = _.isNil(limit) ? this._config.defaultLimit : limit;

    if (this.didParamsChange(true)) {
      this.clearStorage();
    }

    let paramObject = this._params;
    // remove 'command' from params
    paramObject = _.omit(paramObject, "command");

    // if it's paginated - set page and limit
    if (this._config.isPaginated) {
      paramObject = _.merge(paramObject, {
        page: page,
        limit: limit,
      });
    }

    // add extra params
    paramObject = _.merge(paramObject, this.getExtraParams());

    if (!this.validateBeforeCall()) {
      // return fake result to prevent api call
      // without breaking the underlying logic
      return new BehaviorSubject(<ApiResponseT>{}).asObservable();
    }

    return this.api
      .call<ApiResponseT>({
        command: this.getApiCommand().name,
        method: "get",
        useToken: true,
        callType: "background",
        params: paramObject,
        commandVersion: this._commandVersion,
      })
      .pipe(
        tap((data) => {
          // @todo see if we need to remove duplicates
          // maybe add a way to override this if required

          // see if we need this uniq at all, see how expencieve it is
          // i think we need this because if the data chanbes by other source
          // for example a user is deleted from the server, then we might get 2 identical
          // profiles

          let _items = this.getItems(data["data"]);

          if (this._config.sortBy) {
            _items = _.orderBy(
              _items,
              this._config.sortBy,
              this._config.sortDir == "desc" ? "desc" : "asc"
            );
          }

          this.addItems(_items, this._config.doAddToTop);
          //console.log(this.getApiCommand().name, this.getStorage());

          // set next value - if we get less item that the limit, then we're at the end
          // of the data stream, and we return -1 indicating that there's no more data
          if (this._config.doRemoveDuplicates) {
            // we could look if we got less items than limit - but sometimes we
            // filter data on proxy, and we get less items, but that does not mean there's not more items
            // so to be safe, we have to make sure that we're at the and of the list
            // data['data']['next'] = (this.getItems(data['data']).length<limit ? -1 : page+1);

            // see if we got any results - if not, that means that it's the end of the data stream
            data["data"]["next"] =
              this.getItems(data["data"]).length == 0 ? -1 : page + 1;
          }
        })
      );
  }

  /**
   * Function to get items ( child classes might want to verwrite this if needed )
   *
   * @param data
   */
  protected getItems(data: object) {
    return data["items"];
  }

  /**
   * Is async fetch in progress?
   */
  public isLoading() {
    return this.api.isInEventList({ command: this.apiCommandItem.name });
  }

  /**
   * Was there an error with last fetch?
   */
  public wasError() {
    return this.api.isInErrorList({ command: this.apiCommandItem.name });
  }

  /**
   * Get errors if there was any
   */
  public getErrors() {
    return this.api.getFromErrorList({ command: this.apiCommandItem.name });
  }
}
