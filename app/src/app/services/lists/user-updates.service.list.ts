import { ApiParams } from "../api/api.params";
import { ApiResponses } from "../api/api.responses";
import { ListBase } from "./list-base";
import { UsersUpdates } from "../../schemas/users-updates";

export class UserUpdatesServiceItemList<
  ListItemType = UsersUpdates.type,
  ApiResponseT = ApiResponses.UsersUpdatesResponseNext.type,
  FilterT = ApiParams.ProfileMatchFilter.type
> extends ListBase<ListItemType, ApiResponseT, FilterT> {
  /**
   * Initialize config
   */
  protected initConfig() {
    this._config = {
      doRemoveDuplicates: true,
      uniqueKey: "update_id",
      isPaginated: true,
      defaultLimit: 100,
      sortBy: "",
      sortDir: "",
      doAddToTop: false,
    };
  }

  /**
   * Initialize filter
   */
  protected initParams() {
    this._params = {};
  }
}
