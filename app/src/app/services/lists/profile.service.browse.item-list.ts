import { ProfileBase } from "../../schemas/profile-base";
import { ApiCommandItem, ApiCommands } from "../api/api.commands";
import * as _ from "lodash";
import { ApiParams } from "../api/api.params";
import { ApiResponses } from "../api/api.responses";
import { ProfileServiceItemList } from "./profile.service.item-list";

export type ProfileBrowseCommands =
  | "all"
  | "featured"
  | "nearby"
  | "random"
  | "activity"
  | "new"
  | string;

export type ProfileBrowseFilter = ApiParams.ProfileMatchFilter.type & {
  command: ProfileBrowseCommands;
};

/**
 * Class for profile browse service items
 */
export class ProfileServiceBrowseItemList<
  ListItemType = ProfileBase.type,
  ApiResponseT = ApiResponses.ProfileListResponseTotal.type,
  FilterT = ProfileBrowseFilter
> extends ProfileServiceItemList {
  private defaultCommand: ApiCommandItem;

  /**
   * Initialize filter
   */
  protected initParams() {
    this.defaultCommand = this.apiCommandItem;
  }

  /**
   * Override get api command
   */
  protected getApiCommand(): ApiCommandItem {
    let command = this.resolveCommand(this._params?.command);
    this.apiCommandItem = command ?? this.apiCommandItem;
    return command;
  }

  /**
   * Resolve command by name
   *
   * @param {string} command
   */
  private resolveCommand(command: string): ApiCommandItem {
    switch (command) {
      case "all":
        return ApiCommands.BrowseAll;
      case "featured":
        return ApiCommands.BrowseFeatured;
      case "nearby":
        return ApiCommands.BrowseNearby;
      case "random":
        return ApiCommands.BrowseRandomNearby;
      case "activity":
        return ApiCommands.BrowseRecentActivity;
      case "new":
        return ApiCommands.BrowseNew;
      default:
        return this.defaultCommand;
    }
  }
}
