import { Injectable } from "@angular/core";
import { ApiResponses } from "../api/api.responses";
import { ListBase } from "./list-base";
import { ProfileBase } from "../../schemas/profile-base";
import { environment as Env } from "../../../environments/environment";
import { ApiParams } from "../api/api.params";

export class SearchServiceItemList<
  ListItemType = ProfileBase.type,
  ApiResponseT = ApiResponses.ProfileListResponseTotal.type,
  ParamT = object
> extends ListBase<ListItemType, ApiResponseT, ParamT> {
  /**
   * Initialize config
   */
  protected initConfig() {
    this._config = {
      doRemoveDuplicates: true,
      uniqueKey: "user_id",
      isPaginated: true,
      defaultLimit: Env.DEFAULT_LIST_GET_LIMIT,
      sortBy: "",
      sortDir: "",
      doAddToTop: false,
    };
  }
}
