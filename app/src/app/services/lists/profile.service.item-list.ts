import { ProfileBase } from "../../schemas/profile-base";
import * as _ from "lodash";
import { ApiParams } from "../api/api.params";
import { ApiResponses } from "../api/api.responses";
import { ListBase } from "./list-base";
import { environment as Env } from "../../../environments/environment";
import { ConsentPageRoutingModule } from "src/app/pages/signup/consent/consent-routing.module";
import { convertCompilerOptionsFromJson } from "typescript";
import { MonoTypeOperatorFunction, map, tap } from "rxjs";

export class ProfileServiceItemList<
  ListItemType = ProfileBase.type,
  ApiResponseT = ApiResponses.ProfileListResponseTotal.type,
  FilterT = ApiParams.ProfileMatchFilter.type
> extends ListBase<ListItemType, ApiResponseT, FilterT> {
  /**
   * Initialize config
   */
  protected initConfig() {
    this._config = {
      doRemoveDuplicates: true,
      uniqueKey: "user_id",
      isPaginated: true,
      defaultLimit: Env.DEFAULT_LIST_GET_LIMIT,
      sortBy: "",
      sortDir: "",
      doAddToTop: false,
    };
  }

  public haveUsername(username: string): boolean {
    return this.haveItems({ username: username });
  }
  /**
   * Initialize params
   */
  protected initParams() {
    this._params = {
      sort_by: "created_ts",
      sort_dir: "desc",
      filter_by: null,
      filter_val: null,
      filter_opt: null,
    };
  }
}
