import * as _ from "lodash";
import { ApiResponses } from "../api/api.responses";
import { ListBase } from "./list-base";
import { ApiService } from "../api/api.service";
import { ApiCommandItem } from "../api/api.commands";
import { FlirtItem } from "src/app/schemas/flirt";

export class FlirtServiceItemList<
  ListItemType = FlirtItem.type,
  ApiResponseT = ApiResponses.FlirtGetListResponse.type,
  ParamT = object
> extends ListBase<ListItemType, ApiResponseT, ParamT> {
  /**
   * Initialize config
   */
  protected initConfig() {
    this._config = {
      doRemoveDuplicates: true,
      uniqueKey: "flirt_id",
      isPaginated: false,
      defaultLimit: 0,
      sortBy: "",
      sortDir: "",
      doAddToTop: false,
    };
  }

  /**
   * Initialize filter
   */
  protected initParams() {
    this._params = {};
  }
}
