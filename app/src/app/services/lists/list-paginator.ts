import { environment as Env } from "../../../environments/environment";
import _ from "lodash";
import { BehaviorSubject, Observable } from 'rxjs';

export class ListPaginator {
  private list: any;
  private currentPage: number;
  private pageLimit: number;
  private latestResultCount: number;
  private commandVersion: string = "1";

  public constructor(
    list: any,
    pageLimit: number = Env.DEFAULT_LIST_GET_LIMIT,
    commandVersion: string = "1"
  ) {
    this.list = list;
    this.commandVersion = commandVersion;
    this.reset(pageLimit);

    this.list.init();
  }

  public isEmpty() {
    return (
      this.latestResultCount > -1 &&
      !this.list.isLoading() &&
      this.list.isStoredListEmpty() &&
      !this.list.wasError()
    );
  }

  public changeFilter(filter) {
    this.list.changeParams(filter);
    this.reset();
    this.next();
  }

  private _didLoad$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  get didLoad$(): Observable<boolean> {
    return this._didLoad$.asObservable();
  }

  public didLoad() {
    return this.latestResultCount > -1;
  }

  public didReachEnd() {
    return (
      this.latestResultCount > -1 && this.latestResultCount < this.pageLimit
    );
  }

  public refresh(event: any = {}) {
    this.list.init(true);
    this.reset();
    this.next(event);
  }

  public next(
    event: any = {},
    doneCallback: Function = () => {},
    errorCallback: Function = () => {}
  ) {
    return this.list
      .setCommandVersion(this.commandVersion)
      .get(this.currentPage, this.pageLimit)
      .subscribe({
        next: (data) => {
          this.currentPage = data?.data?.next || this.currentPage + 1;

          // @todo set latest result count
          this.latestResultCount = data?.data?.items?.length;

          // set evetn stuff
          if (!_.isEmpty(event)) {
            // we loaded stuff
            event?.target?.complete();

            if (this.didReachEnd()) {
              // no more result
              //event.target.disabled = true;
            }
          }

          if (doneCallback instanceof Function) {
            doneCallback();
          }

          this._didLoad$.next(this.latestResultCount > -1);
        },
        error: (error) => {
          // set evetn stuff
          if (!_.isEmpty(event)) {
            event?.target?.complete();
          }

          if (errorCallback instanceof Function) {
            errorCallback(error);
          }
        },
      });
  }

  /**
   * Reset data
   */
  public reset(pageLimit: number = Env.DEFAULT_LIST_GET_LIMIT) {
    this.currentPage = 1;
    this.pageLimit = pageLimit;
    this.latestResultCount = -1;
  }
}
