import * as _ from "lodash";
import { ApiResponses } from "../api/api.responses";
import { ListBase } from "./list-base";
import { environment as Env } from "../../../environments/environment";
import { Message } from "src/app/schemas/message";
import { Observable } from "rxjs";
import { ChatMessage } from "../chat-message.interface";
import { map } from "rxjs/operators";

export class ChatMessageItemList<
  ListItemType = ChatMessage,
  ApiResponseT = ApiResponses.MessagesResponse.type,
  FilterT = object,
> extends ListBase<ListItemType, ApiResponseT, FilterT> {
  /**
   * Initialize config
   */
  protected initConfig() {
    this._config = {
      doRemoveDuplicates: false,
      uniqueKey: "id",
      isPaginated: true,
      defaultLimit: 50,
      sortBy: "ts",
      sortDir: "asc",
      doAddToTop: true,
    };
  }

  public convertItems: Function = (items) => {
    return items;
  };

  get list$(): Observable<ListItemType[]> {
    return this._list$.pipe(map((list) => this.convertItems(list)));
  }

  protected validateBeforeCall(): boolean {
    // do we have user_id param?
    return !!this._params["user_id"] || !!this._params["thread_id"];
  }

  /**
   * return extra params for api call
   */
  public getExtraParams() {
    return { get_attachments: 1 };
  }

  /**
   * Initialize params
   */
  protected initParams() {
    /*
        thread_id: this.threadData.id,
        user_id: this.threadData.user_id,
        limit: ITEM_LIMIT,
        sort_by: "msg_ts",
        sort_dir: "desc",
        page: this.page,
        get_attachments:
    */

    this._params = {
      sort_by: "msg_ts",
      sort_dir: "desc",
      thread_id: null, // if we don't send it, then we get all the messages
      user_id: null, //this.threadData.user_id2
      get_attachments: 1,
    };
  }
}
