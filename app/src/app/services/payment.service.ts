import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { ApiCommands } from "./api/api.commands";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";

@Injectable({
  providedIn: "root",
})
export class PaymentService {
  constructor(private api: ApiService) {}

  /**
   * Send ios payment to server
   *
   * @param params
   * @returns
   */
  public ios(
    params: ApiParams.PaymentIos.type
  ): Observable<ApiResponses.PaymentResponse.type> {
    return this.api.call<ApiResponses.PaymentResponse.type>({
      command: ApiCommands.PaymentIos.name,
      method: "post",
      params: params,
      useToken: true,
      commandVersion: "1",
    });
  }

  /**
   * Send android payment to server
   *
   * @param params
   * @returns
   */
  public android(
    params: ApiParams.PaymentAndroid.type
  ): Observable<ApiResponses.PaymentResponse.type> {
    return this.api.call<ApiResponses.PaymentResponse.type>({
      command: ApiCommands.PaymentAndroid.name,
      method: "post",
      params: params,
      useToken: true,
      commandVersion: "1",
    });
  }

  /**
   *  send log of payment to server
   *
   * @param params
   * @returns
   */
  public log(
    params: ApiParams.PaymentLog.type
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api.call<ApiResponses.NoDataResponse.type>({
      command: ApiCommands.PaymentLog.name,
      method: "post",
      params: params,
      useToken: true,
    });
  }
}
