import { Injectable } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";
import { Debug } from "../helpers/debug";
import { DeviceInfoService } from "./device-info.service";
import { MiscService } from "./misc.service";
import { UtilsService } from "./utils.service";

@Injectable({
  providedIn: "root",
})
export class AppsflyerHelperService {
  constructor(
    private debug: Debug,
    private misc: MiscService,
    private deviceInfo: DeviceInfoService,
    private utilsService: UtilsService,
  ) {}

  private _attributionData$: BehaviorSubject<any> = new BehaviorSubject({});

  /**
   * Observable to follow boot status
   */
  get attributionData$(): Observable<any> {
    return this._attributionData$.asObservable();
  }

  public get attributionData() {
    return this._attributionData;
  }

  private _attributionData: any = {};

  private initDone = false;

  public init(): Promise<any> {
    // this is a fix to a bug https://hs-phab.dvipdev.com/T20042
    // where appsflyer does not return promise if app is restared on android

    return new Promise((resolve, reject) => {
      // only for android fix
      if (this.misc.devicePlatform == "android") {
        setTimeout(() => {
          if (!this.initDone) {
            this.initDone = true;
            resolve(null);
          }
        }, 4000);
      }
      if (this.misc.isNativePlatform() || 1 == 1) {
        this.utilsService
          .attrib()
          .then((data) => {
            this.initDone = true;
            this.debug.l("Attribution data", data);
            this._attributionData = data?.data || {};
            this._attributionData$.next(this._attributionData);
            resolve(data);
          })
          .catch((e) => {
            this.initDone = true;
            this.debug.le("Attrib service error", e);
            reject(e);
          });
      } else {
        this.initDone = true;
        this.debug.l("Attribution not available on browser");
        resolve(null);
      }
    });
  }

  /**
   * returns value with prefix if value not empty
   *
   * @param obj
   * @param key
   * @param prefix
   */
  private getValIfNotNull(obj: Object, key: string, prefix: string = "_") {
    let _res = "";
    if (obj[key]) {
      _res += prefix + obj[key];
    }
    return _res;
  }

  /**
   * return a unified affiliate link
   */
  public getAffiliateString(): string {
    let _res = "";

    let _possibleKeys = ["media_source", "campaign"];

    _possibleKeys.forEach((el) => {
      _res += this.getValIfNotNull(this._attributionData, el, _res ? "_" : "");
    });

    // if we only have organic data, don't return anything ( we know that it's organic )
    if (
      this._attributionData["af_status"] &&
      this._attributionData["af_status"].toLowerCase() == "organic"
    ) {
      _res = "";
    }

    return _res;
  }

  public logEvent(name: string, value: any = {}): Promise<any> {
    let res = null;

    try {
      /*
      res = AppsFlyer.logEvent({
        eventName: name,
        eventValue: value,
      });
      */
    } catch (err) {
      res = null;
    }

    return res;
  }
  /*
  private regAttribution() {
    AppsFlyer.addListener(AFConstants.OAOA_CALLBACK, (event) => {
      this.debug.l("oaoa appsflyer event", event.data);
      this._attributionData = { ...this._attributionData, ...event.data };

      this._attributionData$.next(this._attributionData);
    });

    AppsFlyer.addListener(AFConstants.CONVERSION_CALLBACK, (event) => {
      this.debug.l("conv appsflyer event", event.data);

      /*
    https://support.appsflyer.com/hc/en-us/articles/360000726098-Conversion-data-payloads-and-scenarios

    DEMO DATA:
    https://support.appsflyer.com/hc/en-us/articles/360000726098-Conversion-data-payloads-and-scenarios

      // onelink example

{
    "callbackName": "onConversionDataSuccess",
    "data": {
        "retargeting_conversion_type": "none",
        "is_incentivized": "false",
        "orig_cost": "0.0",
        "deep_link_value": "deeplink_value",
        "is_first_launch": true,
        "af_click_lookback": "7d",
        "iscache": true,
        "click_time": "2023-03-15 15:32:51.919",
        "af_fp_lookback_window": "30m",
        "match_type": "gp_referrer",
        "shortlink": "5kv1hdl3",
        "af_dp": "seniornextapp://",
        "install_time": "2023-03-15 15:36:20.678",
        "media_source": "affiliate_TEST",
        "deep_link_sub1": "another_deeplink_value",
        "af_status": "Non-organic",
        "utm_term": "utm_term_test",
        "cost_cents_USD": "0",
        "campaign": "affiliate_test_campaign",
        "affiliate": "MY_AFFILIATE",
        "is_retargeting": "false"
    }
}

      // first start after install

      {
    "callbackName": "onConversionDataSuccess",
    "data": {
        "retargeting_conversion_type": "none",
        "is_incentivized": "false",
        "orig_cost": "0.0",
        "is_first_launch": true,
        "af_click_lookback": "7d",
        "iscache": true,
        "click_time": "2023-03-14 15:04:21.070",
        "af_fp_lookback_window": "30m",
        "match_type": "id_matching",
        "install_time": "2023-03-14 15:05:19.188",
        "media_source": "appsflyer_sdk_test_int",
        "advertising_id": "3f8869ad-1920-439f-9b17-9235e512f3e8",
        "clickid": "84d699c9-636d-445d-86aa-0baaeb5b7e3e",
        "af_status": "Non-organic",
        "cost_cents_USD": "0",
        "af_r": "https://sdktest.appsflyer.com/sdk-integration-test/install/launch?sid=84d699c9-636d-445d-86aa-0baaeb5b7e3e&store=androidOther",
        "campaign": "None",
        "is_retargeting": "false",
        "ts": "1678806251"
    }
}

    ... plus any other ?para=value will be here

// not first start after install

{
    "callbackName": "onConversionDataSuccess",
    "data": {
        "retargeting_conversion_type": "none",
        "is_incentivized": "false",
        "orig_cost": "0.0",
        "is_first_launch": false,
        "af_click_lookback": "7d",
        "iscache": true,
        "click_time": "2023-03-14 15:04:21.070",
        "af_fp_lookback_window": "30m",
        "match_type": "id_matching",
        "install_time": "2023-03-14 15:05:19.188",
        "media_source": "appsflyer_sdk_test_int",
        "advertising_id": "3f8869ad-1920-439f-9b17-9235e512f3e8",
        "clickid": "84d699c9-636d-445d-86aa-0baaeb5b7e3e",
        "af_status": "Non-organic",
        "cost_cents_USD": "0",
        "af_r": "https://sdktest.appsflyer.com/sdk-integration-test/install/launch?sid=84d699c9-636d-445d-86aa-0baaeb5b7e3e&store=androidOther",
        "campaign": "None",
        "is_retargeting": "false",
        "ts": "1678806251"
    }
}

    ... plus any other ?para=value will be here

*/
  /*
      this._attributionData = { ...this._attributionData, ...event.data };

      this._attributionData$.next(this._attributionData);
    });
  }*/
}
