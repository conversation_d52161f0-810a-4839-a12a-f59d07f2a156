import { Injectable } from "@angular/core";
import { Debug } from "../helpers/debug";
import { Utils } from "../helpers/utils";
import { Storage } from "@ionic/storage";
import { OfferService } from "./offer.service";
import { Router } from "@angular/router";
import { OfferData } from "../schemas/offerdata";
import { MiscService } from "./misc.service";
import { DialogHelperService } from "./dialog-helper.service";
import { BasicInfoModalParams } from "../pages/modals/basic-info/basic-info-modal-params.class";
import { StateService } from "./state.service";
import { NavController } from "@ionic/angular";
import { MenuHelperService } from "./menu-helper.service";
import { firstValueFrom } from "rxjs";

/**
 * Service to handle opening offers, and user actions
 */

export class OfferAction {
  name: string = "";
  page: string = "";
  params: Object = {};

  public reset() {
    this.name = "";
    this.page = "";
    this.params = {};
  }

  public setData(data: string) {
    this.reset();

    let _dataArray = data.split("|");
    this.name = _dataArray[0];

    // if we have a special name ( open_page ) then we extract the rest of the data

    if (this.name.toLowerCase() == "open_page" && _dataArray.length > 1) {
      this.page = _dataArray[1];
      if (!!_dataArray[2]) {
        try {
          this.params = JSON.parse(_dataArray[2]);
        } catch (err) {
          this.params = {};
        }
      }
    }
  }

  /**
   * Constructor
   * @param data
   */
  public constructor(data: string) {
    this.setData(data);
  }

  /**
   * See if the action is valid
   */
  public isValid() {
    return !!this.name;
  }

  /**
   * is the action a navigation action?
   */
  public isOpenPageAction() {
    return !!this.page;
  }
}

@Injectable({
  providedIn: "root",
})
export class OfferHandlerService {
  public constructor(
    private dbg: Debug,
    private offerSrv: OfferService,
    private storage: Storage,
    private router: Router,
    private misc: MiscService,
    private stateService: StateService,
    private navCtrl: NavController,
    private menuHelper: MenuHelperService
  ) {}

  private _offerData: OfferData.type = null;
  private _lastFetchedTs: number = 0;
  private storageKeyBase: string = "offerServiceIsOfferDisabled_";

  /**
   * get offer data
   */
  public offerData() {
    return this._offerData;
  }

  /**
   * Set offer data
   * @param data
   */
  public setOfferData(data) {
    this._offerData = data;
  }

  /**
   * fetch offer data from server
   * @param disableCache boolean if true, then cache will not be used
   */
  public fetchOffer(disableCache: boolean = false): Promise<any> {
    /*

    offer data should look like this on server side

    uid - HAS to be unique for all offers !!

    Fill 75% - get 1 month FeaturedPlus
    {assign var='MAdata' scope='global' value=[
      'title'=>'Get premium features for FREE!',
      'subtitle'=>'Act now!',
      'description'=>'Fill 75% - get 1 month FeaturedPlus',
      'imageUrl'=>'https://previews.123rf.com/images/iqoncept/iqoncept1808/iqoncept180800062/105834908-free-no-cost-complimentary-gift-premium-offer-calendar-date-3d-illustration.jpg',
      'buttonText'=>'Edit profile',
      'uid' => '4',
      'action' => 'edit_profile'
    ]}

    */

    // cache offer for N seconds (60 minutes), don't re-fetch it
    if (Utils.now() - this._lastFetchedTs < 60 * 60) {
      if (!disableCache) {
        return Promise.resolve(this.offerData());
      }
    }

    // real data fetch
    return new Promise<any>((resolve, reject) => {
      firstValueFrom(this.offerSrv.get())
        .then((data) => {
          // param test
          //data.data.action = "open_page|MyProfileEditMenu|{\"DoOpenPhotoMenu\":true}";

          // do we have data?
          if (!!data.data.uid) {
            this._lastFetchedTs = Utils.now();
            this._offerData = data.data;
            resolve(data.data);
          } else {
            reject("invalid_offer_data");
          }
        })
        .catch((err) => {
          this.dbg.l("offer error", err);
          reject(err);
        });
    });
  }

  /**
   * Set offer state ( enabled / disabled ) per uid bases
   * @param isEnabled boolean
   */
  private setOfferState(isEnabled: boolean): Promise<any> {
    // uid exists?
    if (!!this.offerData().uid) {
      return this.storage.set(
        this.storageKeyBase + this.offerData().uid,
        isEnabled
      );
    } else {
      return Promise.reject("no_offers_data");
    }
  }

  /**
   * Enable offer in local storage
   */
  public enable(): Promise<any> {
    return this.setOfferState(false);
  }

  /**
   * Disable offer in local storage
   */
  public disable(): Promise<any> {
    return this.setOfferState(true);
  }

  /**
   * Fetch offer status based on uid
   * resolves if the offer is enabled, rejected if not
   */
  public isOfferEnabled(doForce: boolean = false): Promise<any> {
    if (doForce) {
      return Promise.resolve();
    }

    return new Promise<any>((resolve, reject) => {
      // uid exists?
      if (!!this.offerData().uid) {
        this.storage
          .get(this.storageKeyBase + this.offerData().uid)
          .then((data) => {
            if (data) {
              // we reject, when the disabled flag is true
              reject();
            } else {
              // we resolve if it does not exists, or it's false
              resolve(null);
            }
          });
      } else {
        // if there's an error, then we tell that it's allowed
        resolve(true);
      }
    });
  }

  private openPage(page: string, param: object = {}) {
    try {
      if (!this.misc.checkUrl("app/" + page)) {
        // if we're not already on the current page, then open it
        this.navCtrl
          .navigateBack(this.menuHelper.previousURL, {
            animated: false,
          })
          .then(() => {
            this.router.navigate(["/app/" + page, param]);
          });
      }
    } catch (err) {
      this.dbg.le("cannot open page from offer action", err);
    }
  }

  /**
   * Execute the action based on the offer
   * @param ignoreDisabled - if true, enabled / disabled state of the given offer will be ignored
   */
  public executeOfferAction(ignoreDisabled: boolean = false): Promise<any> {
    return new Promise((resolve, reject) => {
      let _action = new OfferAction(this.offerData().action);

      if (!_action.isValid()) {
        reject();
      } else {
        let _callback: Function = () => {};

        // if we have an open page action, handle it properly
        if (_action.isOpenPageAction()) {
          // we can navigate to any page, the format is
          // - if we want to open a specific page, then we'll need to use
          // -- open_page|pagename|{paramsobject...}

          _callback = () => {
            this.openPage(_action.page);
          };
        } else {
          // handle special cases
          switch (_action.name.toLowerCase()) {
            case "edit_profile":
              _callback = () => {
                this.openPage("my-profile-edit");
              };
              break;

            case "photo_upload":
              _callback = () => {
                this.openPage("my-profile-edit", { openPhotoUpload: true });
              };
              break;
          }
        }

        // display dialog

        this.isOfferEnabled(ignoreDisabled)
          .then(() => {
            // open only if upgrade dialog not opened
            if (!Utils.isUpgradeUrl(this.router.url)) {
              this.showOfferModal(this.offerData(), _callback);
            }
          })
          .catch((err) => {
            this.dbg.l("offer disabled", this.offerData());
          });

        resolve(true);
      }
    });
  }

  /**
   * Show offer modal dialog ( using data fetched from server)
   * @param data
   * @param callback
   */
  private showOfferModal(data: OfferData.type, callback: Function) {
    let params: BasicInfoModalParams = new BasicInfoModalParams();

    params.title = data.title;
    params.topText = data.subtitle;
    params.bottomText = data.description;

    // do we have an image? if not, default icon is displayed
    if (data.imageUrl) {
      params.imageUrl = data.imageUrl;
    } else {
      params.iconName = "pricetags";
    }

    // set callback
    params.buttonCallback = callback;

    params.buttonVisible = true;
    params.buttonText = data.buttonText;

    params.footerButtonVisible = false;
    params.footerButtonText = "review_never";
    params.footerButtonCallback = () => {
      this.disable();
    };

    params.footerButton2Visible = true;
    params.footerButton2Text = "review_remind";
    params.footerButton2Callback = () => {};

    // params.bgStyle = "transparent";

    this.stateService.set("offerDialogData", "", params);
    this.router.navigate(["/app/modals/offer"]);
  }
}
