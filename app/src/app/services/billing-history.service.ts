import { Injectable } from "@angular/core";
//import { Platform } from "@ionic/angular";
//import { map } from "rxjs/operators";
import { Observable } from "rxjs";
import { Debug } from "../helpers/debug";
import { BillingHistoryItem } from "../schemas/billing-history";
import { ApiCommands } from "./api/api.commands";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import _ from "lodash";
import { firstValueFrom } from "rxjs";
import { ResolveIpData } from "../schemas/utils";
import { convertToObject } from "typescript";

@Injectable({
  providedIn: "root",
})
export class BillingHistoryService {
  constructor(private api: ApiService, private debug: Debug) {}

  private _items = [];

  get items() {
    return this._items;
  }

  get inProgress(): boolean {
    return this.api.isInEventList({
      command: ApiCommands.BillingHistoryGet.name,
    });
  }

  public get(): void {
    this.api
      .call<any>({
        command: ApiCommands.BillingHistoryGet.name,
        method: "get",
        useToken: true,
        commandVersion: "2",
      })
      .subscribe({
        next: (res) => {
          this._items = JSON.parse(JSON.stringify(res.data.items));
        },
        error: (e) => console.error(e),
      });
  }
}
