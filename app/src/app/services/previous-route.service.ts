import { Injectable } from "@angular/core";
import { Router, RoutesRecognized } from "@angular/router";
import { filter, pairwise } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class PreviousRouteService {
  private _previousUrl: string;

  constructor(private router: Router) {}

  public init(): void {
    this.router.events
      .pipe(
        filter((evt: any) => evt instanceof RoutesRecognized),
        pairwise()
      )
      .subscribe(
        (events: RoutesRecognized[]) =>
          (this._previousUrl = events[0].urlAfterRedirects)
      );
  }

  get previousUrl(): string {
    return this._previousUrl;
  }
}
