import { TestBed } from "@angular/core/testing";

import { LoaderService } from "./loader.service";
import { StorageService } from "./storage.service";

describe("LoaderService", () => {
  let service: LoaderService;
  let storageSpy: StorageService;

  beforeEach(() => {
    storageSpy = jasmine.createSpyObj("StorageService", {
      get: Promise.resolve(),
      set: Promise.resolve(),
    });

    TestBed.configureTestingModule({
      providers: [{ provide: StorageService, useValue: storageSpy }],
    });
    service = TestBed.inject(LoaderService);
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });
});
