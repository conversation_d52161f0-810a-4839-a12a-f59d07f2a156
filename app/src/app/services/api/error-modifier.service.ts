import { Injectable } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import _ from "lodash";
import { ErrorItem } from "src/app/schemas/error";

@Injectable({
  providedIn: "root",
})
export class ErrorModifierService {
  constructor(private translate: TranslateService) {}

  /**
   * Function to modify remote error messages to different ones
   * and maybe add links etc by translation
   *
   * @param errorList
   */
  public modifyErrors(errorList: ErrorItem.type[]) {
    let result: ErrorItem.type[] = [];

    // use local translations for these errors

    _.forEach(errorList, (e: ErrorItem.type) => {
      let localTranslationRules = [
        // show reset and/or password link in text

        "register_email_exists",
        "error_email_account_active_standalone",
        "username_exists_error",
        "error_email_account_inactive",
        "error_email_account_inactive_standalone",
        "error_email_account_active",
      ];

      if (localTranslationRules.indexOf(e.key) >= 0) {
        e.error = this.translate.instant(e.key);
      }

      result.push(e);
    });

    return result;
  }
}
