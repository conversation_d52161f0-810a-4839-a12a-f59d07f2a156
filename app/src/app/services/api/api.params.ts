import * as yup from "yup";
import { Utils } from "../../helpers/utils";
import * as _ from "lodash";

/**
 * Api command parameter definitions
 */
export namespace ApiParams {
  export enum WELCOME_STATUS_FLAG {
    Default = 0,
    Complete = 1,
    Incomplete = 2,
  }

  export const AppLoginProviderFacebook = "Facebook";
  export const AppLoginProviderApple = "Apple";

  /**
   * Reasons to report a user profile.
   */
  export enum ProfileReportReasons {
    "block",
    "abuse",
    "spam",
  }

  /**
   * Restriction params to check for.
   */
  export enum RestrictionParams {
    "can_change_location",
    "can_choose_premium_match",
    "can_hide_ads",
    "can_hide_online_status",
    "can_use_undo",
    "swipe_restriction",
    "like_reset",
    "superlike_reset",
  }

  /**
   * MatchPref distances.
   */
  export type MatchPrefDistances = 0 | 10 | 20 | 30 | 50 | 100 | 250;

  /**
   * Use number as boolean.
   */
  export type BooleanNumber = 0 | 1;

  /**
   * Sort direction.
   */
  export enum SortDirection {
    "asc",
    "desc",
  }

  /**
   * Filter options.
   */
  export enum FilterOptions {
    "exact",
    "starts_with",
    "contains",
  }

  /**
   * Online status.
   */
  export enum OnlineStatus {
    "online",
    "offline",
  }

  /**
   * Inbox thread section param values
   */
  export enum InboxThreadSections {
    "inbox",
    "all_messages",
    "sent",
    "deleted",
    "filtered",
  }

  /**
   * Inbox thread show params value
   */
  export enum InboxThreadShow {
    "all",
    "read",
    "unread",
  }

  /**
   * Move thread to inbox and archive destination type
   */
  export enum MoveThreadDestination {
    "archived",
    "inbox",
  }

  /**
   * Authentication with email.
   */
  export namespace AuthSimpleEmail {
    // Schema.
    export let schema = yup.object({
      email: yup.string(),
      password: yup.string(),
      system_id: yup.string(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Authentication with email.
   */
  export namespace ServerStatus {
    // Schema.
    export let schema = yup.object({
      system_id: yup.string(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Authentication with the username.
   */
  export namespace AuthSimpleUsername {
    // Schema.
    export let schema = yup.object({
      username: yup.string(),
      password: yup.string(),
      system_id: yup.string(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace AppPreRegisterParams {
    // Schema.
    export let schema = yup.object({
      system_id: yup.string().notRequired(),
      provider: yup
        .string()
        .oneOf([AppLoginProviderApple, AppLoginProviderFacebook])
        .required(),
      token: yup.string().required(),
      provider_response: yup.mixed().required(),
      platform: yup.string().oneOf(["ios", "android"]).required(),
      force_register: yup.boolean().notRequired(),
      refresh_token: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace AppRegisterParams {
    // Schema.
    export let schema = yup.object({
      system_id: yup.string().notRequired(),
      provider_data: yup.mixed().required(),
      data: yup.mixed().required(),
      platform: yup.string().oneOf(["ios", "android"]).required(),
      force_register: yup.boolean().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Authentication with apple token
   */
  export namespace AuthSimpleApple {
    // Schema.
    export let schema = yup.object({
      token: yup.string().required(),
      platform: yup.string().required(),
      system_id: yup.string().required(),
      force_register: yup.string().notRequired(),

      // consent related stuff
      consent_terms: yup.boolean().notRequired(),
      consent_privacy: yup.boolean().notRequired(),
      consent_offers: yup.boolean().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Reset password params.
   */
  export namespace AuthResetPassword {
    // Schema.
    export let schema = yup.object({
      email: yup.string().required(),
      system_id: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Email verification params.
   */
  export namespace AuthVerifyEmail {
    // Schema.
    export let schema = yup.object({
      email: yup.string().required(),
      system_id: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Username verification params.
   */
  export namespace AuthVerifyUsername {
    // Schema.
    export let schema = yup.object({
      username: yup.string().required(),
      system_id: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Registration params.
   */
  export namespace Registration {
    // Schema.
    export let schema = yup.object({
      age: yup.number().required(),
      email: yup.string().required(),
      first_name: yup.string().required(),
      username: yup.string().required(),
      gender_id: yup.number().required(),
      looking: yup.number().required(),
      ip_address: yup.string().notRequired(),
      latitude: yup.string().notRequired(),
      longitude: yup.string().notRequired(),
      password: yup.string().required(),
      system_id: yup.string().required(),
      // consent related stuff
      consent_terms: yup.boolean().notRequired(),
      consent_privacy: yup.boolean().notRequired(),
      consent_offers: yup.boolean().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Get profile by username.
   */
  export namespace ProfileGet {
    // Schema.
    export let schema = yup.object({
      username: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Request photo.
   */
  export namespace ProfilePhotoRequest {
    // Schema.
    export let schema = yup.object({
      username: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Get potential matches.
   */
  export namespace ProfileSuggestions {
    // Schema.
    export let schema = yup.object({
      page: yup.number().notRequired(),
      limit: yup.number().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Profile match filter
   */
  export namespace ProfileMatchFilter {
    // Schema.
    export let schema = yup.object({
      sort_by: yup.string().notRequired().nullable(),
      sort_dir: yup
        .string()
        .oneOf(Utils.enumValues(SortDirection))
        .notRequired()
        .nullable(),
      filter_by: yup.string().notRequired().nullable(),
      filter_val: yup.string().notRequired().nullable(),
      filter_opt: yup
        .string()
        .oneOf(Utils.enumValues(FilterOptions))
        .notRequired()
        .nullable(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Get matched profiles.
   */
  export namespace ProfileMatches {
    // Schema.
    export let schema = yup.object(
      _.merge(ProfileMatchFilter, {
        page: yup.number().notRequired(),
        limit: yup.number().notRequired(),
      }),
    );

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Mark a profile as...
   */
  export namespace ProfileMark {
    // Schema.
    export let schema = yup.object({
      username: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Report a user profile.
   */
  export namespace ProfileReport {
    // Schema.
    export let schema = yup.object({
      username: yup.string().required(),
      user_id: yup.number().required(),
      reason: yup
        .string()
        //.oneOf(Utils.enumValues(ProfileReportReasons))
        .required(),
      comment: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * CCPA status update params
   */
  export namespace CCPAStatusUpdate {
    // Schema.
    export let schema = yup.object({
      value: yup.boolean().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Product params
   */
  export namespace ProductParams {
    // Schema.
    export let schema = yup.object({
      category: yup
        .string()
        .oneOf(["upgrade", "offer", "campaign", "discount", ""])
        .notRequired(),
      is_active: yup.number().notRequired(),
      store_type: yup.string().oneOf(["android", "ios"]).required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Account update params.
   */
  export namespace AccountUpdateUsername {
    // Schema.
    export let schema = yup.object({
      username: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Account update params.
   */
  export namespace AccountUpdate {
    // Schema.
    export let schema = yup.object({
      email: yup.string().notRequired(),
      password_current: yup.string().notRequired(),
      password_new: yup.string().notRequired(),
      password_new_re: yup.string().notRequired(),
      gender_id: yup.number().notRequired(),
      looking_id: yup.number().notRequired(),
      country: yup.string().notRequired(),
      city: yup.string().notRequired(),
      zip: yup.string().notRequired(),
      first_name: yup.string().notRequired(),
      description: yup.string().notRequired(),
      title: yup.string().notRequired(),
      birthdate: yup.string().notRequired(),
      profile_complete: yup.boolean().notRequired(),
      details: yup.mixed().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Account deactivate params.
   */
  export namespace AccountDeactivate {
    // Schema.
    export let schema = yup.object({
      subject: yup.string().required(),
      body: yup.string().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * AccountMatchPref update.
   */
  export namespace AccountMatchPref {
    // Schema.
    export let schema = yup.object({
      age_from: yup.number().required(),
      age_to: yup.number().required(),
      distance: yup.number().oneOf([0, 10, 20, 30, 50, 100, 250]).required(),
      genders: yup.array().of(yup.number()).required(),
      looking_id: yup.number().required(),
      gender_id: yup.number().required(),
      anybody: yup.boolean().default(false),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * AccountPushEndPoint.
   */
  export namespace AccountPushFirebaseEndPoint {
    // Schema.
    export let schema = yup.object({
      endpoint: yup.string().required(),
      firebase_token: yup.string().required(),
      user_id: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * AccountPushEndPoint.
   */
  export namespace AccountPushEndPoint {
    // Schema.
    export let schema = yup.object({
      push_token: yup.string().required(),
      device_uuid: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * AccountSettings params.
   */
  export namespace AccountSettings {
    // Schema.
    export let schema = yup.object({
      privacy_show_online_status: yup.boolean().required(),
      privacy_show_distance: yup.boolean().required(),
      is_online: yup.boolean().required(),
      push_notify_new_matches: yup.boolean().required(),
      push_notify_messages: yup.boolean().required(),
      push_notify_super_likes: yup.boolean().required(),
      push_notify_likes: yup.boolean().required(),
      push_notify_suggestions: yup.boolean().required(),
      push_notify_reminder: yup.boolean().required(),
      push_notify_campaigns: yup.boolean().required(),
      email_notify_new_matches: yup.boolean().required(),
      email_notify_messages: yup.boolean().required(),
      email_notify_super_likes: yup.boolean().required(),
      email_notify_likes: yup.boolean().required(),
      auto_update_location: yup.boolean().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Photo order params.
   */
  export namespace PhotoOrder {
    // Schema.
    export let schema = yup.object({
      order: yup.object().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * RestrictionCheck params.
   */
  export namespace RestrictionCheck {
    // Schema.
    export let schema = yup.object({
      name: yup.string().oneOf(Utils.enumValues(RestrictionParams)).required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * PictureCrop params.
   */
  export namespace PictureCrop {
    // Schema.
    export let schema = yup.object({
      w: yup.number().required(),
      h: yup.number().required(),
      x: yup.number().required(),
      y: yup.number().required(),
      ratio: yup.number().required(),
      gallery_id: yup.number().required(),
      image_id: yup.number().required(),
      img_w: yup.number().required(),
      img_h: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Helper enum for picture fortaion
   */
  export enum PictureRotateDirection {
    R90 = 1, // rotate 90 deg clockwise
    R180 = 2, // rotate 180 deg clockwise
    R270 = 3, // rotate 270 deg clockwise
    CR90 = -1, // rotate 90 deg counter clockwise
    CR180 = -2, // rotate 180 deg counter clockwise
    CR270 = -3, // rotate 270 deg counter clockwise
  }

  /**
   * PictureCrop params.
   */
  export namespace PictureRotate {
    // Schema.
    export let schema = yup.object({
      rotation: yup
        .string()
        //.oneOf(Utils.enumValues(PictureRotateDirection))
        .required(),
      gallery_id: yup.number().required(),
      image_id: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * PictureUpload params.
   */
  export namespace PictureUpload {
    // Schema.
    export let schema = yup.object({
      image_data: yup.string().notRequired(),
      image_url: yup.string().notRequired(),
      gallery_id: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * PictureUpload params.
   */
  export namespace PictureUploadAttachment {
    // Schema.
    export let schema = yup.object({
      image_data: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * PictureDelete params.
   */
  export namespace PictureDelete {
    // Schema.
    export let schema = yup.array().of(
      yup.object({
        gallery_id: yup.number().required(),
        image_id: yup.number().required(),
      }),
    );

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * PictureDeleteAttachments params.
   */
  export namespace PictureDeleteAttachment {
    // Schema.
    export let schema = yup.object({
      image_id: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * PictureSetAvatar params.
   */
  export namespace PictureSetAvatar {
    // Schema.
    export let schema = yup.object({
      gallery_id: yup.number().required(),
      image_id: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * LocationHelper params.
   */
  export namespace SocketToken {
    // Schema.
    export let schema = yup.object({
      channel: yup.string().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * LocationHelper params.
   */
  export namespace LocationHelper {
    // Schema.
    export let schema = yup.object({
      country_code: yup.string().notRequired(),
      city: yup.string().notRequired(),
      zip: yup.string().notRequired(),
      get_countries: yup.number().oneOf([0, 1]).notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * WelcomeProfile save params.
   */
  export namespace WelcomeProfileSave {
    // Schema.
    export let schema = yup.object({
      what: yup.string().required(),
      id: yup.number().notRequired(),
      value: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * WelcomeProfile save params.
   */
  export namespace WelcomeProfileStatusSave {
    // Schema.
    export let schema = yup.object({
      value: yup
        .number()
        .oneOf(Utils.enumValues(WELCOME_STATUS_FLAG))
        .required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * MessagesGet params.
   */
  export namespace MessagesGet {
    // Schema.
    export let schema = yup.object({
      user_id: yup.number().required(),
      limit: yup.number().notRequired(),
      page: yup.number().notRequired(),
      sort_by: yup.string().notRequired(),
      sort_dir: yup
        .string()
        .oneOf(Utils.enumValues(SortDirection))
        .notRequired(),
      from_ts: yup.number().notRequired(),
      to_ts: yup.number().notRequired(),
      thread_id: yup.number().notRequired(),
      get_attachments: yup.number().oneOf([0, 1]).notRequired(),
      type: yup.string().notRequired(),
      msg_id: yup.number().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * MessagesUnreadCount params.
   */
  export namespace MessagesUnreadCount {
    // Schema.
    export let schema = yup.object({
      "user_ids[]": yup.array().of(yup.number()).required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * MessagesGetReadTs params.
   */
  export namespace MessagesGetReadTs {
    // Schema.
    export let schema = yup.object({
      user_id: yup.number().required(),
      revers: yup.number().oneOf([0, 1]).notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * MessagesPostReadTs params.
   */
  export namespace MessagesPostReadTs {
    // Schema.
    export let schema = yup.object({
      user_id: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * MessagesReceivedTs params.
   */
  export namespace MessagesReceivedTs {
    // Schema.
    export let schema = yup.object({
      user_id: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * params for sending message
   */
  export namespace SendMessage {
    // Schema.
    export let schema = yup.object({
      username: yup.string().required(),
      subject: yup.string().notRequired(),
      body: yup.string().required(),
      image_ids: yup.array().of(yup.number()).notRequired(),
      thread_id: yup.string().notRequired(),
      delivery_notify: yup.boolean().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * params for sending message
   */
  export namespace CanSendMessage {
    // Schema.
    export let schema = yup.object({
      thread_id: yup.number().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Payment ios params.
   */
  export namespace PaymentIos {
    // Schema.
    export let schema = yup.object({
      purchase_id: yup.string().required(),
      "receipt-data": yup.string().required(),
      currency: yup.string().required(),
      amount: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Payment android params.
   */
  export namespace PaymentAndroid {
    // Schema.
    export let schema = yup.object({
      purchase_id: yup.string().required(),
      package_name: yup.string().required(),
      purchase_type: yup.string().required(),
      merchant_order_number: yup.string().required(),
      purchase_token: yup.string().required(),
      currency: yup.string().required(),
      amount: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Payment android log.
   */
  export namespace PaymentLog {
    // Schema.
    export let schema = yup.object({
      request_init: yup.string().required(),
      request_finish: yup.string().required(),
      response_init: yup.string().required(),
      response_finish: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Customer support params.
   */
  export namespace ContactAdd {
    // Schema.
    export let schema = yup.object({
      topic: yup.number().required(),
      subject: yup.string().required(),
      msg: yup.string().required(),
      email: yup.string().nullable(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace ContactAnonymous {
    // Schema.
    export let schema = yup.object({
      topic: yup.number().required(),
      subject: yup.string().required(),
      msg: yup.string().required(),
      email: yup.string().nullable(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * UsersUpdates params.
   */
  export namespace UsersUpdatesParams {
    // Schema.
    export let schema = yup.object({
      limit: yup.number().notRequired(),
      page: yup.number().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Flirt send paramter definition
   */
  export namespace FlirtSendParams {
    // Schema.
    export let schema = yup.object({
      id: yup.number().required(),
      username: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Send wink flirt param def
   */
  export namespace FlirtSendWinkParams {
    // Schema.
    export let schema = yup.object({
      username: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Promo code param
   */
  export namespace PromoCodeParams {
    // Schema.
    export let schema = yup.object({
      user_id: yup.number().required(),
      app_promo_code: yup.string().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Inbox get thread paramter definition
   */
  export namespace InboxGetThreadParams {
    // Schema.
    export let schema = yup.object({
      section: yup
        .string()
        .oneOf(Utils.enumValues(InboxThreadSections))
        .notRequired(),
      show: yup.string().oneOf(Utils.enumValues(InboxThreadShow)).notRequired(),
      page: yup.number().notRequired(),
      limit: yup.number().notRequired(),
      user_id2: yup.number().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Inbox thread id params
   */
  export namespace InboxThreadIdParams {
    // Schema.
    export let schema = yup.object({
      thread: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Thread id list
   */
  export namespace InboxThreadIdListParams {
    // Schema.
    export let schema = yup.object({
      threads: yup.array().of(yup.number()).required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Move threads params
   */
  export namespace InboxMoveThreadParams {
    // Schema.
    export let schema = yup.object({
      threads: yup.array().of(yup.number()).required(),
      move_to: yup
        .string()
        .oneOf(Utils.enumValues(MoveThreadDestination))
        .required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Browse list params.
   */
  export namespace BrowseParams {
    // Schema.
    export let schema = yup.object({
      page: yup.number().notRequired(),
      limit: yup.number().notRequired(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Photo comment [get] params.
   */
  export namespace PhotoCommentGetParams {
    // Schema.
    export let schema = yup.object({
      owner_id: yup.number().required(),
      gallery_id: yup.number().required(),
      image_id: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Photo comment [post] params.
   */
  export namespace PhotoCommentPostParams {
    // Schema.
    export let schema = yup.object({
      content: yup.string().required(),
      owner_id: yup.number().required(),
      gallery_id: yup.number().required(),
      image_id: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Photo comment [delete] params.
   */
  export namespace PhotoCommentDeleteParams {
    // Schema.
    export let schema = yup.object({
      owner_id: yup.number().required(),
      gallery_id: yup.number().required(),
      comment_id: yup.number().required(),
      image_id: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Photo like params.
   */
  export namespace PhotoLikeParams {
    // Schema.
    export let schema = yup.object({
      owner_id: yup.number().required(),
      gallery_id: yup.number().required(),
      image_id: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Introduction params.
   */
  export namespace Introduction {
    // Schema.
    export let schema = yup.object({
      distance_under: yup.number().required(),
      subject: yup.string().required(),
      message: yup.string().required(),
      cont_optin: yup.boolean().default(true),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace PictureSetAccessParams {
    // Schema.
    export let schema = yup.object({
      gallery_id: yup.number().required(),
      image_id: yup.number().required(),
      access: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace SearchBasic {
    // Schema.
    export let schema = yup.object({
      gender: yup.number().required(),
      looking: yup.number().required(),
      age_from: yup.number().required(),
      age_to: yup.number().required(),
      zip: yup.number().required(),
      distance: yup.number().required(),
      has_photo: yup.number().required(),
      country: yup.string().required(),
      // has_video: yup.number().required(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace UtilsConfig {
    export let schema = yup.object({
      system_id: yup.string(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace ProfileVerificationAdd {
    export let schema = yup.object({
      task_id: yup.string(),
      image_data: yup.string(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace ProfileVerificationPending {
    export let schema = yup.object({
      task_id: yup.string(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace UtilsAttribParams {
    // Schema
    export let schema = yup.object({
      os: yup.string().oneOf(["ios", "android"]).required(), // OS parametar
    });

    // Type
    export type type = yup.InferType<typeof schema>;
  }
}
