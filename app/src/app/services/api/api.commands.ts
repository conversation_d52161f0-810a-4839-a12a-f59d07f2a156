import { ObjectSchema } from "yup";
import { ApiResponses } from "./api.responses";

/**
 * Api command definitions
 */

export class ApiCommandItem {
  name: string = "";
  resultSchema: ObjectSchema<any>;
}

/**
 * user status
 * 0 - existing user
 * 1 - user completed all steps
 * 2 - new user needs to complete steps before using website
 */

export namespace ApiCommands {
  /**
   * Authenticate user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AuthSimple: ApiCommandItem = {
    name: "auth.simple",
    resultSchema: ApiResponses.SimpleAuthResponse.schema,
  };

  export const AppPreRegister: ApiCommandItem = {
    name: "auth.appPreRegister",
    resultSchema: ApiResponses.AppPreRegisterResponse.schema,
  };

  export const AppRegister: ApiCommandItem = {
    name: "auth.appRegister",
    resultSchema: ApiResponses.AppRegisterResponse.schema,
  };

  /**
   * Authenticate user with apple token
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AuthApple: ApiCommandItem = {
    name: "auth.apple",
    resultSchema: ApiResponses.SimpleAuthResponse.schema,
  };

  /**
   * Is user authenticated?
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AuthCheck: ApiCommandItem = {
    name: "auth.check",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Log user out from the current session.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AuthLogout: ApiCommandItem = {
    name: "auth.logout",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Register user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AuthRegister: ApiCommandItem = {
    name: "auth.register",
    resultSchema: ApiResponses.RegistrationResponse.schema,
  };

  /**
   * Reset password for the user by email.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AuthResetPassword: ApiCommandItem = {
    name: "auth.resetPassword",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Email verification before registration.
   *
   * Look up if email has already been used for registration,
   * or if the email looks suspicious/fake
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AuthVerifyEmail: ApiCommandItem = {
    name: "auth.verifyEmail",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Username verification before registration.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AuthVerifyUsername: ApiCommandItem = {
    name: "auth.verifyUsername",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Get a single profile data.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileGet: ApiCommandItem = {
    name: "profile",
    resultSchema: ApiResponses.ProfileBaseResponse.schema,
  };

  /**
   * Request photo from a user
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfilePhotoRequest: ApiCommandItem = {
    name: "profile.requestPhoto",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Get potential matches.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileSuggestions: ApiCommandItem = {
    name: "profile.suggestions",
    resultSchema: ApiResponses.ProfileListResponse.schema,
  };

  /**
   * Get matched profiles.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileMatches: ApiCommandItem = {
    name: "profile.matches",
    resultSchema: ApiResponses.ProfileListResponse.schema,
  };

  /**
   * Get user who liked us.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileLikedUs: ApiCommandItem = {
    name: "profile.likedUs",
    resultSchema: ApiResponses.ProfileListResponse.schema,
  };

  /**
   * Get user who we liked.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileWeLiked: ApiCommandItem = {
    name: "profile.weLiked",
    resultSchema: ApiResponses.ProfileListResponse.schema,
  };

  /**
   * Get user who we visited profile
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ListViewedMe: ApiCommandItem = {
    name: "lists.visitors",
    resultSchema: ApiResponses.ProfileListResponse.schema,
  };

  /**
   * List of users I have visited
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ListIViewed: ApiCommandItem = {
    name: "lists.history",
    resultSchema: ApiResponses.ProfileListResponse.schema,
  };

  /**
   * Mark a profile as Liked simple method.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileLikeSimple: ApiCommandItem = {
    name: "profile.simpleLike",
    resultSchema: ApiResponses.ProfileLikeResponse.schema,
  };

  /**
   * Unlike profile ( remove like ) - simple method
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileUnLikeSimple: ApiCommandItem = {
    name: "profile.simpleUnlike",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Mark a profile as Liked.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileLike: ApiCommandItem = {
    name: "profile.like",
    resultSchema: ApiResponses.ProfileLikeResponse.schema,
  };

  /**
   * Mark a profile as Skipper but return to match queue after some time.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileSkip: ApiCommandItem = {
    name: "profile.skip",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Mark a profile as Passed.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfilePass: ApiCommandItem = {
    name: "profile.pass",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Mark a profile as superliked.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileSuperlike: ApiCommandItem = {
    name: "profile.superlike",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Delete already matched user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileUnmatch: ApiCommandItem = {
    name: "profile.unmatch",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Get user who we blocked.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileWeBlocked: ApiCommandItem = {
    name: "lists.blocked",
    resultSchema: ApiResponses.ProfileListResponse.schema,
  };
  /**
   * Block the current profile.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileBlock: ApiCommandItem = {
    name: "profile.block",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Unblock the current profile.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileUnblock: ApiCommandItem = {
    name: "profile.unBlock",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Report a user profile.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ProfileReport: ApiCommandItem = {
    name: "profile.report",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Do require ccpr
   */
  export const UtilsCCPARequired: ApiCommandItem = {
    name: "utils.ccpaRequired",
    resultSchema: ApiResponses.CCPAResponseRequired.schema,
  };

  /**
   * Do require ccpr
   */
  export const AccountCCPARequired: ApiCommandItem = {
    name: "account.ccpaRequired",
    resultSchema: ApiResponses.CCPAResponseRequired.schema,
  };

  /**
   * Get ccpa status item
   */
  export const AccountCCPAGet: ApiCommandItem = {
    name: "account.ccpa",
    resultSchema: ApiResponses.GetCCPAStatusResponse.schema,
  };

  /**
   * Get the authenticated user's profile.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountGet: ApiCommandItem = {
    name: "account",
    resultSchema: ApiResponses.OwnProfileResponse.schema,
  };

  /**
   * Patch ccpa status
   */
  export const AccountCCPAPatch: ApiCommandItem = {
    name: "account.ccpa",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Account products api call
   */
  export const Campaign: ApiCommandItem = {
    name: "campaign",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Account products api call
   */
  export const AccountProducts: ApiCommandItem = {
    name: "account.products",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Update account username data.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountUsernamePatch: ApiCommandItem = {
    name: "account.username",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Update account data.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountPatch: ApiCommandItem = {
    name: "account.details",
    resultSchema: ApiResponses.OwnProfileResponse.schema,
  };

  /**
   * Deactivate user account.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountDeactivate: ApiCommandItem = {
    name: "account",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Delete user account.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountDelete: ApiCommandItem = {
    name: "account.process",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Get the user’s match/contact preferences.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountGetMatchPref: ApiCommandItem = {
    name: "account.matchPref",
    resultSchema: ApiResponses.MatchPrefResponse.schema,
  };

  /**
   * Update match/contact preferences.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountPostMatchPref: ApiCommandItem = {
    name: "account.matchPref",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Update user's location based on coordinates.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountGeolocation: ApiCommandItem = {
    name: "account.geolocation",
    resultSchema: ApiResponses.AccountGeolocationResponse.schema,
  };

  /**
   * Save push endpoint for the user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountPushFirebaseEndPoint: ApiCommandItem = {
    name: "account.pushFirebaseEndPoint",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Save push endpoint for the user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountPushEndPoint: ApiCommandItem = {
    name: "account.pushEndPoint",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Get account settings.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountSettings: ApiCommandItem = {
    name: "account.settings",
    resultSchema: ApiResponses.AccountSettingsResponse.schema,
  };

  /**
   * Get account detailsForm.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountDetailsForm: ApiCommandItem = {
    name: "account.detailsForm",
    resultSchema: ApiResponses.AccountDetailsFormResponse.schema,
  };

  /**
   * Get account interestOptions.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountInterestOptions: ApiCommandItem = {
    name: "account.interestOptions",
    resultSchema: ApiResponses.AccountInterestOptionsResponse.schema,
  };

  /**
   * Save account interests.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const AccountInterests: ApiCommandItem = {
    name: "account.interests",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Get a specific restriction status.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const RestrictionCheck: ApiCommandItem = {
    name: "restriction.check",
    resultSchema: ApiResponses.RestrictionCheckResponse.schema,
  };

  /**
   * Get a all restriction data.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const RestrictionAll: ApiCommandItem = {
    name: "restriction.all",
    resultSchema: ApiResponses.RestrictionAllResponse.schema,
  };

  /**
   * Upload a picture to user's gallery.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const PictureUpload: ApiCommandItem = {
    name: "picture",
    resultSchema: ApiResponses.GalleryImageResponse.schema,
  };

  /**
   * Crop picture
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const PictureCrop: ApiCommandItem = {
    name: "picture.crop",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Rotate picture
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const PictureRotate: ApiCommandItem = {
    name: "picture.rotate",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Upload a picture to user's attachments.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const PictureUploadAttachment: ApiCommandItem = {
    name: "picture.attachment",
    resultSchema: ApiResponses.GalleryImageResponse.schema,
  };

  /**
   * Delete a picture from user’s gallery.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const PictureDelete: ApiCommandItem = {
    name: "picture",
    resultSchema: ApiResponses.PictureDeleteResponse.schema,
  };

  /**
   * Delete a picture from user’s attachment.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const PictureDeleteAttachment: ApiCommandItem = {
    name: "picture.attachment",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Set user’s avatar.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const PictureSetAvatar: ApiCommandItem = {
    name: "picture.setAvatar",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Helper method for location.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ServerStatus: ApiCommandItem = {
    name: "server.status",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Helper function for logging
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const Log: ApiCommandItem = {
    name: "utils.log",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Helper for static page fetch
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const StaticPage: ApiCommandItem = {
    name: "utils.page",
    resultSchema: ApiResponses.StaticPage.schema,
  };

  /**
   * Helper method for location.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ConsentRequired: ApiCommandItem = {
    name: "utils.consentRequired",
    resultSchema: ApiResponses.ConsentRequirement.schema,
  };

  /**
   * Helper method for getting config from server.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const UtilsConfig: ApiCommandItem = {
    name: "utils.config",
    resultSchema: ApiResponses.UtilsConfigResponse.schema,
  };

  /**
   * Notificaion displayed api call
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const NotificationsDisplayed: ApiCommandItem = {
    name: "notifications.displayed",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Notificaion upgrade api call
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const NotificationsUpgrade: ApiCommandItem = {
    name: "notifications.upgraded",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Notificaion clicked api call
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const NotificationsClicked: ApiCommandItem = {
    name: "notifications.clicked",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Helper method for location.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const LocationHelper: ApiCommandItem = {
    name: "utils.locationHelper",
    resultSchema: ApiResponses.LocationHelperResponse.schema,
  };

  /**
   * Get socket token api call
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const SocketToken: ApiCommandItem = {
    name: "comet.socketToken",
    resultSchema: ApiResponses.SocketTokenResponse.schema,
  };

  /**
   * Check if session is ok
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const CheckSession: ApiCommandItem = {
    name: "utils.checkSession",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Get all messages with a specific user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const MessagesGet: ApiCommandItem = {
    name: "messages",
    resultSchema: ApiResponses.MessagesResponse.schema,
  };

  /**
   * Welcome profiel patch
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const WelcomeProfilePatch: ApiCommandItem = {
    name: "welcomeProfile",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Welcome profile patch
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const WelcomeProfileStatusPatch: ApiCommandItem = {
    name: "welcomeProfile.status",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Welcome profile status get
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const WelcomeProfileStatusGet: ApiCommandItem = {
    name: "welcomeProfile.status",
    resultSchema: ApiResponses.WelcomeProfileStatusResponse.schema,
  };

  /**
   * Welcome profile get
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const WelcomeProfileGet: ApiCommandItem = {
    name: "welcomeProfile",
    resultSchema: ApiResponses.WelcomeProfileResponse.schema,
  };

  /**
   * Get attachable image list
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const MessagesGetImages: ApiCommandItem = {
    name: "messages.images",
    resultSchema: ApiResponses.MessagesImageResponse.schema,
  };

  /**
   * Get chat secret for the authenticated user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const MessagesChatSecret: ApiCommandItem = {
    name: "messages.chatSecret",
    resultSchema: ApiResponses.MessagesChatSecretResponse.schema,
  };

  /**
   * Get unread count with a user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const MessagesUnreadCount: ApiCommandItem = {
    name: "messages.unreadCount",
    resultSchema: ApiResponses.MessagesChatSecretResponse.schema,
  };

  /**
   * Get read timestamp with a user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const MessagesGetReadTs: ApiCommandItem = {
    name: "messages.readTs",
    resultSchema: ApiResponses.MessagesGetReadTsResponse.schema,
  };

  /**
   * Save a timestamp when we read a user’s messages.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const MessagesPostReadTs: ApiCommandItem = {
    name: "messages.readTs",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Set a timestamp when we receive a user’s message.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const MessagesReceivedTs: ApiCommandItem = {
    name: "messages.receivedTs",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Can send message to user / thread etc
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const CanSendMessage: ApiCommandItem = {
    name: "messages.canSendMessage",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Get free messages - happy hour
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const FreeMessage: ApiCommandItem = {
    name: "messages.free",
    resultSchema: ApiResponses.MessagesFreeResponse.schema,
  };

  /**
   * Send message to user / thread etc
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const SendMessage: ApiCommandItem = {
    name: "messages",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Payment ios.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const PaymentIos: ApiCommandItem = {
    name: "payment.ios",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Payment log.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const PaymentLog: ApiCommandItem = {
    name: "payment.log",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Payment android.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const PaymentAndroid: ApiCommandItem = {
    name: "payment.android",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Customer support.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ContactAnonymous: ApiCommandItem = {
    name: "contact.anonymous",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Customer support.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const ContactAdd: ApiCommandItem = {
    name: "contact.add",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Ads.
   * @type {{name: string; resultSchema: string}}
   */
  export const Ads: ApiCommandItem = {
    name: "ads",
    resultSchema: ApiResponses.AdsResponse.schema,
  };

  /**
   * Get user's updates from server.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const UsersUpdatesGetUpdates: ApiCommandItem = {
    name: "usersUpdates.browse",
    resultSchema: ApiResponses.UsersUpdatesResponse.schema,
  };

  /**
   * Get new updates for user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const UsersUpdatesGetNewUpdates: ApiCommandItem = {
    name: "usersUpdates.new",
    resultSchema: ApiResponses.UsersUpdatesResponse.schema,
  };

  /**
   * Mark an update as read for the user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const UsersUpdatesMarkAllUpdateRead: ApiCommandItem = {
    name: "usersUpdates.seen",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Get flirt item list data
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const FlirtGetList: ApiCommandItem = {
    name: "flirt.list",
    resultSchema: ApiResponses.FlirtGetListResponse.schema,
  };

  /**
   * Send predefined flirt to user
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const FlirtSend: ApiCommandItem = {
    name: "flirt",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Send wink flirt
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const FlirtWink: ApiCommandItem = {
    name: "flirt.wink",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Unread count
   */
  export const UnreadCount: ApiCommandItem = {
    name: "utils.unread",
    resultSchema: ApiResponses.UnreadCountReponse.schema,
  };

  /**
   * Get inbox threads NEW IMPLEMENTATION
   */
  export const InboxCommandNew: ApiCommandItem = {
    name: "inbox",
    resultSchema: ApiResponses.InboxGetThreadResponse.schema,
  };

  /**
   * Get inbox threads
   */
  export const InboxCommand: ApiCommandItem = {
    name: "inbox",
    resultSchema: ApiResponses.InboxGetThreadResponse.schema,
  };

  /**
   * Get inbox threads mark as read
   */
  export const InboxMarkAsReadCommand: ApiCommandItem = {
    name: "inbox.markAsRead",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Get inbox threads mark as read
   */
  export const InboxMarkAsUnreadCommand: ApiCommandItem = {
    name: "inbox.markAsUnread",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Inbox delete threads
   */
  export const InboxDeleteThreads: ApiCommandItem = {
    name: "inbox",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Move thread to
   */
  export const InboxMoveThreadTo: ApiCommandItem = {
    name: "inbox.moveTo",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Return a list of user ID's that received photo requests by logged in user.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const InboxPhotoRequests: ApiCommandItem = {
    name: "inbox.photoRequests",
    resultSchema: ApiResponses.InboxGetPhotoRequestsResponse.schema,
  };

  /**
   * Check for new messages.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const InboxCheckNewMessagesRequests: ApiCommandItem = {
    name: "inbox.checkNewMessages",
    resultSchema: ApiResponses.InboxGetCheckNewMessagesResponse.schema,
  };

  /**
   * Browse all
   */
  export const BrowseAll: ApiCommandItem = {
    name: "browse.all",
    resultSchema: ApiResponses.ProfileListResponseTotal.schema,
  };

  /**
   * Browse featured
   */
  export const BrowseFeatured: ApiCommandItem = {
    name: "browse.featured",
    resultSchema: ApiResponses.ProfileListResponseTotal.schema,
  };

  /**
   * Browse nearby
   */
  export const BrowseNearby: ApiCommandItem = {
    name: "browse.nearby",
    resultSchema: ApiResponses.ProfileListResponseTotal.schema,
  };

  /**
   * Browse new
   */
  export const BrowseNew: ApiCommandItem = {
    name: "browse.new",
    resultSchema: ApiResponses.ProfileListResponseTotal.schema,
  };

  /**
   * Browse random nearby
   */
  export const BrowseRandomNearby: ApiCommandItem = {
    name: "browse.randomNearby",
    resultSchema: ApiResponses.ProfileListResponseTotal.schema,
  };

  /**
   * Browse recent activity
   */
  export const BrowseRecentActivity: ApiCommandItem = {
    name: "browse.recentActivity",
    resultSchema: ApiResponses.ProfileListResponseTotal.schema,
  };

  /**
   * Get all supported languages from server.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const LanguageAll: ApiCommandItem = {
    name: "language.all",
    resultSchema: ApiResponses.LanguageAllResponse.schema,
  };

  /**
   * Get selected translation from server.
   *
   * @type {{name: string; resultSchema: string}}
   */
  export const LanguageTranslation: ApiCommandItem = {
    name: "language.translation",
    resultSchema: ApiResponses.LanguageTranslationResponse.schema,
  };

  /**
   * Get offers
   *
   */
  export const Offers: ApiCommandItem = {
    name: "offers",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Get offers
   *
   */
  export const CheckPromoData: ApiCommandItem = {
    name: "promo.data",
    resultSchema: ApiResponses.PromoResponse.schema,
  };

  /**
   * Get offers
   *
   */
  export const CheckPromoCode: ApiCommandItem = {
    name: "promo.code",
    resultSchema: ApiResponses.PromoResponse.schema,
  };

  /**
   * Set photo order
   */
  export const PhotosOrderSet: ApiCommandItem = {
    name: "photos.setOrder",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Get comments list.
   */
  export const PhotoCommentGet: ApiCommandItem = {
    name: "photoComment",
    resultSchema: ApiResponses.PhotoCommentGetResponse.schema,
  };

  /**
   * Add new comment.
   */
  export const PhotoCommentPost: ApiCommandItem = {
    name: "photoComment",
    resultSchema: ApiResponses.PhotoCommentPostResponse.schema,
  };

  /**
   * Delete a comment.
   */
  export const PhotoCommentDelete: ApiCommandItem = {
    name: "photoComment",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Like photo.
   */
  export const PhotoLike: ApiCommandItem = {
    name: "photoLike",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Photo set access.
   */
  export const PictureSetAccess: ApiCommandItem = {
    name: "photos.setAccess",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };
  /**
   * Photos get rejected.
   */
  export const PhotosRejected: ApiCommandItem = {
    name: "photos.rejected",
    resultSchema: ApiResponses.RejectedPhotosResponse.schema,
  };

  /**
   * Get faq data.
   */
  export const FaqGet: ApiCommandItem = {
    name: "faq",
    resultSchema: ApiResponses.FaqResponse.schema,
  };

  /**
   * Is introductions available.
   */
  export const IntroductionIsAvailable: ApiCommandItem = {
    name: "introductions.isAvailable",
    resultSchema: ApiResponses.IntroductionIsAvailableResponse.schema,
  };

  /**
   * Add introduction.
   */
  export const IntroductionAdd: ApiCommandItem = {
    name: "introductions.add",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  /**
   * Search basic.
   */
  export const SearchBasic: ApiCommandItem = {
    name: "search.basic",
    resultSchema: ApiResponses.ProfileListResponse.schema,
  };

  /**
   * Get billing history data.
   */
  export const BillingHistoryGet: ApiCommandItem = {
    name: "account.billingHistory",
    resultSchema: ApiResponses.BillingHistoryResponse.schema,
  };

  /**
   * Email Click
   */
  export const EmailClick: ApiCommandItem = {
    name: "analytics.emailClick",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Inbox Message Click
   */
  export const MessageClick: ApiCommandItem = {
    name: "analytics.messageClick",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * Google analytics
   */
  export const GoogleAnalytics: ApiCommandItem = {
    name: "google.analytics",
    resultSchema: ApiResponses.ResponseBase.schema,
  };

  /**
   * AccountDownloadJson
   */
  export const AccountDownloadJson: ApiCommandItem = {
    name: "account.downloadJson",
    resultSchema: ApiResponses.AccountDownloadJson.schema,
  };

  export const AccountGetTags: ApiCommandItem = {
    name: "account.manageTags",
    resultSchema: ApiResponses.AccountTagsResponse.schema,
  };

  export const AccountDisableTag: ApiCommandItem = {
    name: "account.disableTag",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  export const AccountEnableTag: ApiCommandItem = {
    name: "account.enableTag",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  // [get] profileVerified
  export const ProfileVerified: ApiCommandItem = {
    name: "profileVerified",
    resultSchema: ApiResponses.ProfileVerificationResponse.schema,
  };

  // [post] profileVerified.add
  export const ProfileVerifiedAdd: ApiCommandItem = {
    name: "profileVerified.add",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  // [delete] profileVerified.pending
  export const ProfileVerifiedPending: ApiCommandItem = {
    name: "profileVerified.pending",
    resultSchema: ApiResponses.NoDataResponse.schema,
  };

  export const UtilsAttrib: ApiCommandItem = {
    name: "utils.attrib",
    resultSchema: ApiResponses.UtilsAttribResponse.schema,
  };
}
