import { TestBed } from "@angular/core/testing";

import {
  ApiService,
  IApiEvent,
  IApiService_call_args,
  IApiService_filter_args,
} from "./api.service";
import { HttpClient, HttpEventType, HttpHeaders } from "@angular/common/http";
import { Observable, of, Subscriber } from "rxjs";
import * as _ from "lodash";
import { TestScheduler } from "rxjs/testing";
import { pipeFromArray } from "rxjs/internal/util/pipe";
import { map } from "rxjs/operators";

describe("ApiService", () => {
  let service: ApiService;
  let httpSpy: HttpClient;
  let scheduler: TestScheduler;
  let IApiEvent: IApiEvent;

  beforeEach(() => {
    scheduler = new TestScheduler((actual, expected) => {
      expect(actual).toEqual(expected);
    });

    httpSpy = jasmine.createSpyObj("HttpClient", {
      request: new Observable<any>(),
    });

    TestBed.configureTestingModule({
      providers: [{ provide: HttpClient, useValue: httpSpy }],
    });
    service = TestBed.inject(ApiService);

    IApiEvent = {
      callType: "normal",
      event: "complete",
      method: "get",
      _uuid: "123",
      command: "server.status",
      identifier: "a",
      time: 12345,
      data: { a: 1 },
      eventType: HttpEventType.Sent,
    };

    service["apiErrorList"] = [
      { callType: "blocking", command: "test", identifier: "a", method: "get" },
      { callType: "blocking", command: "test", identifier: "b", method: "get" },
    ];
    service["apiEventList"] = [
      { callType: "blocking", command: "test", identifier: "a", method: "get" },
      { callType: "blocking", command: "test", identifier: "b", method: "get" },
    ];
  });

  it("should test observable", () => {
    scheduler.run(({ expectObservable }) => {
      let a = "(abc|)";
      let b = { a: 1, b: 2, c: 3 };
      let c$ = of(1, 2, 3);

      expectObservable(c$).toBe(a, b);
    });
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(ApiService);
  });

  describe("isInErrorList", () => {
    it("should return false if error list is empty", () => {
      let params: IApiService_filter_args = {
        identifier: "a",
      };

      service["apiErrorList"] = [];

      let result = service.isInErrorList(params);

      expect(result).toBeFalse();
    });

    it("should check if a certain item is in error list filtered by identifier and return false", () => {
      let params: IApiService_filter_args = {
        identifier: "c",
      };

      let result = service.isInErrorList(params);

      expect(result).toBeFalse();
    });

    it("should check if a certain item is in error list filtered by identifier and return true", () => {
      let params: IApiService_filter_args = {
        identifier: "a",
      };

      let result = service.isInErrorList(params);

      expect(result).toBeTrue();
    });

    it("should return true if identifier is [ * ]", () => {
      let params: IApiService_filter_args = {
        identifier: "*",
      };

      let result = service.isInErrorList(params);

      expect(result).toBeTrue();
    });
  });

  describe("getFromErrorList", () => {
    it("should return false if error list is empty", () => {
      let params: IApiService_filter_args = {
        identifier: "a",
      };

      service["apiErrorList"] = [];

      let result = service.getFromErrorList(params);

      expect(result).toBeFalse();
    });

    it("should return empty array if item not found by identifier", () => {
      let params: IApiService_filter_args = {
        identifier: "c",
      };

      let result = service.getFromErrorList(params);

      expect(result).toEqual([]);
    });

    it("should return all if identifier is [ * ]", () => {
      let params: IApiService_filter_args = {
        identifier: "*",
      };

      let result = service.getFromErrorList(params);

      expect(result).toEqual(service.errorList);
    });

    it("should return obj[] if item is found by identifier", () => {
      let params: IApiService_filter_args = {
        method: "get",
      };

      let result = service.getFromErrorList(params);

      expect(result).toEqual(service.errorList);
    });
  });

  describe("clearErrorList", () => {
    it("should clear error list and return true", () => {
      expect(service.errorList.length).toBeCloseTo(2);

      let result = service.clearErrorList();

      expect(result).toBeTrue();
      expect(service.errorList.length).toBeCloseTo(0);
    });

    it("should return true if item not found by identifier", () => {
      let params: IApiService_filter_args = {
        identifier: "c",
      };

      expect(service.errorList.length).toBeCloseTo(2);

      let result = service.clearErrorList(params);

      expect(result).toBeTrue();
      expect(service.errorList.length).toBeCloseTo(2);
    });

    it("should remove one item from error list", () => {
      let params: IApiService_filter_args = {
        identifier: "a",
      };

      expect(service.errorList.length).toBeCloseTo(2);

      let result = service.clearErrorList(params);

      expect(result).toBeTrue();
      expect(service.errorList.length).toBeCloseTo(1);
      expect(service.errorList[0]["identifier"]).toBe("b");
    });
  });

  describe("isInEventList", () => {
    it("should check if an api call is in progress and return false", () => {
      let params: IApiService_filter_args = {
        identifier: "c",
      };

      let result = service.isInEventList(params);

      expect(result).toBeFalse();
    });

    it("should check if an api call is in progress and return true", () => {
      let params: IApiService_filter_args = {
        identifier: "a",
      };

      let result = service.isInEventList(params);

      expect(result).toBeTrue();
    });

    it("should return false if event list is empty", () => {
      let params: IApiService_filter_args = {
        identifier: "a",
      };

      service["apiEventList"] = [];

      let result = service.isInEventList(params);

      expect(result).toBeFalse();
    });

    it("should return true if identifier is [ * ]", () => {
      let params: IApiService_filter_args = {
        identifier: "*",
      };

      let result = service.isInEventList(params);

      expect(result).toBeTrue();
    });

    it("should return false if item not found by identifier", () => {
      let params: IApiService_filter_args = {
        identifier: "c",
      };

      let result = service.isInEventList(params);

      expect(result).toBeFalse();
    });

    it("should return true if item found by identifier", () => {
      let params: IApiService_filter_args = {
        identifier: "a",
      };

      let result = service.isInEventList(params);

      expect(result).toBeTrue();
    });

    it("should return true if item found by identifier", () => {
      let params: IApiService_filter_args = {
        identifier: "a",
      };

      let result = service.isInEventList(params);

      expect(result).toBeTrue();
    });
  });

  describe("getFromEventList", () => {
    it("should return false if api event list is empty", () => {
      let params: IApiService_filter_args = {
        identifier: "c",
      };

      service["apiEventList"] = [];

      let result = service.getFromEventList(params);

      expect(result).toBeFalse();
    });

    it("should return false if item not found", () => {
      let params: IApiService_filter_args = {
        identifier: "c",
      };

      let result = service.getFromEventList(params);

      expect(result).toBeFalse();
    });

    it("should return true if identifier is [ * ]", () => {
      let params: IApiService_filter_args = {
        identifier: "*",
      };

      let result = service.getFromEventList(params);

      expect(result).toBeTrue();
    });

    it("should return item from api event list", () => {
      let params: IApiService_filter_args = {
        identifier: "b",
      };

      let result = service.getFromEventList(params);

      expect(result).toBe(service.eventList[1]);
    });
  });

  describe("call", () => {
    it("should not remove errors for the command", () => {
      let spy1 = spyOn(service, "clearErrorList");
      let spy2 = spyOn(_, "omitBy");
      let params: IApiService_call_args = {
        command: "server.status",
        method: "get",
        useToken: false,
        params: {},
        commandVersion: "1",
        callType: "normal",
        reportProgress: false,
        responseType: "json",
        identifier: "",
        doRemoveErrorForCommand: false,
      };

      let result = service.call(params);

      expect(httpSpy.request).toHaveBeenCalled();
      expect(spy1).not.toHaveBeenCalled();
      expect(spy2).toHaveBeenCalledTimes(1);

      expect(result).toBeInstanceOf(Observable);
    });

    it("should remove errors for the command", () => {
      let spy1 = spyOn(service, "clearErrorList");
      let spy2 = spyOn(_, "omitBy");
      let params: IApiService_call_args = {
        command: "server.status",
        method: "get",
        useToken: false,
        params: { a: 1 },
        commandVersion: "2",
        callType: "background",
        reportProgress: false,
        responseType: "arraybuffer",
        identifier: "a",
        doRemoveErrorForCommand: true,
      };

      let result = service.call(params);

      expect(httpSpy.request).toHaveBeenCalled();
      expect(spy1).toHaveBeenCalledTimes(1);
      expect(spy2).toHaveBeenCalledTimes(2);

      expect(result).toBeInstanceOf(Observable);
    });
  });

  describe("buildHeaders", () => {
    it("should not use token", () => {
      let result = service["buildHeaders"](false);

      expect(result).toBeInstanceOf(HttpHeaders);
      expect(result.get("Content-Type")).toBe("application/json");
      expect(result.get("App-Language")).toBe(service["appLanguage"]);
      expect(result.get("Accept-Language")).toBe(service["deviceLanguage"]);
      expect(result.get("App-Version")).toBe(service["appVersion"]);
      expect(result.get("Authorization")).toBeNull();
    });

    it("should not append [Authorization] to headers if apiToken is empty", () => {
      let result = service["buildHeaders"](true);

      expect(result).toBeInstanceOf(HttpHeaders);
      expect(result.get("Content-Type")).toBe("application/json");
      expect(result.get("App-Language")).toBe(service["appLanguage"]);
      expect(result.get("Accept-Language")).toBe(service["deviceLanguage"]);
      expect(result.get("App-Version")).toBe(service["appVersion"]);
      expect(result.get("App-OS")).toBe(service["os"]);
      expect(result.get("Authorization")).toBeNull();
      expect(result.keys().length).toBeCloseTo(5);
    });

    it("should append [Authorization] to headers", () => {
      service["apiToken"] = "123";

      let result = service["buildHeaders"](true);

      expect(result).toBeInstanceOf(HttpHeaders);
      expect(result.get("Content-Type")).toBe("application/json");
      expect(result.get("App-Language")).toBe(service["appLanguage"]);
      expect(result.get("Accept-Language")).toBe(service["deviceLanguage"]);
      expect(result.get("App-Version")).toBe(service["appVersion"]);
      expect(result.get("App-OS")).toBe(service["os"]);
      expect(result.get("Authorization")).toBe("Bearer 123");
      expect(result.keys().length).toBeCloseTo(6);
    });
  });

  describe("buildURL", () => {
    it("should build url", () => {
      let result = service["buildURL"]("server.status", "1");

      expect(result).toBe("https://staging.hellosrv.com/api/v1/server.status");
    });
  });

  describe("buildParams", () => {
    it("should return empty {}", () => {
      let params: { [param: string]: string } | {} = {};

      let result = service["buildParams"](params);

      expect(result.toString()).toEqual("");
    });

    it("should return valid HttpParams", () => {
      let params: { [param: string]: string } | {} = { a: 1 };

      let result = service["buildParams"](params);

      expect(result.toString()).toEqual("a=1");
    });
  });

  describe("eventStream", () => {
    it("should return apiEvent stream as observable", () => {
      let spy1 = spyOn(
        service["apiEventStream$"],
        "asObservable"
      ).and.callThrough();

      let result = service.eventStream$.subscribe();

      expect(result).toBeInstanceOf(Subscriber);
      expect(spy1).toHaveBeenCalledTimes(1);
    });
  });

  describe("initializeApiEventStorage", () => {
    it("should push the data into error list", () => {
      IApiEvent.event = "error";

      let spy1 = spyOn(service["apiEventStream$"], "subscribe").and.callFake(
        (next) => next(IApiEvent)
      );

      expect(service.errorList.length).toBeCloseTo(2);
      expect(service.eventList.length).toBeCloseTo(2);

      let result = service["initializeApiEventStorage"]();

      expect(spy1).toHaveBeenCalled();
      expect(result).toBeUndefined();
      expect(service.errorList.length).toBeCloseTo(3);
      expect(service.eventList.length).toBeCloseTo(2);
      expect(service.errorList).toContain(IApiEvent);
    });

    it("should push the data into event list", () => {
      IApiEvent.event = "init";

      let spy1 = spyOn(service["apiEventStream$"], "subscribe").and.callFake(
        (next) => next(IApiEvent)
      );

      expect(service.errorList.length).toBeCloseTo(2);
      expect(service.eventList.length).toBeCloseTo(2);

      let result = service["initializeApiEventStorage"]();

      expect(spy1).toHaveBeenCalled();
      expect(result).toBeUndefined();
      expect(service.errorList.length).toBeCloseTo(2);
      expect(service.eventList.length).toBeCloseTo(3);
      expect(service.eventList).toContain(IApiEvent);
    });

    it("should remove data from the event list", () => {
      IApiEvent.event = "complete";
      service.eventList.push(IApiEvent);

      let spy1 = spyOn(service["apiEventStream$"], "subscribe").and.callFake(
        (next) => next(IApiEvent)
      );

      expect(service.errorList.length).toBeCloseTo(2);
      expect(service.eventList.length).toBeCloseTo(3);

      let result = service["initializeApiEventStorage"]();

      expect(spy1).toHaveBeenCalled();
      expect(result).toBeUndefined();
      expect(service.errorList.length).toBeCloseTo(2);
      expect(service.eventList.length).toBeCloseTo(2);
      expect(service.eventList).not.toContain(IApiEvent);
    });
  });

  describe("getPipes", () => {
    it("should calc percentage", () => {
      let spy1 = spyOn(service["apiEventStream$"], "next");
      let spy2 = spyOn(_, "clone");

      let result = service["getPipes"](
        "server.status",
        "get",
        "normal",
        "a",
        true,
        true
      );

      let obs = pipeFromArray(result);

      IApiEvent["type"] = 1;
      IApiEvent["loaded"] = 2;
      IApiEvent["total"] = 9;

      let x$ = of(IApiEvent).pipe(
        map((data) => {
          expect(data.data).toEqual({ a: 1 });
          return data;
        })
      );

      obs(x$).subscribe();

      expect(spy1).toHaveBeenCalledTimes(4);
      expect(spy2).toHaveBeenCalledTimes(3);
      expect(spy2.calls.first().args[0]["percentage"]).toBeCloseTo(23);
    });

    it("should set percentage to -1 if !total", () => {
      let spy1 = spyOn(service["apiEventStream$"], "next");
      let spy2 = spyOn(_, "clone");

      IApiEvent["type"] = 1;
      IApiEvent["loaded"] = 0;

      let result = service["getPipes"](
        "server.status",
        "get",
        "normal",
        "a",
        true,
        true
      );

      let obs = pipeFromArray(result);

      let iae$ = of(IApiEvent).pipe(
        map((data) => {
          expect(data.data).toEqual({ a: 1 });
          return data;
        })
      );

      obs(iae$).subscribe();

      expect(spy1).toHaveBeenCalledTimes(4);
      expect(spy2).toHaveBeenCalledTimes(3);
      expect(spy2.calls.first().args[0]["percentage"]).toBeCloseTo(-1);
    });

    it("should not calc percentage", () => {
      let spy1 = spyOn(service["apiEventStream$"], "next");
      let spy2 = spyOn(_, "clone");

      IApiEvent["body"] = { a: 1 };

      let result = service["getPipes"](
        "server.status",
        "get",
        "normal",
        "a",
        true,
        false
      );

      let obs = pipeFromArray(result);

      let iae$ = of(IApiEvent).pipe(
        map((data) => {
          expect(data.data).toEqual({ a: 1 });
          return data;
        })
      );

      obs(iae$).subscribe();

      expect(spy1).toHaveBeenCalledTimes(4);
      expect(spy2).toHaveBeenCalledTimes(3);
      expect(spy2.calls.first().args[0]["percentage"]).toBeUndefined();
      expect(spy2.calls.first().args[0]["event"]).toEqual("finalize");
    });

    it("should throw error", () => {
      let spy1 = spyOn(service["apiEventStream$"], "next");
      let spy2 = spyOn(_, "clone").and.callThrough();

      scheduler.run((helpers) => {
        const marble = "#";
        const expect = { a: IApiEvent };
        const coldObservable = helpers.cold(marble, expect);

        let result = service["getPipes"](
          "server.status",
          "get",
          "normal",
          "a",
          true,
          true
        );
        let obs$ = pipeFromArray(result);

        helpers.expectObservable(obs$(coldObservable)).toBe(marble, expect);
      });

      expect(spy1).toHaveBeenCalledTimes(3);
      expect(spy1.calls.all()[0].args[0].event).toEqual("finalize");
      expect(spy1.calls.all()[1].args[0].event).toEqual("error");
      expect(spy1.calls.all()[2].args[0].event).toEqual("finalize");
      expect(spy2).toHaveBeenCalledTimes(2);
    });
  });
});
