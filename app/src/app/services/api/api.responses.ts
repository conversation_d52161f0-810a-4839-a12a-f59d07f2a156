import * as yup from "yup";
import { ErrorItem } from "../../schemas/error";
import { MessageItem } from "../../schemas/message-item";
import { DebugItem } from "../../schemas/debug";
import { ConsentRequirementType, StaticPageType } from "../../schemas/utils";
import { OfferData, PromoData } from "../../schemas/offerdata";
import { SimpleAuth } from "../../schemas/simple-auth";
import { PreRegister } from "../../schemas/pre-register";
import {
  ProfileList,
  ProfileListTotal,
  ProfileUser,
} from "../../schemas/profile-user";
import { ProfileOwn } from "../../schemas/profile-own";
import { FaqItem } from "../../schemas/faq";
import { Registration } from "../../schemas/registration";
import { ProfileLike } from "../../schemas/profile-like";
import { MatchPref } from "../../schemas/match-pref";
import { RestrictionAll, RestrictionCheck } from "../../schemas/restriction";
import { GalleryImage } from "../../schemas/gallery-image";
import { LocationHelper } from "../../schemas/location-helper";
import { AccountProduct } from "../../schemas/account-product";
import { RejectedPhoto } from "src/app/schemas/rejected-photo";
import { RejectedPhotoResponse } from "src/app/schemas/rejected-photo-response";
import {
  Message,
  MessageChatSecret,
  MessageGetReadTs,
  MessageUnreadCount,
} from "../../schemas/message";
import { AccountSettings } from "../../schemas/account-settings";
import {
  Description,
  DetailsForm,
  Interests,
} from "../../schemas/account-details-form";
import { AccountInterestOptions } from "../../schemas/account-interest-options";
import { Ads } from "../../schemas/ads";
import { UsersUpdates } from "../../schemas/users-updates";
import { FlirtItem } from "../../schemas/flirt";
import { InboxThreadItem } from "../../schemas/inbox-thread";
import { PhotoCommentBase } from "../../schemas/photo-comment";
import { Utils } from "src/app/helpers/utils";
import { ApiParams } from "./api.params";
import { ResolveIpData } from "../../schemas/utils";
import { BillingHistoryItem } from "../../schemas/billing-history";
import { UtilsConfigData } from "../../schemas/utils";

export namespace ApiResponses {
  export const AppPreRegister_UserExists = "USER_EXISTS_N_LOGGED";
  export const AppPreRegister_NewUser = "USER_NOT_EXISTS";

  /**
   * ResponseBase.
   */
  export namespace ResponseBase {
    // Schema.
    export let schema = yup.object({
      errors: yup.array().of(ErrorItem.schema).default([]),
      messages: yup.array().of(MessageItem.schema).default([]),
      data: yup.mixed().default(false),
      timestamp: yup.number().default(0),
      duration: yup.string().default(""),
      status: yup.number().default(0),
      // debug stuff
      debug: yup.array().of(DebugItem.schema).default([]),
      request_stack: yup.array().default([]),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * ConsentRequired.
   */
  export namespace ConsentRequirement {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: ConsentRequirementType.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * OffersResponse.
   */
  export namespace OffersResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: OfferData.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * PromoResponse.
   */
  export namespace PromoResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: PromoData.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * StaticPage.
   */
  export namespace StaticPage {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: StaticPageType.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * NoDataResponse.
   */
  export namespace NoDataResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.boolean().default(false),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * PaymentResponse.
   */
  export namespace PaymentResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        tran_id: yup.string(),
        environment: yup.string(),
        receipt: yup.object({}),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * SimpleAuthResponse.
   */
  export namespace SimpleAuthResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: SimpleAuth.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * AppRegisterResponse.
   */
  export namespace AppRegisterResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: SimpleAuth.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * AppPreRegisterResponse.
   */
  export namespace AppPreRegisterResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: PreRegister.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * AppleAuthResponse.
   */
  export namespace AppleAuthResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: SimpleAuth.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * UsernameUpdateResponse.
   */
  export namespace UsernameUpdateResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        old_username: yup.string(),
        new_username: yup.string(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * ProfileBase response.
   */
  export namespace ProfileBaseResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: ProfileUser.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Utils Config response
   */
  export namespace UtilsConfigResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: UtilsConfigData.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * CCPA get response.
   */
  export namespace CCPAResponseRequired {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        required: yup.boolean(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * CCPA get response.
   */
  export namespace GetCCPAStatusResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        value: yup.boolean(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * CCPAStatus response.
   */
  export namespace CCPAStatusResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        current: yup.boolean(),
        requested: yup.boolean(),
        stored: yup.boolean(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * OwnProfile response.
   */
  export namespace OwnProfileResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: ProfileOwn.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Registration response.
   */
  export namespace RegistrationResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: Registration.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * ProfileList response.
   */
  export namespace ProfileListResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: ProfileList.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * ProfileList response.
   */
  export namespace ProfileListResponseTotal {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: ProfileListTotal.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * ProfileLike response.
   */
  export namespace ProfileLikeResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: ProfileLike.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * MatchPref response.
   */
  export namespace MatchPrefResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: MatchPref.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * RestrictionCheck response.
   */
  export namespace RestrictionCheckResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: RestrictionCheck.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * RestrictionAll response.
   */
  export namespace RestrictionAllResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: RestrictionAll.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * GalleryImage response.
   */
  export namespace GalleryImageResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({ photo: GalleryImage.schema }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * LocationHelper response.
   */
  export namespace SocketTokenResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        channel: yup.string(),
        secret: yup.string(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * getComeTo response.
   */
  export namespace LocationHelperResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: LocationHelper.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Welcome Profile Get Response
   */
  export namespace WelcomeProfileResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.mixed(), // @todo add structure here
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Welcome Profile Status Response
   */
  export namespace WelcomeProfileStatusResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        status: yup
          .number()
          .oneOf(Utils.enumValues(ApiParams.WELCOME_STATUS_FLAG, true)),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Messages post response.
   */
  export namespace PostMessageResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        thread_id: yup.string(),
        msg_id: yup.string(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Messages response.
   */
  export namespace MessagesImageResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        items: yup.array().of(GalleryImage.schema),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace ProductItem {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: AccountProduct.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Messages response.
   */
  export namespace ProductItems {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        items: yup.array().of(AccountProduct.schema),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Messages response.
   */
  export namespace MessagesResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        items: yup.array().of(Message.schema),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * MessagesChatSecret response.
   */
  export namespace MessagesChatSecretResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: MessageChatSecret.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   *  MessagesUnreadCount response.
   */
  export namespace MessagesUnreadCountResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        items: yup.array().of(MessageUnreadCount.schema),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   *  MessagesReadTsResponse response.
   */
  export namespace MessagesGetReadTsResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: MessageGetReadTs.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   *  MessagesReadTsResponse response.
   */
  export namespace MessagesFreeResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        start: yup.number(),
        start_at: yup.number(),
        end: yup.number(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * AccountSettings response.
   */
  export namespace AccountSettingsResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: AccountSettings.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * AccountDetailsFormResponse.
   */
  export namespace AccountDetailsFormResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup
        .array()
        .of(DetailsForm.schema)
        .of(Interests.schema)
        .of(Description.schema),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * AccountInterestOptionsResponse.
   */
  export namespace AccountInterestOptionsResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: AccountInterestOptions.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Ads response
   */
  export namespace AdsResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.array().of(Ads.schema),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Rejected Photos response
   */
  export namespace RejectedPhotosResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: RejectedPhotoResponse.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace RejectedPhoto {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: RejectedPhoto.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * UsersUpdates response.
   */
  export namespace UsersUpdatesResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        items: yup.array().of(UsersUpdates.schema),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace UsersUpdatesResponseNext {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        items: yup.array().of(UsersUpdates.schema),
        next: yup.number().default(0),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * FlirtGetList response.
   */
  export namespace FlirtGetListResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        items: yup.array().of(FlirtItem.schema),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * InboxGetThreads response.
   */
  export namespace InboxGetThreadResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        threads: yup.array().of(InboxThreadItem.schema),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * InboxGetPhotoRequests response.
   */
  export namespace InboxGetPhotoRequestsResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        items: yup.array().of(yup.number()),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * InboxGetCheckNewMessages response.
   */
  export namespace InboxGetCheckNewMessagesResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        new_msg: yup.string(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Language all response.
   */
  export namespace LanguageAllResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        items: yup.array().of(yup.object()),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Language translation response.
   */
  export namespace LanguageTranslationResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        items: yup.object(), // @todo - check this ( items key as object ??? )
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Photo comment [get] response.
   */
  export namespace PhotoCommentGetResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: PhotoCommentBase.schema,
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Unread count
   */
  export namespace UnreadCountReponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        inbox: yup.number(),
        updates: yup.number(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Photo comment [post] response.
   */
  export namespace PhotoCommentPostResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        comment_id: yup.number(),
        datetime: yup.string(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Introduction isAvailable [get] response.
   */
  export namespace IntroductionIsAvailableResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        available: yup.boolean(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Faq response.
   */
  export namespace FaqResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.array().of(FaqItem.schema),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Picture delete response.
   */
  export namespace PictureDeleteResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.array().of(
        yup.object({
          image_id: yup.string(),
          gallery_id: yup.string(),
        }),
      ),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Picture delete response.
   */
  export namespace AccountGeolocationResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.boolean(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * Billing history response.
   */
  export namespace BillingHistoryResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.array().of(BillingHistoryItem.schema),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  /**
   * AccountDownloadJson schema.
   */
  export namespace AccountDownloadJson {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.string(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace AccountTagsResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        tags: yup
          .array()
          .of(yup.object({ title: yup.string(), tag_id: yup.string() })),
        disabled_tags: yup
          .array()
          .of(yup.object({ tag_id: yup.string(), title: yup.string() })),
        main_tag: yup.object({ id: yup.string(), name: yup.string() }),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace ProfileVerificationResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        pv_status: yup.string(),
        pvt_data: yup.object({
          content: yup.string(),
          description: yup.string(),
          images: yup.object({
            image: yup.object({
              0: yup.string(),
              100: yup.string(),
              200: yup.string(),
              400: yup.string(),
            }),
            image2: yup.object({
              0: yup.string(),
              100: yup.string(),
              200: yup.string(),
              400: yup.string(),
            }),
          }),
          task_id: yup.string(),
          title: yup.string(),
          photo_url: yup.string(),
        }),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace ProfileVerificationAddResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object({
        task_id: yup.string(),
        photo_url: yup.string(),
      }),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }

  export namespace UtilsAttribResponse {
    // Schema.
    export let schema = ResponseBase.schema.shape({
      data: yup.object().shape({}).noUnknown(true).default({}).nullable(),
    });

    // Type.
    export type type = yup.InferType<typeof schema>;
  }
}
