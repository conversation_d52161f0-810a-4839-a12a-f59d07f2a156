import {
  HttpClient,
  HttpErrorResponse,
  HttpEventType,
  HttpHeaders,
  HttpParams,
  HttpResponseBase,
} from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment as Env } from "../../../environments/environment";
import * as _ from "lodash";
import {
  identity,
  MonoTypeOperatorFunction,
  Observable,
  Subject,
  UnaryFunction,
} from "rxjs";
import {
  concatMap,
  catchError,
  filter,
  finalize,
  map,
  tap,
  timeout,
} from "rxjs/operators";
import { ErrorModifierService } from "./error-modifier.service";
import { DeviceInfoService } from "../device-info.service";

// const

const DEFAULT_API_VERSION = "1";

// types

export type THttpMethod = "get" | "post" | "put" | "delete" | "patch";
export type TCallType = "normal" | "blocking" | "background" | "ignore_401";
export type TResponseType = "arraybuffer" | "blob" | "json" | "text";
export type TObserve = "body" | "events" | "response";
export type TEventStatus = "next" | "error" | "init" | "complete" | "finalize";

// function interface param definitions

/**
 * call function arg interface
 */
export interface IApiService_call_args {
  command: string;
  method: THttpMethod;
  useToken: boolean;
  commandVersion?: string;
  params?: Object;
  callType?: TCallType | TCallType[];
  reportProgress?: boolean;
  responseType?: TResponseType;
  identifier?: string;
  doRemoveErrorForCommand?: boolean;
}

/**
 * isInProgress args interface
 */
export interface IApiService_filter_args {
  command?: string;
  method?: THttpMethod;
  callType?: TCallType | TCallType[];
  identifier?: string;
}

// interfaces
export interface IHttpOptions {
  headers?: HttpHeaders | { [header: string]: string | string[] };
  observe: TObserve;
  params?: HttpParams | { [param: string]: string | string[] };
  reportProgress?: boolean;
  responseType?: TResponseType;
  withCredentials?: boolean;
  body?: object;
}

/**
 * Interface for api events ( these are emitted when a http call status is change
 * and it has an identifier attached to it )
 */
export interface IApiEvent {
  command: string;
  method: string;
  data: any;
  eventType?: HttpEventType | string;
  event: TEventStatus;
  time: number;
  callType?: TCallType | TCallType[];
  identifier: string;
  _uuid: string;
}

// Api service implementation

@Injectable({
  providedIn: "root",
})
export class ApiService {
  // constructor
  constructor(
    private http: HttpClient,
    private errorModifier: ErrorModifierService,
    private deviceInfo: DeviceInfoService,
  ) {
    // initialize storages
    this.initializeApiEventStorage();

    // init properties
    this.initializeProperties();
  }

  /**
   * Checks if an api call has a certain call type
   * @param callType
   */
  public hasCallType(
    checkFor: TCallType,
    checkOn: TCallType | TCallType[],
  ): boolean {
    // it's an optional param
    if (!checkFor) return false;

    // if it's array, check index
    if (_.isArray(checkOn)) {
      return (checkOn as TCallType[]).indexOf(checkFor) >= 0;
    }

    // if it's string check if it's equal
    return (checkOn as TCallType) === checkFor;
  }

  /**
   * initialize class properties
   */
  private initializeProperties() {
    // set os
    this.appVersion = Env.APP_VERSION;
  }

  /**
   * See if we have a token set
   * @returns
   */
  public hasToken() {
    return !!this.token;
  }

  /**
   * Setter for api token
   */
  set apiToken(token: string) {
    this.token = token;
  }

  // api token
  private token: string = "";

  // set app language
  public setAppLanguage(lang: string) {
    this.appLanguage = lang;
  }

  // set device language
  public setDeviceLanguage(lang: string) {
    this.deviceLanguage = lang;
  }

  // language vals
  private appLanguage: string = "en";
  private deviceLanguage: string = "en";

  private trackingData: any = {};

  public setTrackingData(data: any) {
    // cleanup ( some non ISO-8859-1 characters will - if sent in header - cause exception for ALL api calls )
    // https://github.com/axios/axios/issues/4556

    const cleanKeys = [
      "utm_campaign",
      "utm_content",
      "utm_medium",
      "utm_source",
      "utm_term",
      "aff_id",
      "aff_pg",
      "aff_cp",
      "aff_kw",
      "aff_src",
      "aff_adg",
      "track_id",
    ];

    try {
      const encodedObj = Object.keys(data).reduce((acc, key) => {
        if (cleanKeys.indexOf(key) >= 0) {
          acc[key] = encodeURIComponent(data[key]);
        } else {
          acc[key] = data[key];
        }
        return acc;
      }, {});

      this.trackingData = encodedObj;
    } catch (error) {
      console.error("Error setting tracking data", error);
      this.trackingData = {};
    }
  }

  // app version
  private appVersion: string = "";

  // debug data
  private debugData: any = {};

  // set debug data
  public setDebugData(data: any) {
    this.debugData = data;
  }

  /**
   * get api request headers helper function
   *
   * @param useToken do add token to headers
   */
  private buildHeaders(useToken: boolean): HttpHeaders {
    // header base
    let seed = {
      "Content-Type": "application/json",
      "App-Language": this.appLanguage,
      "Accept-Language": this.deviceLanguage,
      "App-Version": this.appVersion,
      "App-OS": this.deviceInfo.platform,
      "System-ID": Env.API_SYSTEM_ID,
      "App-Device-Info": JSON.stringify(this.deviceInfo.infoFilteredObject),
      "App-Tracking-Data": JSON.stringify(this.trackingData),
      // ,"Cache-Override": "1"
    };

    // add debug data if needed
    if (!_.isEmpty(this.debugData)) {
      seed["App-Debug-Data"] = JSON.stringify(this.debugData);
    }

    // add token if needed for this call
    if (useToken && this.token !== "") {
      seed["Authorization"] = "Bearer " + this.token;
    }

    return new HttpHeaders(seed);
  }

  /**
   * assemble url array
   *
   * @param command command
   * @param version version
   */
  private buildURL(command: string, version: string) {
    return _.replace(
      _.replace(Env.API_SERVER_URL, "{v}", version),
      "{c}",
      command,
    );
  }

  /**
   * Build http options object
   *
   * @param headers
   * @param params
   * @param reportProgress
   * @param responseType
   * @param body
   */
  private buildOptions(
    headers: HttpHeaders,
    params: HttpParams,
    reportProgress: boolean,
    responseType: TResponseType,
    body: object,
  ) {
    let opt: IHttpOptions = {
      headers: headers,
      observe: "events",
      params: params,
      body: body,
      reportProgress: reportProgress,
      responseType: responseType,
      withCredentials: false,
    };

    return opt;
  }

  /**
   * Build proper param object for http request
   * @param params
   */
  private buildParams(params: { [param: string]: string } | {}): HttpParams {
    return new HttpParams(_.isEmpty(params) ? {} : { fromObject: params });
  }

  /**
   * Subject that emmits the changes in http calls
   */
  private apiEventStream$ = new Subject<IApiEvent>();

  /**
   * API event stream subject exposed as observable
   * -- it's not smart esposing the subject, so we
   *    expose it as new observable
   */
  get eventStream$(): Observable<IApiEvent> {
    // return apiEvent stream as observable
    // to prevent access to next() function of
    // subject
    return this.apiEventStream$.asObservable();
  }

  /**
   * Subject that emmits the errors in http calls ( api errors )
   */
  private apiErrorStream$ = new Subject<IApiEvent>();

  /**
   * API error stream subject exposed as observable
   */
  get errorStream$(): Observable<IApiEvent> {
    // return apiEvent stream as observable
    // to prevent access to next() function of
    // subject
    return this.apiErrorStream$.asObservable();
  }

  /**
   * storing the ongoing http calls and statuses
   */
  private apiEventList = [];

  /**
   * storing the error list
   */
  private apiErrorList = [];

  /**
   * Check if a certain item is in error list
   * @param filter
   */
  public isInErrorList(filter: IApiService_filter_args) {
    // no element false
    if (this.apiErrorList.length === 0) {
      return false;
    }

    // is there any error?
    if (filter["identifier"] === "*") {
      return this.apiErrorList.length > 0;
    }

    // check if it meets any of the conditions passed in tha args
    return _.some(this.apiErrorList, filter);
  }

  /**
   * Helper to get error objects ( key, error ) only instead of the whole api response with errors
   * @param result
   * @returns
   */
  private getErrorsOnly(result) {
    let error_list = [];

    result.forEach((element) => {
      if (element?.data?.error?.errors) {
        error_list = error_list.concat(element?.data?.error?.errors);
      }
    });

    return this.errorModifier.modifyErrors(error_list);
  }

  /**
   * Rerurns all errors from list
   */
  public getAllErrors() {
    return this.getFromErrorList({ identifier: "*" }, true);
  }

  /**
   * Get all api errors as an array of strings
   * @returns
   */
  public getAllErrorsTextOnly(): string[] {
    return _.map(this.getFromErrorList({ identifier: "*" }, true), "error");
  }

  /**
   * Get all api errors keys as an array of strings
   * @returns
   */
  public getAllErrorsKeysOnly(): string[] {
    return _.map(this.getFromErrorList({ identifier: "*" }, true), "key");
  }

  /**
   * Get from error list by filter ( with or without the whole response object )
   * @param filter
   * @param doReturnErrorsOnly
   */
  public getFromErrorList(
    filter: IApiService_filter_args,
    doReturnErrorsOnly = false,
  ) {
    // no element []
    if (this.apiErrorList.length === 0) {
      return [];
    }

    // return all if identifier is * otherwise apply filter
    let res =
      filter.identifier === "*"
        ? this.apiErrorList
        : _.filter(this.apiErrorList, filter);
    return doReturnErrorsOnly ? this.getErrorsOnly(res) : res;
  }

  /**
   * Getter for error list
   */
  get errorList() {
    return this.apiErrorList;
  }

  /**
   * Getter for event list
   */
  get eventList() {
    return this.apiEventList;
  }

  /**
   * Clear the error list
   */
  public clearErrorList(
    filter: IApiService_filter_args | null = null,
  ): boolean {
    // clear all if filter is not given
    if (!filter) {
      this.apiErrorList = [];
      return true;
    }

    // remove only filtered if given
    return !!_.remove(this.apiErrorList, filter);
  }

  /**
   * Implementation of checking ongoing api calls in the container
   *
   * @param filter IApiService_filter_args object, if there's an api call with
   *               the data passed, it will return the first one that fits example
   *               { identifier: 'userProfile_12', method: 'get' }
   *               { command: 'server.status' }
   *               etc ...
   * @param doGet get whole result or just tell if it's in progress
   */
  private _isInProgress(
    filter: IApiService_filter_args,
    doGet: boolean = false,
  ) {
    // no element false
    if (this.apiEventList.length === 0) {
      return doGet ? [] : false;
    }

    // if identifier is "*" will return true for any data in the collection
    if (filter["identifier"] === "*") {
      return doGet ? this.apiEventList : this.apiEventList.length > 0;
    }

    // check if it meets any of the conditions passed in tha args
    let res = _.find(this.apiEventList, filter);
    return doGet ? res ?? false : !!res;
  }

  /**
   * Can tell if an api call is in progress
   *
   * @param filter IApiService_filter_args object, if there's an api call with
   *               the data passed, it will return the first one that fits example
   *               { identifier: 'userProfile_12', method: 'get' }
   *               { command: 'server.status' }
   *               etc ...
   */
  public isInEventList(filter: IApiService_filter_args) {
    return this._isInProgress(filter, false);
  }

  /**
   * Returns element from event list with all details
   *
   * @param filter IApiService_filter_args object, if there's an api call with
   *               the data passed, it will return the first one that fits example
   *               { identifier: 'userProfile_12', method: 'get' }
   *               { command: 'server.status' }
   *               etc ...
   */
  public getFromEventList(filter: IApiService_filter_args) {
    return this._isInProgress(filter, true);
  }

  /**
   * Helper function to tell if the event was done
   * complete, error and filanize are done, others are not
   * @param event
   */
  public isEventDone(event: TEventStatus) {
    return ["complete", "error", "finalize"].indexOf(event) >= 0;
  }

  /**
   * Subscribe to apiEventStream and maintain the api call statuses
   */
  private initializeApiEventStorage() {
    this.apiEventStream$.subscribe((data) => {
      // if we have no identifier, we do not keep track
      // if(data.identifier === "") return;

      // if there was an error, push the data into error list
      if (data.event == "error") {
        // push to error stream
        this.apiErrorStream$.next(data);

        // push to error list
        this.apiErrorList.push(data);
      }

      // if it's completed, completed or error, then we remove it
      if (this.isEventDone(data.event)) {
        this.apiEventList = _.filter(this.apiEventList, (v) => {
          return v["_uuid"] !== data["_uuid"];
        });
      } else {
        // everything else we'll add it
        this.apiEventList.push(data);
      }
    });
  }

  /**
   * Generate event tapper OperatorFunctions for inserting events to subject
   * this will maintain the api event list listening to everything happening
   * in the api class
   *
   * @param command
   * @param method
   * @param callType
   * @param identifier
   * @param reportProgress
   * @param doGetFullResponse
   */
  private getPipes(
    command: string,
    method: string,
    callType: TCallType | TCallType[],
    identifier: string,
    reportProgress: boolean,
    doGetFullResponse: boolean,
  ): Array<MonoTypeOperatorFunction<any>> {
    // start request emmit subject
    let eventData: IApiEvent = {
      command: command,
      method: method,
      data: {},
      event: "init",
      callType: callType,
      identifier: identifier,
      time: _.now(),
      _uuid: _.uniqueId(),
    };

    this.apiEventStream$.next(eventData);

    return [
      // tap and expose stream for the Subject

      tap({
        next: (data: object) => {
          eventData.event = "next";
          eventData.data = data;
          eventData.time = _.now();

          // set event type
          eventData.eventType = HttpEventType[data["type"]];

          // calculate percentage
          if (
            reportProgress &&
            [
              HttpEventType.DownloadProgress,
              HttpEventType.UploadProgress,
            ].indexOf(data["type"]) >= 0
          ) {
            data["percentage"] = -1;
            // add percentage if total exists, if not ( sometimes we don't know ) set it to -1, so we konw that it's in progress but
            // we cannot calculate ...

            if (data["total"]) {
              data["percentage"] = Math.min(
                Math.ceil(
                  (parseFloat(data["loaded"]) / parseFloat(data["total"])) *
                    100,
                ),
                100,
              );
            }

            eventData["percentage"] = data["percentage"];
          }

          this.apiEventStream$.next(_.clone(eventData));
        },
        error: (error) => {
          eventData.event = "error";
          eventData.data = error;
          eventData.time = _.now();
          this.apiEventStream$.next(_.clone(eventData));
        },
        complete: () => {
          eventData.event = "complete";
          eventData.data = null;
          eventData.time = _.now();
          this.apiEventStream$.next(_.clone(eventData));
        },
      }),

      // map body to data ( return only object )

      map((data) => (doGetFullResponse ? data : data?.body)),

      // filter out everything that's not a response, and return only the httpresponse object

      filter((data) => !!data["body"]),

      // make sure that finalized events are processed ( cancelled, etc )

      finalize(() => {
        eventData.event = "finalize";
        // eventData.data = null;
        eventData.time = _.now();
        this.apiEventStream$.next(_.clone(eventData));
      }),

      // add http timeout
      timeout(Env.API_REQUEST_TIMEOUT_MILLIS),

      catchError((error) => {
        if (error["status"] == 0) {
          this._wasNetworkError = true;
        }

        this._httpErrorStream$.next(error);

        throw error;
      }),
    ];
  }

  /**
   * Execute api call - returns body
   *
   * @param param0 ApiService_call_params
   */
  public call<ResponseT>({
    command,
    method,
    useToken,
    params = {},
    commandVersion = DEFAULT_API_VERSION,
    callType = "normal",
    reportProgress = false,
    responseType = "json",
    identifier = "",
    doRemoveErrorForCommand = true,
  }: IApiService_call_args): Observable<ResponseT> {
    return <Observable<ResponseT>>this.callRaw({
      command: command,
      method: method,
      useToken: useToken,
      params: params,
      commandVersion: commandVersion,
      callType: callType,
      reportProgress: reportProgress,
      responseType: responseType,
      identifier: identifier,
      doRemoveErrorForCommand: doRemoveErrorForCommand,
    }).pipe(map((data) => data["body"]));
  }

  /**
   * Execute raw api call - get Http response object
   *
   * @param param0 ApiService_call_params
   */
  public callRaw({
    command,
    method,
    useToken,
    params = {},
    commandVersion = DEFAULT_API_VERSION,
    callType = "normal",
    reportProgress = false,
    responseType = "json",
    identifier = "",
    doRemoveErrorForCommand = true,
  }: IApiService_call_args): Observable<HttpResponseBase> {
    // reset network error flag
    this._wasNetworkError = false;

    // do remove errors for the command?
    if (doRemoveErrorForCommand) {
      let _filter = <IApiService_filter_args>{
        command: command,
        method: method,
        identifier: identifier,
        callType: callType,
      };

      // remove empty
      _filter = _.omitBy(_filter, _.isEmpty);

      // clear error list
      this.clearErrorList(_filter);
    }

    this._wasNetworkError = false;

    // remove empty keys from params
    params = _.omitBy(params, _.isNil);

    let doWaitForSession = useToken && !this.hasToken();

    // generate observable
    return <Observable<HttpResponseBase>>this.waitForTokenObserver(
      doWaitForSession,
    ).pipe(
      concatMap(() => {
        // build request before we execute it

        let contentHeader = this.buildHeaders(useToken);
        let url = this.buildURL(command, commandVersion);
        let httpParams = this.buildParams(params);

        // set http params only if we send get
        if (method != "get") {
          httpParams = null;
        }

        let options = this.buildOptions(
          contentHeader,
          httpParams,
          reportProgress,
          responseType,
          params,
        );

        // adding tapper for event list maintenance
        let httpPipe = this.getPipes(
          command,
          method,
          callType,
          identifier,
          reportProgress,
          true,
        );

        // make api call
        return this.http
          .request(method, url, options)
          .pipe(this.pipeFromArray(httpPipe));
      }),
    );
  }

  /**
   * Observer that resolves only if we have a token set
   * @returns
   */
  private waitForTokenObserver(doWaitForSession): Observable<any> {
    let repeatNum = 0;
    return new Observable<any>((subscriber) => {
      // we do not need to do anything, we have token or we don't need token
      if (!doWaitForSession) {
        subscriber.next({});
        subscriber.complete();
        return;
      }

      // check for token until we have it
      let _i = setInterval(() => {
        repeatNum++;

        // repeat max 20 times, to avoid problems, then fail
        if (this.hasToken() || repeatNum > 20) {
          clearInterval(_i);
          subscriber.next({});
          subscriber.complete();
        }
      }, 500);
    });
  }

  /**
   * Subject to emit http errors
   */
  private _httpErrorStream$ = new Subject<HttpErrorResponse>();

  /**
   * Emits non standard http errors ( the api specific stuff is in apiErrorList)
   */
  get httpErrorStream$(): Observable<HttpErrorResponse> {
    // return apiEvent stream as observable
    // to prevent access to next() function of
    // subject
    return this._httpErrorStream$.asObservable();
  }

  // container for was network error flag
  private _wasNetworkError: boolean = false;

  /**
   * Flag if there was an network error in the last call
   * @returns
   */
  get wasNetworkError() {
    return this._wasNetworkError;
  }

  // extracted pipeFromArray function from rxjs previous version ( 6.5.5 ) because it's
  // removed in new version

  public pipeFromArray<T, R>(
    fns: Array<UnaryFunction<T, R>>,
  ): UnaryFunction<T, R> {
    if (fns.length === 0) {
      return identity as UnaryFunction<any, any>;
    }

    if (fns.length === 1) {
      return fns[0];
    }

    return function piped(input: T): R {
      return fns.reduce(
        (prev: any, fn: UnaryFunction<T, R>) => fn(prev),
        input as any,
      );
    };
  }
}
