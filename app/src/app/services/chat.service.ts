import { Injectable, <PERSON><PERSON>one } from "@angular/core";
import { Observable, BehaviorSubject } from "rxjs";
import { ApiService } from "./api/api.service";
import { ApiCommands } from "src/app/services/api/api.commands";
import { filter, map } from "rxjs/operators";
import { ProfileBase } from "src/app/schemas/profile-base";
import { ProfileService } from "./profile.service";
import _ from "lodash";
import { Debug } from "../helpers/debug";
import { PaginatorHelperService } from "./paginator-helper.service";
import { ChatContactItem } from "./chat-contact-item.interface";
import { DialogHelperService } from "./dialog-helper.service";
import { ChatIncomingService } from "./chat-incoming.service";
import { UtilsService } from "./utils.service";
import { ChatMessage } from "./chat-message.interface";
import { ChatMessageService } from "./chat-message.service";
import { Router } from "@angular/router";
import { environment as Env } from "src/environments/environment";

/*
attachment: "0"
avatar: "1"
avatar_url: "https://dev-files.dvipcdn.com/data/dating/m1/photos4/61/1/4154611_1.jpg"
checked: ""
delivery_status: "0"
filter_status: "0"
gender_id: "2"
im_link: ""
is_gift: false
is_imlive: false
is_intro: false
last_message: "Wink! Wink! Lets chat!"
last_msg_uid: "17"
msg_count: 0
replied: "0"
thread_id: "1332337"
thread_status: "-2"
thread_subject: "HEY!"
thread_ts: "1632753406"
user_id2: "4154611"
username: "BoldizsarBednarik"
verified: false
*/

@Injectable({
  providedIn: "root",
})
export class ChatService {
  // tmp - for debug
  public debugOffers: boolean = false;
  public offers: any[] = [];

  constructor(
    private api: ApiService,
    private profileService: ProfileService,
    private dbg: Debug,
    private paginatorHelper: PaginatorHelperService,
    private dialog: DialogHelperService,
    private utils: UtilsService,
    private chatIncoming: ChatIncomingService,
    private chatMessageSrv: ChatMessageService,
    private ngZone: NgZone,
    private router: Router,
  ) {
    // get match appension from config
    this.DO_APPEND_MATCHES = Env?.DO_APPEND_MATCHES_TO_INBOX || false;
  }

  private DO_APPEND_MATCHES: boolean = false;

  private matchList: ProfileBase.type[] = [];

  public sortContactList() {
    // sort list
    // - unread first
    // - then by thread_ts

    // force re-render list
    this.chatContactItems = _.orderBy(this.chatContactItems, ["ts"], "desc");

    this.emmitList();
  }

  /**
   * Get contact item from contact list by username
   * @param username
   * @returns
   */
  public getChatContactItem(username: string) {
    return (
      _.find(this.chatContactItems, {
        username: username,
      }) || null
    );
  }

  /**
   * Initialize chat service
   */
  public init() {
    this.getThreadList().subscribe({
      next: (data) => {
        this.chatContactItems = this.getValidData(data);
        this.sortContactList();

        this._roster$.next(this.chatContactItems);

        if (this.DO_APPEND_MATCHES) {
          this.profileService.listMatches.list$.subscribe({
            next: (data) => {
              this.matchList = this.chatContactItems;
              this.processList();
            },
          });
        }
      },
      error: (e) => {},
    });

    this.handleIncommingCometMessages();

    // add listener to send read status api call,
    // and update the list for that user as read

    this.chatMessageSrv.markReadEmiter$.subscribe({
      next: (user_id) => {
        this.chatContactItems.forEach((e, index) => {
          if (e.user_id === user_id) {
            this.chatContactItems[index].is_read = true;
          }
        });

        this.sortContactList();
      },
    });
  }

  /**
   * Filter data.
   */
  private getValidData(data: ChatContactItem[]): ChatContactItem[] {
    // Remove data without username.
    return data.filter((item) => item.username);
  }

  private handleIncommingCometMessages() {
    this.chatIncoming.messageStream$
      .pipe(
        filter((data) => {
          return (
            !this.utils.isChatWithUserActive(data?.cometData?.username) &&
            !data?.isOwn
          );
        }),
      )
      .subscribe((data: ChatMessage) => {
        // display notification if not in chat with the user

        this.dialog.clickableToast(
          data?.cometData?.first_name ?? data?.cometData?.username,
          data?.text,
          data?.cometData?.user_avatar,
          "💬",
          () => {
            this.router.navigate([
              "/app/chat-messages/" + data?.cometData?.username,
            ]);
          },
        );

        // play sound

        this.playSound();

        // mark as received

        this.chatMessageSrv.setReceivedTs(data?.userId);

        // @todo update contact list and set as unread, set last message text and other stuff if needed

        /*

        {
    "id": "1332533",
    "ts": "1642001096",
    "subject": "",
    "msg_count": 0,
    "username": "asdf11",
    "user_id": "4154223",
    "avatar_url": "https://dev-files.dvipcdn.com/data/dating/m1/photos4/22/3/4154223_1.jpg",
    "avatar_blurhash": null,
    "thread_status": "1",
    "last_message": "dsgasdgsda",
    "is_archived": false,
    "is_read": false,
    "type_name": "message",
    "type_index": 1,
    "appended_data": {
        "first_name": "asdf11",
        "age": "34",
        "distance": 0
    },
    "hasThread": true,
    "userObject": null
}

        */
        this.chatContactItems.forEach((e, index) => {
          if (e.username === data?.cometData?.username) {
            this.chatContactItems[index].is_read = false;
            this.chatContactItems[index].last_message = data?.text;
            this.chatContactItems[index].ts =
              data?.time ?? this.chatContactItems[index].ts;

            // add item to the top
            /*
            let temp = _.cloneDeep(this.chatContactItems[index]);
            this.chatContactItems.splice(index, 1);
            this.chatContactItems.unshift(temp);
            */
          }
        });

        // sort list after marking as unread

        this.sortContactList();

        // @todo update unread count
        // maybe fetch from server and update all?
      });
  }

  /**
   * Emmit the current list ( refresh without api call, for gui)
   */
  public emmitList() {
    this._roster$.next(this.chatContactItems);
  }

  /**
   * Play sound.
   */
  private playSound(): void {
    let audio = new Audio("assets/snd/notification.mp3");

    audio.play().catch((err) => {
      this.dbg.l("audio play error", err);
    });
  }

  public refreshRoster(event = null) {
    this.matchList = [];

    this.chatContactItems = [];
    this._roster$.next([]);

    this.getThreadList().subscribe({
      next: (data) => {
        event?.detail?.complete();

        this.chatContactItems = data;
        this.sortContactList();

        this._roster$.next(this.chatContactItems);

        if (this.DO_APPEND_MATCHES) {
          this.paginatorHelper.matchPaginator.refresh();
        }
      },
      error: (e) => {
        event?.detail?.complete();
      },
    });
  }

  private processList() {
    let list = this.chatContactItems;

    // add items to the chat contanct list which are not in the thread list
    this.matchList.forEach((e) => {
      // do we have that user as thread?
      if (_.findIndex(this.chatContactItems, { username: e.username }) < 0) {
        // if not, add it as contact
        let _item: ChatContactItem = {
          user_id: e.user_id,
          username: e.username,
          last_message: "",
          id: e.user_id,
          ts: 0,
          hasThread: false,
          userObject: e,
          avatar_url: e.avatar_url,
          is_read: true,
          appended_data: {
            age: e.age,
            first_name: e.first_name,
            distance: "99999",
          },
        };

        _item["_is_match"] = true;

        list.push(_item);
      }

      // this.chatContactItems = list;
    });

    this._roster$.next(list);
  }

  public getThreadList() {
    return this.api
      .call<any>({
        command: ApiCommands.InboxCommandNew.name,
        method: "get",
        useToken: true,
        params: { limit: 100000, merge_by_type: 1 }, // fetch all
        commandVersion: "3",
      })
      .pipe(
        map((data) => {
          // add missing stuff

          // Set offers data.
          if (!this.debugOffers) {
            // @todo - Just for testing purposes.
            this.offers = data.data?.offers;
          }

          //this.offers.reverse();
          this.offers = _.orderBy(this.offers, ["ts"], "asc");
          if (!_.isEmpty(data.data.threads)) {
            data.data.threads.map((e) => {
              e["hasThread"] = true;
              e["userObject"] = null;
              return e;
            });
          }

          // return only the data
          return data.data.threads;
        }),
      );
  }

  public isOffersEmpty() {
    return !this.isError() && !this.isLoading() && _.isEmpty(this.offers);
  }

  /*

  some plan:

  - need a function that fetches inbox.new results, that contains the newest threads with
    each user.
  - when we fetch the threads, we need to merge that with our match list, so we have all
    the potential users in one list, ordered by how long have we communicated with them
  - so we assemble the list, merge it, and trigger the _roster$ observable so the front can refresh
  - we need to add new users on the fly to the list ( listen to match list observable ? )

  {

    - we might need to merge the liked users as well, so user can try to start conversation with them
      and we can offer premium

    ( we don't need this, complicates things - user can send message from liked list )

  }

  - we need to fetch unread messages as well and add to the list
  - also we need to listen to the comet, and if we get new message act accordingly
  - when user clicks on 'thread' which for us will be a roster item, we open messages - or
    in case it's a new conversation, we check if the user can send message, if not then we'll
    show premium upgrade dialog
  */

  private chatContactItems: ChatContactItem[] = [];

  // observable for roster
  private _roster$ = new BehaviorSubject<ChatContactItem[]>([]);

  /**
   * Get unread count for current chat contact items in roster
   */
  public unreadCount(): number {
    let unreadCnt: number = 0;

    this.chatContactItems.forEach((i) => {
      if (!i.is_read) {
        unreadCnt++;
      }
    });

    return unreadCnt;
  }

  get roster$(): Observable<ChatContactItem[]> {
    return this._roster$.asObservable();
  }

  public isLoading() {
    return (
      this.api.isInEventList({
        command: ApiCommands.InboxCommandNew.name,
      }) ||
      this.api.isInErrorList({
        command: ApiCommands.ProfileMatches.name,
      })
    );
  }

  public isError() {
    return this.api.isInErrorList({
      command: ApiCommands.InboxCommandNew.name,
    });
  }

  public isRosterEmpty() {
    return (
      !this.isError() &&
      !this.isLoading() &&
      (_.isEmpty(this.matchList) || !this.DO_APPEND_MATCHES) &&
      _.isEmpty(this.chatContactItems)
    );
  }

  public didReachEnd() {
    if (this.DO_APPEND_MATCHES) {
      return this.paginatorHelper.matchPaginator.didReachEnd();
    }

    return true;
  }

  public next(e) {
    if (this.DO_APPEND_MATCHES) {
      this.paginatorHelper.matchPaginator.next(e);
    }

    return false;
  }

  // observable for messages with user
}
