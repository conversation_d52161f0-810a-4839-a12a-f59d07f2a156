import { Injectable } from "@angular/core";
import { BehaviorSubject, Observable, firstValueFrom } from "rxjs";
import { AccountService } from "./account.service";
import { DeeplinkService } from "./deeplink.service";
import { DialogHelperService } from "./dialog-helper.service";
import { ProfileSuggestionsService } from "./profile-suggestions.service";
import { ProfileService } from "./profile.service";
import { ChatService } from "./chat.service";
import { PaginatorHelperService } from "./paginator-helper.service";
import { AdsService } from "./ads.service";
import { StatusCheckerService } from "../status-checker.service";
import { AclService } from "./acl.service";
import { UnreadCountService } from "./unread-count.service";
import { HappyHourService } from "./happy-hour.service";
import { GeolocationService } from "./geolocation.service";
import { ChatMessageService } from "./chat-message.service";
import { PushNotificationService } from "./push-notification.service";
import { PurchaseService } from "./purchase.service";
import { AuthService } from "./auth.service";
import { Debug } from "../helpers/debug";
import { StorageService } from "./storage.service";
import { PreviousRouteService } from "./previous-route.service";
import { RefresherService } from "./refresher.service";
import { AnalyticsService } from "./analytics.service";
import { Utils } from "../helpers/utils";

export interface IBootAction {
  boot(): Promise<any>;
}

export interface IBootStatus {
  isInitialBoodDone: boolean;
  isBackgroundBootDone: boolean;
  isRecoveryEvent: boolean;
}

@Injectable({
  providedIn: "root",
})
export class BootService {
  constructor(
    private accountService: AccountService,
    private deeplinkService: DeeplinkService,
    private pushNotificationService: PushNotificationService,
    private dialog: DialogHelperService,
    private profileSuggestionsService: ProfileSuggestionsService,
    private profileService: ProfileService,
    private chatService: ChatService,
    private paginatorHelper: PaginatorHelperService,
    private ads: AdsService,
    private statusCheckerService: StatusCheckerService,
    private acl: AclService,
    private unreadCount: UnreadCountService,
    private happyHour: HappyHourService,
    private geolocation: GeolocationService,
    private chatMessageService: ChatMessageService,
    private purchaseService: PurchaseService,
    private authService: AuthService,
    private debug: Debug,
    private storageService: StorageService,
    private previousRouteService: PreviousRouteService,
    private refresherService: RefresherService,
    private analytics: AnalyticsService,
  ) {}

  private _bootStatus$: BehaviorSubject<IBootStatus> =
    new BehaviorSubject<IBootStatus>({
      isInitialBoodDone: false,
      isBackgroundBootDone: false,
      isRecoveryEvent: false,
    });

  /**
   * Observable to follow boot status
   */
  get bootStatus$(): Observable<IBootStatus> {
    return this._bootStatus$.asObservable();
  }

  /**
   * Get the last value of the boot status
   */
  get bootStatus(): IBootStatus {
    return this._bootStatus$.value;
  }

  /**
   * Initialize mandatory data
   * @returns
   */
  public async startInitialBoot(wasRecovery: boolean = false): Promise<any> {
    this.dialog.showSplash();

    this.analytics.addMarker("init boot");

    // emmit status
    this._bootStatus$.next({
      isInitialBoodDone: false,
      isBackgroundBootDone: false,
      isRecoveryEvent: wasRecovery,
    });

    try {
      // mandatory
      await this.accountService.boot();
      // @todo
      // - get settings data
    } catch (error) {
      // we have error in mandatory stuff
      // reset session
      return Promise.reject();
    }

    // emmit status
    this._bootStatus$.next({
      isInitialBoodDone: true,
      isBackgroundBootDone: false,
      isRecoveryEvent: wasRecovery,
    });

    this.analytics.addMarker("init boot end");

    // call background initialization asyncronously
    await this.startBackgroundBoot();

    // all ok
    return Promise.resolve();
  }

  /**
   * Initialize stuff that can be done in the background
   */
  private async startBackgroundBoot(wasRecovery: boolean = false) {
    this.analytics.addMarker("bg boot");

    // purchase
    await this.purchaseService.boot();

    // wait until prducts are read ( or 5 sec ... max )

    // we need to wait for products to be ready, becuase under some conditions, for some reason
    // on ios products are read in the background and async is finished before the products are
    // actually ready ( ticket ) T20356 this helps and waits until they're available avoiding
    // promotions not being shown to some users

    try {
      await Utils.waitForCondition(
        () => {
          return this.purchaseService.areProductsReady();
        },
        250,
        5000,
      );
    } catch (e) {
      this.debug.le("purchase products not ready");
      this.analytics.addMarker("condition_timeout_error");
    }

    this.previousRouteService.init();

    if (this.authService.wasRegistration) {
      // geolocation
      await this.geolocation.boot();
    }

    // get ads
    this.ads.boot();

    // - get suggestion list
    this.profileSuggestionsService.boot();

    // - get happy hour
    this.happyHour.boot();

    // init chat message - get photo requests list
    this.chatMessageService.init();

    // - initialize inbox, check for new inbox messages, subscribe to comet
    // - get interests
    // - if we have access try to fetch user location
    //   ( if we don't have that, then leave it for later to ask for it maybe )
    // - get access control library data ( remaining credits, etc )
    this.acl.boot();
    // - get CCPA status for the user

    // Register services that should handle things if the acc premium status changes.
    this.refresherService.add(this.accountService).add(this.acl);

    // initialize push notifications here
    // @todo test if it works after session restore, login etc, if we have bigger delay
    // @note we disabled this, and we're doing it on browse page ( start page ) - since we need to ask for ios
    this.debug.l("boot_start push notification init");
    this.pushNotificationService.conditionalInitialization();
    this.debug.l("boot_end push notification init");

    // initialize deeplink service, hopefully it will react after bootstrapping
    // @todo test what if we have session restore and stuff like that
    this.debug.l("boot_start deeplink init");
    this.deeplinkService.init(true);
    this.debug.l("boot_end deeplink init");

    // get lists, init chat
    // @todo detect idle in api calls, and initialize it
    // or add a timeout delay to optimize bandwidth

    this.paginatorHelper.init();

    // init chat
    this.chatService.init();

    // initialize status checker
    this.statusCheckerService.init();

    // emmit status
    this._bootStatus$.next({
      isInitialBoodDone: true,
      isBackgroundBootDone: true,
      isRecoveryEvent: wasRecovery,
    });

    // fetch unread count
    this.unreadCount.get();

    this.profileService.listBlocked.setCommandVersion("2").get().subscribe();

    this.profileService.setViewedMeBadge(this.accountService.account.user_id);

    if (!this.authService.wasRegistration) {
      // Store the key with the user_id so that we can retrieve it and see if we have already opened the AppDownloaded page.
      let key = "is_web_registration_" + this.accountService.account.user_id;
      // Get data from local storage.
      let isWebRegistration = await this.storageService.get(key);

      let deeplink: string = "";

      this.deeplinkService.wasDeeplink$.subscribe({
        next: (res) => {
          deeplink = res.status;
        },
      });

      // // Only web registered acc.
      if (
        this.accountService.isWebRegistration() &&
        !isWebRegistration &&
        !deeplink
      ) {
        // Set new key if do not exists.
        this.storageService.set(key, true);
        // Open go premium downloaded page.
        this.purchaseService.openGoPremiumDownloaded((error) => {
          this.dialog.showToastBaseOnErrorResponse(error, null, false);
        });
      }

      // uncomment to test instant open

      // this.purchaseService.openGoPremiumDownloaded((error1) => {
      //   this.dialog.showToastBaseOnErrorResponse(error1, null, false);
      // });
    }
  }
}
