import { Injectable } from "@angular/core";
import { environment as Env } from "src/environments/environment";
import { Debug } from "../helpers/debug";
import { ApiCommands } from "./api/api.commands";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { ChatIncomingService } from "./chat-incoming.service";
import { ChatMessage } from "./chat-message.interface";
import { ChatMessageService } from "./chat-message.service";
import { DialogHelperService } from "./dialog-helper.service";
import { MiscService } from "./misc.service";
import _ from "lodash";
import * as Faye from "faye";
import { AnalyticsService } from "./analytics.service";
import { firstValueFrom } from "rxjs";
import { RefresherService } from "./refresher.service";
import { ProfileService } from "./profile.service";

export class AuthTokenItem {
  public channel: string = "";
  public token: string = "";

  public isEmpty() {
    return this.token.length == 0;
  }
}

@Injectable({
  providedIn: "root",
})
export class CometService {
  /**
   * Constructor, initialization
   * @param dbg
   */
  public constructor(
    private dbg: Debug,
    private api: ApiService,
    private chatIncoming: ChatIncomingService,
    private chatMessages: ChatMessageService,
    private dialog: DialogHelperService,
    private misc: MiscService,
    private analytics: AnalyticsService,
    private refresherService: RefresherService,
    private profileService: ProfileService,
  ) {
    this.client = new Faye.Client(Env.SOCKET_URI);
    this.addExtensions();
  }

  private client;

  public getCometSocketToken() {}

  private handleIncomingUpdate(data) {
    this.dbg.l("Coment Handler Incoming", data);

    // @todo: refactor after Norbert finishes T18153

    // see what did we get, and emmit event
    // !!!!! THIS IS NOT THE FINAL STUFF !!!!!

    /**
     * what to do:
     *
     * - when we get the info, we fetch stuff from server
     * -- unread count for the user
     * -- full formatted message and attachments
     * -- if we need anything else
     *
     * then we fire the event
     */

    //if (undefined !== data["data"]["_callback"][0]["wld.Messages.checkNew"]) {
    if (
      undefined !== data?.data?.notification_type &&
      (data?.data?.notification_type == "reply_message" ||
        data?.data?.notification_type == "new_message")
    ) {
      let _params = data.data;

      firstValueFrom(
        this.chatMessages.getMessages({
          get_attachments: 1,
          user_id: _params.user_id,
          msg_id: _params.msg_id,
          thread_id: _params.thread_id,
        }),
      )
        .then((chatMessage: ChatMessage[]) => {
          chatMessage[0].cometData = data.data;

          if (!_.isEmpty(chatMessage)) {
            this.chatIncoming.emmitMessage(chatMessage[0]);
          }
        })
        .catch((e) => this.dbg.le("get messages error", e));
    }

    // offer update comet handler

    /*

    offer template has to have the following body format ( for commet message ) - example:

    Notification to Mobile Agent about 3 day VIP Membership is sent!
    {assign var='MAdata' scope='global' value=[
      'popup'=>'Your Profile is over 75% complete, Your 3 day VIP Membership has been Activated!',
      'notification_type' => 'offer_update'
    ]}

    */

    if (
      !!data?.data?.agent?.notification_type &&
      data?.data?.agent?.notification_type == "offer_update"
    ) {
      this.analytics.logEvent("offer_fulfilled");

      this.dialog
        .showAlert(
          "thanks",
          data["data"]["agent"]["popup"] || "thanks",
          "ok",
          () => this.refresherService.refresh(),
        )
        .catch((err) => {
          this.dbg.le("dialog message error", err);
        });
    }

    if (
      !!data?.data?.notification_type &&
      data?.data?.notification_type == "match_update"
    ) {
      if (!data?.data?.username) return;
      this.profileService.processProfile(
        data?.data?.username,
        true,
        true,
        false,
      );
    }
  }

  public init() {
    let NOTIFY_CHANNEL = "updates";

    // init comet
    this.api
      .call<ApiResponses.SocketTokenResponse.type>({
        command: ApiCommands.SocketToken.name,
        method: "get",
        useToken: true,
        params: <ApiParams.SocketToken.type>{
          channel: NOTIFY_CHANNEL,
        },
      })
      .subscribe({
        next: (data) => {
          let _channel = data?.data?.channel;

          this.addAuthToken(_channel, data?.data?.secret);

          this.subscribe(_channel, (data) => {
            this.handleIncomingUpdate(data);
          });
        },
        error: (error) => {
          this.dbg.le("commet init error", error);
        },
      });
  }

  /**
   * Acces to client;
   */
  public Client() {
    return this.client;
  }

  /**
   * auth token
   */
  private authTokens: AuthTokenItem[] = [];

  /**
   * Current auth token to use with new subscriptions
   */
  private currentAuthToken: string = "";

  /**
   * Set auth token
   */
  public addAuthToken(channel: string, token: string) {
    // if any of it is empty, don't add
    if (!(channel && token)) {
      this.dbg.le("comet token add error");
      return;
    }

    let item = new AuthTokenItem();
    item.channel = channel;
    item.token = token;
    this.authTokens.push(item);
  }

  /**
   * get token for a channel
   * @param channel token - string
   */
  public getAuthToken(channel: string) {
    let item = this.authTokens.find((el) => {
      return el.channel == channel;
    });

    if (item && !item.isEmpty()) {
      return item.token;
    }

    return "";
  }

  /**
   * Add extensions to the client when initializing
   */
  private addExtensions() {
    // @todo replace this with real token fetched form api
    this.client.addExtension(this.extLogger());
    this.client.addExtension(this.extAuth());
  }

  /**
   * Generate channel string
   * @param system
   * @param username
   * @param channel
   */
  public getChannel(system: string, username: string, channel: string) {
    return "/" + system + "/users/" + username + "/" + channel;
  }
  /**
   * returns logger fraye extension
   */
  private extLogger() {
    return {
      incoming: (message, callback) => {
        this.dbg.l("Socket IN", message);
        callback(message);
      },
      outgoing: (message, callback) => {
        this.dbg.l("Socket OUT", message);
        callback(message);
      },
    };
  }

  /**
   * Subscribe to channel
   *
   * @param channel
   * @param callback
   */
  public subscribe(channel: string, callback: Function) {
    this.currentAuthToken = this.getAuthToken(channel);

    this.dbg.l("Connected to chat socket server");
    // this.analytics.logEvent("connected_to_chat");

    this.client.subscribe(channel, (data) => {
      callback(data);
    });
  }

  /**
   * returns auth fraye extension
   */
  private extAuth() {
    return {
      outgoing: (message, callback) => {
        // do not modify /meta/subscribe !
        if (message.channel !== "/meta/subscribe") {
          return callback(message);
        }

        if (!message.ext) {
          message.ext = {};
        }

        message.ext.authToken = this.currentAuthToken;
        callback(message);
      },
    };
  }
}
