import { Injectable } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  CanActivate,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from "@angular/router";
import { firstValueFrom, Observable } from "rxjs";
import { Debug } from "../helpers/debug";
import { Utils } from "../helpers/utils";
import { AccountService } from "./account.service";
import { ChatContactItem } from "./chat-contact-item.interface";
import { DialogHelperService } from "./dialog-helper.service";
import { ProfileService } from "./profile.service";
import { PurchaseService } from "./purchase.service";
import { StateService } from "./state.service";
import { PreviousRouteService } from "./previous-route.service";

/**
 * A service to check and redirect routes globally
 * with the goal of redirecting the user to the upgrade page
 * if necessary
 *
 * use this class as a global place to do this for routes - as a route guard
 */
@Injectable({
  providedIn: "root",
})
export class UpgradeRedirectService implements CanActivate {
  constructor(
    private stateService: StateService,
    private accountService: AccountService,
    private purchaseService: PurchaseService,
    private dbg: Debug,
    private profileService: ProfileService,
    private dialog: DialogHelperService,
    private router: Router,
    private previousRouteService: PreviousRouteService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | boolean
    | UrlTree
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree> {
    return new Promise<boolean>(async (resolve) => {
      let _result = true;

      switch (route.routeConfig.path) {
        // chat message case
        case "chat-messages/:username":
          _result = await this.checkInbox(route.params?.username || null);
          break;

        // @note we can add more cases here
      }

      resolve(_result);
    });
  }

  /**
   * Check if we need to show upgrade page and return restul
   * @param username
   * @returns return result that we can return from canActivate ( true if we can let the user use the path, false, if we want to show something else )
   */
  private async checkInbox(username: string): Promise<boolean> {
    // check if the current path was the upgrade page
    // in that case we don't open it because that causes infinite loop
    if (!username) {
      return Promise.resolve(false);
    }

    if (Utils.isUpgradeUrl(this.router.url)) {
      return Promise.resolve(true);
    }

    if (this.checkPrevUrl()) {
      return Promise.resolve(true);
    }

    // do we have an inbox item clicked?
    let _item = null;

    // we only use the item if it's younger than 1 second
    // this is to ensure that we'll not use it if we already opened an
    // item on inbox, and we get a push notification after that while
    // the app is active

    _item = this.stateService.get("lastClickedInboxItem", null, null, 1000);
    let _doShow = this.inboxDoOpenUpgrade(_item);

    // we don't have to show the upgrade, so we'll allow the navigation
    // we also don't show the upgrade dialog for system users
    if (!_doShow || this.profileService.isKnownSystemUser(username)) {
      return Promise.resolve(true);
    }

    // open inbox message upgrade dialog
    let _pdata = _item;

    // fetch profile data if possible
    if (username) {
      try {
        await this.dialog.showLoading();
        let _profile = await firstValueFrom(this.profileService.get(username));
        _pdata = _profile.data;
      } catch (err) {
        this.dbg.le("Error loading profile on inbox for upgrade", err);
      }
    }

    await this.dialog.hideLoading();

    // show upgrade with user's profile
    this.stateService.set("sendMessageProfile", null, _pdata);

    this.purchaseService
      .setDialogType("upgrade_to_read")
      .openUpgradeDialog((error) => {
        this.dialog.showToastBaseOnErrorResponse(error, null, false);
      });

    return Promise.resolve(false);
  }

  private inboxDoOpenUpgrade(item: ChatContactItem | any) {
    if (!item) {
      return !this.accountService.isPremium();
    } else {
      // ensure to open upgrade even if we don't get the do_open_upgrade value
      // for example want to send message to a new user
      return (
        (item?.do_open_upgrade ?? true) && !this.accountService.isPremium()
      );
    }
  }

  /**
   * Routes to exclude.
   */
  private checkPrevUrl(): boolean {
    switch (this.previousRouteService.previousUrl) {
      case "/app/inbox":
        return false;
        break;
      default:
        return true;
        break;
    }
  }
}
