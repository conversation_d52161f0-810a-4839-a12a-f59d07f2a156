import { Injectable, NgZone } from "@angular/core";
import { firstValueFrom, Observable, tap } from "rxjs";
import { ApiCommands } from "./api/api.commands";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { environment as Env } from "../../environments/environment";
import { StaticPageType, UtilsConfigData } from "../schemas/utils";
import { map } from "rxjs/operators";
import { TranslateService } from "@ngx-translate/core";
import { MiscService } from "./misc.service";
import _ from "lodash";
import { Router } from "@angular/router";
import { Debug } from "../helpers/debug";

@Injectable({
  providedIn: "root",
})
export class UtilsService {
  /**
   * Config from proxy.
   */
  private _config: UtilsConfigData.type;

  constructor(
    private api: ApiService,
    private translateService: TranslateService,
    private router: Router,
    private misc: MiscService,
    private dbg: Debug,
  ) {}

  get config(): UtilsConfigData.type {
    return this._config;
  }

  set config(data: UtilsConfigData.type) {
    this._config = data;
  }

  public log(data) {
    return firstValueFrom(
      this.api.call<ApiResponses.ResponseBase.type>({
        command: ApiCommands.Log.name,
        method: "post",
        useToken: true,
        reportProgress: true,
        params: <ApiParams.ServerStatus.type>{
          system_id: Env.API_SYSTEM_ID,
          data: data,
        },
      }),
    );
  }

  public attrib() {
    return firstValueFrom(
      this.api.call<ApiResponses.UtilsAttribResponse.type>({
        command: ApiCommands.UtilsAttrib.name,
        method: "post",
        useToken: false,
        reportProgress: true,
        params: <ApiParams.UtilsAttribParams.type>{
          os: this.misc.devicePlatform,
        },
      }),
    );
  }

  /**
   * Get config from proxy.
   */
  public getConfig() {
    return firstValueFrom(
      this.api
        .call<ApiResponses.UtilsConfigResponse.type>({
          command: ApiCommands.UtilsConfig.name,
          method: "get",
          useToken: false,
          reportProgress: true,
          params: <ApiParams.UtilsConfig.type>{
            system_id: Env.API_SYSTEM_ID,
          },
        })
        .pipe(
          tap({
            next: ({ data }) => {
              this.config = data;
              // If no data from proxy - set data from Env.
              if (!this.config) {
                this.config = Env;
              } else {
                // We have data from proxy but property does not exist.
                this.setProps();
              }
            },
          }),
          map((data) => data.data),
        ),
    );
  }

  public isChatWithUserActive(username: string) {
    if (!username) {
      return false;
    }
    return this.router.url.indexOf("/chat-messages/" + username) > -1;
  }

  /**
   * If we have a api error, then we show that, if not then we try to see thrown error, if not we pass generic
   * @param error error object from a cantch block
   * @returns
   */
  public figureOutTheErrorKey(
    error: any,
    defaultError: string = "unkonw_error",
  ) {
    // if we have an
    return this.api.getAllErrorsKeysOnly().length > 0
      ? this.api.getAllErrorsKeysOnly()[0]
      : error["message"]
        ? error["message"]
        : defaultError;
  }

  /**
   * If we have a api error, then we show that, if not then we try to see thrown error, if not we pass generic
   * @param error error object from a cantch block
   * @returns
   */
  public figureOutTheErrorMessage(
    error: any,
    defaultError: string = "unkonw_error",
  ) {
    // if we have an
    return this.api.getAllErrorsTextOnly().length > 0
      ? this.api.getAllErrorsTextOnly()[0]
      : error["message"]
        ? error["message"]
        : this.translateService.instant(defaultError);
  }

  /**
   * This is for when we want to check the error param first, and then if
   * there is no message, we will try to fetch the first api error message.
   * @param error error object
   * @returns string
   */
  public figureOutTheErrorMessageFromError(
    error: any,
    defaultError: string = "unkonw_error",
  ): string {
    // First try with error message.
    if (error["message"]) return error["message"];
    // We don't have error message - try with api errors or if not use default error.
    return this.api.getAllErrorsTextOnly().length > 0
      ? this.api.getAllErrorsTextOnly()[0]
      : this.translateService.instant(defaultError);
  }

  public isCheckingServerStatus(): boolean {
    return this.api.isInEventList({
      command: ApiCommands.ServerStatus.name,
    });
  }

  /**
   * Check server status
   *
   * @param params ApiParams.ServerStatus.type
   * @returns Observable<ApiResponses.ResponseBase>
   */
  public checkServerStatus(): Observable<ApiResponses.ResponseBase.type> {
    return this.api.call<ApiResponses.ResponseBase.type>({
      command: ApiCommands.ServerStatus.name,
      method: "get",
      useToken: false,
      reportProgress: true,
      params: <ApiParams.ServerStatus.type>{
        system_id: Env.API_SYSTEM_ID,
      },
    });
  }

  /**
   * Get static page content
   *
   * @param pageName
   * @returns
   */
  public getStaticPage(pageName: string): Observable<StaticPageType.type> {
    return this.api
      .call<ApiResponses.StaticPage.type>({
        command: ApiCommands.StaticPage.name,
        method: "get",
        useToken: false,
        params: {
          system_id: Env.API_SYSTEM_ID,
          page: pageName,
        },
      })
      .pipe(
        map((data) => {
          // convert data
          return data.data;
        }),
      );
  }

  /**
   * Let server know that notification was displayed
   * @param id
   * @param data
   * @returns
   */
  public notificationDisplayed(
    id: string,
    data: object,
  ): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.NotificationsDisplayed.name,
        useToken: false,
        method: "post",
        params: {
          id: id,
          data: data,
          system_id: Env.API_SYSTEM_ID,
        },
      }),
    );
  }

  /**
   * Let server know that there was an upgrade from notification
   * @param id
   * @param data
   * @returns
   */
  public notificationUpgrade(
    id: string,
    data: object,
  ): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.NotificationsUpgrade.name,
        useToken: false,
        commandVersion: "2",
        method: "post",
        params: {
          id: id,
          data: data,
          system_id: Env.API_SYSTEM_ID,
        },
      }),
    );
  }

  /**
   * Let server know that notification was clicked
   * @param id
   * @param data
   * @returns
   */
  public notificationClicked(
    id: string,
    data: object,
  ): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.NotificationsClicked.name,
        useToken: false,
        method: "post",
        params: {
          id: id,
          data: data,
          system_id: Env.API_SYSTEM_ID,
        },
      }),
    );
  }

  /**
   * Return gender object based on gender id
   * return null if no pair found
   */
  public getGenderById(id: number | string) {
    let res = null;

    Env.APP_GENDERS.forEach((itm) => {
      if (itm.id == id) {
        res = itm;
      }
    });

    return res;
  }

  /**
   * Set properties from Env in case it is not set on the proxy.
   */
  private setProps(): void {
    Object.keys(Env).forEach((key) => {
      if (this.config[key] === undefined) {
        this.config[key] = Env[key];
      }
    });
  }
}
