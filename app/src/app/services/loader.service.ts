import { Injectable } from "@angular/core";
import { Debug } from "../helpers/debug";

import { StorageService } from "./storage.service";

const DEBUG_LOADER_STATUS: string = "storage_loader_status";

@Injectable({
  providedIn: "root",
})
export class LoaderService {
  constructor(private storage: StorageService, private debug: Debug) {}

  /**
   * Loader status.
   */
  private _status: boolean = false;

  /**
   * Get loader status.
   *
   * @return boolean
   */
  get status(): boolean {
    return this._status;
  }

  /**
   * Set loader status.
   *
   * @param value
   */
  set status(value: boolean) {
    this._status = value;

    this.storeLoaderStatus(this.isLoading());
  }

  async init() {
    await this.storeLoaderStatus();
  }

  /**
   * Get loader status.
   *
   * @return boolean
   */
  public isLoading(): boolean {
    return this._status;
  }

  /**
   * Store loader status.
   *
   * @param value
   */
  private storeLoaderStatus(value?: boolean): void {
    this.storage
      .get(DEBUG_LOADER_STATUS)
      .then((res) => {
        if (res !== undefined && res !== null) {
          this._status = value !== undefined ? value : res;
        }

        return this.storage.set(DEBUG_LOADER_STATUS, this.isLoading());
      })
      .catch((err) => this.debug.l("loader status err", err));
  }
}
