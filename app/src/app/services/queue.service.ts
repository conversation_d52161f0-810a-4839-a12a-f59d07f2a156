import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { BehaviorSubject, Observable } from "rxjs";
import { Debug } from "../helpers/debug";
import { BootService } from "./boot.service";
import { CometService } from "./comet.service";
import { DialogHelperService } from "./dialog-helper.service";
import { MiscService } from "./misc.service";
import { QueueCommandService } from "./queue/queue-command.service";
import { QueueItem } from "./queue/queue-item.class";
import { WelcomeQueueItemService } from "./queue/welcome-queue-item.service";
import { OfferQueueItemService } from "./queue/offer-queue-item.service";
import { PromoQueueService } from "./promo-queue.service";
import { StateService } from "./state.service";
import { UserActionObserverService } from "./user-action-observer.service";
import { environment as Env } from "src/environments/environment";
import { IntroductionQueueItemService } from "./queue/introduction-queue-item.service";

@Injectable({
  providedIn: "root",
})
export class QueueService {
  constructor(
    private bootService: BootService,
    private router: Router,
    private debug: Debug,
    private dialog: DialogHelperService,
    private misc: MiscService,
    private welcomeQueueItem: WelcomeQueueItemService,
    private queueCommand: QueueCommandService,
    private comet: CometService,
    private offerQueueItem: OfferQueueItemService,
    private promoQueueItem: PromoQueueService,
    private stateService: StateService,
    private userActionObserver: UserActionObserverService,
    private introductionQueueItem: IntroductionQueueItemService,
  ) {
    this.stateService.set("queueFinished", "", false);

    // subscribe to queue commander
    this.queueCommand.queueForward$.subscribe({
      next: () => {
        this.executeNextQueueItem();
      },
    });
  }

  private _queue: QueueItem[] = [];

  /**
   * execute next item
   * @returns
   */
  private executeNextQueueItem() {
    let qi = this._queue.shift();

    this.debug.l("queue item", qi);

    if (!qi) {
      // we have no more items
      this.queueFinished();
      return;
    }

    qi.executeItem(this.queueCommand);
  }

  private _didQueueFinish$: BehaviorSubject<boolean> = new BehaviorSubject(
    false,
  );

  get didQueueFinish$(): Observable<boolean> {
    return this._didQueueFinish$.asObservable();
  }

  private _queueFinished = false;

  /**
   * Value to return queue status
   */
  public get queueFinishedValue() {
    return this._queueFinished;
  }

  private queueFinished() {
    // emmit finished flag
    this._didQueueFinish$.next(true);
    this.debug.l("!!! queue finished");

    // do stuff after queue finish
    // initialize comet

    if (!this._queueFinished) this.comet.init();

    this._queueFinished = true;

    this.stateService.set("queueFinished", "", true);
  }

  /**
   * Initialize boot service
   */
  public async init() {
    // initialize queue

    this.bootService.bootStatus$.subscribe({
      next: async (status) => {
        this.debug.l("boot status in queue", status);

        // was init done? if not nothing to do here
        if (!status.isInitialBoodDone || status.isBackgroundBootDone)
          return Promise.resolve();

        // do we have a login or it is a recovery event
        if (!status.isRecoveryEvent) {
          // @todo
          // execute queue here
          // for now I'll just open fast match, but we'll need to do a lot here
          // for example
          // -- see if welcome wizard is required
          // -- show offers
          // etc - see old app

          this._queue = [
            this.welcomeQueueItem,
            this.promoQueueItem,
            this.introductionQueueItem,

            // we need a scheduler class and we need to enalbe it here, when the queue is done
            // that scheduler class will execute things in 3 period
            // - short ( when user is using the app for a short time - N swipes, or N navigations)
            // - middle ( a few days )
            // - long ( 1 2 weeks )
            // offer queue should be handled by that and go as "short" scheduled

            /*this.offerQueueItem,*/
            this.userActionObserver,
          ];

          // redirect only if we don't have direct url
          this.redirectIfNotDirectLink();

          this.executeNextQueueItem();
        } else {
          // we don't need to process queue it's not an initial bootstrap
          this.debug.l(
            "@todo we don't need to process queue it's not an initial bootstrap ...",
          );
        }
      },
    });

    return Promise.resolve();
  }

  /**
   * Redirect to main page if necessary ( if not direct link given )
   */
  private async redirectIfNotDirectLink() {
    if (!(this.misc.isAppUrl() || this.misc.isErrorUrl())) {
      await this.router.navigate([Env.HOME_PATH], {
        replaceUrl: true,
      });
    }

    /*
    // @note if we have problems that menu helper is not working, we need to do something here, force path events to be executed
    else
    {
      // we redirect to same page just to fire events for router, to prevent direct linking to page that's not allowed
      this.debug.l("add direct tag to url");
      await this.router.navigateByUrl("/"+this.router.url);
    }
    */

    this.dialog.hideSplash(500);
  }
}
