import { Injectable } from "@angular/core";
import { filter } from "rxjs";
import { TME_photo_upload, TME_sign_up } from "../classes/gtm-event.class";
import { Debug } from "../helpers/debug";
import { AccountService } from "./account.service";
import { AnalyticsService } from "./analytics.service";
import { ApiCommands } from "./api/api.commands";
import { ApiService, IApiEvent } from "./api/api.service";
import { AuthService } from "./auth.service";

@Injectable({
  providedIn: "root",
})
export class AnalyticsHelperService {
  constructor(
    private analytics: AnalyticsService,
    private auth: AuthService,
    private account: AccountService,
    private api: ApiService,
    private dbg: Debug
  ) {}

  public init() {
    // auth analytics event
    this.auth.registerEvents$.subscribe({
      next: (data) => {
        this.dbg.l("register obs data", data);

        // send sign_up analytics event when we have signup
        this.analytics.logEvent("sign_up", <TME_sign_up>{
          method: data.method,
          user_id: this.analytics.getUserHash(data?.data["user_id"]),
        });
      },
    });

    // photo upload analytics event
    this.api.eventStream$
      .pipe(filter((e: IApiEvent) => e.event == "finalize"))
      .subscribe({
        next: (data: any) => {
          if (data.command == ApiCommands.PictureUpload.name) {
            this.analytics.logEvent("photo_upload", <TME_photo_upload>{
              upload_type:
                this.account.account.photos.length == 0 ? "initial" : "other",
            });
          }
        },
      });
  }
}
