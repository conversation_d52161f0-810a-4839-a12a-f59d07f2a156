import { Injectable } from "@angular/core";
import { AccountService } from "./account.service";
import { Debug } from "../helpers/debug";
import { environment as Env } from "../../environments/environment";

@Injectable({
  providedIn: "root",
})
export class MatchPrefService {
  constructor(public accountService: AccountService, public debug: Debug) {}

  /**
   * Fetch account matchPref data.
   */
  public getMatchPref(): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.accountService.getMatchPref().subscribe(
        (data) => {
          this.debug.log({ message: "GET MATCH PREF" });
          data["age_from"] =
            data["age_from"] < Env.APP_VALID_AGE
              ? Env.APP_VALID_AGE
              : data["age_from"];
          data["gender_id"] = this.accountService.account.gender_id;
          data["looking_id"] = this.accountService.account.looking_id;
          // this._setData(data);
        },
        (error) => {
          this.debug.log({
            message: "get match pref error",
            data: error,
            severity: "error",
          });
        }
      );
    });
  }
}
