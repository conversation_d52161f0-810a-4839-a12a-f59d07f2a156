import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { environment as Env } from "../../environments/environment";
import * as _ from "lodash";
import { AccountService } from "./account.service";

@Injectable({
  providedIn: "root",
})
export class WizardService {
  constructor(
    protected router: Router,
    protected accountService: AccountService
  ) {
    this.initSteps();
  }

  protected _isProcessing: boolean = false;

  // data that the wizard has to hold, but will not gather from user
  protected _storedData: Object = {};

  /**
   * Add store data to the wizard
   */
  public setStoredDataByKey(key: string, data: any) {
    this._storedData[key] = data;
  }

  /**
   * Get a specific store data
   * @param key
   * @returns
   */
  public getStoredData(key: string): any {
    return this._storedData[key];
  }

  /**
   * initialize stored data object
   */
  protected initStoredData() {
    this._storedData = {};
  }

  /**
   *
   */
  get isPorcessing(): boolean {
    return this._isProcessing;
  }

  protected _urlPrefix: string = "placeholder";

  get urlPrefix(): string {
    return this._urlPrefix;
  }

  // keep step data
  private stepList: StepData[] = [];

  // overwrite this function to set steps
  protected initSteps() {
    // set step data here with set steps
  }

  /**
   * Get merged data for all steps
   * @returns
   */
  public getMergedData() {
    // we start with stored data, then overwrite - if we have new - from wizard
    let res = this._storedData;

    this.stepList.forEach((e) => {
      res = _.assign(e.data, res);
    });
    return res;
  }

  /**
   * Step count
   */
  public get stepCount() {
    return this.stepList.length;
  }

  /**
   * Returns step object
   */
  public getStepList() {
    return this.stepList;
  }

  /**
   * Set step data for future referencestep
   * @param initialStepList
   */
  protected setSteps(initialStepList: StepData[]) {
    this.stepList = _.cloneDeep(initialStepList);

    // prepare elements for easier checkin later
    for (let i = 0; i < this.stepList.length; i++) {
      this.stepList[i].index = i;

      // is first element
      this.stepList[i].isFirst = i === 0;

      // is last element
      this.stepList[i].isLast = i === this.stepList.length - 1;

      // next url
      this.stepList[i].nextUrl = this.stepList[i].isLast
        ? ""
        : `${this.urlPrefix}/${this.stepList[i + 1].url}`;

      // next index
      this.stepList[i].nextIndex = this.stepList[i].isLast ? -1 : i + 1;

      // add url prefix
      this.stepList[i].url = `${this.urlPrefix}/${this.stepList[i].url}`;
    }
  }

  /**
   * Helper function to refresh indexes after list modification
   */
  protected refreshStepIndexes() {
    // prepare elements for easier checkin later
    for (let i = 0; i < this.stepList.length; i++) {
      this.stepList[i].index = i;

      // is first element
      this.stepList[i].isFirst = i === 0;

      // is last element
      this.stepList[i].isLast = i === this.stepList.length - 1;

      // next url
      this.stepList[i].nextUrl = this.stepList[i].isLast
        ? ""
        : this.stepList[i + 1].url;

      // gender index
      this.stepList[i].nextIndex = this.stepList[i].isLast ? -1 : i + 1;
    }
  }

  /**
   * Get StepData object based on the current route
   * @param url
   * @returns
   */
  public getStepByUrl(url: string) {
    return (
      _.find(this.stepList, (el) => {
        return url.indexOf(el.url) >= 0;
      }) || null
    );
  }

  /**
   * Get step by index
   * @param index
   * @returns
   */
  public getStepByIndex(index: number): StepData | null {
    return index >= 0 && index < this.stepList.length
      ? this.stepList[index]
      : null;
  }

  /**
   * Get step by name
   *
   * @param name
   * @returns
   */
  public getStepByName(name: string) {
    return (
      _.find(this.stepList, (el) => {
        return this.nameStepByUrl(el.url) === name;
      }) || null
    );
  }

  /**
   * Set step data by url
   * @param url only the end part of the url for example gender or age
   * @param data
   */
  public setStepData(url: string, data: any) {
    let step = _.findIndex(this.stepList, (el) => {
      return el.url.indexOf(`/${url}`) >= 0;
    });

    if (step >= 0) {
      this.stepList[step].data = data;
      this.stepList[step].isPopulated = true;
    }
  }

  /**
   * Generate an object for paginator display
   * @param currentIndex
   */
  public getPagination(currentIndex: number): IPaginator[] {
    let paginatorObject: IPaginator[] = [];

    _.forEach(this.stepList, (el, i) => {
      paginatorObject.push({
        route: el.url,
        isActive: i == currentIndex,
      });
    });

    return paginatorObject;
  }

  /**
   * Navigate to next page by index
   * @param index
   */
  public navigateByIndex(index: number) {
    if (!(index >= 0 && index < this.stepList.length)) {
      throw new Error("Navigation index out of bounds");
    }

    // we add an url fragment for analytics basically
    this.router.navigateByUrl(`${this.stepList[index].url}#step_${index + 1}`);
  }

  /**
   * Get first unpopulated step
   * @returns
   */
  public getFirstUnpopulated() {
    return (
      _.find(this.stepList, (el) => {
        return !el.isPopulated;
      }) || null
    );
  }

  public finishWizard() {
    alert("finish registration - send data");
  }

  /**
   * Helper to get step name based on url
   * @param url
   * @returns
   */
  public nameStepByUrl(url: string): string {
    return "signup_" + url.replace(/\W/g, "");
  }

  /**
   * Initialize all data, steps, etc
   */
  public initialize() {
    this.initSteps();
  }

  /**
   * Remove element from step by url
   * @param url
   */
  public removeStepByUrl(url: string) {
    _.remove(this.stepList, (e) => {
      return e.url.indexOf(`/${url}`) >= 0;
    });
    this.refreshStepIndexes();
  }
}

/**
 * StepData class definition
 */
export class StepData {
  public url: string = "";
  public data: { [key: string]: any } = {};
  public isPopulated: boolean = false; // flip this to true when we have the data ( preloaded or user filled )

  // all bellow given automatically

  public index?: number = -1;
  public nextUrl?: string = "";
  public isLast?: boolean = false;
  public isFirst?: boolean = false;
  public nextIndex?: number = -1;
}
/**
 * Paginator interface
 */
export interface IPaginator {
  route: string;
  isActive: boolean;
}
