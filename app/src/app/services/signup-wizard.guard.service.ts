import { Injectable } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from "@angular/router";
import { SignupWizardService } from "./signup-wizard.service";
import * as _ from "lodash";
import { WizardService } from "./wizard.service";
import { AppleSignupWizardService } from "./apple-signup-wizard.service";

@Injectable({
  providedIn: "root",
})
export class SignupWizardGuardService {
  private currentWizardInstance: WizardService;

  constructor(
    private router: Router,
    private signupWizard: SignupWizardService,
    private appleWizard: AppleSignupWizardService
  ) {
    this.currentWizardInstance = this.signupWizard;
  }

  /**
   * Return signup wizard instance based on current url
   * - if we're doing signup, then proper wizard is used
   */
  get getSignupWizardInstance(): WizardService {
    return this.currentWizardInstance;
  }

  /**
   * Look up which url is activated, and set the current wizard instance
   * to the appropriate one, so we can interract with it from wizard services
   *
   * @param url
   */
  private storeActiveInstance(url: string) {
    const activeWizards = [this.signupWizard, this.appleWizard];

    let res = _.find(activeWizards, (data) => {
      return url.indexOf(`/${data.urlPrefix}/`) >= 0;
    });

    this.currentWizardInstance = res ? res : this.signupWizard;
  }

  public canActivateChild(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ) {
    // we set the wizard instance by url
    this.storeActiveInstance(state.url);

    // we cannot open any page in the wizard if the previous pages are not marked as populated
    // so user can only navigate back to pages already visited

    let _firstUnpopulatedPageUrl =
      this.currentWizardInstance.getFirstUnpopulated();
    let _currentPage = this.currentWizardInstance.getStepByUrl(state.url);

    if (
      _firstUnpopulatedPageUrl &&
      _currentPage &&
      _currentPage.index > _firstUnpopulatedPageUrl.index
    ) {
      let newUrl: UrlTree = this.router.parseUrl(
        this.currentWizardInstance.getStepByIndex(
          _firstUnpopulatedPageUrl.index
        ).url
      );
      return newUrl;
    }

    return true;
  }
}
