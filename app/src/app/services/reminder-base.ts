import { Injectable } from "@angular/core";
import { StorageService } from "./storage.service";
import _ from "lodash";
import { Debug } from "../helpers/debug";
import { Utils } from "../helpers/utils";
import { DialogHelperService } from "./dialog-helper.service";

/**
 * A service that handles reminders for users that can be dismissed, and repeated on certain event
 */

export const REMINDER_STORAGE_KEY_BASE = "reminderService_";

export interface ReminderServiceItem {
  lastActive: number;
  isDisabled: boolean;
}

@Injectable({
  providedIn: "root",
})
export class ReminderServiceBase {
  // variable to keep data
  protected data: ReminderServiceItem = null;

  // repeat period for the type
  protected repeatPeriod: number = 24 * 60 * 60; // one day default

  /**
   * Constructor
   *
   * @param storage
   * @param dbg
   * @param dlg
   */
  public constructor(
    public storage: StorageService,
    public dbg: Debug,
    public dlg: DialogHelperService,
  ) {}

  /**
   * Set repeat time in seconds
   * @param repeatTime repeat time in seconds
   */
  public setRepeatPeriod(repeatTime: number = 24 * 60 * 60) {
    this.repeatPeriod = repeatTime;
  }

  /**
   * key name to store data
   */
  protected storageKey: string = REMINDER_STORAGE_KEY_BASE + "base";

  /**
   * Fetch data from local storage
   */
  private fetchData(): Promise<ReminderServiceItem> {
    return new Promise((resolve, reject) => {
      this.storage
        .get(this.storageKey)
        .then((data) => {
          // no data yet, use default
          if (!data) {
            this.data = {
              lastActive: 0,
              isDisabled: false,
            };

            this.saveData().then(() => {
              resolve(this.data);
            });
          } else {
            this.data = data as ReminderServiceItem;
            resolve(this.data);
          }
        })
        .catch((error) => {
          this.dbg.le(
            `Error fetching data for reminder ${this.storageKey}`,
            error,
          );
          reject(error);
        });
    });
  }

  /**
   * Save data to local storage
   */
  private saveData(): Promise<any> {
    return this.storage.set(this.storageKey, <ReminderServiceItem>this.data);
  }

  /**
   * Initialize class
   */
  public async init() {
    // fetch latest data when initializing
    return this.fetchData();
  }

  /**
   * is the reminder relevant?
   * @note child classes should overwrite this function and implement logic to
   * check if we need to remind the user at all
   *
   * @return promise resolves if relevant, rejects if not
   */
  public checkIfRelevant(): Promise<any> {
    return Promise.resolve();
  }

  /**
   * Is reminder disabled?
   */
  private isDisabled() {
    return (this.data && this.data.isDisabled) || false;
  }

  /**
   * Is it time to execute the reminder?
   */
  public isTimeToExec() {
    if (!this.data) return true;

    return Utils.now() > this.data.lastActive + this.repeatPeriod;
  }

  /**
   * should we execute the reminder? resolved if yes
   * @return promise resolves if needs execution, rejects if not
   */
  public checkIfExecute(forceResolve: boolean = false): Promise<any> {
    if (forceResolve) {
      this.dbg.l("force exec");

      this.data = new (class implements ReminderServiceItem {
        isDisabled: boolean;
        lastActive: number;
      })();

      return Promise.resolve();
    }

    return new Promise<any>((resolve, reject) => {
      if (this.isDisabled() || !this.isTimeToExec()) {
        let status: string = this.isDisabled()
          ? "reminder disabled"
          : "reminder postponed";
        this.dbg.l(status);
        reject(status);
      } else {
        this.checkIfRelevant()
          .then(() => {
            this.dbg.l("ok, show reminder");
            resolve(null);
          })
          .catch(() => {
            this.dbg.l("not relevant");
            reject("not relevant");
          });
      }
    });
  }

  /**
   * postpone reminder
   *
   * @param doReactivate set this to true if you want to re-activate the reminder
   */
  public postponeReminder(doReactivate: boolean = false): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.fetchData()
        .then(() => {
          this.data.lastActive = Utils.now();
          if (doReactivate) {
            this.data.isDisabled = false;
          }
          this.afterPostpone();
          this.saveData()
            .then(() => {
              resolve(null);
            })
            .catch((error) => {
              reject(error);
            });
        })
        .catch((err) => {
          this.dbg.le(`Error postponing reminder ${this.storageKey}`, err);
          this.afterPostpone();
          reject(err);
        });
    });
  }

  /**
   * Disable this specific reminder
   */
  public disableReminder() {
    return new Promise<any>((resolve, reject) => {
      this.fetchData()
        .then(() => {
          this.data.lastActive = Utils.now();
          this.data.isDisabled = true;
          this.afterDisable();
          this.saveData()
            .then(() => {
              resolve(null);
            })
            .catch((err) => {
              reject(err);
            });
        })
        .catch((err) => {
          this.dbg.le(`Error disabling reminder ${this.storageKey}`, err);
          this.afterDisable();
          reject(err);
        });
    });
  }

  /**
   * public function to do stuff that needs to be done when dialog is disabled
   * @note child classes should overwrite this function
   */
  public afterDisable() {
    this.dbg.l("DISABLED");
  }

  /**
   * public function to do stuff that needs to be done when dialog is dismissed
   * @note child classes should overwrite this function
   */
  public afterPostpone() {
    this.dbg.l("DISMISS");
  }

  /**
   * Execute reminder content
   * @note child classes should overwrite this function to implement logic,
   * we should show dialogs, here, or whatever
   * @note child classes should call this parent function
   *
   * @param forceExecute
   */
  public execute(forceExecute: boolean = false): Promise<any> {
    this.dbg.l("reminder exec fn");

    return new Promise((resolve, reject) => {
      this.checkIfExecute(forceExecute)
        .then(() => {
          this.data.lastActive = Utils.now();
          return this.saveData();
        })
        .then(() => {
          this.dbg.l("EXEC OK");
          resolve(null);
        })
        .catch((e) => {
          this.dbg.l("EXEC FAILED", e);
          reject(e);
        });
    });
  }
}
