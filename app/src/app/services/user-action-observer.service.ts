import { Injectable } from "@angular/core";
import { NavigationEnd, Router } from "@angular/router";
import { Observable, Subject } from "rxjs";
import { filter } from "rxjs/operators";
import { Debug } from "../helpers/debug";
import { ApiCommands } from "./api/api.commands";
import { ApiService } from "./api/api.service";
import { QueueCommandService } from "./queue/queue-command.service";
import { QueueItem } from "./queue/queue-item.class";

export type UserActionEvent = {
  count: number;
  actionType: string;
};

/**
 * Calss to observe user activity, and emmit action periodically as
 * a helper to other classes to take action.
 *
 * It also should be aware on which pages we should never emmit actions,
 * like upgrade to premium, or settings
 */

@Injectable({
  providedIn: "root",
})
export class UserActionObserverService extends QueueItem {
  // emmit every time when user action reaches this number
  private EMMIT_FREQUENCY = 30;

  // on these pages actions will not be executed
  private IGNORE_PAGES = [
    "/app/settings",
    "/app/modals/go-premium",
    "/app/modals/try-premium",
  ];

  // list of api calls that count as an action point increase
  private COUNT_ACTIONS = [
    ApiCommands.ProfileLike.name,
    ApiCommands.ProfilePass.name,
    ApiCommands.ProfileSuperlike.name,
  ];

  protected queueAction(): void {
    this.initActionSubscriptions();
    this.queueCommand.gotoNextItem();
  }

  constructor(
    private router: Router,
    private queueCommand: QueueCommandService,
    private dbg: Debug,
    private api: ApiService
  ) {
    super();
  }

  private _userActionPoints: number = 0;
  private _currentUrl: string = "";

  /**
   * Increase action points
   */
  private increaseActionPoints() {
    this._userActionPoints++;
  }

  // keep last emmited count for periodic check
  private lastEmmit: number = this.EMMIT_FREQUENCY;

  /**
   * Helper function to check and emmit observable event
   * @param actionType
   * @returns
   */
  private emmit(actionType: string) {
    // don't emmit on ingored pages
    if (this.IGNORE_PAGES.indexOf(this._currentUrl) >= 0) {
      return;
    }

    if (this.lastEmmit + this.EMMIT_FREQUENCY <= this._userActionPoints) {
      this.lastEmmit = this._userActionPoints;

      let _evnt: UserActionEvent = {
        count: this._userActionPoints,
        actionType: actionType,
      };

      this._trigger$.next(_evnt);

      this.dbg.l("user action emmited", _evnt);
    }
  }

  /**
   * Initialize event subscriptions
   */
  private initActionSubscriptions() {
    // user action for navigation
    this.router.events.subscribe({
      next: (e: any) => {
        this._currentUrl = e.url;

        if (e instanceof NavigationEnd) {
          // don't count ignored pages
          this.increaseActionPoints();
          this.emmit("onNavigation");
        }
      },
    });

    // api call spy service

    this.api.eventStream$
      .pipe(
        filter((data) => {
          // on profile update or match pref update refresh lists

          return (
            data.event == "complete" &&
            this.COUNT_ACTIONS.indexOf(data.command) >= 0
          );
        })
      )
      .subscribe(() => {
        this.increaseActionPoints();
        this.emmit("onApiAction");
      });
  }

  // private trigger subject
  private _trigger$: Subject<UserActionEvent> = new Subject();

  // exposed trigger obeserver
  public get trigger$(): Observable<UserActionEvent> {
    return this._trigger$.asObservable();
  }
}
