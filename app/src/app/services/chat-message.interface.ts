import { GalleryImage } from "../schemas/gallery-image";
import { Message } from "../schemas/message";
import { ProfileBase } from "../schemas/profile-base";

export interface ChatMessage {
  text: string; // message text
  id: string; // message id
  time: number; // message time
  // isDelivered: boolean; // is message seen by other user?
  // isImported: boolean; // is message imported or got from xmpp?
  isOwn: boolean; // is the message by the user?
  attachments?: GalleryImage.type[];
  userId: number;
  originalData: Message.type; // container for origilan data
  system_msg: boolean;
  cometData?: any; // container for comet data
}
