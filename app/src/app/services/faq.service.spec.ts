import { TestBed } from "@angular/core/testing";

import { FaqService } from "./faq.service";
import { ApiService } from "./api/api.service";
import { Platform } from "@ionic/angular";

describe("FaqService", () => {
  let service: FaqService;
  let apiSpy: ApiService;

  beforeEach(() => {
    apiSpy = jasmine.createSpyObj("ApiService", ["call"]);

    TestBed.configureTestingModule({
      providers: [{ provide: ApiService, useValue: apiSpy }, Platform],
    });
    service = TestBed.inject(FaqService);
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });
});
