import { Injectable } from "@angular/core";
import { ApiParams } from "./api/api.params";
import { ApiService } from "./api/api.service";
import { ApiResponses } from "./api/api.responses";
import { ApiCommands } from "./api/api.commands";
import { firstValueFrom } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class IntroductionService {
  public doShow: boolean = false;

  /**
   * Constructor.
   */
  constructor(private api: ApiService) {}

  /**
   * Is introductions available?
   *
   * @return {Promise<ApiResponses.IntroductionIsAvailableResponse.type>}
   */
  public isAvailable(): Promise<ApiResponses.IntroductionIsAvailableResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.IntroductionIsAvailableResponse.type>({
        command: ApiCommands.IntroductionIsAvailable.name,
        method: "get",
        useToken: true,
      })
    );
  }

  /*
   * Add introduction.
   *
   * @param {ApiParams.Introduction.type} params
   * @return {Promise<ApiResponses.NoDataResponse.type>}
   */

  public addIntroduction(
    params: ApiParams.Introduction.type
  ): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.IntroductionAdd.name,
        method: "post",
        useToken: true,
        params,
        callType: ["ignore_401"],
      })
    );
  }
}
