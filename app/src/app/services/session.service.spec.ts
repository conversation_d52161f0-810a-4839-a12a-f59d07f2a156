import { fakeAsync, TestBed } from "@angular/core/testing";

import { SessionService } from "./session.service";
import { StorageService } from "./storage.service";
import { AuthEvent, AuthService } from "./auth.service";
import { ApiService, IApiEvent } from "./api/api.service";
import { JwtData } from "../schemas/jwt";
import { Observable } from "rxjs";
import { HttpEventType } from "@angular/common/http";
import createSpy = jasmine.createSpy;
import { CredentialData } from "../schemas/credentials";

describe("SessionService", () => {
  let service: SessionService;
  let storageSpy: StorageService;
  let apiSpy: ApiService;

  beforeEach(() => {
    storageSpy = jasmine.createSpyObj("StorageService", {
      get: Promise.resolve(),
      set: Promise.resolve(),
    });
    apiSpy = jasmine.createSpyObj("ApiService", ["call"], {
      token: "token",
      eventStream$: new Observable(),
    });

    TestBed.configureTestingModule({
      providers: [
        { provide: StorageService, useValue: storageSpy },
        AuthService,
        { provide: ApiService, useValue: apiSpy },
      ],
    });
    service = TestBed.inject(SessionService);
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });

  it("setTokenData - should set token", () => {
    let params: JwtData.type = {
      token: "token",
      payload: {},
    };
    let spy1 = spyOn(service["_tokenData$"], "next");
    storageSpy.set = createSpy().and.returnValue(Promise.reject("error"));

    service["setTokenData"](params);

    expect(service["jwtData"]).toEqual(params);
    expect(spy1).toHaveBeenCalledTimes(1);
    expect(apiSpy["token"]).toEqual(params.token);
  });

  describe("isSessionActive", () => {
    it("should check if we have an active session and return true", () => {
      let params: JwtData.type = {
        token: "token",
        payload: {},
      };

      service["setTokenData"](params);

      expect(service["jwtData"]["token"]).toEqual("token");
      expect(service.isSessionActive).toBeTrue();
    });

    it("should check if we have an active session and return false", () => {
      expect(service.isSessionActive).toBeFalse();

      let params: JwtData.type = {
        token: null,
        payload: {},
      };

      service["setTokenData"](params);

      expect(service["jwtData"]["token"]).toBeNull();
      expect(service.isSessionActive).toBeFalse();
    });
  });

  it("tokenData$ - should be instance of observable", () => {
    expect(service.tokenData$).toBeInstanceOf(Observable);
  });

  describe("restoreSession", () => {
    it("should failed to restore session if storage not set", fakeAsync(async () => {
      storageSpy.get = createSpy().and.returnValue(Promise.reject());

      await expectAsync(service.restoreSession()).toBeRejected();
    }));

    it("should failed to restore session if token not valid", fakeAsync(async () => {
      storageSpy.get = createSpy().and.returnValue(Promise.resolve());
      service["setTokenData"] = createSpy().and.callThrough();

      await expectAsync(service.restoreSession()).toBeRejected();
      expect(service["setTokenData"]).toHaveBeenCalledWith(null);
    }));

    it("should restore session if token is valid", fakeAsync(async () => {
      let token: JwtData.type = {
        token: "test",
        payload: {},
      };
      storageSpy.get = createSpy().and.returnValue(Promise.resolve(token));
      service["setTokenData"] = createSpy().and.callThrough();

      await expectAsync(service.restoreSession()).toBeResolved();
      expect(service["setTokenData"]).toHaveBeenCalledWith(token);
    }));
  });

  describe("initializeEventTapper", () => {
    it("should init event tapper", () => {
      let params: AuthEvent = {
        credentialData: {
          type: "credential",
          identifier: "a",
          secret: "a",
        },
        jwtData: {
          payload: {},
          token: "token",
        },
      };
      let params2: IApiEvent = {
        eventType: HttpEventType.Sent,
        event: "next",
        callType: "normal",
        time: 0,
        _uuid: "_uuid",
        data: { a: 1 },
        identifier: "a",
        command: "server.status",
        method: "get",
      };
      let spy1 = spyOn(console, "log");
      let spy2 = spyOnProperty(
        AuthService.prototype,
        "authEvents$"
      ).and.returnValue(new Observable());
      let spy3 = spyOn(
        AuthService.prototype.authEvents$,
        "subscribe"
      ).and.callFake((next) => next(params));
      let spy4 = spyOn<any>(service, "saveFailsafeCredentials").and.returnValue(
        Promise.reject()
      );
      let spy5 = spyOn<any>(service, "setTokenData");
      let spy6 = spyOn(apiSpy.eventStream$, "subscribe").and.callFake((next) =>
        next(params2)
      );

      service["initializeEventTapper"]();

      expect(spy1).toHaveBeenCalledWith("auth event");
      expect(spy1).toHaveBeenCalledTimes(2);
      expect(spy2).toHaveBeenCalled();
      expect(spy3).toHaveBeenCalled();
      expect(service["credentialsFailsafe"]).toEqual(params.credentialData);
      expect(spy4).toHaveBeenCalledWith(params.credentialData);
      expect(spy5).toHaveBeenCalledWith(params.jwtData);
      expect(spy6).toHaveBeenCalled();
    });
  });

  describe("getFailsafeCredentials", () => {
    it("should failed to get data from storage", fakeAsync(async () => {
      storageSpy.get = createSpy().and.returnValue(Promise.reject());

      await expectAsync(service["getFailsafeCredentials"]()).toBeRejected();
    }));

    it("should get data from storage", fakeAsync(async () => {
      let params: CredentialData.type = {
        secret: "secret",
        identifier: "identifier",
        type: "type",
      };
      storageSpy.get = createSpy().and.returnValue(Promise.resolve(params));

      await expectAsync(service["getFailsafeCredentials"]()).toBeResolvedTo(
        params
      );
    }));
  });

  describe("saveFailsafeCredentials", () => {
    it("should failed to save credentials", fakeAsync(async () => {
      let params: CredentialData.type = {
        type: "type",
        identifier: "identifier",
        secret: "secret",
      };
      storageSpy.set = createSpy().and.returnValue(Promise.reject());

      await expectAsync(
        service["saveFailsafeCredentials"](params)
      ).toBeRejected();
    }));

    it("should save credentials to the storage", fakeAsync(async () => {
      let params: CredentialData.type = {
        type: "type",
        identifier: "identifier",
        secret: "secret",
      };
      storageSpy.set = createSpy().and.returnValue(Promise.resolve());

      await expectAsync(
        service["saveFailsafeCredentials"](params)
      ).toBeResolved();
    }));
  });

  describe("deleteFailsafeCredentials", () => {
    it("should failed to delete credentials", fakeAsync(async () => {
      storageSpy.remove = createSpy().and.returnValue(Promise.reject());

      await expectAsync(service["deleteFailsafeCredentials"]()).toBeRejected();
    }));

    it("should delete credentials from storage", fakeAsync(async () => {
      storageSpy.remove = createSpy().and.returnValue(Promise.resolve());

      await expectAsync(service["deleteFailsafeCredentials"]()).toBeResolved();
    }));
  });

  describe("deleteTokenData", () => {
    it("should failed to delete token data", fakeAsync(async () => {
      storageSpy.remove = createSpy().and.returnValue(Promise.reject());

      await expectAsync(service["deleteTokenData"]()).toBeRejected();
    }));

    it("should delete token data from storage", fakeAsync(async () => {
      storageSpy.remove = createSpy().and.returnValue(Promise.resolve());

      await expectAsync(service["deleteTokenData"]()).toBeResolved();
    }));
  });

  it("deleteSession - should delete all session related data and set jwt to default", () => {
    storageSpy.remove = createSpy().and.returnValue(Promise.reject("error"));
    let spy1 = spyOn<any>(service, "setTokenData");

    service.deleteSession();

    expect(storageSpy.remove).toHaveBeenCalledTimes(2);
    expect(spy1).toHaveBeenCalled();
  });
});
