import { Injectable } from "@angular/core";
import { ApiService } from "./api/api.service";
import { ApiResponses } from "./api/api.responses";
import { Observable } from "rxjs";
import { ApiCommands } from "./api/api.commands";
import { ApiParams } from "./api/api.params";
import { map } from "rxjs/operators";

export enum WELCOME_STATUS_FLAG {
  Default = 0,
  Complete = 1,
  Incomplete = 2,
}
@Injectable({
  providedIn: "root",
})
export class WelcomeProfileService {
  constructor(private api: ApiService) {}

  /**
   * Get welcomeProfile data
   */
  public get(): Observable<ApiResponses.WelcomeProfileResponse.type> {
    return this.api.call<ApiResponses.WelcomeProfileResponse.type>({
      command: ApiCommands.WelcomeProfileGet.name,
      method: "get",
      useToken: true,
    });
  }

  /**
   * Get welcomeProfile data
   */
  public save(
    params: ApiParams.WelcomeProfileSave.type
  ): Observable<ApiResponses.ResponseBase.type> {
    return this.api.call<ApiResponses.ResponseBase.type>({
      command: ApiCommands.WelcomeProfilePatch.name,
      method: "patch",
      useToken: true,
      params,
    });
  }

  /**
   * Get welcome profile status
   *
   * @returns Observable<boolean> if tru then the wizard is incomplete, and should be executed
   */
  public getStatus(): Observable<boolean> {
    return this.api
      .call<ApiResponses.WelcomeProfileStatusResponse.type>({
        command: ApiCommands.WelcomeProfileStatusGet.name,
        method: "get",
        useToken: true,
        callType: "background",
      })
      .pipe(
        map((data) => {
          // @todo remove:  overwrite for testing
          // data.data.status = WELCOME_STATUS_FLAG.Incomplete;
          return data?.data.status == WELCOME_STATUS_FLAG.Incomplete;
        })
      );
  }

  /**
   * Get welcomeProfile data
   */
  public saveStatus(
    params: ApiParams.WelcomeProfileStatusSave.type
  ): Observable<ApiResponses.ResponseBase.type> {
    return this.api.call<ApiResponses.ResponseBase.type>({
      command: ApiCommands.WelcomeProfileStatusPatch.name,
      method: "patch",
      useToken: true,
      params,
    });
  }
}
