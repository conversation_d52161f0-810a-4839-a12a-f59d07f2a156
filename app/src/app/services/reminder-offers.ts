import { Injectable } from "@angular/core";
import { StorageService } from "./storage.service";
import { Debug } from "../helpers/debug";
import {
  REMINDER_STORAGE_KEY_BASE,
  ReminderServiceBase,
} from "./reminder-base";
import { OfferHandlerService } from "./offer-handler";
import { DialogHelperService } from "./dialog-helper.service";

/**
 * A service that handles reminders for users that can be dismissed, and repeated on certain event
 */

@Injectable({
  providedIn: "root",
})
export class ReminderServiceOffers extends ReminderServiceBase {
  // repeat period for the type
  protected repeatPeriod: number = 24 * 60 * 60; // one day default

  /**
   * Constructor
   *
   * @param storage
   * @param dbg
   * @param dlg
   */
  public constructor(
    public storage: StorageService,
    public dbg: Debug,
    public dlg: DialogHelperService,
    public offerHandler: OfferHandlerService,
  ) {
    // call parent constructor
    super(storage, dbg, dlg);
    this.storageKey = REMINDER_STORAGE_KEY_BASE + "offer";
  }

  /**
   * is the reminder relevant?
   * @note child classes should overwrite this function and implement logic to
   * check if we need to remind the user at all
   *
   * @return promise resolves if relevant, rejects if not
   */
  public checkIfRelevant(): Promise<any> {
    return new Promise((resolve, reject) => {
      // we fetch the offer
      this.offerHandler
        .fetchOffer()
        .then(() => {
          // check if offer is enabled
          this.offerHandler
            .isOfferEnabled()
            .then(() => {
              resolve(true);
            })
            .catch(() => {
              reject();
            });
        })
        .catch((err) => {
          this.dbg.l("no offers fetched", err);

          this.postponeReminder()
            .then(() => {
              this.dbg.l("offers postponed");
            })
            .catch(() => {
              this.dbg.l("offers postpone - error");
            });

          reject();
        });
    });
  }

  /**
   * show notification dialog
   */
  private showDialog() {
    this.offerHandler
      .fetchOffer()
      .then(() => {
        this.offerHandler.executeOfferAction();
      })
      .catch(() => {
        this.dbg.l("no offers it looks like");
      });
  }

  /**
   * Execute reminder content
   * @note child classes should overwrite this function to implement logic,
   * we should show dialogs, here, or whatever
   * @note child classes should call this parrent function
   *
   * @param forceExecute
   */
  public async execute(forceExecute: boolean = false): Promise<any> {
    await this.init();

    return new Promise((resolve, reject) => {
      super
        .execute()
        .then(() => {
          // we postpone the dialog when shown, so it does not show again if dismissed
          // with back button or some other method

          this.postponeReminder()
            .then(() => {
              // show notification dialog
              this.showDialog();
            })
            .catch((err) => {
              this.dbg.le("postpone on execute", err);
            });

          resolve(true);
        })
        .catch((err) => {
          this.dbg.l("catch", err);
          reject();
        });
    });
  }
}
