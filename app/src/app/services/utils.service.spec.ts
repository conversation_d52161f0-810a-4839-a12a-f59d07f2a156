import { TestBed } from "@angular/core/testing";

import { UtilsService } from "./utils.service";
import { ApiService } from "./api/api.service";

describe("UtilsService", () => {
  let service: UtilsService;
  let apiSpy: ApiService;

  beforeEach(() => {
    apiSpy = jasmine.createSpyObj("ApiService", ["call"]);

    TestBed.configureTestingModule({
      providers: [{ provide: ApiService, useValue: apiSpy }],
    });

    service = TestBed.inject(UtilsService);
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });
});
