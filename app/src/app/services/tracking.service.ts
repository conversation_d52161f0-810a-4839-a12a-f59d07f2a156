import { Injectable, NgZone } from "@angular/core";
import { App, URLOpenListenerEvent } from "@capacitor/app";
import _ from "lodash";
import { BehaviorSubject, Observable } from "rxjs";
import { Debug } from "../helpers/debug";
import { Utils } from "../helpers/utils";
import { AppsflyerHelperService } from "./appsflyer-helper.service";
import { PushNotificationService } from "./push-notification.service";
import { StorageService } from "./storage.service";
import { AnalyticsService } from "./analytics.service";
import { ApiService } from "./api/api.service";
import { PurchaseService } from "./purchase.service";

/**
 * A service for tracking affiliate links and campaign data
 * This is a service that accompanies analytics.service.ts
 */

export const TRACKING_DATA_STORAGE_KEY = "TRACKING_DATA";
export type TrackingDataType =
  | "DEEPLINK"
  | "INSTALL_APPSFLYER"
  | "PUSH"
  | "OTHER"
  | "COMPITLED";
/**
 * Tracking overwrite mode
 * OVERWRITE_CLEAR_ALL - clear all existing data then saves new
 * OVERWRITE_IF_KEY_NOT_EXISTS - overwrite only if key does not exist already, keeps rest
 * OVERWRITE_NEW_KEEP_EXISTING - overwrites existing, adds new, keep existing keys not in data
 */
export type TrackingOverwriteMode =
  | "OVERWRITE_CLEAR_ALL"
  | "OVERWRITE_IF_KEY_NOT_EXISTS"
  | "OVERWRITE_NEW_KEEP_EXISTING";

export type CompiledTrackingData = {
  source: TrackingDataType;
  update_ts: number;
  _campaign?: string;
  utm_campaign?: string;
  utm_content?: string;
  utm_medium?: string;
  utm_source?: string;
  utm_term?: string;
  aff_id?: string;
  aff_pg?: string;
  aff_cp?: string;
  aff_kw?: string;
  aff_src?: string;
  aff_adg?: string;
  track_id?: string;
};

export type TrackingData = {
  update_ts: number;

  // latest session
  // this should always be the source of where the user came from last
  // for example email, just opened the app, or maybe some call to action
  // not sure if we already have this in google analytics?
  session_source?: string;

  DEEPLINK?: any;
  INSTALL_APPSFLYER?: any;
  PUSH?: any;
  OTHER?: any;
  COMPITLED?: CompiledTrackingData;
};

@Injectable({
  providedIn: "root",
})
export class TrackingService {
  private _trackingData$: BehaviorSubject<TrackingData> =
    new BehaviorSubject<TrackingData>({ update_ts: Utils.now() });

  private extractTrackingDataOfImportance(dtType: TrackingDataType | null) {
    if (!dtType || dtType == "OTHER" || dtType == "COMPITLED") {
      this.dbg.l(
        "Did not extract tracking data of importance, meaningless type",
        dtType
      );
      return;
    }

    this.dbg.l("Extract tracking data of importance");

    let _data: CompiledTrackingData = {
      update_ts: Utils.now(),
      source: dtType,
    };

    // save if there's already one - so we don't overwrite it
    // we save the 1st affiliate for sure, if it's not overwritten
    let original_affiliate = this.trackingData.COMPITLED?.aff_id || "";
    /*
        aff_id (string) Affiliate ID
    aff_pg (string) Affiliate program: PPU (Pay Per Upgrade), PPP (Pay Per Profile). If not set the default is PPU
    aff_cp (string) Affiliate campaign
    aff_kw (string) Affiliate keyword
    aff_src (string) Affiliate source
    aff_adg (string) Affiliate adgroup
    track_id (string) Affiliate tracking ID
    */

    if (dtType == "DEEPLINK") {
      _data = {
        update_ts: Utils.now(),
        source: dtType,
        aff_id: this.trackingData.DEEPLINK?.aff_id || "",
        _campaign: this.trackingData.DEEPLINK?.campaign || "",
        utm_campaign: this.trackingData.DEEPLINK?.utm_campaign || "",
        utm_content: this.trackingData.DEEPLINK?.utm_content || "",
        utm_medium: this.trackingData.DEEPLINK?.utm_medium || "",
        utm_source: this.trackingData.DEEPLINK?.utm_source || "",
        utm_term: this.trackingData.DEEPLINK?.utm_term || "",
        aff_pg: this.trackingData.DEEPLINK?.aff_pg || "",
        aff_cp: this.trackingData.DEEPLINK?.aff_cp || "",
        aff_kw: this.trackingData.DEEPLINK?.aff_kw || "",
        aff_src: this.trackingData.DEEPLINK?.aff_src || "",
        aff_adg: this.trackingData.DEEPLINK?.aff_adg || "",
        track_id: this.trackingData.DEEPLINK?.track_id || "",
      };
    } else if (dtType == "INSTALL_APPSFLYER") {
      // https://support.appsflyer.com/hc/en-us/articles/360000726098-Conversion-data-payloads-and-scenarios

      let campaign = this.getFirstExistingKey(
        this.trackingData.INSTALL_APPSFLYER,
        ["utm_campaign", "campaign"]
      );

      // eliminate default None string from appsflyer
      campaign = campaign == "None" ? "" : campaign;

      _data = {
        update_ts: Utils.now(),
        source: dtType,
        aff_id:
          this.getFirstExistingKey(this.trackingData.INSTALL_APPSFLYER, [
            "aff_id",
          ]) || "",
        _campaign: campaign,
        utm_campaign: campaign,
        utm_medium: this.getFirstExistingKey(
          this.trackingData.INSTALL_APPSFLYER,
          ["utm_medium", "media_source", "af_channel"]
        ),
        utm_content: this.getFirstExistingKey(
          this.trackingData.INSTALL_APPSFLYER,
          ["utm_contet"]
        ),
        utm_source: this.getFirstExistingKey(
          this.trackingData.INSTALL_APPSFLYER,
          ["utm_source", "media_source"]
        ),
        utm_term: this.getFirstExistingKey(
          this.trackingData.INSTALL_APPSFLYER,
          ["utm_term"]
        ),
        aff_adg:
          this.getFirstExistingKey(this.trackingData.INSTALL_APPSFLYER, [
            "aff_adg",
          ]) || "",
        aff_cp:
          this.getFirstExistingKey(this.trackingData.INSTALL_APPSFLYER, [
            "aff_cp",
          ]) || "",
        aff_kw:
          this.getFirstExistingKey(this.trackingData.INSTALL_APPSFLYER, [
            "aff_kw",
          ]) || "",
        aff_pg:
          this.getFirstExistingKey(this.trackingData.INSTALL_APPSFLYER, [
            "aff_pg",
          ]) || "",
        aff_src:
          this.getFirstExistingKey(this.trackingData.INSTALL_APPSFLYER, [
            "aff_src",
          ]) || "",
        track_id:
          this.getFirstExistingKey(this.trackingData.INSTALL_APPSFLYER, [
            "track_id",
          ]) || "",
      };
    } else if (dtType == "PUSH") {
      _data = {
        update_ts: Utils.now(),
        source: dtType,
        aff_id: this.trackingData.PUSH?.aff_id || "",
        _campaign: this.trackingData.PUSH?.campaign || "",
        utm_campaign: this.trackingData.PUSH?.utm_campaign || "",
        utm_content: this.trackingData.PUSH?.utm_content || "",
        utm_medium: this.trackingData.PUSH?.utm_medium || "",
        utm_source: this.trackingData.PUSH?.utm_source || "",
        utm_term: this.trackingData.PUSH?.utm_term || "",
        aff_pg: this.trackingData.PUSH?.aff_pg || "",
        aff_cp: this.trackingData.PUSH?.aff_cp || "",
        aff_kw: this.trackingData.PUSH?.aff_kw || "",
        aff_src: this.trackingData.PUSH?.aff_src || "",
        aff_adg: this.trackingData.PUSH?.aff_adg || "",
        track_id: this.trackingData.PUSH?.track_id || "",
      };
    } else {
      this.dbg.l(
        "Did not extract tracking data of importance, no default",
        dtType
      );
      return;
    }

    // no empty affiliate id - use the original one if it exists
    _data.aff_id = _data?.aff_id || original_affiliate;

    // save filtered dtaa
    this.saveEntry("COMPITLED", _data, "OVERWRITE_NEW_KEEP_EXISTING");
  }

  /**
   * Get first existing key
   *
   * @param data
   * @param keys
   * @returns
   */
  private getFirstExistingKey(data: any, keys: string[]) {
    for (let key of keys) {
      if (data[key]) {
        return data[key];
      }
    }

    return "";
  }

  public get trackingData$(): Observable<TrackingData> {
    return this._trackingData$.asObservable();
  }

  /**
   * value of current tracking data
   */
  public get currentData(): TrackingData {
    return this.trackingData;
  }

  /**
   * Return compiled tracking data
   */
  public get compiledData(): CompiledTrackingData {
    return this.trackingData?.COMPITLED || null;
  }

  /**
   * Tracking data - this is the data that will be saved to storage
   */
  private trackingData: TrackingData;
  /**
   * Constructor
   */
  constructor(
    private storage: StorageService,
    private dbg: Debug,
    private appsflyer: AppsflyerHelperService,
    private pushNotificationService: PushNotificationService,
    private analyticsService: AnalyticsService,
    private api: ApiService,
    private purchaseService: PurchaseService,
    private zone: NgZone
  ) {
    this.initTrackingData();
  }

  /**
   * Initialize tracking data
   */
  private initTrackingData() {
    this.trackingData = {
      update_ts: Utils.now(),
    };
  }

  private subscribeToData() {
    // save install attribution from appsflyer

    this.appsflyer.attributionData$.subscribe({
      next: (data) => {
        this.dbg.l("tracking appsflyer attribution data", data);

        if (_.isEmpty(data)) {
          // no data, nothing to do
          return;
        }

        // don't save if the data has not been changed
        if (this.trackingData.INSTALL_APPSFLYER) {
          if (
            this.trackingData.INSTALL_APPSFLYER?.click_time ||
            this.trackingData.INSTALL_APPSFLYER?.install_time
          ) {
            if (
              this.trackingData.INSTALL_APPSFLYER?.click_time ==
                data?.click_time &&
              this.trackingData.INSTALL_APPSFLYER?.install_time ==
                data?.install_time
            ) {
              this.dbg.l(
                "tracking appsflyer attribution data - no change - skip save"
              );

              return;
            }
          }
        }

        // overwrite existing data
        this.saveEntry(
          "INSTALL_APPSFLYER",
          data,
          "OVERWRITE_NEW_KEEP_EXISTING"
        );
      },
    });

    // save push notification data
    this.pushNotificationService.wasPushNotification$.subscribe({
      next: (was) => {
        if (was) {
          this.dbg.l(
            "tracking push notification data",
            this.pushNotificationService.LatestPushData
          );

          // overwrite existing data
          this.saveEntry(
            "PUSH",
            {
              ...this.pushNotificationService.LatestPushData.data,
              ...{
                title: this.pushNotificationService.LatestPushData?.title || "",
                body: this.pushNotificationService.LatestPushData?.body || "",
              },
            },
            "OVERWRITE_CLEAR_ALL"
          );
        }
      },
    });
  }

  public handleUrlTracking(url: string, tdType: TrackingDataType) {
    this.dbg.l("handleUrlTracking", [url, tdType]);

    let urlObj = new URL(url);

    let deeplinkData = {};

    urlObj.searchParams.forEach((v, k) => {
      deeplinkData[k] = v;
    });

    // send data for analytics
    this.analyticsService.emailClick(deeplinkData);

    deeplinkData["_url"] = url;

    // overwrite existing data
    this.saveEntry(tdType, deeplinkData, "OVERWRITE_CLEAR_ALL");
  }

  /**
   * Initialize the service
   */
  public async init() {
    var data = null;

    try {
      data = await this.storage.get(TRACKING_DATA_STORAGE_KEY);
    } catch (e) {
      this.dbg.le("Error getting tracking data from storage", e);
      // fallback
      data = this.trackingData;
    }

    if (data) {
      this.trackingData = data;
    }

    this.subscribeToData();

    // set to analytics
    this.analyticsService.setTrackingData(this.trackingData?.COMPITLED || {});
    this.api.setTrackingData(this.trackingData?.COMPITLED || {});
    this.purchaseService.setTrackingData(this.trackingData || {});

    return Promise.resolve(data);
  }

  /**
   * Save data to local storage
   * @returns
   */
  private save(lastDtype: TrackingDataType | null = null): Promise<any> {
    this.trackingData.update_ts = Utils.now();

    // emmit
    this._trackingData$.next(this.trackingData);

    // extract data

    this.extractTrackingDataOfImportance(lastDtype);

    // set to analytics
    this.analyticsService.setTrackingData(this.trackingData?.COMPITLED || {});
    this.api.setTrackingData(this.trackingData?.COMPITLED || {});

    return this.storage.set(TRACKING_DATA_STORAGE_KEY, this.trackingData);
  }

  /**
   * Save a single entry to the tracking data and storage
   *
   * @param tdType type of tracking data
   * @param overwriteIfExists overwrite if exists
   * @param overwriteIfOlderThan overwrite if older than this timestamp
   * @returns
   */
  public saveEntry(
    tdType: TrackingDataType,
    data: object,
    overwriteMode: TrackingOverwriteMode
  ) {
    var doSet = false;
    var wasChange = false;

    if (overwriteMode == "OVERWRITE_CLEAR_ALL") {
      // overwrite all data, but clear existing data
      doSet = true;
      this.trackingData[tdType] = {};
    }

    // remove empty keys from data
    for (let key in data) {
      if (!data?.[key]) {
        delete data[key];
      }
    }

    // handle data assembling
    for (let key in data) {
      if (overwriteMode == "OVERWRITE_IF_KEY_NOT_EXISTS") {
        // overwrite only if key does not exists, or it's empty
        doSet = !_.has(this.trackingData, `${tdType}.${key}`);
      } else if (overwriteMode == "OVERWRITE_NEW_KEEP_EXISTING") {
        // overwrite all, but keep existing data ( merge - sort of)
        doSet = true;
      }

      if (doSet) {
        if (!this.trackingData[tdType]) {
          this.trackingData[tdType] = {};
        }

        this.trackingData[tdType][key] = data[key];
        this.trackingData[tdType].update_ts = Utils.now();
        wasChange = true;
      }
    }

    if (wasChange) {
      this.save(tdType);
    }
  }
}
