export interface IapItem {
  store_id: string; // store id
  id: number; // dating payment id
  default: boolean; // is it the default item
  price: string; // price string
  currency: string; // price currency
  storeTitle: string; // title grabbed from app store
  storeDescription: string; // description grabbed from app store
  billingPeriod: string; // billing period day, week, etc
  billingPeriodText: string; // billing period day, week but with proper plural
  billingLength: number; // billing period length

  isBestOption: boolean; // is it the best option
  savingsInPercentage: number; // how many percentage is this item cheaper than the most expencieve

  hasTrial: boolean; // does it have trial
  hasFreeTrial: boolean; // does it have FREE trial
  trialPrice?: string; // does it have trial ( if 0 that's free )
  trialPeriod?: string; // week, month, year, etc
  trialPeriodText?: string; // proper plural for week, month, year, etc
  trialLength?: number; // number of periods

  ribbonText: string; // text put on a ribbon, like FREE
  titleText: string; // the title of the item
  periodText: string; // (billed quarterly)
  periodTextSimple: string; // (billed quarterly)
  pricePerDayText: string; // text containing price per day
  description: string; // long description like "Your subscription renews xy usd / billed quarterly. You can cancel at any time etc ..."
  priceAsDecimal: number; // calculate price by deviding it 1000000
  pricePerDay: number; // how much it coses per day

  // extra data

  canPurchase: boolean; // can user purchase the current element
  wasApproved: boolean; // we'll set this to true if the product was approved already, so we can finish the purchase if user tries to buy it again
  transactionData: any; // save transaction data here

  originalProductObject: any; // save original product object here

  // trial page data
  trialPriceText: string;
  trialCallToAction: string;

  // promo page data
  promoPriceText: string;
  promoCallToAction: string;
  promoSubscriptionDetails: string;
}

export type IapItemArray = IapItem[];
