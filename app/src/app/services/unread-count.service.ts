import { Injectable } from "@angular/core";
import { firstValueFrom } from "rxjs";
import { filter, tap } from "rxjs/operators";
import { ApiCommands } from "./api/api.commands";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { ChatService } from "./chat.service";
import { UserUpdatesService } from "./user-updates.service";

@Injectable({
  providedIn: "root",
})
export class UnreadCountService {
  constructor(
    private api: ApiService,
    private chatService: ChatService,
    private userUpdateService: UserUpdatesService
  ) {
    // when the roster changes, we update unread count
    this.chatService.roster$.subscribe({
      next: () => {
        this.countInbox = this.chatService.unreadCount();
      },
    });

    // do the same for updates
    // @todo see if this will be updates on 'mark all as read' api call, or if we need to watch that
    this.userUpdateService.listUpdates.list$.subscribe({
      next: (data) => {
        let cnt = 0;

        // count new updates
        data.forEach((i) => {
          cnt += i.new ? 1 : 0;
        });

        this.countUpdates = cnt;
      },
    });

    // reset update count when we mark them as read

    this.api.eventStream$
      .pipe(
        filter(
          (data) =>
            data.command == ApiCommands.UsersUpdatesMarkAllUpdateRead.name &&
            data.event == "complete"
        )
      )
      .subscribe({
        next: (data) => {
          this.countUpdates = 0;
        },
      });
  }

  public countInbox: number = 0;
  public countUpdates: number = 0;

  /**
   * Sum of unread stuff
   */
  public get countAll(): number {
    return this.countInbox + this.countUpdates;
  }

  /**
   * Has any updates
   */
  public get hasAny() {
    return this.hasInbox || this.hasUpdates;
  }

  /**
   * Has new inbox messages?
   */
  public get hasInbox() {
    return this.countInbox > 0;
  }

  /**
   * Has new updates?
   */
  public get hasUpdates() {
    return this.countUpdates > 0;
  }

  /**
   * Fetch new updates from server
   * @returns
   */
  public get(): Promise<ApiResponses.UnreadCountReponse.type> {
    return firstValueFrom(
      this.api
        .call<ApiResponses.UnreadCountReponse.type>({
          command: ApiCommands.UnreadCount.name,
          useToken: true,
          method: "get",
          commandVersion: "2",
          params: {},
        })
        .pipe(
          tap({
            next: (data) => {
              // set local data
              this.countInbox = data.data.inbox;
              this.countUpdates = data.data.updates;
            },
          })
        )
    );
  }
}
