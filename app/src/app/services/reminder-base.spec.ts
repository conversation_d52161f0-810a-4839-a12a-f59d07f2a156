import { ReminderServiceBase, ReminderServiceItem } from "./reminder-base";
import { Debug } from "../helpers/debug";
import { DialogService } from "./dialog";
import { Storage } from "@ionic/storage";
import { Platform } from "ionic-angular";
import { fakeAsync, tick } from "@angular/core/testing";
import { Utils } from "../helpers/utils";

declare let Promise: any;

describe("ReminderServiceBase test", () => {
  let o: ReminderServiceBase;
  let platform: Platform;
  let storage: Storage;
  let debug: Debug;
  let dialog: DialogService;

  beforeEach(() => {
    platform = new Platform();
    storage = new Storage(null);
    debug = new Debug(platform);
    dialog = new DialogService(null, null, null, null, null, null, null, null);
    o = new ReminderServiceBase(storage, debug, dialog);
  });

  afterEach(() => {
    platform = null;
    storage = null;
    debug = null;
    dialog = null;
    o = null;
  });

  it("should be created", () => {
    expect(o).toBeTruthy();
  });

  it("setRepeatPeriod - should set repeat time in seconds", () => {
    o.setRepeatPeriod();
    expect(o["repeatPeriod"]).toBe(86400);

    o.setRepeatPeriod(2);
    expect(o["repeatPeriod"]).toBe(2);
  });

  it("init - should fail to fetch latest data", fakeAsync(() => {
    let spy1 = spyOn(o, "fetchData").and.returnValue(Promise.reject());
    let spy2 = spyOn(debug, "log");

    o.init();

    tick();

    expect(spy1).toHaveBeenCalledTimes(1);
    expect(spy2).toHaveBeenCalledTimes(1);
  }));

  it("init - should initialize class", fakeAsync(() => {
    let spy1 = spyOn(o, "fetchData").and.returnValue(Promise.resolve());
    let spy2 = spyOn(debug, "log");

    o.init();

    tick();

    expect(spy1).toHaveBeenCalledTimes(1);
    expect(spy2).not.toHaveBeenCalled();
  }));

  it("checkIfRelevant - should resolve", (done) => {
    o.checkIfRelevant()
      .then(() => done())
      .catch(() => done.fail("should not fail"));
  });

  it("checkIfExecute - should force execute the reminder", (done) => {
    let spy = spyOn(debug, "log");

    o.checkIfExecute(true)
      .then(() => {
        expect(spy).toHaveBeenCalledTimes(1);
        expect(spy.calls.mostRecent().args).toEqual(["force exec"]);

        done();
      })
      .catch(() => done.fail("should not fail"));
  });

  it("checkIfExecute - should not execute the reminder when it is disabled", (done) => {
    o["data"] = {
      lastActive: 0,
      isDisabled: true,
    };

    let spy1 = spyOn(debug, "log");
    let spy2 = spyOn(o, "isDisabled").and.returnValue(true);
    let spy3 = spyOn(o, "isTimeToExec").and.returnValue(false);

    o.checkIfExecute(false)
      .then(() => done.fail("should not succeed"))
      .catch(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy1.calls.mostRecent().args).toEqual(["reminder disabled"]);
        expect(spy2).toHaveBeenCalledTimes(2);
        expect(spy3).not.toHaveBeenCalled();

        done();
      });
  });

  it("checkIfExecute - should not execute the reminder if not time to exec", (done) => {
    o["data"] = {
      lastActive: 0,
      isDisabled: false,
    };

    let spy1 = spyOn(debug, "log");
    let spy2 = spyOn(o, "isDisabled").and.returnValue(false);
    let spy3 = spyOn(o, "isTimeToExec").and.returnValue(false);

    o.checkIfExecute()
      .then(() => done.fail("should not succeed"))
      .catch(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy1.calls.mostRecent().args).toEqual(["reminder postponed"]);
        expect(spy2).toHaveBeenCalledTimes(2);
        expect(spy3).toHaveBeenCalledTimes(1);

        done();
      });
  });

  it("checkIfExecute - should not execute the reminder if not relevant", (done) => {
    o["data"] = {
      lastActive: 0,
      isDisabled: false,
    };

    let spy1 = spyOn(debug, "log");
    let spy2 = spyOn(o, "isDisabled").and.returnValue(false);
    let spy3 = spyOn(o, "isTimeToExec").and.returnValue(true);
    let spy4 = spyOn(o, "checkIfRelevant").and.returnValue(Promise.reject());

    o.checkIfExecute()
      .then(() => done.fail("should not succeed"))
      .catch(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy1.calls.mostRecent().args).toEqual(["not relevant"]);
        expect(spy2).toHaveBeenCalledTimes(1);
        expect(spy3).toHaveBeenCalledTimes(1);
        expect(spy4).toHaveBeenCalledTimes(1);

        done();
      });
  });

  it("checkIfExecute - should execute the reminder", (done) => {
    o["data"] = {
      lastActive: 0,
      isDisabled: false,
    };

    let spy1 = spyOn(debug, "log");
    let spy2 = spyOn(o, "isDisabled").and.returnValue(false);
    let spy3 = spyOn(o, "isTimeToExec").and.returnValue(true);
    let spy4 = spyOn(o, "checkIfRelevant").and.returnValue(Promise.resolve());

    o.checkIfExecute()
      .then(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy1.calls.mostRecent().args).toEqual(["ok, show reminder"]);
        expect(spy2).toHaveBeenCalledTimes(1);
        expect(spy3).toHaveBeenCalledTimes(1);
        expect(spy4).toHaveBeenCalledTimes(1);

        done();
      })
      .catch(() => done.fail("should not fail"));
  });

  it("postponeReminder - should fail to fetch data", (done) => {
    let spy1 = spyOn(o, "fetchData").and.returnValue(Promise.reject());
    let spy2 = spyOn(debug, "log");
    let spy3 = spyOn(o, "afterPostpone").and.callThrough();

    o.postponeReminder()
      .then(() => done.fail("should not succeed"))
      .catch(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).toHaveBeenCalledTimes(2);
        expect(spy2.calls.first().args).toEqual([
          "Error postponing reminder reminderService_base",
          undefined,
          "error",
        ]);
        expect(spy2.calls.mostRecent().args).toEqual(["DISMISS"]);
        expect(spy3).toHaveBeenCalledTimes(1);

        done();
      });
  });

  it("postponeReminder - should reject and should fail to save data", (done) => {
    o["data"] = {
      lastActive: 0,
      isDisabled: false,
    };

    let spy1 = spyOn(o, "fetchData").and.returnValue(Promise.resolve());
    let spy2 = spyOn(debug, "log");
    let spy3 = spyOn(o, "afterPostpone").and.callThrough();
    let spy4 = spyOn(o, "saveData").and.returnValue(Promise.reject());

    o.postponeReminder()
      .then(() => done.fail("should not succeed"))
      .catch(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).toHaveBeenCalledTimes(1);
        expect(spy2.calls.mostRecent().args).toEqual(["DISMISS"]);
        expect(spy3).toHaveBeenCalledTimes(1);
        expect(spy4).toHaveBeenCalledTimes(1);

        done();
      });
  });

  it("postponeReminder - should resolve and should save data", (done) => {
    o["data"] = {
      lastActive: 0,
      isDisabled: false,
    };

    let spy1 = spyOn(o, "fetchData").and.returnValue(Promise.resolve());
    let spy2 = spyOn(debug, "log");
    let spy3 = spyOn(o, "afterPostpone").and.callThrough();
    let spy4 = spyOn(o, "saveData").and.returnValue(Promise.resolve());

    o.postponeReminder(true)
      .then(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).toHaveBeenCalledTimes(1);
        expect(spy2.calls.mostRecent().args).toEqual(["DISMISS"]);
        expect(spy3).toHaveBeenCalledTimes(1);
        expect(spy4).toHaveBeenCalledTimes(1);

        done();
      })
      .catch(() => done.fail("should not fail"));
  });

  it("disableReminder - should fail to fetch data", (done) => {
    let spy1 = spyOn(o, "fetchData").and.returnValue(Promise.reject());
    let spy2 = spyOn(debug, "log");
    let spy3 = spyOn(o, "afterDisable").and.callThrough();

    o.disableReminder()
      .then(() => done.fail("should not succeed"))
      .catch(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).toHaveBeenCalledTimes(2);
        expect(spy2.calls.first().args).toEqual([
          "Error disabling reminder reminderService_base",
          undefined,
          "error",
        ]);
        expect(spy2.calls.mostRecent().args).toEqual(["DISABLED"]);
        expect(spy3).toHaveBeenCalledTimes(1);

        done();
      });
  });

  it("disableReminder - should fail to save data", (done) => {
    o["data"] = {
      lastActive: 0,
      isDisabled: false,
    };

    let spy1 = spyOn(o, "fetchData").and.returnValue(Promise.resolve());
    let spy2 = spyOn(debug, "log");
    let spy3 = spyOn(o, "afterDisable").and.callThrough();
    let spy4 = spyOn(o, "saveData").and.returnValue(Promise.reject());

    o.disableReminder()
      .then(() => done.fail("should not succeed"))
      .catch(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).toHaveBeenCalledTimes(1);
        expect(spy2.calls.mostRecent().args).toEqual(["DISABLED"]);
        expect(spy3).toHaveBeenCalledTimes(1);
        expect(spy4).toHaveBeenCalledTimes(1);

        done();
      });
  });

  it("disableReminder - should resolve", (done) => {
    o["data"] = {
      lastActive: 0,
      isDisabled: false,
    };

    let spy1 = spyOn(o, "fetchData").and.returnValue(Promise.resolve());
    let spy2 = spyOn(debug, "log");
    let spy3 = spyOn(o, "afterDisable").and.callThrough();
    let spy4 = spyOn(o, "saveData").and.returnValue(Promise.resolve());

    o.disableReminder()
      .then(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).toHaveBeenCalledTimes(1);
        expect(spy2.calls.mostRecent().args).toEqual(["DISABLED"]);
        expect(spy3).toHaveBeenCalledTimes(1);
        expect(spy4).toHaveBeenCalledTimes(1);

        done();
      })
      .catch(() => done.fail("should not fail"));
  });

  it("execute - should fail to execute when checkIfExecute reject", (done) => {
    o["data"] = {
      lastActive: 0,
      isDisabled: false,
    };

    let spy1 = spyOn(o, "checkIfExecute").and.returnValue(Promise.reject());
    let spy2 = spyOn(debug, "log");

    o.execute()
      .then(() => done.fail("should not succeed"))
      .catch(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).toHaveBeenCalledTimes(2);
        expect(spy2.calls.first().args).toEqual(["reminder exec fn"]);
        expect(spy2.calls.mostRecent().args).toEqual(["EXEC FAILED"]);

        done();
      });
  });

  it("execute - should fail to save data", (done) => {
    o["data"] = {
      lastActive: 0,
      isDisabled: false,
    };

    let spy1 = spyOn(o, "checkIfExecute").and.returnValue(Promise.resolve());
    let spy2 = spyOn(debug, "log");
    let spy3 = spyOn(o, "saveData").and.returnValue(Promise.reject());

    o.execute()
      .then(() => done.fail("should not succeed"))
      .catch(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).toHaveBeenCalledTimes(2);
        expect(spy2.calls.first().args).toEqual(["reminder exec fn"]);
        expect(spy2.calls.mostRecent().args).toEqual(["EXEC FAILED"]);
        expect(spy3).toHaveBeenCalledTimes(1);

        done();
      });
  });

  it("execute - should succeed", (done) => {
    o["data"] = {
      lastActive: 0,
      isDisabled: false,
    };

    let spy1 = spyOn(o, "checkIfExecute").and.returnValue(Promise.resolve());
    let spy2 = spyOn(debug, "log");
    let spy3 = spyOn(o, "saveData").and.returnValue(Promise.resolve());

    o.execute(true)
      .then(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).toHaveBeenCalledTimes(2);
        expect(spy2.calls.first().args).toEqual(["reminder exec fn"]);
        expect(spy2.calls.mostRecent().args).toEqual(["EXEC OK"]);
        expect(spy3).toHaveBeenCalledTimes(1);

        done();
      })
      .catch(() => done.fail("should not fail"));
  });

  it("isDisabled - should return false if data not set", () => {
    expect(o["isDisabled"]()).toBeFalsy();
  });

  it("isDisabled - should return false if isDisabled not set", () => {
    o["data"] = {
      lastActive: 0,
      isDisabled: null,
    };

    expect(o["isDisabled"]()).toBeFalsy();
  });

  it("isDisabled - should check is reminder disabled and return false", () => {
    o["data"] = {
      lastActive: 0,
      isDisabled: false,
    };

    expect(o["isDisabled"]()).toBeFalsy();
  });

  it("isDisabled - should check is reminder disabled and return true", () => {
    o["data"] = {
      lastActive: 0,
      isDisabled: true,
    };

    expect(o["isDisabled"]()).toBeTruthy();
  });

  it("isTimeToExec - should return true if data not set", () => {
    o.setRepeatPeriod(60 * 3);

    expect(o["isTimeToExec"]()).toBeTruthy();
  });

  it("isTimeToExec - should check is it time to execute the reminder and return false", () => {
    o.setRepeatPeriod(60 * 3);

    o["data"] = {
      lastActive: Utils.now() + 2,
      isDisabled: false,
    };

    expect(o["isTimeToExec"]()).toBeFalsy();
  });

  it("isTimeToExec - should check is it time to execute the reminder and return true", () => {
    o.setRepeatPeriod(60 * 3);
    o["data"] = {
      lastActive: Utils.now() - 60 * 4,
      isDisabled: false,
    };

    expect(o["isTimeToExec"]()).toBeTruthy();
  });

  it("saveData - should save data", () => {
    let spy = spyOn(storage, "set");

    o["saveData"]();

    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy.calls.mostRecent().args).toEqual(["reminderService_base", null]);
  });

  it("fetchData - should fail to fetch data", (done) => {
    let spy1 = spyOn(storage, "get").and.returnValue(Promise.reject());
    let spy2 = spyOn(debug, "log");

    o["fetchData"]()
      .then(() => done.fail("should not succeed"))
      .catch(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).toHaveBeenCalledTimes(1);
        expect(spy2.calls.mostRecent().args).toEqual([
          "Error fetching data for reminder reminderService_base",
          undefined,
          "error",
        ]);

        done();
      });
  });

  it("fetchData - should save and return data", (done) => {
    let spy1 = spyOn(storage, "get").and.returnValue(Promise.resolve(null));
    let spy2 = spyOn(o, "saveData").and.returnValue(Promise.resolve());

    o["fetchData"]()
      .then(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).toHaveBeenCalledTimes(1);

        done();
      })
      .catch(() => done.fail("should not fail"));
  });

  it("fetchData - should fetch data", (done) => {
    let data: ReminderServiceItem = {
      isDisabled: true,
      lastActive: 0,
    };
    let spy1 = spyOn(storage, "get").and.returnValue(Promise.resolve(data));
    let spy2 = spyOn(o, "saveData");

    o["fetchData"]()
      .then(() => {
        expect(spy1).toHaveBeenCalledTimes(1);
        expect(spy2).not.toHaveBeenCalled();

        done();
      })
      .catch(() => done.fail("should not fail"));
  });
});
