import { Injectable } from "@angular/core";
import { ApiService } from "./api/api.service";
import { Observable } from "rxjs";
import { ApiParams } from "./api/api.params";
import { ApiCommands } from "./api/api.commands";
import { ApiResponses } from "./api/api.responses";
import { tap } from "rxjs/operators";
import { PhotoCommentBase } from "../schemas/photo-comment";

@Injectable({
  providedIn: "root",
})
export class PhotoService {
  public photoComments: PhotoCommentBase.type;

  constructor(public api: ApiService) {}

  public isAddCommentInProgress(): boolean {
    return this.api.isInEventList({
      command: ApiCommands.PhotoCommentPost.name,
      method: "post",
    });
  }

  public isDeleteCommentInProgress(): boolean {
    return this.api.isInEventList({
      command: ApiCommands.PhotoCommentDelete.name,
      method: "delete",
    });
  }

  public isGetCommentsInProgress(): boolean {
    return this.api.isInEventList({
      command: ApiCommands.PhotoCommentGet.name,
      method: "get",
    });
  }

  /**
   * Get comments list.
   *
   * @param {ApiParams.PhotoCommentGetParams} params
   * @return {Promise<ApiResponses.PhotoCommentGetResponse>}
   */
  public getComments(
    params: ApiParams.PhotoCommentGetParams.type
  ): Observable<ApiResponses.PhotoCommentGetResponse.type> {
    return this.api
      .call<ApiResponses.PhotoCommentGetResponse.type>({
        command: ApiCommands.PhotoCommentGet.name,
        method: "get",
        useToken: true,
        params,
      })
      .pipe(
        tap(async (data) => {
          // keep data
          this.photoComments = data.data;
        })
      );
  }

  /**
   * Add new comment.
   *
   * @param {ApiParams.PhotoCommentPostParams} params
   * @return {Promise<ApiResponses.PhotoCommentPostResponse>}
   */
  public addComment(
    params: ApiParams.PhotoCommentPostParams.type
  ): Observable<ApiResponses.PhotoCommentPostResponse.type> {
    return this.api.call<ApiResponses.PhotoCommentPostResponse.type>({
      command: ApiCommands.PhotoCommentPost.name,
      method: "post",
      useToken: true,
      params,
    });
  }

  /**
   * Delete a comment.
   *
   * @param {ApiParams.PhotoCommentDeleteParams} params
   * @return {Promise<ApiResponses.NoDataResponse>}
   */
  public deleteComment(
    params: ApiParams.PhotoCommentDeleteParams.type
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api.call<ApiResponses.NoDataResponse.type>({
      command: ApiCommands.PhotoCommentDelete.name,
      method: "delete",
      useToken: true,
      params,
    });
  }

  /**
   * Like photo.
   *
   * @param {ApiParams.PhotoLikeParams} params
   * @return {Promise<ApiResponses.NoDataResponse>}
   */
  public like(
    params: ApiParams.PhotoLikeParams.type
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api.call<ApiResponses.NoDataResponse.type>({
      command: ApiCommands.PhotoLike.name,
      method: "post",
      useToken: true,
      params,
    });
  }

  /**
   * Unlike photo.
   *
   * @param {ApiParams.PhotoLikeParams} params
   * @return {Promise<ApiResponses.NoDataResponse>}
   */
  public unlike(
    params: ApiParams.PhotoLikeParams.type
  ): Observable<ApiResponses.NoDataResponse.type> {
    return this.api.call<ApiResponses.NoDataResponse.type>({
      command: ApiCommands.PhotoLike.name,
      method: "delete",
      useToken: true,
      params,
    });
  }
}
