import { Injectable } from "@angular/core";
import {
  BehaviorSubject,
  filter,
  firstValueFrom,
  forkJoin,
  Observable,
  Subject,
  Subscription,
} from "rxjs";
import _ from "lodash";
import { map, tap } from "rxjs/operators";
import { AccountProduct } from "../schemas/account-product";
import { ApiCommands } from "./api/api.commands";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { ProfileOwn } from "../schemas/profile-own";
import { AccountSettings } from "../schemas/account-settings";
import { ConsentRequirementType } from "../schemas/utils";
import {
  Description,
  DetailsForm,
  Interests,
  Question,
} from "../schemas/account-details-form";
import { Debug } from "../helpers/debug";
import { ProfileService } from "./profile.service";
import { DeviceInfoService } from "./device-info.service";
import { IBootAction } from "./boot.service";
import { MatchPref } from "../schemas/match-pref";
import { TranslateService } from "@ngx-translate/core";
import { AccountInterestOptions } from "../schemas/account-interest-options";
import { environment as Env } from "src/environments/environment";
import { ProfileBaseDetails } from "../schemas/profile-base-details";
import { Refreshable } from "./refresher.service";
import { RejectedPhoto } from "../schemas/rejected-photo";
import { Utils } from "../helpers/utils";

@Injectable({
  providedIn: "root",
})
export class AccountService implements IBootAction, Refreshable {
  /**
   * Account data (OwnProfile).
   */
  public account: ProfileOwn.type = null;

  /**
   * Account settings.
   */
  public settings: AccountSettings.type = null;

  /**
   * Match pref container
   */
  public matchPref: MatchPref.type = null;

  public rejectedPhotos: Array<RejectedPhoto.type> = [];

  private tmpDetailsForm:
    | DetailsForm.type[]
    | Interests.type[]
    | Description.type[] = null;

  /**
   * Details form.
   */
  public detailsForm:
    | DetailsForm.type[]
    | Interests.type[]
    | Description.type[] = null;

  /**
   * Interests.
   */
  public interests: AccountInterestOptions.type = {
    data: null,
    page_title: null,
    interests: null,
  };

  /**
   * Backup details-from data.
   */
  private bckDetailsForm: string;

  /**
   * Backup account settings.
   */
  private _bckSettings: string;

  /**
   * Tags.
   */
  public tags: ApiResponses.AccountTagsResponse.type["data"];
  public utils = Utils;

  public profileVerificationData: ApiResponses.ProfileVerificationResponse.type["data"];
  public profileVerificationAddData: ApiResponses.ProfileVerificationAddResponse.type["data"];

  constructor(
    public api: ApiService,
    public debug: Debug,
    public profile: ProfileService,
    public deviceInfo: DeviceInfoService,
    public translate: TranslateService,
  ) {}

  public async boot(): Promise<any> {
    await firstValueFrom(this.getInterestOptions());
    await firstValueFrom(this.getDetailsForm());
    await firstValueFrom(this.get());
    await firstValueFrom(this.getSettings());
    await firstValueFrom(this.getMatchPref());
    return Promise.resolve();
  }

  public async refreshData(): Promise<any> {
    return await this.boot();
  }

  /**
   * Check if the username is the logged in user's
   * @param username
   * @returns
   */
  public isOwnProfile(username) {
    return username == this.account.username;
  }

  // values to cache store latest ccpa statuses
  private _ccpaStatus: boolean = true;
  private _ccpaRequired: boolean = false;

  /**
   * CCPA status - if true, that means that user gave permission unde ccpa
   */
  get CCPAStatus(): boolean {
    return this._ccpaStatus;
  }

  set CCPAStatus(val: boolean) {
    this._ccpaStatus = val;
  }

  /**
   * CCPA required - do we have to get user's permission
   */
  get isCCPARequired(): boolean {
    return this._ccpaRequired;
  }

  /**
   * Get ccpa status from server
   * @note the value is TRUE if user gave consent to use personal data, if the user denied it's false
   */
  public getCCPAStatus(): Observable<boolean> {
    return this.api
      .call<ApiResponses.GetCCPAStatusResponse.type>({
        command: ApiCommands.AccountCCPAGet.name,
        method: "get",
        useToken: true,
      })
      .pipe(
        tap({
          next: (data) => {
            // cache value
            this.CCPAStatus = data.data.value;
          },
        }),
        map((data) => {
          // transform value
          return data.data.value;
        }),
      );
  }

  /**
   * Get value to know if ccpa is required for given user
   */
  public getCCPARequired(): Observable<boolean> {
    return this.api
      .call<ApiResponses.CCPAResponseRequired.type>({
        command: ApiCommands.AccountCCPARequired.name,
        method: "get",
        useToken: true,
      })
      .pipe(
        tap({
          next: (data) => {
            // cache value
            this._ccpaRequired = data.data.required;
          },
        }),
        map((data) => {
          // transform value
          return data.data.required;
        }),
      );
  }

  /**
   * Get product for a specific campaign
   * @param campaignId
   * @returns
   */
  public getCampaignProduct(params): Promise<AccountProduct.type> {
    return firstValueFrom(
      this.api
        .call<ApiResponses.ProductItem.type>({
          command: ApiCommands.Campaign.name,
          method: "get",
          useToken: true,
          params: params,
          commandVersion: "2",
        })
        .pipe(
          map((data) => {
            return data.data;
          }),
        ),
    );
  }

  /**
   * get products for the account
   */
  public getProducts(
    params: ApiParams.ProductParams.type,
  ): Observable<AccountProduct.type[]> {
    return this.api
      .call<ApiResponses.ProductItems.type>({
        command: ApiCommands.AccountProducts.name,
        method: "get",
        useToken: true,
        params: params,
      })
      .pipe(
        map((data) => {
          return data.data.items;
        }),
      );
  }

  /**
   * Update ccpa status
   */
  public updateCCPAStatus(
    status: boolean = null,
  ): Observable<ApiResponses.CCPAStatusResponse.type> {
    // send api call
    return this.api
      .call<ApiResponses.CCPAStatusResponse.type>({
        command: ApiCommands.AccountCCPAPatch.name,
        method: "patch",
        useToken: true,
        params: <ApiParams.CCPAStatusUpdate.type>{
          value: status !== null ? status : this.CCPAStatus,
        },
      })
      .pipe(tap(async ({ data }) => (this.CCPAStatus = data.current)));
  }

  /**
   * Is AccountCCPAPatch in progress?
   */
  public isCCPAInProgress(): boolean {
    return this.api.isInEventList({
      command: ApiCommands.AccountCCPAPatch.name,
      method: "patch",
    });
  }

  /**
   * Get account settings.
   */
  public getSettings(): Observable<ApiResponses.AccountSettingsResponse.type> {
    return this.api
      .call<ApiResponses.AccountSettingsResponse.type>({
        command: ApiCommands.AccountSettings.name,
        method: "get",
        useToken: true,
      })
      .pipe(
        tap({
          next: (data) => {
            // keep data
            this.settings = data.data;
            this._bckSettings = JSON.stringify(this.settings);
          },
        }),
      );
  }

  /**
   * Save account settings.
   */
  public saveSettings(): Subscription {
    return this.api
      .call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.AccountSettings.name,
        method: "post",
        useToken: true,
        params: <ApiParams.AccountSettings.type>this.settings,
      })
      .subscribe();
  }

  /**
   * Get details form.
   *
   * @return {Observable<ApiResponses.AccountDetailsFormResponse.type>}
   */
  public getDetailsForm(): Observable<ApiResponses.AccountDetailsFormResponse.type> {
    return this.api
      .call<ApiResponses.AccountDetailsFormResponse.type>({
        command: ApiCommands.AccountDetailsForm.name,
        method: "get",
        useToken: true,
      })
      .pipe(
        tap((data) => {
          // keep data
          this.detailsForm = data.data;
          this.removeFromDetailsFormIfNoQuestions();
        }),
      );
  }

  private removeFromDetailsFormIfNoQuestions() {
    this.detailsForm.forEach((val, i) => {
      // Remove group if no questions.
      if (!val.questions.length) {
        this.detailsForm.splice(i, 1);
      }
    });
  }

  /**
   * Get interest options.
   *
   * @return {Promise<ApiResponses.AccountInterestOptionsResponse>}
   */
  public getInterestOptions(): Observable<ApiResponses.AccountInterestOptionsResponse.type> {
    return this.api
      .call<ApiResponses.AccountInterestOptionsResponse.type>({
        command: ApiCommands.AccountInterestOptions.name,
        method: "get",
        useToken: true,
      })
      .pipe(
        tap((data) => {
          // keep data
          this.interests = data.data;
        }),
      );
  }

  /**
   * Get DetailsForm.
   *
   * @return {DetailsForm[]}
   */
  public details(): DetailsForm.type[] {
    let res: DetailsForm.type[] = [];

    this.detailsForm.forEach((val) => {
      if (["description"].indexOf(val.group_id) <= -1) {
        res.push(<DetailsForm.type>val);
      }
    });

    return res;
  }

  private _accountChanges$: BehaviorSubject<ProfileOwn.type> =
    new BehaviorSubject(null);

  get accountChanges$(): Observable<ProfileOwn.type> {
    return this._accountChanges$.asObservable();
  }

  private _accountLoaded$: BehaviorSubject<boolean> = new BehaviorSubject(
    false,
  );

  get accountLoaded$(): Observable<boolean> {
    return this._accountLoaded$.asObservable();
  }

  /**
   * Get the authenticated user's profile.
   */
  public get() {
    return this.api
      .call<ApiResponses.OwnProfileResponse.type>({
        command: ApiCommands.AccountGet.name,
        method: "get",
        useToken: true,
      })
      .pipe(
        tap(async (data) => {
          // keep data

          this.account = data.data;

          await this.mergeDetailsFormWithProfileDetails();
          await this.preselectDetailsFormSliderAnswers();

          this.bckDetailsForm = JSON.stringify(this.detailsForm);
          this._accountChanges$.next(this.account);
          this._accountLoaded$.next(true);
        }),
      );
  }

  /**
   * Get Rejecetd Photos List
   */
  public getRejectedPhotos() {
    //: Observable<AccountProduct.type[]>
    this.rejectedPhotos = [];
    firstValueFrom(
      this.api.call<ApiResponses.RejectedPhotosResponse.type>({
        command: ApiCommands.PhotosRejected.name,
        method: "get",
        useToken: true,
      }),
    )
      .then((res) => {
        if (res.data["rejected_photos"]) {
          res.data["rejected_photos"].forEach((value) => {
            this.rejectedPhotos.push(value);
          });
        }
      })
      .catch((err) => {
        this.debug.le("Error occurred while fetching Rejected Photos", err);
        this.rejectedPhotos = [];
      });
  }

  public getNewGeolocation() {
    return this.api
      .call<ApiResponses.OwnProfileResponse.type>({
        command: ApiCommands.AccountGet.name,
        method: "get",
        useToken: true,
      })
      .pipe(
        tap(async (data) => {
          // keep data
          this.account.city = data.data.city;
          this.account.country = data.data.country;
          this.account.zip = data.data.zip;

          this._accountChanges$.next(this.account);
        }),
      );
  }

  // cached valud for consent requirements, with some defaults

  private consentRequirementCache: ConsentRequirementType.type = {
    offers_consent: true,
    required: true,
  };

  /**
   * Get consent requirements cache
   */
  get consentRequirement(): ConsentRequirementType.type {
    return this.consentRequirementCache;
  }

  /**
   * Get consent requirements from server
   *
   * @returns
   */
  public getConsentRequirements(): Observable<ConsentRequirementType.type> {
    return this.api
      .call<ApiResponses.ConsentRequirement.type>({
        command: ApiCommands.ConsentRequired.name,
        method: "get",
        useToken: false,
      })
      .pipe(
        tap((data) => {
          // cache consent data
          this.consentRequirementCache = data.data;
        }),
        map((data) => {
          return data.data;
        }),
      );
  }

  /**
   * Is a premium member.
   *
   * @return {boolean}
   */
  public isPremium(): boolean {
    return this.account.is_premium;
  }

  /**
   * Is mobile registration?
   *
   * @return {boolean}
   */
  public isMobileRegistration(): boolean {
    return this.account.mobile_registration;
  }

  /**
   * Is web registration?
   *
   * @return {boolean}
   */
  public isWebRegistration(): boolean {
    return !this.account.mobile_registration;
  }

  /**
   * Get the user’s match/contact preferences.
   *
   * @returns {Promise<ApiResponses.MatchPrefResponse>}
   */
  public getMatchPref(): Observable<ApiResponses.MatchPrefResponse.type> {
    return this.api
      .call<ApiResponses.MatchPrefResponse.type>({
        command: ApiCommands.AccountGetMatchPref.name,
        method: "get",
        useToken: true,
      })
      .pipe(
        tap((data) => {
          this.matchPref = data.data;
          // Fix for the empty genders list! dvip side
          // genders ⇒ [0 => '']
          this.matchPref.genders = this.matchPref.genders.filter((e) => e);
        }),
      );
  }

  public getTags(): Observable<ApiResponses.AccountTagsResponse.type> {
    return this.api
      .call<ApiResponses.AccountTagsResponse.type>({
        command: ApiCommands.AccountGetTags.name,
        method: "get",
        useToken: true,
      })
      .pipe(
        tap((data) => {
          this.tags = data.data;
        }),
      );
  }

  public enableTag(id: string) {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.AccountEnableTag.name,
        method: "post",
        useToken: true,
        params: { id },
      }),
    );
  }

  public disableTag(id: string) {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.AccountDisableTag.name,
        method: "post",
        useToken: true,
        params: { id },
      }),
    );
  }

  /**
   * Update match pref.

   * @param {ApiParams.AccountMatchPref.type} params
   * @returns {Promise<ApiResponses.MatchPrefResponse>}
   */
  public updateMatchPref(
    params: ApiParams.AccountMatchPref.type,
  ): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.AccountPostMatchPref.name,
        method: "post",
        useToken: true,
        params,
      }),
    );
  }

  /**
   * Update push notification token on server side
   *
   * @param pushToken
   * @returns
   */
  public updatePushFirebaseEndpoint(
    endpoint: string,
    user_id: number,
  ): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.AccountPushFirebaseEndPoint.name,
        method: "post",
        useToken: true,
        params: <ApiParams.AccountPushFirebaseEndPoint.type>{
          endpoint: endpoint,
          firebase_token: endpoint,
          user_id: user_id,
          device_uuid: this.deviceInfo.uuid,
        },
      }),
    );
  }

  /**
   * Update push notification token on server side
   *
   * @param pushToken
   * @returns
   */
  public _deprecated_updatePushEndpoint(
    pushToken: string,
  ): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.AccountPushEndPoint.name,
        method: "post",
        useToken: true,
        params: <ApiParams.AccountPushEndPoint.type>{
          device_uuid: this.deviceInfo.uuid,
          push_token: pushToken,
        },
      }),
    );
  }

  /**
   * Update account data.
   *
   * @param {ApiParams.AccountUpdate.type} params
   * @returns {Promise<ApiResponses.OwnProfileResponse.type>}
   */
  public update(
    params: ApiParams.AccountUpdate.type,
  ): Promise<ApiResponses.OwnProfileResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.OwnProfileResponse.type>({
        command: ApiCommands.AccountPatch.name,
        method: "patch",
        useToken: true,
        params,
      }),
    );
  }

  /**
   * Deactivate user account.
   *
   * @param {ApiParams.AccountDeactivate.type} params
   * @returns {Promise<ApiResponses.NoDataResponse.type>}
   */
  public deactivate(
    params: ApiParams.AccountDeactivate.type,
  ): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.AccountDeactivate.name,
        method: "delete",
        useToken: true,
        params,
      }),
    );
  }

  /**
   * Delete user account.
   *
   * @returns {Promise<ApiResponses.NoDataResponse.type>}
   */
  public delete(): Promise<ApiResponses.NoDataResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.AccountDelete.name,
        method: "delete",
        useToken: true,
      }),
    );
  }

  /**
   * Set account location manually.
   *
   * @param data
   */
  public setLocation(data) {
    this.account.country = data.country;
    this.account.city = data.city;
    this.account.zip = data.zip;
  }

  /**
   * Update user’s location based on IP address.
   *
   * @returns {Promise<ApiResponses.AccountGeolocationResponse.type>}
   */
  public geolocation(): Promise<ApiResponses.AccountGeolocationResponse.type> {
    return firstValueFrom(
      this.api.call<ApiResponses.AccountGeolocationResponse.type>({
        method: "post",
        command: ApiCommands.AccountGeolocation.name,
        useToken: true,
        commandVersion: "2",
      }),
    );
  }

  /**
   * Merge details form with profile details.
   *
   * @return {Promise<any>}
   */
  private async mergeDetailsFormWithProfileDetails(): Promise<void> {
    this.setupInterests();
    this.pushInterestsToDetailsFormQuestions();
    this.setAccAnswersToDetailsForm();
    this.setDetailsFormSliderData();
    this.setDetailsFormSelectAndCheckData();
  }

  private setupInterests(): void {
    // Set 'asnwers' key to interests data.
    this.interests["answers"] = [];
    // Set 'group_name' key to interests data.
    this.interests["group_name"] = this.interests.page_title;
    Object.keys(this.interests.interests).forEach((key) => {
      this.interests["answers"].push({
        key,
        data: this.interests.interests[key],
        checked: this.interests.data && this.interests.data.indexOf(key) > -1,
      });
    });
  }

  private pushInterestsToDetailsFormQuestions(): void {
    if (!this.detailsForm.length) return;

    this.detailsForm.forEach((df) => {
      if (df.group_id === "interests") {
        df.questions.push(this.interests);
      }
    });
  }

  private setAccAnswersToDetailsForm(): void {
    // Loop details group.
    this.detailsForm.forEach((df) => {
      Object.values(this.account.details).forEach((accDetails) => {
        // Match detailsForm group with acc details to see if we have answers.
        if (df.group_name.includes(accDetails.title)) {
          // We don't have questions.
          if (!df.questions) return;

          // Loop details group questions.
          df.questions.forEach((question, index) => {
            // Set 'selectedAnswers' key.
            question["selectedAnswers"];
            // Loop acc details data.
            accDetails.data.forEach((accDetailsGroup) => {
              // Return if detailsForm and account details do not match.
              if (
                !accDetailsGroup.question_key ||
                accDetailsGroup.question_key != question.key
              )
                return;

              // If no answers in detailsForm question group.
              if (!question.answers) {
                // Set 'answer' key and assign value from acc details.
                question["answer"] = this.utils.stripHtml(
                  accDetailsGroup.answer,
                );
              } else {
                // Loop detailsForm question group answers.
                question.answers.forEach((answer, i) => {
                  // Set checked to false as default.
                  answer.checked = false;
                  // Split answers from profile data.
                  let tmpAnswers: string[] = accDetailsGroup.answer.split(",");
                  // Trim entire array.
                  tmpAnswers = tmpAnswers.map((val: string) => val.trim());

                  // If we have answer data, set checked to true.
                  if (tmpAnswers.indexOf(answer.data) > -1) {
                    answer.checked = true;
                    // Set 'selectedAnswers' key and assign value.
                    question["selectedAnswers"] = answer;
                    return;
                  }
                });
              }
            });
          });
        }
      });
    });
  }

  private setDetailsFormSliderData(): void {
    this.detailsForm.forEach((group) => {
      if (!group.questions.length) return;

      group.questions.forEach((question) => {
        // Set 'selectedAnswers' key.
        if (!question["selectedAnswers"]) question["selectedAnswers"];

        // Multiselect true.
        if (question.type === "slider" && question.multiselect === "1") {
          if (question.answers.length) {
            question["range_min"] = 0;
            question["range_max"] = question.answers.length - 1;
            question["range_step"] = 1;
            let checkedAnswers: any[] = [];
            question.answers.forEach((answer, i) => {
              if (answer.checked) {
                checkedAnswers.push({ value: answer.data, index: i });
              }
            });

            // fix for multiselect range slider
            if (!checkedAnswers.length) {
              if (question.answers.length) {
                checkedAnswers.push({
                  value: question.answers[0]?.data,
                  index: 0,
                });
                checkedAnswers.push({
                  value: question.answers[question.answers.length - 1]?.data,
                  index: question.answers.length - 1,
                });
              }
            }

            if (checkedAnswers.length > 1) {
              question["rangeValueMin"] = Number(checkedAnswers[0]?.value);
              question["rangeValueMax"] = Number(checkedAnswers[1]?.value);
              question["range_value_min"] = checkedAnswers[0]?.index;
              question["range_value_max"] = checkedAnswers[1]?.index;
            } else {
              question["rangeValueMin"] = Number(checkedAnswers[0]?.value);
              question["rangeValueMax"] = Number(checkedAnswers[0]?.value);
              question["range_value_min"] = checkedAnswers[0]?.index;
              question["range_value_max"] = checkedAnswers[0]?.index;
            }
          } else {
            if (question.value_max > "0") {
              question["range_min"] = question.value_min;
              question["range_max"] = question.value_max;
              question["range_value_min"] = question.value_min;
              question["range_value_max"] = question.value_max;
              question["range_step"] = 1;
              question["rangeValueMin"] = Number(question.value_min);
              question["rangeValueMax"] = Number(question.value_max);
            }
          }
        }

        // Multiselect false.
        if (question.type === "slider" && question.multiselect === "0") {
          if (question.answers.length) {
            question["range_min"] = 0;
            question["range_max"] = question.answers.length - 1;
            question["range_step"] = 1;
            question["rangeValueMin"] = question.answers[0].data;
            question["answer"] = [question.answers[0].key];
            // check for the range slider
            question.answers.forEach((answer, i) => {
              if (answer.checked) {
                question["answer"] = [answer.key];
                question["rangeValueMin"] = answer.data;
                question["range_step_value"] = i;
              }
            });
          } else {
            if (question.value_max > "0") {
              question["range_min"] = question.value_min;
              question["range_min"] = question.value_max;
              question["range_step"] = 1;
              question["rangeValueMin"] = question.value_min;
              question["answer"] = [question.value_min];
            }
          }
        }
      });
    });
  }

  private setDetailsFormSelectAndCheckData(): void {
    this.detailsForm.forEach((df) => {
      df.questions.forEach((question) => {
        if (question.type !== "select" && question.type !== "check") return;
        if (!question.answers) return;

        let answer = {
          key: 0,
          data: this.translate.instant("ill_tell_you_later"),
          checked: !question.answers.some((answer) => answer.checked),
        };

        if (
          question.type === "select" &&
          question.answers[0] &&
          question.answers[0].data !==
            this.translate.instant("ill_tell_you_later")
        ) {
          question.answers.unshift(answer);
        }

        if (answer.checked) {
          question["selectedAnswers"] = answer;
        }
      });
    });
  }

  /**
   * Details-form preselect slider answers.
   */
  private preselectDetailsFormSliderAnswers(): void {
    this.detailsForm.forEach((dfGroup) => {
      dfGroup.questions.forEach((question) => {
        if (question.type !== "slider") return;

        if (this.isMultiselectQuestion(question)) {
          question["answer"] = [];
          if (this.hasAnswers(question)) {
            question.answers.forEach((answer) => {
              if (answer.checked) question.answer.push(answer.key);
            });
          } else {
            question.answer = [question.rangeValueMin, question.rangeValueMax];
          }
        }

        if (!this.isMultiselectQuestion(question)) {
          if (this.hasAnswers(question)) {
            question.answers.forEach(
              (answer) =>
                (answer.checked = question.rangeValueMin === answer.data),
            );
          }
        }
      });
    });
  }

  /**
   * Restore bck details form.
   */
  public restoreBckDetailsForm(): void {
    this.detailsForm = JSON.parse(this.bckDetailsForm);
  }

  /**
   * Backup details form.
   */
  public backupDetailsForm(): void {
    this.bckDetailsForm = JSON.stringify(this.detailsForm);
  }

  /**
   * Is multiselect?
   *
   * @param question
   */
  private isMultiselectQuestion(question: Question.type): boolean {
    return question.multiselect === "1";
  }

  /**
   * Has answers?
   *
   * @param question
   */
  private hasAnswers(question: Question.type): boolean {
    return !!question.answers.length;
  }

  /**
   * Check if account has description and title?
   *
   * @returns {boolean}
   */
  public hasProfileDetails(): boolean {
    return !!this.account.title?.length && !!this.account.description?.length;
  }

  public hasProfileHeadline(): boolean {
    return !!this.account.description.length && !!this.account.title.length;
  }

  public hasApprovedPhotos(): boolean {
    return !!_.filter(this.account.photos, (photo) => photo.approved).length;
  }

  public hasValidEmail(): boolean {
    return !(!this.account.email.length || this.account.valid_email == false);
  }

  public showRulesPopup(): boolean {
    return (
      (!this.hasProfileHeadline() && !this.hasValidEmail()) ||
      (!this.hasProfileHeadline() && !this.hasApprovedPhotos()) ||
      (!this.hasValidEmail() && !this.hasApprovedPhotos())
    );
  }

  public downloadJson() {
    return this.api
      .call<ApiResponses.AccountDownloadJson.type>({
        command: ApiCommands.AccountDownloadJson.name,
        method: "get",
        useToken: true,
      })
      .pipe(
        map((data) => {
          return data.data;
        }),
      );
  }

  public getProfileVerificationData() {
    return firstValueFrom(
      this.api
        .call<ApiResponses.ProfileVerificationResponse.type>({
          command: ApiCommands.ProfileVerified.name,
          method: "get",
          useToken: true,
        })
        .pipe(
          tap(({ data }) => {
            this.profileVerificationData = data;
            // FIX - DATING!!!
            if (this.profileVerificationData.pvt_data["photo_url"]) {
              this.profileVerificationData.pvt_data["photo_url"] =
                new URL(data.pvt_data.images.image[0]).origin +
                "/" +
                this.profileVerificationData.pvt_data["photo_url"];
            }
          }),
        ),
    );
  }

  public profileVerificationAdd(params: ApiParams.ProfileVerificationAdd.type) {
    return firstValueFrom(
      this.api
        .call<ApiResponses.ProfileVerificationAddResponse.type>({
          command: ApiCommands.ProfileVerifiedAdd.name,
          method: "post",
          useToken: true,
          params,
        })
        .pipe(tap(({ data }) => (this.profileVerificationAddData = data))),
    );
  }

  public profileVerificationDelete(
    params: ApiParams.ProfileVerificationPending.type,
  ) {
    return firstValueFrom(
      this.api.call<ApiResponses.NoDataResponse.type>({
        command: ApiCommands.ProfileVerifiedPending.name,
        method: "delete",
        useToken: true,
        params,
      }),
    );
  }
}
