import { TestBed } from "@angular/core/testing";

import { UserUpdatesService } from "./user-updates.service";
import { ApiService } from "./api/api.service";

describe("UserUpdatesService", () => {
  let service: UserUpdatesService;
  let apiSpy: ApiService;

  beforeEach(() => {
    apiSpy = jasmine.createSpyObj("ApiService", ["call"]);

    TestBed.configureTestingModule({
      providers: [{ provide: ApiService, useValue: apiSpy }],
    });
    service = TestBed.inject(UserUpdatesService);
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });
});
