import { Injectable, Ng<PERSON>one } from "@angular/core";
import { Router, NavigationExtras } from "@angular/router";
import { App, URLOpenListenerEvent } from "@capacitor/app";
import { Debug } from "../helpers/debug";
import { AccountService } from "./account.service";
import { AnalyticsService } from "./analytics.service";
import { DialogHelperService } from "./dialog-helper.service";
import { PurchaseService } from "./purchase.service";
import { UtilsService } from "./utils.service";
import { environment as Env } from "../../environments/environment";
import { Platform } from "@ionic/angular";
import { BehaviorSubject, Observable, firstValueFrom, timer } from "rxjs";
import { TrackingService } from "./tracking.service";
import { ChatService } from "./chat.service";
import { IntroductionService } from "./introduction.service";

interface DeeplinkHandlerItem {
  name: string;
  rule: RegExp;
  callback: Function;
  useHash?: boolean; // use the # part of the url ( default false )
  allowGuest?: boolean; // if true, then deeplink will be opened even if user is not logged in
}
interface DeeplinkActionStatus {
  status: "init" | "complete";
}

/**
 * NOTE:
 *
 * use mob/* url format for url's that will be used in mobile only, so for example
 * datingvip.com/mob/ANYTHING url , if hit from browser will display a message, and
 * ask user to open it on a mobile device where the app is installed
 *
 * so if we need something like mobile only offer, or mobile only options that we'll send
 * in email, then we can use it
 *
 * for now we only have mob/try - which will open the trial dialog ( if user is not premium )
 *
 */
@Injectable({
  providedIn: "root",
})
export class DeeplinkService {
  constructor(
    private zone: NgZone,
    private debug: Debug,
    private analytics: AnalyticsService,
    private router: Router,
    private purchase: PurchaseService,
    private dialog: DialogHelperService,
    private utilsService: UtilsService,
    private accountService: AccountService,
    private platform: Platform,
    private tracking: TrackingService,
    private chatService: ChatService,
    private introduction: IntroductionService,
  ) {
    // expose the deeplink handler function app - wide
    // @todo there's probably a more elegant way to do this
  }

  private baseUrl: string = "";
  private baseUrlObject: URL = null;
  private host: string = "";
  private controller: string = "";
  private action: string = "";
  private pathParts: string[] = [];
  private params: URLSearchParams = null;
  private paramsObject: Object = {};
  private hashTag: string = "";
  private isDeeplink: boolean = true;

  private campaignName: string = "";
  private isSpecialOffer: boolean = false;
  private campaignDialogType: string = "";

  // do we have a session - set from boot service
  // false by default
  private hasSession: boolean = false;

  /**
   * get params object for last url
   * @returns params as object from url
   */
  public getParamObject(): Object {
    return this.paramsObject;
  }

  /**
   * Get url string
   * @returns returns the base url as string
   */
  public getBaseUrl(): string {
    return this.baseUrl;
  }

  /**
   * Get url object
   * @returns returns url object
   */
  public getUrlBaseObject(): URL {
    return this.baseUrlObject;
  }

  /**
   * Deeplink observable.
   */
  private _wasDeeplink$: BehaviorSubject<DeeplinkActionStatus> =
    new BehaviorSubject({ status: null });

  public get wasDeeplink$(): Observable<DeeplinkActionStatus> {
    return this._wasDeeplink$.asObservable();
  }

  /**
   * Set session status
   * @param hasSession
   */
  public setSessionStatus(hasSession: boolean) {
    this.hasSession = hasSession;
  }

  private wasInitialized: boolean = false;

  public CallbackNoSession: Function = () => {};

  public async init(hasSession: boolean = true) {
    this.hasSession = hasSession;

    if (this.wasInitialized) {
      return;
    }

    this.wasInitialized = true;

    // initialize deeplinking
    App.addListener("appUrlOpen", async (data: URLOpenListenerEvent) => {
      // handle url deeplink tracking when opened from outside the app ( not firebase dynamic link )
      this.tracking.handleUrlTracking(data.url, "DEEPLINK");

      this.zone.run(() => {
        this.debug.l("deeplink data", data);

        this._wasDeeplink$.next({ status: "init" });

        if (this.hasSession) {
          // se have session, so we handle the deeplink
          // handle deeplink
          this.setLinkData(data["url"], true).handle();
        } else {
          // we do not have session, we'll try to log user in if we have h= param
          this.CallbackNoSession(data);
        }
      });
    });

    // Wait for appUrlOpen listener.
    await firstValueFrom(timer(200));
    this._wasDeeplink$.next({ status: "complete" });
  }

  /**
   * Handle the currently set deeplink
   */
  public handle() {
    // safely quit if there no url set yet
    if (!this.baseUrlObject) {
      this.debug.l("No base url object yet, quitting deeplink handle");
      return false;
    }

    this.debug.l("Handle deeplink start", this.baseUrlObject);

    this.analytics.addMarker("handle deeplink", this.baseUrl);

    // handle only domain actions
    if (
      //(Env.IS_DEBUG || this.host.indexOf(Env.APP_WEBSITE) > -1)
      this.host.indexOf(Env.APP_WEBSITE) > -1
    ) {
      this.debug.l(
        "Execute deeplink handler " + this.controller + ", " + this.action,
      );

      let handlerObject: DeeplinkHandlerItem = this.getHandler();

      if (!handlerObject) {
        this.openExternally(this.baseUrl);

        this.debug.l("execute default system browser for " + this.baseUrl);
      } else {
        this.debug.l(
          "executing handler function for deeplink: " + handlerObject.name,
        );

        // see if the user is logged in and if it's needed at all
        if (!this.isDeeplink && !handlerObject?.allowGuest) {
          this.debug.l(
            "User session needed for this deeplink " + handlerObject.name,
          );

          if (!this.accountService.account?.user_id) {
            this.dialog.showToast("woops", "please_log_in_try");
            return false;
          }
        }

        handlerObject.callback.apply(this);
      }
    } else {
      this.debug.le("Deeplink domain exception", this.baseUrlObject);

      // if we're opening links from within the app and it cannot be handled
      // internally, we open in in external browser
      if (!this.isDeeplink) {
        this.openExternally(this.baseUrl);

        this.clearData();
      }
    }
  }

  public openExternally(url: string) {
    window.open(url, "_system");
  }

  /**
   * Pass an URL to set url data
   *
   * @param url valid url
   * @param isDeeplink tell if it's opened from deeplink or from app itself to handle inner links
   */
  public setLinkData(url: string, isDeeplink = true) {
    this.baseUrl = "";
    this.baseUrlObject = null;
    this.isDeeplink = isDeeplink;

    try {
      this.baseUrlObject = new URL(url);
      this.baseUrl = url;
      this.params = null;
      this.pathParts = [];
      this.host = this.baseUrlObject.hostname;
      this.hashTag = this.baseUrlObject.hash;

      if (this.baseUrlObject.pathname !== "/") {
        this.pathParts = this.baseUrlObject.pathname.slice(1).split("/");
      }

      this.controller = this.pathParts[0] || "";

      this.action = this.pathParts[1] || "";

      this.params = this.baseUrlObject.searchParams;

      let paramsSearch = this.baseUrlObject.search.substring(1);

      // convert url params to simple object
      try {
        this.paramsObject = JSON.parse(
          '{"' + paramsSearch.replace(/&/g, '","').replace(/=/g, '":"') + '"}',
          function (key, value) {
            return key === "" ? value : decodeURIComponent(value);
          },
        );
      } catch (e) {
        this.paramsObject = {};
        this.debug.le("Error decoding search params", [e, this.baseUrl]);
      }

      this.debug.l("param object", this.paramsObject);

      // set campaign param if we have it
      if (this.params) {
        let medium = this.params.get("utm_medium");

        // set campaign name
        this.campaignName =
          this.params.get("campaign") ?? this.params.get("utm_campaign");

        // the cdt=[A|B] param can be used to test different offer dialogs
        this.campaignDialogType = this.params.get("cdt");

        this.analytics.campaignName =
          this.params.get("utm_campaign") ?? this.campaignName;

        // if it starts with ng_ it's a special offer
        this.isSpecialOffer = this.campaignName.indexOf("nl_") === 0;

        // set medium
        if (medium == "email") {
          this.analytics.activationSource = "email";
        }
      }
    } catch (e) {
      this.debug.l("Wrong url format", e);
    }

    return this;
  }

  /**
   * clear deeplink data
   */
  public clearData() {
    this.baseUrl = "";
    this.baseUrlObject = null;
    this.host = "";
    this.controller = "";
    this.action = "";
    this.pathParts = [];
    this.params = null;
  }

  /**
   * Get path param by index
   *
   * @param index index of the param we want to get
   */
  public getPathParm(index: number) {
    return this.pathParts[index] || null;
  }
  /**
   * fetches the handler for current url
   */
  private getHandler(): DeeplinkHandlerItem {
    let _handler = null;
    this.handlerRules.forEach((v) => {
      this.debug.l("pathname deeplink", this.baseUrlObject.pathname);
      if (
        v.rule.test(
          this.baseUrlObject.pathname +
            (!!v?.useHash ? this.baseUrlObject.hash : ""),
        )
      ) {
        _handler = v;
      }
    });

    return _handler;
  }

  /**
   * hande profile if appropriate
   */
  private _caseOpenProfile() {
    // exception - open own photos with comments

    let userName = this.getPathParm(1);

    const rawUserName = userName;
    const hasPhotosSuffix = /,photos(?:\.html)?$/i.test(rawUserName);

    userName = userName.split(",")[0].replace(/\.html$/i, "");

    let hash = this.hashTag;
    let isPhotoDeeplink = false;
    let isOwnProfile =
      userName.toLocaleLowerCase() ==
      this.accountService.account.username.toLocaleLowerCase();

    // if the deeplink is a photo commend deeplink, then open our own photos
    // link example
    // https://front-m1.dvipdev.com/profile/BoldizsarBednarik?utm_campaign=new_comment&utm_medium=email&utm_source=membership_premiumplus&utm_term=primili_ste_novi_komentar&h=1047210g48149b7199bd3401857cc6a0497db938&eid=*********#photo-5
    // https://front-m1.dvipdev.com/profile/BoldizsarBednarik,photos.html?utm_content=viewphotos&utm_campaign=profile_view&utm_medium=email&utm_source=membership_premiumplus&utm_term=sender_first_name_vas_merka&h=1418407ge8cac037556cd8b63c659bb42c9e6f35&eid=*********

    if ((isOwnProfile && hash.indexOf("#photo-") === 0) || hasPhotosSuffix) {
      isPhotoDeeplink = true;
    }

    // open photo gallery if we have photo hash, and if own profile

    if (isPhotoDeeplink) {
      return this.router.navigate(["/app/photo-gallery/", userName]);
    }

    // if is own profile, open own profile
    if (isOwnProfile) {
      return this.router.navigate(["/app/tabs/my-profile"]);
    }

    return this.router.navigate(["/app/profile", userName]);
  }

  private _caseOpenOwnProfileEdit() {
    return this.router.navigate(["/app/my-profile-edit"]);
  }

  private _caseOpenDeactivate() {
    return this.router.navigate(["/app/deactivation"]);
  }

  private _caseOpenDelete() {
    return this.router.navigate(["/app/delete-account"]);
  }

  private _caseOpenInbox() {
    return this.router.navigate(["/app/inbox"]);
  }

  private _caseOpenThread() {
    //open the message in case we have valid thread
    //else - we open inbox
    let thread_id: number = parseInt(this.params.get("thread_id"));
    if (thread_id > 0) {
      this.chatService.getThreadList().subscribe({
        next: (data) => {
          data.forEach((item) => {
            if (item.id == thread_id) {
              return this.router.navigate([
                "/app/chat-messages/" + item.username,
              ]);
            }
          });
        },
      });
    }
    return this.router.navigate(["/app/inbox"]);
  }

  private _caseOpenManageAccount() {
    return this.router.navigate(["/app/manage-menu"]);
  }

  private _caseOpenMatchMenu() {
    return this.router.navigate(["/app/match-options-menu"]);
  }

  private _caseOpenSettings() {
    return this.router.navigate(["/app/settings"]);
  }

  private _caseJustOpenTheApp() {
    return this.router.navigate([Env.HOME_PATH]);
  }

  private _caseOpenContactPage() {
    return this.router.navigate(["/app/customer-support-menu"], {
      fragment: this.hashTag.substring(1),
    });
  }

  private _caseOpenMeet() {
    return this.router.navigate(["/app/tabs/meet/fast-match"]);
  }

  private _caseOpenMeetMatch() {
    return this.router.navigate(["/app/tabs/meet/matches"]);
  }

  private _caseOpenMeetWeLiked() {
    return this.router.navigate(["/app/tabs/meet/we-liked"]);
  }

  private _caseOpenMeetLikedUs() {
    return this.router.navigate(["/app/tabs/meet/liked-us"]);
  }

  private _caseOpenBrowsePage() {
    return this.router.navigate(["/app/tabs/browse"]);
  }

  private _caseOpenStaticPage() {
    return this.router.navigate(["/page", this.getPathParm(1)]);
  }

  private _caseOpenBillingHistory() {
    return this.router.navigate(["/app/billing-history"]);
  }

  private _caseOpenIntroduction() {
    this.introduction.doShow = true;
    return this.router.navigate(["/app/tabs/browse"]);
  }

  private async _caseOpenCampaignDialog() {
    this.purchase.campaignDialogType = this.campaignDialogType;

    // @todo test this
    this.purchase
      .setPromoDialogType("default")
      .openCampaignDialog(this.campaignName, this.errorOccured, () => {
        this.debug.l("try premium cancelled");
      });
  }

  private async _caseOpenTryPremium() {
    // @todo test this
    this.purchase.openTrialDialog(this.errorOccured, () => {
      this.debug.l("try premium cancelled");
    });
  }

  private async _caseOpenUpgrade() {
    if (this.baseUrlObject.href.indexOf("mobile_offer=1") > 0) {
      // we have a ?mob_show_offer=1 param with url, and we need to open try dialog
      // instead of the upgrade dialog

      this._caseOpenTryPremium();

      return;
    }

    this.purchase.openUpgradeDialog(this.errorOccured, () => {
      this.debug.l("upgrade cancelled");
    });
  }

  /**
   * Common function for handling error display
   * @param error
   */
  private async errorOccured(error) {
    await this.dialog.hideLoading();

    if (error?.["doDisplay"] !== false) {
      this.dialog.showToastBaseOnErrorResponse(
        "purchase_error",
        this.utilsService.figureOutTheErrorMessageFromError(error),
      );
    }
  }

  private _caseOpenFaqPage() {
    return this.router.navigate(["/app/faq"]);
  }

  /**
   * define handler rules
   */
  private handlerRules: DeeplinkHandlerItem[] = [
    {
      name: "Open user's profile",
      rule: new RegExp("^/profile/.+$", "i"),
      callback: this._caseOpenProfile,
    },
    {
      name: "Edit own profile",
      rule: new RegExp("^((/edit)|(/edit[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenOwnProfileEdit,
    },
    {
      name: "Edit own photos",
      rule: new RegExp("^((/photos)|(/photos[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenOwnProfileEdit,
    },
    {
      name: "Open inbox, or inbox thread",
      rule: new RegExp("^((/message)|(/message[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenThread,
    },
    {
      name: "Open manage account",
      rule: new RegExp("^((/account)|(/account[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenManageAccount,
    },
    {
      name: "Open match filters",
      rule: new RegExp(
        "^((/account/contact)|(/account/contact[^a-zA-Z0-9_].*))$",
        "i",
      ),
      callback: this._caseOpenMatchMenu,
    },
    {
      name: "Open settings",
      rule: new RegExp(
        "^((/account/notifications)|(/account/notifications[^a-zA-Z0-9_].*))$",
        "i",
      ),
      callback: this._caseOpenSettings,
    },
    {
      name: "Open settings for notifications",
      rule: new RegExp("^(/account#notifications)$", "i"),
      useHash: true,
      callback: this._caseOpenSettings,
    },
    {
      name: "Browse",
      rule: new RegExp("^((/browse)|(/browse[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseJustOpenTheApp,
    },
    {
      name: "Customer support",
      rule: new RegExp("^((/contact)|(/contact[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenContactPage,
      allowGuest: true,
      useHash: true,
    },
    {
      name: "Search",
      rule: new RegExp("^((/search)|(/search[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenBrowsePage,
    },
    {
      name: "Match queue",
      rule: new RegExp("^((/rapid_match)|(/rapid_match[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenMeet,
    },
    {
      name: "Meet Liked Us",
      rule: new RegExp(
        "^((/autolist_likes/meet)|(/autolist_likes/meet[^a-zA-Z0-9_].*))$",
        "i",
      ),
      callback: this._caseOpenMeetLikedUs,
    },
    {
      name: "Matched",
      rule: new RegExp(
        "^((/matches/meet)|(/matches/meet[^a-zA-Z0-9_].*))$",
        "i",
      ),
      callback: this._caseOpenMeetMatch,
    },
    {
      name: "I like",
      rule: new RegExp("^((/likes/meet)|(/likes/meet[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenMeetWeLiked,
    },
    {
      name: "Login",
      rule: new RegExp("^((/login)|(/login[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseJustOpenTheApp,
      allowGuest: true,
    },
    {
      name: "Register",
      rule: new RegExp("^((/register)|(/register[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseJustOpenTheApp,
      allowGuest: true,
    },
    {
      name: "Inbox settings - match pref",
      rule: new RegExp("^((/inbox)|(/inbox[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenInbox,
    },
    {
      name: "Upgrade page",
      rule: new RegExp("^((/upgrade)|(/upgrade[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenUpgrade,
    },
    {
      name: "Try premium page",
      rule: new RegExp("^((/mob/try)|(/mob/try[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenCampaignDialog,
    },
    {
      name: "CCPA button handler",
      rule: new RegExp(
        "^((/do-not-sell-my-personal-information)|(/do-not-sell-my-personal-information[^a-zA-Z0-9_].*))$",
        "i",
      ),
      callback: this._ccpaHandler,
    },
    {
      name: "Open FAQ page",
      rule: new RegExp("^((/faq)|(/faq[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenFaqPage,
      allowGuest: true,
    },
    {
      name: "Deactivate account",
      rule: new RegExp("^((/deactivate)|(/deactivate[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseOpenDeactivate,
    },
    {
      name: "Delete account",
      rule: new RegExp("^(/delete)$", "i"),
      callback: this._caseOpenDeactivate,
    },
    {
      name: "Purge account",
      rule: new RegExp("^(/authorize/purge,([0-9].+).html)$", "i"),
      callback: this._caseOpenDeactivate,
    },
    {
      name: "Static pages",
      rule: new RegExp("^((/pages).*)$", "i"),
      callback: this._caseOpenStaticPage,
      allowGuest: true,
    },
    {
      name: "Billing history",
      rule: new RegExp("^(/account#billing-history)$", "i"),
      callback: this._caseOpenBillingHistory,
      useHash: true,
    },
    {
      name: "Introduction",
      rule: new RegExp(
        "^((/introduction)|(/introduction[^a-zA-Z0-9_].*))$",
        "i",
      ),
      callback: this._caseOpenIntroduction,
      useHash: true,
    },
    {
      name: "AppBanner",
      rule: new RegExp("^(/app_banner)$", "i"),
      callback: this._caseJustOpenTheApp,
      allowGuest: true,
      useHash: true,
    },
    {
      name: "Debug",
      rule: new RegExp("^((/_dlg)|(/_dlg[^a-zA-Z0-9_].*))$", "i"),
      callback: this._caseDialog,
      useHash: true,
    },
  ];

  private _caseDialog() {
    let hash = this.hashTag.substring(1);

    switch (hash) {
      case "24h":
        // Store default registration ts.
        let art = JSON.stringify(this.accountService.account.registration_ts);
        //set reg time 5h ago
        this.accountService.account.registration_ts =
          Math.floor(Date.now() / 1000) - 5 * 60 * 60;
        this.purchase.openDiscounted24hDialog(
          (error) => {
            // this.dialog.showErrorAlert(
            //   "purchase_error",
            //   this.utilsService.figureOutTheErrorMessage(error),
            //   this.utilsService.figureOutTheErrorKey(error),
            // );
          },
          () => {
            console.log("Cancelled");
          },
          true,
        );
        setTimeout(() => {
          // Reset default registration ts.
          this.accountService.account.registration_ts = JSON.parse(art);
        }, 1000);
        break;

      case "download":
        this.purchase.openGoPremiumDownloaded(
          (error) => {
            // this.dialog.showErrorAlert(
            //   "purchase_error",
            //   this.utilsService.figureOutTheErrorMessage(error),
            //   this.utilsService.figureOutTheErrorKey(error),
            // );
          },
          () => {
            console.log("Cancelled");
          },
          true,
        );
        break;

      case "campaign":
        return this._caseOpenCampaignDialog();
        break;

        return true;
    }

    return true;
  }

  private _ccpaHandler() {
    this.dialog.confirm("CCPA", "ccpa_opt_out", () => {
      firstValueFrom(this.accountService.updateCCPAStatus(false))
        .then(() => {
          this.dialog.showAlert("CCPA", "ccpa_info_inactive");
        })
        .catch((err) => {
          this.debug.le("ccpa status update error", err);
        });
    });
  }
}
