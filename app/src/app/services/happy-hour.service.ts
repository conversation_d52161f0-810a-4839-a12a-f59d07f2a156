import { Injectable } from "@angular/core";
import { IBootAction } from "./boot.service";
import { ChatMessageService } from "./chat-message.service";
import { AccountService } from "./account.service";
import { ApiResponses } from "./api/api.responses";

@Injectable({
  providedIn: "root",
})
export class HappyHourService implements IBootAction {
  /**
   * HH data.
   *
   * if (start > 0) that means that the happy hour did not begin yet,
   * if (start > 0) display the dialog on when will it begin,
   * if (start == 0 && end > 0) that means that it has begun
   * if (start == 0) display the dialog on when will it end
   */
  private _data: ApiResponses.MessagesFreeResponse.type["data"];

  /**
   * Time remaining - timestamp.
   */
  private _time: number;

  /**
   * Display time.
   */
  private _displayTime: string;

  constructor(
    private accountService: AccountService,
    private msgService: ChatMessageService
  ) {}

  /**
   * Boot.
   * @returns {Promise<void>}
   */
  public boot(): Promise<void> {
    if (!this.accountService.hasProfileDetails()) return;

    return this.msgService
      .free()
      .then(({ data }) => (this.data = data))
      .catch((err) => (this.data = null));
  }

  /**
   * Get remaining time.
   *
   * @return {string}
   */
  get displayTime(): string {
    return this._displayTime;
  }

  /**
   * Set remaining time.
   *
   * @param {string} value
   */
  set displayTime(value: string) {
    this._displayTime = value;
  }

  /**
   * Get HH data.
   *
   * @return {{start: number; end: number}}
   */
  get data(): ApiResponses.MessagesFreeResponse.type["data"] {
    return this._data;
  }

  /**
   * Set HH data.
   *
   * @param {number} value
   */
  set data(value: ApiResponses.MessagesFreeResponse.type["data"]) {
    this._data = value;

    if (!this._data) return;

    // Set time first time when we assign data.
    this.time = this.data.start > 0 ? this.data.start : this.data.end;

    if (this.time <= 0) return;

    // Init timer.
    this.timerTick();
  }

  /**
   * Get HH time.
   *
   * @return {number}
   */
  get time(): number {
    return this._time;
  }

  /**
   * Set HH time.
   *
   * @param {number} value
   */
  set time(value: number) {
    this._time = value;
  }

  /**
   * Display happy hour component?
   *
   * @return {boolean}
   */
  public showHH(): boolean {
    let status: boolean = false;

    // start = 0 && end > 0
    // start > 0 && end > start
    if (this.data) {
      if (this.data.start >= 0 && this.data.end > this.data.start) {
        status = true;
      }
    }

    return status;
  }

  /**
   * Set seconds as digital clock.
   */
  private secondsAsDigitalClock(): void {
    let hours = Math.floor(this.time / 60 / 60);
    let minutes = Math.floor(this.time / 60) - hours * 60;
    let seconds = this.time % 60;
    let hoursString = "";
    let minutesString = "";
    let secondsString = "";
    hoursString = hours < 10 ? "0" + hours : hours.toString();
    hoursString = hoursString == "00" ? "" : hoursString + ":";
    minutesString = minutes < 10 ? "0" + minutes : minutes.toString();
    secondsString = seconds < 10 ? "0" + seconds : seconds.toString();
    this.displayTime = hoursString + minutesString + ":" + secondsString;
  }

  /**
   * Time tick.
   */
  private timerTick(): void {
    setTimeout(() => {
      this.time--;

      if (this.data.start > 0) this.data.start--;
      if (this.data.end > 0) this.data.end--;

      if (this.data.start === 0 && this.data.end > 0) this.time = this.data.end;

      this.secondsAsDigitalClock();

      if (this.time > 0) this.timerTick();
    }, 1000);
  }
}
