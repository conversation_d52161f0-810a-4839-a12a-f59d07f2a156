import { Injectable } from "@angular/core";
import { ListPaginator } from "./lists/list-paginator";
import { ProfileService } from "./profile.service";
import { environment as Env } from "../../environments/environment";

@Injectable({
  providedIn: "root",
})
export class PaginatorHelperService {
  public likedUsPaginator: ListPaginator;
  public weLikedPaginator: ListPaginator;
  public matchPaginator: ListPaginator;
  public browsePaginator: ListPaginator;
  public listViewedMe: ListPaginator;
  public listIViewed: ListPaginator;

  constructor(private profile: ProfileService) {
    // initialize paginated lists
    this.likedUsPaginator = new ListPaginator(this.profile.listLikedUs);
    this.weLikedPaginator = new ListPaginator(this.profile.listWeLiked);
    this.matchPaginator = new ListPaginator(this.profile.listMatches);
    this.browsePaginator = new ListPaginator(this.profile.listBrowse);
    this.listIViewed = new ListPaginator(
      this.profile.listIViewed,
      Env.DEFAULT_LIST_GET_LIMIT,
      "2"
    );
    this.listViewedMe = new ListPaginator(
      this.profile.listViewedMe,
      Env.DEFAULT_LIST_GET_LIMIT,
      "2"
    );
  }

  /**
   * Initialize paginated lists
   */
  public init() {
    this.browsePaginator.next();
    this.likedUsPaginator.next();
    this.weLikedPaginator.next();
    this.matchPaginator.next();
    this.listViewedMe.next();
    this.listIViewed.next();
  }
}
