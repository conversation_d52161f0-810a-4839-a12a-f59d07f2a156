import { QueueCommandService } from "./queue-command.service";

export class QueueItem {
  private _queueCommandService: QueueCommandService;

  /**
   * Override this function, and call resolveQueueItem() when it's done
   */
  protected queueAction() {
    console.warn("queueAction() empty for queue class");
  }

  /**
   * function that will be called to execute the flow, and has to return an observable
   * which is used to call the next item
   */
  public executeItem(queueCommandService: QueueCommandService) {
    this._queueCommandService = queueCommandService;
    this.queueAction();
  }

  protected queueItemDone() {
    this._queueCommandService;
  }
}
