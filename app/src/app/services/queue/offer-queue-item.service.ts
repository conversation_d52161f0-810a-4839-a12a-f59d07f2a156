import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Debug } from "src/app/helpers/debug";
import { QueueCommandService } from "./queue-command.service";
import { QueueItem } from "./queue-item.class";
import { ReminderServiceOffers } from "../reminder-offers";
import { environment as Env } from "../../../environments/environment";

@Injectable({
  providedIn: "root",
})
export class OfferQueueItemService extends QueueItem {
  constructor(
    private dbg: Debug,
    private queueCommand: QueueCommandService,
    private offerReminder: ReminderServiceOffers
  ) {
    super();
  }

  protected queueAction() {
    this.offerReminder.setRepeatPeriod(Env.OFFERS_REMINDER_REPEAT_TIME);
    this.offerReminder.execute(true).catch((err) => {
      this.dbg.le("offer reminder exec error", err);
      this.queueCommand.gotoNextItem();
    });
  }
}
