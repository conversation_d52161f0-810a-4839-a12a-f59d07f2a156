import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { firstValueFrom } from "rxjs";
import { Debug } from "src/app/helpers/debug";
import { CredentialData } from "src/app/schemas/credentials";
import { AuthService } from "../auth.service";
import { WelcomeProfileService } from "../welcome-profile.service";
import { WelcomeWizardService } from "../welcome-wizard.service";
import { QueueCommandService } from "./queue-command.service";
import { QueueItem } from "./queue-item.class";

@Injectable({
  providedIn: "root",
})
export class WelcomeQueueItemService extends QueueItem {
  private _wasWelcomeWizard: boolean = false;
  get wasWelcomeWizard(): boolean {
    return this._wasWelcomeWizard;
  }

  constructor(
    private welcomeProfile: WelcomeProfileService,
    private debug: Debug,
    private router: Router,
    private welcomeWizard: WelcomeWizardService,
    private queueCommand: QueueCommandService,
    private auth: AuthService
  ) {
    super();
  }

  protected queueAction() {
    firstValueFrom(this.welcomeProfile.getStatus())
      .then((doShowWelcomeWizard) => {
        if (doShowWelcomeWizard) {
          this.openWelcomeWizard();
        } else {
          this.queueCommand.gotoNextItem();
        }
      })
      .catch(() => {
        this.queueCommand.gotoNextItem();
      });
  }

  public openWelcomeWizard() {
    firstValueFrom(this.welcomeProfile.get())
      .then((data) => {
        let wdata = data.data;
        this._openWelcomeWizard(wdata);
        this._wasWelcomeWizard = true;
      })
      .catch((err) => {
        this.debug.le("welcome profile get error", err);
        this.queueCommand.gotoNextItem();
      });
  }

  private _openWelcomeWizard(wData) {
    this.debug.l(">>> open welcome wizard <<<", wData);
    this.welcomeWizard.reset();
    this.welcomeWizard.doHideFirstBackButton = true;

    this.welcomeWizard.addPageList(
      this.welcomeWizard.getPageListFromData(wData)
    );

    // if user regged through apple, just skip the 1st step

    let i = 0;
    if (this.auth.regType == CredentialData.CredType.Apple) {
      i = 1;
    }

    // open wizard
    this.welcomeWizard.open(i);
  }
}
