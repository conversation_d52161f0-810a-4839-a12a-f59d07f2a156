import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Debug } from "src/app/helpers/debug";
import { QueueCommandService } from "./queue-command.service";
import { QueueItem } from "./queue-item.class";
import { ReminderIntroductionService } from "../reminder-introduction.service";
import { environment as Env } from "../../../environments/environment";
import { DeeplinkService } from "../deeplink.service";
import { AuthService } from "../auth.service";
import { PushNotificationService } from "../push-notification.service";
import { WelcomeQueueItemService } from "./welcome-queue-item.service";
import { firstValueFrom, timer } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class IntroductionQueueItemService extends QueueItem {
  /**
   * Assume that we have session by default.
   */
  private hasSession: boolean = true;

  /**
   * In case of push-notifications do not open introduction dialog.
   */
  private wasPushNotification: boolean = false;

  constructor(
    private reminderIntroduction: ReminderIntroductionService,
    private dbg: Debug,
    private queueCommand: QueueCommandService,
    private deeplinkService: DeeplinkService,
    private authService: AuthService,
    private pushNotificationService: PushNotificationService,
    private welcomeQueue: WelcomeQueueItemService,
  ) {
    super();

    this.authService.authEvents$.subscribe({
      next: (res) => {
        if (res) {
          // Register or login!
          this.hasSession = false;
        }
      },
    });
    this.pushNotificationService.wasPushNotification$.subscribe({
      next: (res) => {
        if (res) this.wasPushNotification = true;
      },
    });
  }

  protected async queueAction() {
    // Wait for deeplink to start!
    let deeplinkStatus: boolean = false;
    this.deeplinkService.wasDeeplink$.subscribe({
      next: async (res) => {
        await firstValueFrom(timer(3000));

        if (res.status === "init") deeplinkStatus = true;
        // Start the introduction reminder only if the deeplink
        // hasn't already been started, and if we have session,
        // and if not push-notification.
        if (
          res.status === "complete" &&
          !deeplinkStatus &&
          this.hasSession &&
          !this.wasPushNotification &&
          !this.welcomeQueue.wasWelcomeWizard &&
          !this.authService.wasRegistration
        ) {
          this.initIntroductionReminder();
        } else {
          this.queueCommand.gotoNextItem();
        }
      },
    });
  }

  private initIntroductionReminder(): void {
    this.dbg.l("introduction queue exec");
    this.reminderIntroduction.setRepeatPeriod(
      Env.INTRODUCTION_REMINDER_REPEAT_TIME,
    );
    this.reminderIntroduction.execute().catch((err) => {
      this.dbg.le("introduction reminder exec error", err);
      this.queueCommand.gotoNextItem();
    });
  }
}
