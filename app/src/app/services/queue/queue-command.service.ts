import { Injectable } from "@angular/core";
import { Observable, Subject } from "rxjs";

/**
 * helper service to command queue
 */

@Injectable({
  providedIn: "root",
})
export class QueueCommandService {
  private _queueForward$: Subject<any> = new Subject<any>();

  get queueForward$(): Observable<any> {
    return this._queueForward$.asObservable();
  }

  public gotoNextItem() {
    this._queueForward$.next(null);
  }
}
