import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { AccountService } from "./account.service";
import { WizardService } from "./wizard.service";
import { environment as Env } from "../../environments/environment";
import { Debug } from "../helpers/debug";
import { DialogHelperService } from "./dialog-helper.service";
import { UtilsService } from "./utils.service";
import { ApiService } from "./api/api.service";
import { ApiCommands } from "./api/api.commands";

@Injectable({
  providedIn: "root",
})
export class AppleSignupWizardService extends WizardService {
  constructor(
    protected router: Router,
    protected accountService: AccountService,
    protected debug: Debug,
    protected dialog: DialogHelperService,
    protected utils: UtilsService,
    protected api: ApiService
  ) {
    super(router, accountService);
  }

  get isPorcessing(): boolean {
    return (
      this._isProcessing ||
      this.api.isInEventList({ command: ApiCommands.AppPreRegister.name }) ||
      this.api.isInEventList({ command: ApiCommands.AppRegister.name })
    );
  }

  /**
   * Initialize steps
   */
  protected initSteps() {
    this._urlPrefix = Env.APPLE_SIGNUP_URL_PREFIX;
    this.initStoredData();

    let steps = [
      // we ask for gender pair all the time
      {
        url: "gender",
        data: {},
        isPopulated: false,
      },

      // we ask for age only if we don't have it from pre-reg
      {
        url: "age",
        data: {},
        isPopulated: false,
      },

      // we ask for username - NOT password, so that has to be hidden
      {
        url: "username",
        data: {
          doHidePassword: true,
        },
        isPopulated: false,
      },

      // if we got valid email from server we'll not ask for it
      {
        url: "email",
        data: {},
        isPopulated: false,
      },
    ];

    // add consent page only if it's required

    if (this.accountService.consentRequirement.required) {
      steps.push({
        url: "consent",
        data: {},
        isPopulated: false,
      });
    }

    this.setSteps(steps);
  }

  private _finishCallback: Function = (data) => {
    this.debug.log({
      message: "Finish callback not set",
      data: data,
      severity: "warning",
    });
  };

  /**
   * Set the finish callback function outside of the class
   * @todo consider using obesrvable? or promise ... maybe callback is appropriate here
   * @param func
   */
  public setFinishCallback(func: Function) {
    this._finishCallback = func;
  }

  /**
   * we give the opportunity to finish the registration outside of the class
   */
  public async finishWizard() {
    try {
      this._finishCallback(this.getMergedData());
    } catch (error) {
      this.dialog.showErrorAlert(
        "error",
        this.utils.figureOutTheErrorMessage(error),
        this.utils.figureOutTheErrorKey(error)
      );
    }
  }
}
