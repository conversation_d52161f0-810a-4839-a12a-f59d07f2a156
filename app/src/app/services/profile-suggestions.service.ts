import { Injectable, Ng<PERSON>one } from "@angular/core";
import { BehaviorSubject, firstValueFrom, Observable } from "rxjs";
import { ProfileBase } from "../schemas/profile-base";
import { ApiCommands } from "./api/api.commands";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiService } from "./api/api.service";
import { IBootAction } from "./boot.service";
import { environment as Env } from "src/environments/environment";
import { map, tap } from "rxjs/operators";
import _ from "lodash";
import { Debug } from "../helpers/debug";
import { ProfileService } from "./profile.service";
import { AdsService } from './ads.service';

export type SuggestionListStatus =
  | "loading"
  | "no_more_results"
  | "error"
  | "normal";

export interface SuggestedItem {
  previousItem: ProfileBase.type;
  currentItem: ProfileBase.type;
  nextItem: ProfileBase.type;
  status: SuggestionListStatus;
}

@Injectable({
  providedIn: "root",
})
export class ProfileSuggestionsService implements IBootAction {
  // array to contain profiles
  private _container: ProfileBase.type[] = [];

  constructor(
    private api: ApiService,
    private debug: Debug,
    private ngZone: NgZone,
    private profileServcie: ProfileService,
    private adsService: AdsService,
  ) {
    // set status to error if we have a netowrk error
    this.api.httpErrorStream$.subscribe({
      next: (data) => {
        if (this.api.wasNetworkError) {
          if (this._itemContainer) {
            this._itemContainer.status = "error";
          }
        } else {
          if (this._itemContainer) {
            this._itemContainer.status = this.getStatus();
          }
        }
      },
    });
  }

  /**
   * Array to contain profiles
   */
  private _itemContainer: SuggestedItem;
  /**
   * Current index in the array
   */
  private _currentIndex: number = -1;

  private _currentItem$: BehaviorSubject<ProfileBase.type> =
    new BehaviorSubject<ProfileBase.type>(null);
  private _nextItem$: BehaviorSubject<ProfileBase.type> =
    new BehaviorSubject<ProfileBase.type>(null);

  get currentProfile() {
    return this._currentItem$.value;
  }

  get currentItem$(): Observable<ProfileBase.type> {
    return this._currentItem$.asObservable();
  }

  get nextItem$(): Observable<ProfileBase.type> {
    return this._nextItem$.asObservable();
  }

  /**
   * Item reflector subject
   */
  private _items$: BehaviorSubject<SuggestedItem> =
    new BehaviorSubject<SuggestedItem>({
      previousItem: null,
      currentItem: null,
      nextItem: null,
      status: "loading",
    });

  /**
   * Observable to follow items
   */
  get items$(): Observable<SuggestedItem> {
    return this._items$.asObservable();
  }

  private initData() {
    this._container = [];

    this._currentIndex = -1;
    this._wasLastResult = false;
    this._wasError = false;

    this._itemContainer = {
      previousItem: null,
      currentItem: null,
      nextItem: null,
      status: "loading",
    };
  }

  public boot(): Promise<any> {
    this.initData();
    return this.next();
  }

  public refresh() {
    this.initData();

    // emmit when refreshing
    this._items$.next(this._itemContainer);

    this.next();
    this.emmitCards();
  }

  public getStatus(): SuggestionListStatus {
    /*
    - if the list is empty - and we're fetching the nest batch of results, then it's "loading"
    - if we're loading the data in background, then the status is "normal"
    - if we're not loading, and we have elements in the list then status is "normal"
    - if the list is empty, because the last fetch returned 0 results ( total = 0 ) then it's "no_more_results"
    - if there was an error in loading elements, then the status is "error"
    */

    if (this.wasError()) {
      return "error";
    }

    if (this.isLoading() && !this._itemContainer.currentItem) {
      return "loading";
    }

    if (this._wasLastResult && !this._itemContainer.currentItem) {
      return "no_more_results";
    }

    return "normal";
  }

  private _wasLastResult: boolean = false;
  private _wasError: boolean = false;

  public getSuggestionsList(
    limit: number = 10
  ): Observable<ProfileBase.type[]> {
    return this.api
      .call<ApiResponses.ProfileListResponseTotal.type>({
        command: ApiCommands.ProfileSuggestions.name,
        method: "get",
        useToken: true,
        callType: "background",
        params: <ApiParams.ProfileSuggestions.type>{
          limit: Env.MATCH_QUEUE_CONFIG.limit,
          page: 1,
        },
      })
      .pipe(
        tap({
          next: (data) => {
            // add data to container

            let items = data.data.items;

            this._container = [...this._container, ...items];

            // remove duplicates
            this._container = _.uniqBy(this._container, "user_id");

            this._wasLastResult = items.length < limit;

            this.mergeAdsWithProfiles();
          },
        }),
        map((data) => {
          // map main photo
          data.data.items = data.data.items.map((item) => {
            // updating avatar url to higher resolution
            item.avatar_url = this.profileServcie.getMainPhoto(item);

            // pre-load images so user have a smooth experience when scrolling through
            new Image().src = item.avatar_url;

            return item;
          });

          return data.data.items;
        })
      );
  }

  private mergeAdsWithProfiles() {
    try {
      // Load ad after the 3rd profile in the deck.
      for (let i = 0; i < this._container.length; i++) {
        if (this.adsService.adDataArr.length && !(i % 3) && (i >= 1 && i <= 3)) {
          let randAdsIndex = Math.floor(Math.random() * this.adsService.adDataArr.length);
          this.adsService.adDataArr[randAdsIndex]['profile_type'] = 'ads';
          this._container.splice(i, 0, this.adsService.adDataArr[randAdsIndex]);
        } else {
          this._container[i].profile_type = 'profile';
        }
      }

      // Load ad at every 4th place.
      // for (let i = 0; i < this._container.length; i++) {
      //   // First we will check for %3 because of 0 base array, so we will obtain an item in the fourth position.
      //   if (this.adsService.adDataArr.length && !(i % 3) && (i >= 1 && i <= 3)) {
      //     let randAdsIndex = Math.floor(Math.random() * this.adsService.adDataArr.length);
      //     this.adsService.adDataArr[randAdsIndex]['profile_type'] = 'ads';
      //     this._container.splice(i, 0, this.adsService.adDataArr[randAdsIndex]);
      //   }

      //   if (this.adsService.adDataArr.length && !(i % 4) && i > 3) {
      //     let randAdsIndex = Math.floor(Math.random() * this.adsService.adDataArr.length);
      //     this.adsService.adDataArr[randAdsIndex]['profile_type'] = 'ads';
      //     this._container.splice(i, 0, this.adsService.adDataArr[randAdsIndex]);
      //   } else {
      //     this._container[i].profile_type = 'profile';
      //   }
      // }

      // // Remove one ad fom list because we have on 4 and 5 place ad items.
      // if (this.adsService.adDataArr.length) {
      //   this._container.splice(4,1);
      // }
    } catch (e) {
      this.debug.le('merge ads with profiles error', e);
    }
  }

  private isLoading() {
    return this.api.isInEventList({
      command: ApiCommands.ProfileSuggestions.name,
    });
  }

  private wasError() {
    return this.api.isInErrorList({
      command: ApiCommands.ProfileSuggestions.name,
    });
  }

  private emitEvent() {
    this._itemContainer = {
      currentItem: this._container[this._currentIndex] || null,
      nextItem: this._container[this._currentIndex + 1] || null,
      previousItem: this._container[this._currentIndex - 1] || null,
      status: this._itemContainer.status,
    };

    // fetch status when we already have item container data, beucase that function uses it
    // if you set it above, it will be "late" to the changes presented in the object definition
    this._itemContainer.status = this.getStatus();

    // emmit
    this.emmitCards();
  }

  private emmitCards() {
    // make sure to update everything when data is emmited
    this.ngZone.run(() => {
      this._items$.next(this._itemContainer);
      this._currentItem$.next(this._itemContainer.currentItem);
    });

    setTimeout(() => {
      // we emmit current item instantly, but we emmit next item with a delay
      // to avoid flickering

      // holy fu**ing s*it i hate how this work
      // @todo see how could we make it better ...

      this.ngZone.run(() => {
        this._nextItem$.next(this._itemContainer.nextItem);
      });
    }, 20);
  }

  /**
   * Undo in list ( step back )
   */
  public undo() {
    if (this._currentIndex > 0) {
      this._currentIndex--;
      this.emitEvent();
    }
  }

  public async next() {
    try {
      let res = null;

      if (this._currentIndex < 0 && !this.isLoading()) {
        res = await firstValueFrom(
          this.getSuggestionsList(Env.MATCH_QUEUE_CONFIG.limit)
        );
      }

      // remove elements not needed for undo

      if (this._currentIndex > Env.MATCH_QUEUE_CONFIG.undo_limit) {
        this._container.shift();
      } else {
        this._currentIndex++;
      }

      if (!(this._container[this._currentIndex] || null)) {
        // emmit if we have no stuff ( to show loading )
        this.emitEvent();
      }

      // if there was no last result, and we have less than trashhold, then fetch results in the bg here

      if (
        this._container.length - this._currentIndex <
          Env.MATCH_QUEUE_CONFIG.prefetch_limit &&
        !this._wasLastResult &&
        !this.isLoading()
      ) {
        firstValueFrom(
          this.getSuggestionsList(Env.MATCH_QUEUE_CONFIG.limit)
        ).then(() => {
          this.emitEvent();
        });
      }

      this.emitEvent();

      return Promise.resolve();
    } catch (error) {
      this.emitEvent();

      return Promise.reject();
    }
  }
}
