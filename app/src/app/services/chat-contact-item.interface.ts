import { ProfileBase } from "src/app/schemas/profile-base";
// @todo ideally we'll use a chat item that's better than this native stuff
//       when we have that on server side, then we'll rewrite the app part

/*
export interface ChatContactItem {
  attachment?: string;
  avatar?: string;
  avatar_url?: string;
  checked?: string;
  delivery_status?: string;
  filter_status?: string;
  gender_id?: string;
  im_link?: string;
  is_gift?: boolean;
  is_imlive?: boolean;
  is_intro?: boolean;
  last_message?: string;
  last_msg_uid?: string;
  msg_count?: number;
  replied?: string;
  thread_id?: string;
  thread_status?: string;
  thread_subject?: string;
  thread_ts?: string;
  user_id2?: string;
  username?: string;
  first_name?: string;
  verified?: boolean;

  // ------- attached stuff

  hasThread?: boolean; // do we have thread with the user already or is it just here because of the match list?
  userObject?: ProfileBase.type; // if we have user profile in matchse, attach here
  is_read?: boolean;
}
*/

export interface ChatContactItem {
  id?: number;
  ts?: number;
  subject?: string;
  msg_count?: number;
  username?: string;
  user_id?: number;
  avatar_url?: string;
  last_message?: string;
  thread_status?: number;
  is_archived?: boolean;
  is_read?: boolean;
  type_name?: string;
  type_index?: number;
  do_open_upgrade?: boolean;
  add_thread_id?: boolean;
  appended_data?: InboxThreadItemAppendedData;
  thread_ids?: number[];

  // attached for functionality

  hasThread?: boolean; // do we have thread with the user already or is it just here because of the match list?
  userObject?: ProfileBase.type; // if we have user profile in matchse, attach here
}

export interface InboxThreadItemAppendedData {
  first_name?: string;
  age?: number;
  distance?: string;
}
