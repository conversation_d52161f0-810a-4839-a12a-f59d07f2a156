import { Injectable } from "@angular/core";
import { CredentialData } from "../schemas/credentials";
import { JwtData } from "../schemas/jwt";
import { AuthService } from "./auth.service";
import {
  BehaviorSubject,
  firstValueFrom,
  map,
  Observable,
  Subject,
} from "rxjs";
import { ApiService } from "./api/api.service";
import { StorageService } from "./storage.service";
import { ApiResponses } from "./api/api.responses";
import { ApiCommands } from "./api/api.commands";
import { Debug } from "../helpers/debug";
import _ from "lodash";
import { DialogHelperService } from "./dialog-helper.service";
import { MiscService } from "./misc.service";
import { BootService } from "./boot.service";
import { HttpErrorResponse } from "@angular/common/http";
import { AnalyticsService } from "./analytics.service";
import { TME_session_restore_failed } from "../classes/gtm-event.class";
import { PushNotificationService } from "./push-notification.service";
import { Router } from "@angular/router";
import { Utils } from "../helpers/utils";

/**
 * what will it do?
 *
 * -> session should only manage sessni, login should be managed separately so:
 * --> keep token
 * --> handle recovery seamlessly ( if possible with Subject / BehaviorSubject / Observable)
 * --> refresh token
 * --> provide token to users
 * --> keep the logged in users's data
 * --> re-auth if token lost
 * --> every class would use token from session
 *
 *
 */

const CREDENTIAL_KEY: string = "CRED_FAILSAFE";
const JWT_KEY: string = "JWT";

export type SessionEvent = {
  name: "sing_in" | "register";
};

@Injectable({
  providedIn: "root",
})
export class SessionService {
  /**
   * json web token
   */
  private jwtData: JwtData.type;

  /**
   * @todo
   * !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   *
   * not good solution to set api token in api class from outside,
   * but we cannot reference services from eachother
   *
   * maybe we need a middle service just so we can communicate between the classes with proper observables !!
   *
   * think about this !!!
   */

  /**
   * Current route url.
   */
  //private currentRoute: string = "/";

  private redirectUrl: string = null;

  private _sessionEvents$: Subject<SessionEvent> = new Subject<SessionEvent>();

  get sessionEvents$(): Observable<SessionEvent> {
    return this._sessionEvents$.asObservable();
  }

  public isRestore: boolean = false;

  constructor(
    private storage: StorageService,
    private auth: AuthService,
    private api: ApiService,
    private debug: Debug,
    private dialog: DialogHelperService,
    private misc: MiscService,
    private boot: BootService,
    private analytics: AnalyticsService,
    private pushNotificationService: PushNotificationService,
    private router: Router,
  ) {
    /*

    @note not sure why do we need this

    this.router.events.subscribe((event: NavigationEnd) => {
      if (event.url) this.currentRoute = event.url;
    });

    this.jwtData = { token: null, payload: null };
    this.debug.l(
      ">>> SET DEFAULT LOCAL JWT DATA WHEN APP INIT <<<",
      this.jwtData
    );
    */

    // initialize api tapper
    this.initializeEventTapper();
  }

  /**
   * Do we have an active session?
   */
  get isSessionActive(): boolean {
    return !!this.jwtData?.token;
  }

  // @todo create an observable, which lest parts of the app know when session is
  //       recovered, or lost or something ...

  /**
   * Subject to broadcast token changes
   */
  private _tokenData$: BehaviorSubject<JwtData.type> =
    new BehaviorSubject<JwtData.type>({ token: "", payload: {} });

  /**
   * Observable to follow token changes
   */
  get tokenData$(): Observable<JwtData.type> {
    return this._tokenData$.asObservable();
  }

  private _session$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(
    null,
  );

  get session$(): Observable<boolean> {
    return this._session$.asObservable();
  }

  public async quickFetchToken() {
    let jwt_data: JwtData.type = await this.storage.get(JWT_KEY);
    if (!jwt_data?.token) {
      await this.setTokenData(jwt_data);
    }
  }

  public async initialSessionRestore(deeplinkUrl: string = null): Promise<any> {
    this.analytics.addMarker("initial session restore", deeplinkUrl);

    // don't restore if we're just initializing
    this._restoreInProgress = false;

    let jwt_data: JwtData.type = await this.storage.get(JWT_KEY);

    this.debug.l("jwt data", jwt_data);
    this.debug.l("deeplink init url", deeplinkUrl);

    // we have nothing saved, reject
    if (!jwt_data?.token) {
      // get activation url
      // we try to restore session and get JWT from deeplink data
      // let activationUrl = await App.getLaunchUrl();
      let activationUrl = null;

      // debug
      // deeplinkUrl = 'https://seniornext.com/?h=1124882gdc9ba016a1e180b606e0e06ac2797f90';

      // overwrite if we have a deeplinkUrl param defined
      if (!!deeplinkUrl) {
        activationUrl = {
          url: deeplinkUrl,
        };
      }

      this.debug.l("app launch url", activationUrl);
      this.debug.l("push hash", this.pushNotificationService.sessionHash);

      // session restore from alternative sources

      if (activationUrl?.url) {
        // if we have an activation url use that to restore the session from hash string

        this.debug.l("Trying login with deeplink url hash", activationUrl?.url);

        let url = activationUrl.url;

        try {
          this.isRestore = true;
          await this.auth.withUrlSessionId(
            this.auth.extractSessionIdFromUrl(url),
          );

          // testing withouth valid h param
          // await firstValueFrom(this.auth.withUsername('bbtest2','123456'));
        } catch (error) {
          this.isRestore = false;
          // no session in deeplink url, we reject
          return Promise.reject(error);
        }

        this._session$.next(true);

        return Promise.resolve();
      } else if (this.pushNotificationService.sessionHash) {
        // if we have a hash and app was started from push use that to restore

        this.debug.l(
          "Trying login with push notification session value",
          this.pushNotificationService.sessionHash,
        );

        try {
          this.isRestore = true;
          await this.auth.withUrlSessionId(
            this.pushNotificationService.sessionHash,
          );

          // testing withouth valid h param
          // await firstValueFrom(this.auth.withUsername('bbtest2','123456'));
        } catch (error) {
          this.isRestore = false;
          // no session in deeplink url, we reject
          return Promise.reject(error);
        }

        this._session$.next(true);

        return Promise.resolve();
      }

      // no url, we reject
      return Promise.reject("no_token");
    }

    // set token data
    try {
      await this.setTokenData(jwt_data);
    } catch (error) {
      return Promise.reject(error);
    }

    // check if the session is still valid
    try {
      await firstValueFrom(this.auth.check());

      // uncomment to test session restore failsafe
      // throw new Error("test session restore failsafe error");
    } catch (error1) {
      try {
        // console.log("RESTORE TEST!");
        await this.restoreSessionFromFailsafe();
      } catch (error2) {
        return Promise.reject([error1, error2]);
      }
    }

    // session restored, we emmit
    // @todo we don't use these i think
    this._session$.next(true);
  }

  /**
   * Delete all session related data and set jwt to default
   */
  public async deleteSession() {
    await this.deleteFailsafeCredentials().catch((error) =>
      this.debug.l(">>> deleteFailsafeCredentials error", error),
    );
    await this.deleteTokenData().catch((error) =>
      this.debug.l(">>> deleteTokenData error", error),
    );

    await this.setTokenData({ token: null, payload: null });

    this.debug.l("_session.next(false)");
    this._session$.next(false);
    this.debug.l("navigate to login page");
  }

  /**
   * Set token data and emmit
   * @param jwtData
   */
  private setTokenData(jwtData: JwtData.type) {
    // set local token data
    this.jwtData = jwtData;
    this._tokenData$.next(this.jwtData);

    // this.jwtData.token = this.jwtData.token ? this.jwtData.token + 'a' : null;

    // set api class token
    this.api.apiToken = this?.jwtData?.token ?? null;

    // save to storage
    return this.storage
      .set(JWT_KEY, this.jwtData)
      .catch((error) => this.debug.l("storage set error", error));
  }

  /**
   * watch api communications, and handle session related stuff
   */
  private initializeEventTapper() {
    // auth event watcher

    this.auth.authEvents$.subscribe({
      next: async (data) => {
        // reset restore in progress after login
        this._restoreInProgress = false;

        this.debug.l(">>> auth event", data);
        /**
         * -- detect login and save jwt and failsafe credentials
         */

        // save failsafe data
        this.saveFailsafeCredentials(data.credentialData).catch((error) => {
          this.debug.l("saveFailsafeCredentials error", error);
        });

        // set/ save jwt data
        this.setTokenData(data.jwtData);

        // send login to analytics
        // only if there was no registration in the session

        if (!this.auth.wasRegistration) {
          this.analytics.doFireLoginEvent = true;
          this.analytics.loginMethod = data.credentialData.type;
        }

        // if this is a pure login, start bootstrap

        if (
          !this.boot.bootStatus.isInitialBoodDone &&
          !this.boot.bootStatus.isRecoveryEvent &&
          !this.isRestore
        ) {
          try {
            await this.boot.startInitialBoot();
          } catch (error) {
            this.logoutAndRestart();
          }
        }
      },
    });

    /**
     * Catch non standard errors, show toast
     */
    this.api.httpErrorStream$.subscribe({
      next: async (error: HttpErrorResponse) => {
        this.debug.l("error status", error.status);

        if (error.status == 0) {
          // await this.dialog.showErrorAlert('error','network_error');
          // @todo test this with no internet - it will not work if we switch browser to offline mode
          // because it cannot connect to local server and will throw exceptions
          // we need to - for example - block the stagign server

          await this.dialog.showToast("", "connection_error", "error");

          if (!this.misc.isAppUrl()) {
            this.misc.gotoErrorUrl();
          }

          // @todo maybe we show a full screen error message here if the user is not logged in yet?
        } else {
          this.debug.l("http error", error);

          /*
          if(_.isArray(error?.error?.errors))
          {
            await this.dialog.showToastBaseOnErrorResponse(error.error);
          }
          else
          {
            await this.dialog.showToast("error", error.message, "error");
          }
          */
        }
      },
    });

    /**
     * @todo
     * -- detect token change, and replace/save it here
     *  not api service, something else - move this to auth?
     *  or create a watcher service?
     */
    this.api.eventStream$.subscribe({
      next: async (data) => {
        // this.debug.l("http spy", data);

        // refresh token check

        if (data?.data && data?.data["headers"]) {
          let rt = data["data"]["headers"].get("refresh-token");
          if (rt) {
            // @todo refresh JWT in credentials and api class

            this.debug.l("refresh token", rt);
          }
        }

        // 401 error check for session restore potentially

        if (data?.data && data?.data["status"]) {
          let status = data.data["status"];

          switch (status) {
            case 401:
              // @todo show blocking loader while restoring session !!

              this.debug.l(
                "error callback for 401",
                this.auth.isAppRegInProgress(),
              );

              if (
                !this.auth.isAuthInProgress() &&
                !this.isRestoreInProgress &&
                !this.api.hasCallType("ignore_401", data.callType)
              ) {
                this.dialog.showLoading("logging_you_in");
                await this.restoreSessionFromFailsafe();
                this.dialog.hideLoading();
              } else {
                this.debug.l("401 recovery already in progress - skipping");
              }

              break;

            case 403:
              // you have no permission toast

              this.debug.l("error callback for 403");

              if (!this.api.hasCallType("background", data.callType)) {
                this.dialog.showToast("", "no_access_error", "error");
              }

              break;

            case 500:
              // general error toast

              this.debug.l("error callback for 500");

              if (!this.api.hasCallType("background", data.callType)) {
                this.dialog.showToast("", "unknown_error", "error");
              }

              // if we're not logged in then we show error page

              if (!this.misc.isAppUrl()) {
                this.misc.gotoErrorUrl();
              }

              break;

            case 404:
              // general error toast

              this.debug.l("error callback for 404");

              if (!this.api.hasCallType("background", data.callType)) {
                // Remove 404 error from /profile page
                if (!this.router.url.includes("/app/profile")) {
                  this.dialog.showToast("", "not_found", "error");
                }
              }

              break;
          }

          // @todo if we have network error - maybe show error page - only if we starting app?
        }
      },
    });
    /**
     * @todo
     * - we need an observable on api service where we communicate different errors, like no interenet / internet back
     *   or auth error or access denied error , or 500
     * - we need to subscribe to that in session, and when we get for example auth error then we try to recover stuff ?
     * - see how could we do retry if we get some of these errors, for example:
     * -- we try to fetch something, we get error 401
     * -- we restore the session, and retry -if possible, see how it's done
     */
  }

  private _restoreInProgress: boolean = false;

  get isRestoreInProgress(): boolean {
    return this._restoreInProgress;
  }

  public async restoreSessionFromFailsafe() {
    // @todo this will be deleted and removed from code when we switch to login page instead of templates as start page
    //       in that case boot sequencer will only be run in caso fo 1st app startup or manual login

    //this.bootSequencer.enabled = true;
    //this.analytics.logEvent("session_restore");

    this.analytics.addMarker("session failsafe");

    this._restoreInProgress = true;

    try {
      let cred = await this.getFailsafeCredentials();

      // try to auth
      await this.auth.withCredentials(cred);
    } catch (error) {
      this.debug.le("session restore failure", error);
      await this.analytics.logEvent(
        "session_restore_failed",
        <TME_session_restore_failed>{},
      );

      this.analytics.addMarker("failsafe failed");

      this.logoutAndRestart();
    }

    return Promise.resolve();
  }

  public async logoutAndRestart() {
    // logout
    this.dialog.showSplash();

    // send aggregated stats before logout
    await this.analytics.sendAggregatedEvents();

    // delete session, and logout
    await this.deleteSession();
    this.misc.restartApplication();
  }

  /**
   * Function to tell if saved credentials are valid
   * @param credentials
   * @returns
   */
  public isCredentialValid(credentials: CredentialData.type) {
    return !(!credentials?.type || !credentials?.secret);
  }

  /**
   * get failsafe credentials data from storage
   */
  private getFailsafeCredentials(): Promise<CredentialData.type> {
    return new Promise<CredentialData.type>((resolve, reject) => {
      this.storage
        .get(CREDENTIAL_KEY)
        .then((data: CredentialData.type) => {
          if (!this.isCredentialValid(data)) {
            reject("invalid credentials");
          } else {
            // decrypt secret
            // @todo find a better way
            data.secret = Utils.atobUnicode(data.secret);

            resolve(data);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * Save failsafe credentials data to storage
   *
   * @param credentials
   */
  private saveFailsafeCredentials(
    credentials: CredentialData.type,
  ): Promise<any> {
    // encrypt secret
    // @todo find a better way
    credentials.secret = Utils.btoaUnicode(credentials.secret);

    return this.storage.set(CREDENTIAL_KEY, credentials);
  }

  /**
   * Empty credentials failsafe data
   */
  private deleteFailsafeCredentials(): Promise<any> {
    return this.storage.remove(CREDENTIAL_KEY);
  }

  /**
   * Delete JWT data
   */
  private deleteTokenData(): Promise<any> {
    return this.storage.remove(JWT_KEY);
  }

  /**
   * Check if we have a session
   *
   * @param params ApiParams.SocketToken
   */
  public checkSession(): Observable<ApiResponses.ResponseBase.type> {
    return this.api.call({
      command: ApiCommands.CheckSession.name,
      method: "get",
      useToken: false,
    });
  }
}
