import { ElementRef, Injectable } from "@angular/core";
import { Title } from "@angular/platform-browser";
import { NavigationEnd, Router } from "@angular/router";
import { App, BackButtonListenerEvent } from "@capacitor/app";
import {
  IonRouterOutlet,
  MenuController,
  NavController,
  Platform,
} from "@ionic/angular";
import _ from "lodash";
import { Observable, Subject } from "rxjs";
import { RouteSettings } from "../app-routing.module";
import { Debug } from "../helpers/debug";
import { AutocloseOverlaysService } from "./autoclose-overlays.service";
import { DeeplinkService } from "./deeplink.service";

@Injectable({
  providedIn: "root",
})
export class MenuHelperService {
  constructor(
    private router: Router,
    private menuController: MenuController,
    private debug: Debug,
    private platform: Platform,
    private title: Title,
    private navController: NavController,
    private autocloseOverlaysService: AutocloseOverlaysService,
    private deeplinkService: DeeplinkService
  ) {
    this.setDefaults();
  }

  private DEFAULT_MENU_VISIBILITY = false;
  private DEFAULT_BACK_NAVIGATION = true;

  private menuId: string;
  private isBackEnabled: boolean = this.DEFAULT_BACK_NAVIGATION;
  private isMenuEnabled: boolean = this.DEFAULT_MENU_VISIBILITY;

  private _latestSettings: RouteSettings = {};
  private _backButtonObserver = null;

  private setDefaults() {
    this._latestSettings = {
      doEnableBackNavigation: this.DEFAULT_BACK_NAVIGATION,
      isMenuVisible: this.DEFAULT_MENU_VISIBILITY,
    };
  }

  /**
   * Tesll if back button is enabled at the moment
   */
  public get isBackButtonEnabled(): boolean {
    return this.isBackEnabled;
  }

  /**
   * Tells if the menu is visible at the moment
   */
  public get isMenuButtonEnabled(): boolean {
    return this.isMenuEnabled;
  }

  public routerOutlet: IonRouterOutlet;

  public setPageTitle() {}

  private backEvent$ = new Subject<BackButtonListenerEvent | any>();

  /**
   * Expose back button event
   */
  get BackEventStream$(): Observable<BackButtonListenerEvent | any> {
    return this.backEvent$.asObservable();
  }

  private _currentURL: string = "/";

  get currentURL() {
    return this._currentURL;
  }

  private _previousUrl: string = "/";

  get previousURL() {
    return this._previousUrl;
  }

  public init(menuId: string) {
    this.menuId = menuId;

    this.router.events
      // .pipe( filter ( event => event instanceof NavigationEnd))
      .subscribe({
        next: (e: any) => {
          // if we have this somewhere in the events, we store them
          if (
            !_.isUndefined((<RouteSettings>e?.snapshot?.data)?.isMenuVisible)
          ) {
            this._latestSettings.isMenuVisible = (<RouteSettings>(
              e.snapshot.data
            )).isMenuVisible;
          }

          // if we have this somewhere in the events, we store them
          if (
            !_.isUndefined(
              (<RouteSettings>e?.snapshot?.data)?.doEnableBackNavigation
            )
          ) {
            this._latestSettings.doEnableBackNavigation = (<RouteSettings>(
              e.snapshot.data
            )).doEnableBackNavigation;
          }

          // we know that this is the end of navigation, so we apply and reset the container
          if (e instanceof NavigationEnd) {
            // set url variables

            this._previousUrl = this._currentURL;
            this._currentURL = this.router.url;

            // set menu rules

            this.isMenuEnabled = this._latestSettings.isMenuVisible;
            this.menuController.enable(
              this._latestSettings.isMenuVisible,
              this.menuId
            );
            this.isBackEnabled = this._latestSettings.doEnableBackNavigation;
            this.routerOutlet.swipeGesture =
              this._latestSettings.doEnableBackNavigation;

            // let's set defaults after applying them
            this.setDefaults();
          }
        },
        error: (error) => {
          this.debug.le("menu helper error", error);
        },
      });

    // back navigation handler

    this.platform.backButton.subscribeWithPriority(
      9999,
      async (processNextHandler) => {
        // Close all popovers on back navigation.
        try {
          const closeOverlay = await this.autocloseOverlaysService.trigger();
          // Prevent going back if the popovers were successfully closed.
          if (closeOverlay) return;
        } catch (error) {
          this.debug.le("close overlays error", error);
        }

        // fire observer
        this.backEvent$.next(processNextHandler);

        if (this.isBackEnabled) {
          // can go back

          if (await this.menuController.isOpen("main")) {
            await this.menuController.close("main");
          } else {
            // go back
            // processNextHandler();
            this.navController.pop();
          }
        } else {
          if (await this.menuController.isOpen("main")) {
            await this.menuController.close("main");
          }
        }

        // if no back enabled, don't do anything
      }
    );

    this.deeplinkService.wasDeeplink$.subscribe({
      next: (res) => {
        if (res.status === "init") this.autocloseOverlaysService.trigger();
      },
    });
  }
}
