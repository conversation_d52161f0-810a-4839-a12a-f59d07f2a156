import { Injectable } from "@angular/core";
import { environment as Env } from "../../environments/environment";
import { Platform } from "@ionic/angular";
import { AccountService } from "./account.service";
import { UtilsService } from "./utils.service";
import { MiscService } from "./misc.service";
import { ApiResponses } from "./api/api.responses";
import { ProfileOwn } from "../schemas/profile-own";
import { MatchPref } from "../schemas/match-pref";
import { ApiService } from "./api/api.service";
import { ApiCommands } from "./api/api.commands";
import { Debug } from "../helpers/debug";
import { IBootAction } from "./boot.service";
import { Device } from "@capacitor/device";
import { firstValueFrom } from "rxjs";

export class AdsData {
  public redirect: string;
  public image: string;
  public title: string;
  public description: string;
}

@Injectable({
  providedIn: "root",
})
export class AdsService implements IBootAction {
  /**
   * @type {number}
   */
  private defaultFormat: number;

  /**
   * @type {string}
   */
  private forceUserAgent: string = "";

  /**
   * @type {string}
   */
  private referer: string = "app." + Env.APP_WEBSITE;

  /**
   * Preferred language
   * @type {string}
   */
  private language: string = "";

  /**
   * @type {number}
   */
  private format: number;

  /**
   * @type {OwnProfile}
   */
  private account: ProfileOwn.type = null;

  /**
   * @type {MatchPref}
   */
  private matchPref: MatchPref.type;

  /**
   * @type {AdsData}
   */
  public adDataArr: AdsData[] = [];

  /**
   * @type {{nocache: string; servefrom: string; accept_language: string; tag: string; stat_tag: string; network: string; raw: string; quantity: string; zone: string; looking_for: string; gender: string; vip_subnet: string}}
   */
  private keys: any = {
    nocache: "_nocache",
    servefrom: "_servefrom",
    accept_language: "accept_language",
    tag: ">tag",
    stat_tag: "_stat_tag",
    network: "_network",
    raw: "_raw",
    quantity: "_quantity",
    zone: "_zone",
    looking_for: ">looking_for",
    gender: ">gender",
    vip_subnet: "vip_subnet",
    testing: "_testing",
    debug: "_debug",
  };

  /**
   * @type {{small_picture_text_100x80: number; sponsor_logo_120x60: number; text_ad_headline_description: number; iframe_mixed_zone: number; banner_leaderboard_728x90: number; banner_standard_468x60: number; banner_wide_skyscraper160x600: number; banner_rectangle300x250: number; im_pop_banner: number; ip_pop_advanced_text_image: number; banner_wide_footer950x250: number; popunder: number; text_ad_navbar: number; square_banner_text250x250: number; mobile_banner_300x250: number; mobile_banner_picture_text100x80: number; application_ad: number; large_square_1040x1040: number; inbox: number}}
   */
  private types: any = {
    small_picture_text_100x80: 1,
    sponsor_logo_120x60: 2,
    text_ad_headline_description: 5,
    iframe_mixed_zone: 105,
    banner_leaderboard_728x90: 728090,
    banner_standard_468x60: 468060,
    banner_wide_skyscraper160x600: 160600,
    banner_rectangle300x250: 300250,
    im_pop_banner: 90000,
    ip_pop_advanced_text_image: 90001,
    banner_wide_footer950x250: 950250,
    popunder: 111,
    text_ad_navbar: 100,
    square_banner_text250x250: 250250,
    mobile_banner_300x250: ********,
    mobile_banner_picture_text100x80: 881,
    application_ad: 444,
    large_square_1040x1040: ********,
    inbox: 90002,
  };

  /**
   * Constructor
   */
  constructor(
    private platform: Platform,
    private accountService: AccountService,
    private utils: UtilsService,
    private misc: MiscService,
    private api: ApiService,
    private dbg: Debug,
  ) {
    this.defaultFormat = this.types.small_picture_text_100x80;

    this.platform.ready().then(async () => {
      if (this.misc.isNativePlatform()) {
        // Set the User Agent
        // this.forceUserAgent = this.platform.userAgent(); // @todo !!!

        // Set the preferred Ad language
        let lngRes = await Device.getLanguageCode();
        this.language = lngRes.value;
      }
    });
  }

  public async boot(): Promise<any> {
    if (this.accountService.account.is_premium) return Promise.resolve();

    // I performed hundreds of fetch ad tests (3 requests in a row), and
    // 90% of the time or more, if the first request fails, the other two
    // will also fail. So I'll stick with sending only one request at the
    // beginning of the boot process, and if we don't receive any data, we
    // won't show any ads.
    await this.fetchAds(250250, { _quantity: 5 });
    // don't wait for ads
    return Promise.resolve();
  }

  /**
   * Fetch Ads.
   *
   * @param {string} referrer
   * @param {number} format
   * @return {Promise<never | AdsData[]>}
   */
  public fetchAds(
    format: number = this.defaultFormat,
    overwrite?: {},
  ) {
    // For testing!
    // this.fakeAdsData.forEach(val => {
    //   this.adDataArr.push(<AdsData>val);
    // });
    // return;

    return new Promise<ApiResponses.AdsResponse.type>((resolve, reject) => {
      this.account = this.accountService.account;
      this.matchPref = this.accountService.matchPref;
      this.format = format;
      let params = {
        ip: Env.AD.ip,
        referer: this.referer,
        force_ua: this.forceUserAgent,
        data: this.getRequestData(overwrite),
      };

      firstValueFrom(
        this.api.call<ApiResponses.AdsResponse.type>({
          command: ApiCommands.Ads.name,
          method: "post",
          useToken: true,
          params,
        }),
      )
        .then((res) => {
          this.adDataArr = <AdsData[]>res.data;
          resolve(res);
        })
        .catch((err) => {
          this.dbg.le("Error occurred while fetching Ads", err);
          reject(err);
        })
    });
  }

  /**
   * Get Ads data.
   *
   * @return {AdsData}
   */
  public getAdData(): AdsData {
    return this.adDataArr[0];
  }

  /**
   * Get Ads data.
   *
   * @return {AdsData[]}
   */
  public getAdsData(): AdsData[] {
    return this.adDataArr;
  }

  /**
   * @returns {number}
   */
  public getType() {
    return this.defaultFormat;
  }

  /**
   * Get language.
   *
   * @returns {string}
   */
  public getLang(): string {
    return this.language;
  }

  //private getOverwritenValue()

  /**
   * Set request data.
   *
   * @returns {object}
   */
  private getRequestData(overwrite: object = {}) {
    let data = {};

    data[this.keys.nocache] = new Date();

    data[this.keys.servefrom] = overwrite[this.keys.servefrom] ?? Env.AD.serve_from;
    data[this.keys.accept_language] = overwrite[this.keys.accept_language] ?? (Env.AD.lng || this.getLang());
    data[this.keys.tag] = overwrite[this.keys.tag] ?? Env.AD.tags;
    data[this.keys.stat_tag] = overwrite[this.keys.stat_tag] ?? Env.APP_NAME + Env.AD.stat_tag_home;
    data[this.keys.network] = overwrite[this.keys.network] ?? Env.AD.network;
    data[this.keys.raw] = overwrite[this.keys.raw] ?? 1;
    data[this.keys.quantity] = overwrite[this.keys.quantity] ?? 1;
    data[this.keys.zone] = overwrite[this.keys.zone] ?? this.format;
    data[this.keys.vip_subnet] = overwrite[this.keys.vip_subnet] ?? Env.AD.subnet;

    data[this.keys.gender] = this.utils.getGenderById(this.account.gender_id).name;

    data[this.keys.looking_for] = this.utils.getGenderById(this.account.looking_id).name;

    // unset personal stuff if we don't have CCPA consent
    if (this.accountService.isCCPARequired && !this.accountService.CCPAStatus) {
      // we unset gender and looking for - because CCPA does not allow sending personal data
      // @todo see what to do here - selet a random gender instead? or what? or just hide ads alltogehter?
      data[this.keys.gender] = "";
      data[this.keys.looking_for] = "";
      data[this.keys.tag] = "";
    }

    if (Env.IS_DEBUG) {
      data[this.keys.accept_language] = Env.AD.lng_debug;
      data[this.keys.debug] = 1;
      data[this.keys.testing] = 1;
    }

    return data;
  }

  /**
   * Is fetch ads in progress?
   * @returns
   */
  public isFetchAdsInProgress() {
    return this.api.isInEventList({
      command: ApiCommands.Ads.name,
    });
  }

  private fakeAdsData = [
    {
        "width": 250,
        "height": 250,
        "type": "text",
        "title": "Upoznaj prelepe udovice iz tvog mesta!",
        "description": "Puno njih ima profil na na\u0161em sajtu! Na\u0111i ih!",
        "image": "http:\/\/ww2.cdndeposit.com\/1\/8\/18f25073bf42b239435689b3b0df95e5f6e60801.jpg",
        "iframe": "http:\/\/ad.singlesadnetwork.com\/data\/frames\/framegen-50-82117.html?http%3A%2F%2Fad.singlesadnetwork.com%2Fadserve%2Fgo.php%3Fid%3D82117%26xp%3DMS41MDA%253D%26network%3D50%26c%3D2222%26p%3Dapp.front-m1.dvipdev.com%26ticket%3D3807052b7058069919f24fcea595f1c7c1ea354d%26_testing%3D1%26_mapper_geo_city%3DBe%25C4%258Dej%26_zone_id%3D250250%26_creative_name%3DDatingaWidower.net%2B-%2BRS%2BF%2B-%2BSquare%2BBanner%2BAd1%26_track_id%3D%26__stat_tag%3DSenior%2BNext%2BDev%2B-%2BHome%26_aths%3D7vxSKTBHca33kHa4fHOE49Xzcwo%26_algo%3Dx",
        "redirect": "http:\/\/ad.singlesadnetwork.com\/adserve\/go.php?id=82117&xp=MS41MDA%3D&network=50&c=2222&p=app.front-m1.dvipdev.com&ticket=3807052b7058069919f24fcea595f1c7c1ea354d&_testing=1&_mapper_geo_city=Be%C4%8Dej&_zone_id=250250&_creative_name=DatingaWidower.net+-+RS+F+-+Square+Banner+Ad1&_track_id=&__stat_tag=Senior+Next+Dev+-+Home&_aths=7vxSKTBHca33kHa4fHOE49Xzcwo&_algo=x",
        "_debug_cpc": "1.500",
        "redirect_label": "http:\/\/www.datingawidower.net\/free_db\/?aff_id=online_rs&aff_tr=1&aff_pg=2&aff_cp=[[mapper_geo_city]]&aff_adg=[[zone_id]]&aff_src=[[placement]]&aff_kw=[[creative_name]]&track_id=[[track_id]]"
    },
    {
        "width": 250,
        "height": 250,
        "type": "text",
        "title": "Na\u0111i starije dame koje tra\u017ee ljubav!",
        "description": "Klikni ovde ako si usamljen!",
        "image": "http:\/\/ww2.cdndeposit.com\/1\/3\/130472922f7fe8bb0c588b5b9035efdccfa00be2.jpg",
        "iframe": "http:\/\/ad.singlesadnetwork.com\/data\/frames\/framegen-50-82124.html?http%3A%2F%2Fad.singlesadnetwork.com%2Fadserve%2Fgo.php%3Fid%3D82124%26xp%3DMS41MDA%253D%26network%3D50%26c%3D2222%26p%3Dapp.front-m1.dvipdev.com%26ticket%3D579d046f283718e3fd3936720a7e8cba2608e3d7%26_testing%3D1%26_mapper_geo_city%3DBe%25C4%258Dej%26_zone_id%3D250250%26_creative_name%3DDatingForSeniors.com%2B-%2BRS%2BF%2B-%2BSquare%2BBanner%2BAd1%26_track_id%3D%26__stat_tag%3DSenior%2BNext%2BDev%2B-%2BHome%26_aths%3D7vxSKTBHca33kHa4fHOE49Xzcwo%26_algo%3Dx",
        "redirect": "http:\/\/ad.singlesadnetwork.com\/adserve\/go.php?id=82124&xp=MS41MDA%3D&network=50&c=2222&p=app.front-m1.dvipdev.com&ticket=579d046f283718e3fd3936720a7e8cba2608e3d7&_testing=1&_mapper_geo_city=Be%C4%8Dej&_zone_id=250250&_creative_name=DatingForSeniors.com+-+RS+F+-+Square+Banner+Ad1&_track_id=&__stat_tag=Senior+Next+Dev+-+Home&_aths=7vxSKTBHca33kHa4fHOE49Xzcwo&_algo=x",
        "_debug_cpc": "1.500",
        "redirect_label": "http:\/\/www.datingforseniors.com\/free_db\/?aff_id=online_rs&aff_tr=1&aff_pg=2&aff_cp=[[mapper_geo_city]]&aff_adg=[[zone_id]]&aff_src=[[placement]]&aff_kw=[[creative_name]]&track_id=[[track_id]]"
    },
    {
        "width": 250,
        "height": 250,
        "type": "text",
        "title": "Upoznaj devojke pravoslavne veroispovesti!",
        "description": "Na\u0111i pravu ljubav na na\u0161em sajtu!",
        "image": "http:\/\/ww2.cdndeposit.com\/d\/9\/d9a4478e97792b34c031fd94e362b2fcc379eee5.jpg",
        "iframe": "http:\/\/ad.singlesadnetwork.com\/data\/frames\/framegen-50-82099.html?http%3A%2F%2Fad.singlesadnetwork.com%2Fadserve%2Fgo.php%3Fid%3D82099%26xp%3DMS41MDA%253D%26network%3D50%26c%3D2222%26p%3Dapp.front-m1.dvipdev.com%26ticket%3D59c487bdab43879a46cda6cf4353bd3b99040ff3%26_testing%3D1%26_mapper_geo_city%3DBe%25C4%258Dej%26_zone_id%3D250250%26_creative_name%3DChristianLifestyle.com%2B-%2BRS%2BChristian%2B-%2BSquare%2BBanner%2BF%2BAd1%26_track_id%3D%26__stat_tag%3DSenior%2BNext%2BDev%2B-%2BHome%26_aths%3D7vxSKTBHca33kHa4fHOE49Xzcwo%26_algo%3Dx",
        "redirect": "http:\/\/ad.singlesadnetwork.com\/adserve\/go.php?id=82099&xp=MS41MDA%3D&network=50&c=2222&p=app.front-m1.dvipdev.com&ticket=59c487bdab43879a46cda6cf4353bd3b99040ff3&_testing=1&_mapper_geo_city=Be%C4%8Dej&_zone_id=250250&_creative_name=ChristianLifestyle.com+-+RS+Christian+-+Square+Banner+F+Ad1&_track_id=&__stat_tag=Senior+Next+Dev+-+Home&_aths=7vxSKTBHca33kHa4fHOE49Xzcwo&_algo=x",
        "_debug_cpc": "1.500",
        "redirect_label": "http:\/\/www.christianlifestyle.com\/free_db\/?aff_id=online_rs&aff_tr=1&aff_pg=2&aff_cp=[[mapper_geo_city]]&aff_adg=[[zone_id]]&aff_src=[[placement]]&aff_kw=[[creative_name]]&track_id=[[track_id]]"
    },
    {
        "width": 250,
        "height": 250,
        "type": "text",
        "title": "Do\u017eivite ljubav u poznim godinama!",
        "description": "Priklju\u010dite nam se i upoznajte seniore iz va\u0161eg kom\u0161iluka.",
        "image": "http:\/\/ww2.cdndeposit.com\/3\/8\/38401a8cdff01197067f3a56afc444658f555fc0.jpg",
        "iframe": "http:\/\/ad.singlesadnetwork.com\/data\/frames\/framegen-50-143994.html?http%3A%2F%2Fad.singlesadnetwork.com%2Fadserve%2Fgo.php%3Fid%3D143994%26xp%3DMS41MDA%253D%26network%3D50%26c%3D2222%26p%3Dapp.front-m1.dvipdev.com%26ticket%3D23575bbbb9f0b9c463070a56cf8e6853587dae41%26_testing%3D1%26_mapper_geo_city%3DBe%25C4%258Dej%26_zone_id%3D250250%26_creative_name%3DDatingForSeniors.com%2B-%2BRS%2BF%2B-%2BSquare%2BBanner%2BAd2%26_track_id%3D%26__stat_tag%3DSenior%2BNext%2BDev%2B-%2BHome%26_aths%3D7vxSKTBHca33kHa4fHOE49Xzcwo%26_algo%3Dx",
        "redirect": "http:\/\/ad.singlesadnetwork.com\/adserve\/go.php?id=143994&xp=MS41MDA%3D&network=50&c=2222&p=app.front-m1.dvipdev.com&ticket=23575bbbb9f0b9c463070a56cf8e6853587dae41&_testing=1&_mapper_geo_city=Be%C4%8Dej&_zone_id=250250&_creative_name=DatingForSeniors.com+-+RS+F+-+Square+Banner+Ad2&_track_id=&__stat_tag=Senior+Next+Dev+-+Home&_aths=7vxSKTBHca33kHa4fHOE49Xzcwo&_algo=x",
        "_debug_cpc": "1.500",
        "redirect_label": "http:\/\/www.datingforseniors.com\/free_db\/?aff_id=online_rs&aff_tr=1&aff_pg=2&aff_cp=[[mapper_geo_city]]&aff_adg=[[zone_id]]&aff_src=[[placement]]&aff_kw=[[creative_name]]&track_id=[[track_id]]"
    },
    {
        "width": 250,
        "height": 250,
        "type": "text",
        "title": "Hri\u0161\u0107anke tra\u017ee nova poznanstva!",
        "description": "Upoznaj devojke \u010distog srca na na\u0161em sajtu!",
        "image": "http:\/\/ww2.cdndeposit.com\/8\/8\/8841ceb81e0974477fbc4e56959fbc21270db400.jpg",
        "iframe": "http:\/\/ad.singlesadnetwork.com\/data\/frames\/framegen-50-143989.html?http%3A%2F%2Fad.singlesadnetwork.com%2Fadserve%2Fgo.php%3Fid%3D143989%26xp%3DMS41MDA%253D%26network%3D50%26c%3D2222%26p%3Dapp.front-m1.dvipdev.com%26ticket%3Ded0be0a1f20f3e46ef10329d412edd3121a6077e%26_testing%3D1%26_mapper_geo_city%3DBe%25C4%258Dej%26_zone_id%3D250250%26_creative_name%3DChristianLifestyle.com%2B-%2BRS%2BChristian%2B-%2BSquare%2BBanner%2BF%2BAd2%26_track_id%3D%26__stat_tag%3DSenior%2BNext%2BDev%2B-%2BHome%26_aths%3D7vxSKTBHca33kHa4fHOE49Xzcwo%26_algo%3Dx",
        "redirect": "http:\/\/ad.singlesadnetwork.com\/adserve\/go.php?id=143989&xp=MS41MDA%3D&network=50&c=2222&p=app.front-m1.dvipdev.com&ticket=ed0be0a1f20f3e46ef10329d412edd3121a6077e&_testing=1&_mapper_geo_city=Be%C4%8Dej&_zone_id=250250&_creative_name=ChristianLifestyle.com+-+RS+Christian+-+Square+Banner+F+Ad2&_track_id=&__stat_tag=Senior+Next+Dev+-+Home&_aths=7vxSKTBHca33kHa4fHOE49Xzcwo&_algo=x",
        "_debug_cpc": "1.500",
        "redirect_label": "http:\/\/www.christianlifestyle.com\/free_db\/?aff_id=online_rs&aff_tr=1&aff_pg=2&aff_cp=[[mapper_geo_city]]&aff_adg=[[zone_id]]&aff_src=[[placement]]&aff_kw=[[creative_name]]&track_id=[[track_id]]"
    }
  ];
}
