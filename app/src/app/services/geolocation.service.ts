import { Injectable } from "@angular/core";
import { Debug } from "../helpers/debug";
import { DialogHelperService } from "./dialog-helper.service";
import { IBootAction } from "./boot.service";
import { AccountService } from "./account.service";
import { firstValueFrom } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class GeolocationService implements IBootAction {
  /**
   * Constructor.
   */
  constructor(
    private dbg: Debug,
    private dlg: DialogHelperService,
    private accountService: AccountService
  ) {}

  /**
   * Boot.
   *
   * @returns {Promise<void>}
   */
  public async boot(): Promise<any> {
    if (!this.accountService.settings.auto_update_location) return;

    return this.updateLocation();
  }

  /**
   * Update account location.
   *
   * @returns {Promise<boolean>}
   */
  public updateLocation(): Promise<boolean> {
    return this.accountService
      .geolocation()
      .then(async ({ data }) => {
        if (!data) return Promise.resolve(false);
        await firstValueFrom(this.accountService.getNewGeolocation());
        return Promise.resolve(true);
      })
      .catch((e) => {
        return Promise.reject(false);
        this.dbg.le("get new geolocation error", e);
      });
  }
}
