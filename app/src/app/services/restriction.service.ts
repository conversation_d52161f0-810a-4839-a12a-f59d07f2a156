import { Injectable } from "@angular/core";
import { ApiService } from "./api/api.service";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiCommands } from "./api/api.commands";
import { firstValueFrom } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class RestrictionService {
  constructor(private api: ApiService) {}

  /**
   * Get a specific restriction status.
   * Tells if user is restricted to do a certain action.
   *
   * @param {ApiParams.RestrictionCheck.type} params
   * @returns {Promise<ApiResponses.RestrictionCheckResponse.type>}
   */
  public check(params: ApiParams.RestrictionCheck.type) {
    return firstValueFrom(
      this.api.call<ApiResponses.RestrictionCheckResponse.type>({
        command: ApiCommands.RestrictionCheck.name,
        method: "get",
        useToken: true,
        params,
      })
    );
  }

  /**
   * Get a all restriction data.
   *
   * @returns {Promise<ApiResponses.RestrictionAllResponse.type>}
   */
  public all() {
    return firstValueFrom(
      this.api.call<ApiResponses.RestrictionAllResponse.type>({
        command: ApiCommands.RestrictionAll.name,
        method: "get",
        useToken: true,
      })
    );
  }
}
