import { Injectable } from "@angular/core";
import { Observable, Subject } from "rxjs";
import { ChatMessage } from "./chat-message.interface";

/**
 * Class to emmit and liste to new chat messages
 */

@Injectable({
  providedIn: "root",
})
export class ChatIncomingService {
  // subject for new incoming message
  private _messageStream = new Subject<ChatMessage>();

  get messageStream$(): Observable<ChatMessage> {
    return this._messageStream.asObservable();
  }

  /**
   * Use this to emmit message to the outside world
   * @param message
   */
  public emmitMessage(message: ChatMessage) {
    this._messageStream.next(message);
  }

  constructor() {}
}
