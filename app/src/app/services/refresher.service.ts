import { Injectable } from "@angular/core";
import { Debug } from "../helpers/debug";
import { DialogHelperService } from "./dialog-helper.service";
import { Observable, Subject } from "rxjs";
import { MiscService } from "./misc.service";
import { environment as Env } from "../../environments/environment";
import { UtilsService } from "./utils.service";

/**
 * Refreshable interface.
 */
export interface Refreshable {
  /**
   * Handle refresh data.
   */
  refreshData(): void;
}

/**
 * Basic schedule data.
 */
interface ScheduleData {
  obj: any;
  done: boolean;
}

@Injectable({
  providedIn: "root",
})
export class RefresherService {
  /**
   * Turn ON/OFF refresher.
   */
  public isActive: boolean = Env.REFRESHER;

  /**
   * Schedule list.
   *
   * @type {Array}
   */
  private schedule: ScheduleData[] = [];

  /**
   * Subject to emit when refresh start.
   */
  private _refreshStart$: Subject<void> = new Subject<void>();

  /**
   * Subject to emit when refresh end.
   */
  private _refreshEnd$: Subject<void> = new Subject<void>();

  /**
   * Subject to emit on refresh error.
   */
  private _refreshError$: Subject<any> = new Subject<any>();

  /**
   * Constructor.
   *
   * @param {Debug} dbg
   */
  constructor(
    public dbg: Debug,
    public dlg: DialogHelperService,
    public misc: MiscService,
    public utils: UtilsService,
  ) {}

  /**
   * Refresh start.
   */
  get refreshStart$(): Observable<void> {
    return this._refreshStart$.asObservable();
  }

  /**
   * Refresh end.
   */
  get refreshEnd$(): Observable<void> {
    return this._refreshEnd$.asObservable();
  }

  /**
   * Refresh error.
   */
  get refreshError$(): Observable<any> {
    return this._refreshError$.asObservable();
  }

  /**
   * Start refresh.
   */
  public async refresh(): Promise<void> {
    // Turn off refresher and use old restartApplication method.
    // In this case, we don't want to trigger any of the following methods: onStart, or onEnd, onError.
    if (!this.utils.config["REFRESHER"]) {
      this.dbg.l("Refresher service disabled!");
      return this.misc.restartApplication();
    }

    this.dbg.l("Refresher service enabled!");
    await this.onRefreshStart();

    try {
      await this.handleSchedule();
    } catch (e) {
      await this.onRefreshError(e);
    }

    await this.onRefreshEnd();
  }

  /**
   * Add to schedule.
   *
   * @param data
   * @return {RefresherService}
   */
  public add(data: any): RefresherService {
    if (data instanceof Array) {
      data.forEach((value) => {
        this.add(value);
      });

      return this;
    }

    // We don't want duplicates in the schedule list.
    if (!this.isScheduled(data)) {
      // Add to schedule.
      this.schedule.push({ obj: data, done: false });
    }

    return this;
  }

  /**
   * Execute handlers.
   */
  private async handleSchedule() {
    for (let i = 0; i < this.schedule.length; i++) {
      await this.schedule[i].obj["refreshData"]();
      // this.schedule[i].done = true;
    }
  }

  /**
   * Check if we already have object in schedule list?
   *
   * @param {ScheduleData} data
   * @returns {boolean}
   */
  private isScheduled(data: ScheduleData): boolean {
    return this.schedule.some((value) => {
      return (
        value.obj.constructor.name !== "undefined" &&
        value.obj.constructor.name === data.constructor.name
      );
    });
  }

  /**
   * Things we want to do as the refresh process begins...
   */
  private async onRefreshStart(): Promise<void> {
    this._refreshStart$.next();
    await this.dlg.showLoading();
  }

  /**
   * Things we want to do after the refresh ends...
   */
  private async onRefreshEnd(): Promise<void> {
    await this.dlg.hideLoading();
    this._refreshEnd$.next();
  }

  /**
   * Things we want to do when an error occurs...
   */
  private async onRefreshError(error: any): Promise<void> {
    this.dbg.le("Refreshable error", error);
    await this.dlg.hideLoading();
    this._refreshError$.next(error);
  }
}
