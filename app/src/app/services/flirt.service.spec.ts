import { TestBed } from "@angular/core/testing";

import { FlirtService } from "./flirt.service";
import { ApiService } from "./api/api.service";

describe("FlirtService", () => {
  let service: FlirtService;
  let apiSpy: ApiService;

  beforeEach(() => {
    apiSpy = jasmine.createSpyObj("ApiService", ["call"]);

    TestBed.configureTestingModule({
      providers: [{ provide: ApiService, useValue: apiSpy }],
    });
    service = TestBed.inject(FlirtService);
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });
});
