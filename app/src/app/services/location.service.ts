import { Injectable } from "@angular/core";
import { Debug } from "../helpers/debug";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiCommands } from "./api/api.commands";
import { tap } from "rxjs/operators";
import { ApiService } from "./api/api.service";
import { LocationHelper } from "../schemas/location-helper";
import { Router } from "@angular/router";
import _ from "lodash";
import { NavController } from "@ionic/angular";
import { StateService } from "./state.service";
import { firstValueFrom } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class LocationService {
  /**
   * List of pages.
   */
  public pages: string[] = [
    "location-country",
    "location-city",
    "location-zip",
  ];

  /**
   * Location.
   */
  public location: LocationHelper.type;

  /**
   * @type {boolean}
   */
  public isProcessing: boolean = false;

  /**
   * Filtered countries.
   */
  public filteredList: any[] = [];

  /**
   *
   * @type {string}
   */
  public filter: string = "";

  /**
   * Show cancel button in search input.
   *
   * @type {boolean}
   */
  public shouldShowCancel: boolean = false;

  /**
   * Backup country list.
   */
  public countryList: any[] = [];

  /**
   * Backup country list.
   */
  public cityList: any[];

  /**
   * Selected country.
   *
   * @type {string}
   */
  public selectedCountry: string = "";

  /**
   * Selected city.
   *
   * @type {string}
   */
  public selectedCity: string = "";

  /**
   * Selected zip.
   *
   * @type {string}
   */
  public selectedZip: string = "";

  /**
   * Action.
   *
   * @type {string}
   */
  public actionTitle: string = "next";

  /**
   * Root page.
   *
   * @type {string}
   */
  public rootPage: string = "/app/my-profile-edit";

  /**
   * Next page.
   *
   * @type {string}
   */
  public nextPage: string = "";

  constructor(
    public api: ApiService,
    public dbg: Debug,
    public router: Router,
    public navCtrl: NavController,
    public stateService: StateService
  ) {}

  public resetSelectionData() {
    this.selectedCity = "";
    this.selectedCountry = "";
    this.selectedZip = "";
  }

  /**
   * Helper method for location.
   *
   * @param {ApiParams.LocationHelper.type} params
   * @returns {Promise<ApiResponses.LocationHelperResponse.type>}
   */
  public locationHelper(
    params?: ApiParams.LocationHelper.type | any
  ): Promise<ApiResponses.LocationHelperResponse.type> {
    return firstValueFrom(
      this.api
        .call<ApiResponses.LocationHelperResponse.type>({
          command: ApiCommands.LocationHelper.name,
          method: "get",
          useToken: true,
          params,
        })
        .pipe(
          tap((data) => {
            // keep data
            this.location = data.data;
          })
        )
    );
  }

  /**
   * Set default country list.
   */
  public setFilteredList(list: string) {
    this.filteredList = _.map(this.location[list], (v, i) => {
      return { key: i, value: v };
    });
    if (list === "country_list") {
      this.countryList = this.filteredList;
    }

    switch (list) {
      case "country_list":
        this.countryList = this.filteredList;
        break;
      case "city_list":
        this.cityList = this.filteredList;
        break;
    }
  }

  /**
   * Go to the next page.
   */
  public next() {
    if (!this.hasNextPage()) return;

    if (this.navigateToRootPage()) {
      this.stateService.set("location", "manual", { status: "completed" });

      return this.navCtrl
        .navigateBack(this.rootPage)
        .catch((error) => this.dbg.le("nav back error", error));
    }

    this.router
      .navigate(["/app/" + this.nextPage])
      .catch((error) => this.dbg.le("navigate error", error));
  }

  /**
   * Do we have next page?
   *
   * @returns {boolean}
   */
  private hasNextPage(): boolean {
    return !!this.nextPage.length;
  }

  /**
   * Go to root page?
   *
   * @returns {boolean}
   */
  private navigateToRootPage(): boolean {
    return this.nextPage === this.rootPage;
  }
}
