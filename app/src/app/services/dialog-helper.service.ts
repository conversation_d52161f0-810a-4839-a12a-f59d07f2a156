import {
  Injectable,
  Component,
  HostListener,
  Optional,
  Inject,
} from "@angular/core";
import {
  AlertController,
  AlertOptions,
  LoadingController,
  ModalController,
  ToastController,
} from "@ionic/angular";
import { TranslateService } from "@ngx-translate/core";
import * as _ from "lodash";
import { MiscService } from "./misc.service";
import { firstValueFrom, Observable } from "rxjs";
import { ApiResponses } from "./api/api.responses";
import { StateService } from "./state.service";
import { Router } from "@angular/router";
import { AnalyticsService } from "./analytics.service";
import { Debug } from "../helpers/debug";

@Injectable({
  providedIn: "root",
})
export class DialogHelperService {
  constructor(
    private alertCtrl: AlertController,
    private translateService: TranslateService,
    private misc: MiscService,
    private loadingController: LoadingController,
    private router: Router,
    private analytics: AnalyticsService,
    private debug: Debug,
    private toastCtrl:ToastController
  ) {}

  /**
   * Get dom ref for animates splash screen
   */
  private getSplashDOM() {
    return window.document.getElementById("global_animated_splash");
  }

  public isSplashVisible = false;

  /**
   * Show splash screen
   */
  public showSplash() {
    if (!this.isSplashVisible) {
      this.getSplashDOM().style.display = "flex";
    }

    this.isSplashVisible = true;
  }

  /**
   * Hide splash screen
   */
  public hideSplash(timeout: number = 1) {
    setTimeout(() => {
      this.getSplashDOM().style.display = "none";
      this.isSplashVisible = false;
    }, timeout);
  }

  private lc = null;
  private lc_countdown = null;

  /**
   * Show loading dialog ( will be closed in 65 seconds by default so we don't block the user interface unnecessarely )
   *
   * @param message
   * @param emoji
   * @param duration millis sec before auto close
   */
  public async showLoading(
    message: string = "loading",
    emoji: string = "",
    duration: number = 15000,
  ) {
    await this.hideLoading();

    this.lc_countdown = setTimeout(() => {
      this.debug.le("loading dialog timeout", {
        route: this.router.url,
      });
      this.hideLoading();
    }, duration);

    this.lc = await this.loadingController.create({
      message:
        this.translateService.instant(message) + (emoji ? ` ${emoji}` : ""),
      duration: duration,
    });

    await this.lc.present();
  }

  public async hideLoading() {
    if (this.lc_countdown) {
      clearTimeout(this.lc_countdown);
      this.lc_countdown = null;
    }

    if (this.lc) {
      await this.lc.dismiss();
      this.lc = null;
    }
  }

  /**
   * Helper function to show error
   * @todo style it
   *
   * @param title
   * @param message
   */
  public async showErrorAlert(
    title: string,
    content: string | string[],
    key: string = null,
    btnText: string = "OK",
  ) {
    this.analytics.logError(key ?? content ?? "default_error", "alert");

    this.analytics.addMarker("error_dialog", content)

    // hide loading on error
    this.hideLoading();

    let _content = "";

    if (_.isArray(content)) {
      _content =
        content.length == 1
          ? content[0]
          : "● " + (<string[]>content).join("<br />\n● ");
    } else {
      _content = this.translateService.instant(content);
    }

    let dlg = await this.alertCtrl.create({
      header: this.translateService.instant(title),
      message: _content,
      buttons: [this.translateService.instant(btnText)],
    });

    await dlg.present();
  }

  /**
   * Display a confirmation dialog
   *
   * @param {string} title
   * @param {string} text
   * @param {Function} onConfirm
   * @param {Function} onDismiss
   * @return {Promise<any>}
   */
  public async confirm(
    title: string,
    text: string,
    onConfirm: Function = () => {},
    onDismiss: Function = () => {},
  ) {
    let dlg = await this.alertCtrl.create({
      header: this.translateService.instant(title),
      message: this.translateService.instant(text),
      buttons: [
        {
          text: this.translateService.instant("yes"),
          handler: () => {
            onConfirm();
          },
        },
        {
          text: this.translateService.instant("no"),
          role: "cancel",
        },
      ],
    });

    dlg.onDidDismiss().finally(() => {
      onDismiss();
    });

    await dlg.present();
  }

  /**
   * Helper function to show alert
   * @todo style it
   *
   * @param title
   * @param message
   */
  public async showAlert(
    title: string,
    content: string,
    btnText: string = "ok",
    onClose: Function = () => {},
    rawMessagePrefix: string = "",
    rawTitlePrefix: string = "",
  ) {
    // hide loading on message
    this.hideLoading();

    let dlg = await this.alertCtrl.create({
      header: rawTitlePrefix + this.translateService.instant(title),
      message: rawMessagePrefix + this.translateService.instant(content),
      buttons: [this.translateService.instant(btnText)],
    });

    dlg.onDidDismiss().finally(() => {
      if (onClose instanceof Function) {
        // execute if function
        onClose();
      }
    });

    await dlg.present();
  }

  /**
   * More complex toast with click event, icon, image, etc
   *
   * @param title
   * @param body
   * @param image
   * @param icon
   * @param onClickCallback
   */
  public async clickableToast(
    title: string,
    body: string,
    image: string,
    icon: string = "💬",
    onClickCallback: Function = null,
    delay: number = 10000
  ) {
    // truncate message
    if (!body) {
      body = "";
    }
    let truncatedMessage = body.substring(0, 250);

    if (truncatedMessage.length < body.length) {
      truncatedMessage = truncatedMessage + " ...";
    }

    let completeContent =
      (title ? `${title} : ` : "") +
      truncatedMessage;

    let t = await this.toastCtrl.create({
      animated: true,
      duration: delay,
      message: icon+" "+completeContent,
      position: "top",
      color: "dark",
      id: truncatedMessage,
      layout: "stacked",
      swipeGesture: "vertical",
      translucent: true,
      buttons: [
        {
          text: this.translateService.instant('dismiss'),
          handler:()=>{
            t.remove()
          }
        },
        {
          text: this.translateService.instant('open'),
          handler:()=>{
            if (onClickCallback) {
              onClickCallback();
            }
          }
        }
      ]
    })

    t.present()
  }

  // list to store on screen toast messages, so we don't show duplicates while one is visible
  /*
   * Display toast message
   * @param message
   */
  public async showToast(
    title: string,
    message: string,
    toastType: "error" | "success" | "info" | "loading" | "warning" = "info",
    duration = 10000
  ) {
    // @todo add timestamp to prevent showing the same toast 100 times if we have a bunch of them

    let translatedMessage = this.translateService.instant(message);
    let translatedTitle = title ? this.translateService.instant(title) : "";

    let completeContent =
      (translatedTitle ? `${translatedTitle} : ` : "") +
      translatedMessage;
    
    var mapIcons = {
      "error": "⛔",
      "success": "✅",
      "info": "️",
      "loading": "⌛",
      "warning": "❗"
    }

    if (!mapIcons[toastType]) {
      toastType = "info";
    }

    let t = await this.toastCtrl.create({
      animated: true,
      duration: duration,
      message: mapIcons[toastType]+" "+completeContent,
      position: "top",
      color: "dark",
      id: message,
      layout: "stacked",
      swipeGesture: "vertical",
      translucent: true,
      buttons: [
        {
          text: this.translateService.instant('dismiss'),
          handler:()=>{
            t.remove()
          }
        }
      ]
    })

    t.present()
  }

  /**
   * Executes the api call, and displays success message, or error if any
   * @param apiCall
   */
  public apiCallWithMessage(
    apiCall: Observable<ApiResponses.ResponseBase.type>,
    successMessage: string = "",
    defaultErrorMessage: string = null,
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      firstValueFrom(apiCall)
        .then((data) => {
          if (successMessage) {
            this.showToast("", successMessage, "success");
          }
          resolve(data);
        })
        .catch((error) => {
          this.showToastBaseOnErrorResponse(error, defaultErrorMessage);
          reject(error);
        });
    });
  }

  /**
   * Display error toast from default error object
   *
   * @param error
   * @param {string} defaultKey
   */
  public showToastBaseOnErrorResponse(
    error: any,
    defaultKey?: string,
    doSendAnalytics: boolean = true,
  ) {

    if (error?.doDisplay === false) {
      return;
    }

    var _text = this.misc.getErrorText(error, defaultKey)

    this.analytics.addMarker("error_toast", _text)

    this.showErrorToast(
      error,
      _text,
      doSendAnalytics,
    );
  }

  /**
   * Show a simple error toast
   * @param error
   * @param text
   */
  public async showErrorToast(error, text, doSendAnalytics: boolean = true) {
    if (doSendAnalytics) {
      this.analytics.logError(this.misc.getErrorKey(error) ?? text, "toast");
    }
    this.showToast("", text, "error");
  }

  public async showPrompt(
    title: string,
    text: string,
    okHandler: Function,
    cancelHandler: Function = () => {},
    placeholderText: string = "...",
    okText: string = "ok",
    cancelText: string = "cancel",
  ) {
    const alert = await this.alertCtrl.create({
      header: this.translateService.instant(title),
      message: this.translateService.instant(text),
      inputs: [
        {
          name: "code",
          type: "text",
          placeholder: this.translateService.instant(placeholderText),
        },
      ],
      buttons: [
        {
          text: this.translateService.instant(okText),
          handler: (data) => {
            okHandler(data);
          },
        },
        {
          text: this.translateService.instant(cancelText),
          role: "cancel",
          handler: () => {
            cancelHandler();
          },
        },
      ],
    });

    await alert.present();
  }

  /**
   * Confirm dialog with 3 options [yes|no|never].
   *
   * @param {string} title
   * @param {Function} okHandler
   * @param {Function} cancelHandler
   * @param {Function} skipHandler
   * @param {string} okText
   * @param {string} cancelText
   * @param {string} skipText
   * @return {Promise<boolean>}
   */
  public async advanceConfirm(
    title: string,
    okHandler: Function,
    cancelHandler: Function = function () {},
    skipHandler: Function = function () {},
    okText: string = "yes",
    cancelText: string = "no",
    skipText: string = "never",
  ) {
    let op: AlertOptions = {
      subHeader: this.translateService.instant(title),
      buttons: [
        {
          text: this.translateService.instant(okText),
          handler: () => {
            okHandler();
          },
        },
        {
          text: this.translateService.instant(cancelText),
          handler: () => {
            skipHandler();
          },
        },
        {
          text: this.translateService.instant(skipText),
          role: "cancel",
          handler: () => {
            cancelHandler();
          },
        },
      ],
    };

    let dlg = await this.alertCtrl.create(op);

    await dlg.present();
  }
}
