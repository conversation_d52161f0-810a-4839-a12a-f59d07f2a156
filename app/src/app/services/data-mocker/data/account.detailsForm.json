[{"group_id": "1", "site_id": "0", "group_order": "10", "group_searchable": "1", "group_points": "5", "group_visibility": "1", "group_name": "My Appearance", "group_name_profile": "Appearance", "group_name_code": "", "group_prompt": "", "group_description": "More information about your looks", "group_text": "Please explain to people what you look like. This will allow you and the other members to \nfilter their searches to the traits that they prefer in a partner.", "niche_only": "0", "x_group_id": "0", "tags": [], "skip_niches": [], "questions": [{"key": "q_1", "question_id": "1", "group_id": "1", "question_order": "0", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My ethnicity is:", "question_name_profile": "Ethnicity", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 1, "data": "Caucasian / White"}, {"key": 2, "data": "Black"}, {"key": 3, "data": "Asian"}, {"key": 4, "data": "Latino / Hispanic"}, {"key": 5, "data": "Mixed Race"}, {"key": 6, "data": "Middle Eastern"}, {"key": 7, "data": "Indian"}, {"key": 8, "data": "Native American"}, {"key": 9, "data": "Other"}]}, {"key": "q_57", "question_id": "57", "group_id": "1", "question_order": "1", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My national heritage is:", "question_name_profile": "National heritage", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 840, "data": "Afghani"}, {"key": 841, "data": "Albanian"}, {"key": 842, "data": "Algerian"}, {"key": 843, "data": "American"}, {"key": 844, "data": "Andorran"}, {"key": 845, "data": "Angolan"}, {"key": 846, "data": "Argentinean"}, {"key": 847, "data": "Armenian"}, {"key": 848, "data": "Australian"}, {"key": 849, "data": "Austrian"}, {"key": 850, "data": "Azerbaijani"}, {"key": 851, "data": "<PERSON><PERSON><PERSON>"}, {"key": 852, "data": "Bahraini"}, {"key": 853, "data": "Bangladeshi"}, {"key": 854, "data": "Basque"}, {"key": 855, "data": "Belarusian"}, {"key": 856, "data": "Belgian"}, {"key": 857, "data": "Belizean"}, {"key": 858, "data": "Bolivian"}, {"key": 859, "data": "Brazilian"}, {"key": 860, "data": "British"}, {"key": 861, "data": "Bulgarian"}, {"key": 862, "data": "Burundian"}, {"key": 863, "data": "Cambodian"}, {"key": 864, "data": "Cameroonian"}, {"key": 865, "data": "Canadian"}, {"key": 1012, "data": "Caribbean"}, {"key": 866, "data": "Catalonian"}, {"key": 867, "data": "Chadian"}, {"key": 868, "data": "Chilean"}, {"key": 869, "data": "Chinese"}, {"key": 870, "data": "Colombian"}, {"key": 871, "data": "Costa Rican"}, {"key": 872, "data": "Croatian"}, {"key": 873, "data": "Cuban"}, {"key": 874, "data": "Cypriot"}, {"key": 875, "data": "Czech"}, {"key": 876, "data": "<PERSON>"}, {"key": 1009, "data": "<PERSON><PERSON>"}, {"key": 877, "data": "Djiboutian"}, {"key": 878, "data": "Dominican"}, {"key": 879, "data": "Dutch"}, {"key": 880, "data": "Ecuadorian"}, {"key": 881, "data": "Egyptian"}, {"key": 882, "data": "El Salvadorian"}, {"key": 883, "data": "English"}, {"key": 884, "data": "Eritrean"}, {"key": 885, "data": "Estonian"}, {"key": 886, "data": "Ethiopian"}, {"key": 887, "data": "Finnish"}, {"key": 888, "data": "French"}, {"key": 889, "data": "Galician"}, {"key": 890, "data": "Gambian"}, {"key": 891, "data": "Georgian"}, {"key": 892, "data": "German"}, {"key": 893, "data": "Ghanaian"}, {"key": 894, "data": "Greek"}, {"key": 895, "data": "Guinean"}, {"key": 896, "data": "Hong Kong"}, {"key": 897, "data": "Hungarian"}, {"key": 898, "data": "Icelandic"}, {"key": 899, "data": "Indian"}, {"key": 900, "data": "Indonesian"}, {"key": 901, "data": "Iranian"}, {"key": 902, "data": "Iraqi"}, {"key": 903, "data": "Irish"}, {"key": 904, "data": "Israeli"}, {"key": 905, "data": "Italian"}, {"key": 906, "data": "Jamaican"}, {"key": 907, "data": "Japanese"}, {"key": 908, "data": "<PERSON><PERSON>"}, {"key": 909, "data": "Kazakh"}, {"key": 910, "data": "Kenyan"}, {"key": 911, "data": "Korean"}, {"key": 912, "data": "Kuwaiti"}, {"key": 913, "data": "Kyrgyz"}, {"key": 914, "data": "Latvian"}, {"key": 915, "data": "Lebanese"}, {"key": 916, "data": "Libyan"}, {"key": 917, "data": "Lithuanian"}, {"key": 918, "data": "Luxembourgian"}, {"key": 919, "data": "Macedonian"}, {"key": 920, "data": "Malawian"}, {"key": 921, "data": "Malaysian"}, {"key": 922, "data": "<PERSON><PERSON>"}, {"key": 923, "data": "Mauritanian"}, {"key": 924, "data": "<PERSON><PERSON><PERSON>"}, {"key": 925, "data": "Mexican"}, {"key": 926, "data": "Micronesian"}, {"key": 927, "data": "Moldavian"}, {"key": 928, "data": "Mongolian"}, {"key": 929, "data": "Moroccan"}, {"key": 930, "data": "New Zealander"}, {"key": 931, "data": "Nicaraguan"}, {"key": 932, "data": "Nigerian"}, {"key": 933, "data": "Norwegian"}, {"key": 934, "data": "Omani"}, {"key": 935, "data": "Pakistani"}, {"key": 936, "data": "Paraguayan"}, {"key": 937, "data": "Peruvian"}, {"key": 938, "data": "<PERSON><PERSON>"}, {"key": 939, "data": "Polish"}, {"key": 940, "data": "Portuguese"}, {"key": 941, "data": "Puerto Rican"}, {"key": 942, "data": "Qatari"}, {"key": 943, "data": "Rhodesian"}, {"key": 944, "data": "Romanian"}, {"key": 945, "data": "Russian"}, {"key": 946, "data": "Saudi Arabian"}, {"key": 947, "data": "Scottish"}, {"key": 948, "data": "Serbian"}, {"key": 949, "data": "Singaporean"}, {"key": 950, "data": "Slovakian"}, {"key": 951, "data": "Slovenian"}, {"key": 952, "data": "South African"}, {"key": 953, "data": "Spaniard"}, {"key": 954, "data": "Sudanese"}, {"key": 955, "data": "Swedish"}, {"key": 956, "data": "Swiss"}, {"key": 957, "data": "Syrian"}, {"key": 958, "data": "Tai"}, {"key": 959, "data": "Tajik"}, {"key": 960, "data": "Tanzanian"}, {"key": 961, "data": "Thai"}, {"key": 962, "data": "Tunisian"}, {"key": 963, "data": "Turkish"}, {"key": 964, "data": "Turkmen"}, {"key": 965, "data": "Ugandan"}, {"key": 966, "data": "Ukrainian"}, {"key": 967, "data": "Uruguayan"}, {"key": 968, "data": "Uzbek"}, {"key": 969, "data": "Venezuelan"}, {"key": 970, "data": "Vietnamese"}, {"key": 971, "data": "Welsh"}, {"key": 972, "data": "Zambian"}, {"key": 973, "data": "Zimbabwean"}]}, {"key": "q_2", "question_id": "2", "group_id": "1", "question_order": "2", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My build is:", "question_name_profile": "Build", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 25, "data": "A few extra pounds"}, {"key": 26, "data": "Athletic / toned"}, {"key": 27, "data": "Average"}, {"key": 28, "data": "BBW/BHM"}, {"key": 29, "data": "<PERSON>urvy"}, {"key": 30, "data": "Fit"}, {"key": 31, "data": "Heavyset"}, {"key": 32, "data": "Muscular"}, {"key": 33, "data": "<PERSON><PERSON><PERSON>"}, {"key": 34, "data": "<PERSON>"}, {"key": 35, "data": "Voluptuous"}, {"key": 36, "data": "<PERSON><PERSON>"}, {"key": 37, "data": "<PERSON><PERSON><PERSON>"}, {"key": 38, "data": "Other"}, {"key": 1013, "data": "Bodybuilder"}]}, {"key": "q_3", "question_id": "3", "group_id": "1", "question_order": "3", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My height is:", "question_name_profile": "Height", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 39, "data": "4' 11\" or shorter"}, {"key": 40, "data": "5' 0\""}, {"key": 41, "data": "5' 1\""}, {"key": 42, "data": "5' 2\""}, {"key": 43, "data": "5' 3\""}, {"key": 44, "data": "5' 4\""}, {"key": 45, "data": "5' 5\""}, {"key": 46, "data": "5' 6\""}, {"key": 47, "data": "5' 7\""}, {"key": 48, "data": "5' 8\""}, {"key": 49, "data": "5' 9\""}, {"key": 50, "data": "5' 10\""}, {"key": 51, "data": "5' 11\""}, {"key": 52, "data": "6' 0\""}, {"key": 53, "data": "6' 1\""}, {"key": 54, "data": "6' 2\""}, {"key": 55, "data": "6' 3\""}, {"key": 56, "data": "6' 4\""}, {"key": 57, "data": "6' 5\""}, {"key": 58, "data": "6' 6\""}, {"key": 59, "data": "6' 7\""}, {"key": 60, "data": "6' 8\""}, {"key": 61, "data": "6' 9\""}, {"key": 62, "data": "6' 10\""}, {"key": 63, "data": "6' 11\""}, {"key": 64, "data": "7' 0\" or taller"}]}, {"key": "q_4", "question_id": "4", "group_id": "1", "question_order": "4", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My eye color is:", "question_name_profile": "Eye Color", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 65, "data": "Black"}, {"key": 66, "data": "Blue"}, {"key": 67, "data": "<PERSON>"}, {"key": 68, "data": "Green"}, {"key": 69, "data": "Grey"}, {"key": 70, "data": "<PERSON>"}, {"key": 71, "data": "Depends on my mood"}]}, {"key": "q_5", "question_id": "5", "group_id": "1", "question_order": "5", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My hair color is:", "question_name_profile": "Hair Color", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 72, "data": "Auburn / red"}, {"key": 73, "data": "Bald"}, {"key": 74, "data": "Black"}, {"key": 75, "data": "Blonde"}, {"key": 76, "data": "<PERSON>"}, {"key": 77, "data": "Dark blonde"}, {"key": 78, "data": "Dark brown"}, {"key": 79, "data": "Grey"}, {"key": 80, "data": "Light brown"}, {"key": 81, "data": "Platinum"}, {"key": 82, "data": "Salt and pepper"}, {"key": 83, "data": "Silver"}, {"key": 84, "data": "Other"}]}, {"key": "q_6", "question_id": "6", "group_id": "1", "question_order": "6", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My hair length is:", "question_name_profile": "Hair Length", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 85, "data": "Bald"}, {"key": 86, "data": "Neck length"}, {"key": 87, "data": "Short"}, {"key": 88, "data": "Shoulder length"}, {"key": 89, "data": "Very long"}, {"key": 90, "data": "Waist length"}]}, {"key": "q_8", "question_id": "8", "group_id": "1", "question_order": "8", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My best feature is:", "question_name_profile": "Best feature", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 97, "data": "Arms"}, {"key": 98, "data": "<PERSON><PERSON>"}, {"key": 99, "data": "Butt"}, {"key": 100, "data": "<PERSON><PERSON>"}, {"key": 101, "data": "Chest"}, {"key": 102, "data": "Eyes"}, {"key": 103, "data": "Feet"}, {"key": 104, "data": "Hair"}, {"key": 105, "data": "Hands"}, {"key": 106, "data": "Legs"}, {"key": 107, "data": "Lips"}, {"key": 108, "data": "Neck"}, {"key": 109, "data": "Smile"}]}, {"key": "q_10", "question_id": "10", "group_id": "1", "question_order": "10", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My health condition is:", "question_name_profile": "Health Condition", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 121, "data": "No special Health Conditions"}, {"key": 700, "data": "No problems"}, {"key": 701, "data": "Addiction recovery"}, {"key": 122, "data": "AIDS / HIV"}, {"key": 123, "data": "Amputee"}, {"key": 702, "data": "Bipolar"}, {"key": 703, "data": "Burn victim"}, {"key": 124, "data": "Cancer"}, {"key": 704, "data": "Carers only"}, {"key": 705, "data": "Cerebral Palsy"}, {"key": 706, "data": "Cystic Fibrosis"}, {"key": 707, "data": "Deaf / hard of hearing"}, {"key": 708, "data": "Depression"}, {"key": 709, "data": "Developmental delay"}, {"key": 710, "data": "Diabetes"}, {"key": 711, "data": "Disabled"}, {"key": 712, "data": "Disfigurement"}, {"key": 713, "data": "Down syndrome"}, {"key": 714, "data": "Dwarfism"}, {"key": 715, "data": "Epilepsy"}, {"key": 716, "data": "Genital Herpes"}, {"key": 717, "data": "Handicapped"}, {"key": 718, "data": "Hemophilia"}, {"key": 719, "data": "Hepatitis B"}, {"key": 720, "data": "Hepatitis C"}, {"key": 721, "data": "HPV"}, {"key": 1008, "data": "HSV"}, {"key": 722, "data": "Huntington disease"}, {"key": 723, "data": "Oral Herpes"}, {"key": 724, "data": "Other STD"}, {"key": 725, "data": "Paralysis"}, {"key": 726, "data": "Parkinson disease"}, {"key": 727, "data": "Skin conditions"}, {"key": 728, "data": "Speech disorders"}, {"key": 729, "data": "Spina bifida"}, {"key": 730, "data": "Syphilis"}, {"key": 731, "data": "Urea conditions"}, {"key": 732, "data": "Visual impairment / blind"}, {"key": 733, "data": "Wheelchair"}]}, {"key": "q_9", "question_id": "9", "group_id": "1", "question_order": "20", "searchable": "1", "type": "check", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "1", "question_name": "My body art is:", "question_name_profile": "Body Art", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 110, "data": "Belly button ring"}, {"key": 111, "data": "Branded"}, {"key": 112, "data": "<PERSON><PERSON>"}, {"key": 113, "data": "Inked all over"}, {"key": 114, "data": "None"}, {"key": 115, "data": "Pierced... but only ear(s)"}, {"key": 116, "data": "Secret piercings"}, {"key": 117, "data": "Scarred"}, {"key": 118, "data": "Strategically placed tattoo"}, {"key": 119, "data": "Visible tattoo"}, {"key": 120, "data": "Other"}]}], "completed": true}, {"group_id": "2", "site_id": "0", "group_order": "10", "group_searchable": "1", "group_points": "5", "group_visibility": "1", "group_name": "My Situation", "group_name_profile": "Situation", "group_name_code": "", "group_prompt": "", "group_description": "What's your present situation?", "group_text": "Sharing more personal information helps narrow down undesirable suitors!", "niche_only": "0", "x_group_id": "0", "tags": [], "skip_niches": [], "questions": [{"key": "q_11", "question_id": "11", "group_id": "2", "question_order": "1", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My marital status is", "question_name_profile": "Marital Status", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 125, "data": "Single"}, {"key": 126, "data": "In a relationship"}, {"key": 127, "data": "Living together"}, {"key": 128, "data": "Married"}, {"key": 129, "data": "Divorced"}, {"key": 130, "data": "Separated"}, {"key": 131, "data": "Widowed"}]}, {"key": "q_12", "question_id": "12", "group_id": "2", "question_order": "2", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "Do you have children?", "question_name_profile": "Has children", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 132, "data": "No children"}, {"key": 133, "data": "One"}, {"key": 134, "data": "Two"}, {"key": 135, "data": "Three or more"}, {"key": 136, "data": "Children don't live with me"}]}, {"key": "q_13", "question_id": "13", "group_id": "2", "question_order": "3", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "Do you want kids?", "question_name_profile": "Wants kids", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 137, "data": "Yes"}, {"key": 138, "data": "No"}, {"key": 139, "data": "Not sure"}]}, {"key": "q_14", "question_id": "14", "group_id": "2", "question_order": "4", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My housing situation is", "question_name_profile": "Housing Situation", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 140, "data": "There is no noise"}, {"key": 141, "data": "Friends come over occasionally"}, {"key": 142, "data": "Friends come over often"}, {"key": 143, "data": "There is always a party going on"}, {"key": 144, "data": "All is calm"}, {"key": 145, "data": "It gets a bit crazy sometimes"}]}, {"key": "q_15", "question_id": "15", "group_id": "2", "question_order": "5", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My living arrangement is", "question_name_profile": "Living Arrangements", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 146, "data": "Alone"}, {"key": 147, "data": "With roomate(s)"}, {"key": 148, "data": "With parent(s)"}, {"key": 149, "data": "With kid(s)"}, {"key": 150, "data": "With pet(s)"}]}, {"key": "q_16", "question_id": "16", "group_id": "2", "question_order": "6", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "Are you willing to relocate?", "question_name_profile": "Wants to Relocate", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 151, "data": "Yes"}, {"key": 152, "data": "No"}, {"key": 1007, "data": "Maybe"}]}, {"key": "q_53", "question_id": "53", "group_id": "2", "question_order": "7", "searchable": "0", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "1", "multiselect": "0", "question_name": "Do you have a car?", "question_name_profile": "Has a car", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 741, "data": "Yes"}, {"key": 742, "data": "No"}]}, {"key": "q_17", "question_id": "17", "group_id": "2", "question_order": "10", "searchable": "1", "type": "check", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "1", "question_name": "Do you have pets?", "question_name_profile": "<PERSON> Pets", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 153, "data": "Cat"}, {"key": 154, "data": "Dog"}, {"key": 155, "data": "<PERSON>"}, {"key": 156, "data": "Exotic animal"}, {"key": 157, "data": "Fish"}, {"key": 158, "data": "Gerbil or guinea pig"}, {"key": 159, "data": "Horse"}, {"key": 160, "data": "Reptile"}, {"key": 161, "data": "<PERSON><PERSON>"}, {"key": 162, "data": "Other"}, {"key": 163, "data": "I own a farm"}]}, {"key": "q_66", "question_id": "66", "group_id": "2", "question_order": "10", "searchable": "0", "type": "slider", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "I am available", "question_name_profile": "Availability", "question_name_code": "", "callback": "default_slider", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "136", "skip_niches": [], "answers": [{"key": 1027, "data": "Anytime / Anyplace"}, {"key": 1028, "data": "My Place"}, {"key": 1029, "data": "Your Place"}, {"key": 1030, "data": "Hotel Room"}, {"key": 1031, "data": "Discreetly"}]}], "completed": true}, {"group_id": "3", "site_id": "0", "group_order": "10", "group_searchable": "1", "group_points": "5", "group_visibility": "1", "group_name": "My Education & Employment", "group_name_profile": "Education & Employment", "group_name_code": "", "group_prompt": "", "group_description": "What do you do for a living?", "group_text": "Please tell others about your education and employement status.", "niche_only": "0", "x_group_id": "0", "tags": [], "skip_niches": [], "questions": [{"key": "q_18", "question_id": "18", "group_id": "3", "question_order": "1", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My education level is:", "question_name_profile": "Education", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 165, "data": "High school Diploma - GED or equivalent"}, {"key": 166, "data": "Professional - Technical - Vocational School"}, {"key": 167, "data": "College Associates"}, {"key": 168, "data": "University Bachelors"}, {"key": 169, "data": "University Masters"}, {"key": 170, "data": "University Doctorate"}]}, {"key": "q_19", "question_id": "19", "group_id": "3", "question_order": "2", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My specialty is:", "question_name_profile": "Specialty", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 173, "data": "Administrative / Management"}, {"key": 174, "data": "Art / Music / Literature"}, {"key": 175, "data": "Computers / Electronic"}, {"key": 176, "data": "Construction / Craftsman"}, {"key": 177, "data": "Education / Academic"}, {"key": 178, "data": "Entertainment / Media"}, {"key": 179, "data": "Finance / Trading"}, {"key": 180, "data": "Food / Catering"}, {"key": 181, "data": "Hospitality / Travel"}, {"key": 182, "data": "Legal"}, {"key": 183, "data": "Manufacturing / Distribution"}, {"key": 184, "data": "Medical / Health"}, {"key": 185, "data": "Military"}, {"key": 186, "data": "Municipal / Law Enforcement"}, {"key": 187, "data": "Politics / Government"}, {"key": 188, "data": "Research / Science / Engineering"}, {"key": 189, "data": "Retired"}, {"key": 190, "data": "Sales / Marketing"}, {"key": 191, "data": "Other"}, {"key": 192, "data": "Student"}, {"key": 193, "data": "Teacher / Education"}, {"key": 194, "data": "Transportation"}]}, {"key": "q_20", "question_id": "20", "group_id": "3", "question_order": "3", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My employment status is:", "question_name_profile": "Employment Status", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 195, "data": "Full-time"}, {"key": 196, "data": "Homemaker"}, {"key": 197, "data": "Part-time"}, {"key": 198, "data": "Retired"}, {"key": 199, "data": "Self-employed"}, {"key": 200, "data": "Student"}, {"key": 201, "data": "Unemployed"}, {"key": 202, "data": "Work At Home"}]}, {"key": "q_22", "question_id": "22", "group_id": "3", "question_order": "4", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My annual income is:", "question_name_profile": "Annual income", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 217, "data": "Seeking support"}, {"key": 218, "data": "up to $25,000"}, {"key": 219, "data": "$25,000 - $50,000"}, {"key": 220, "data": "$50,000 - $100,000"}, {"key": 221, "data": "$100,000 - $250,000"}, {"key": 222, "data": "$250,000 - $500,000"}, {"key": 223, "data": "$500,000 - $1,000,000"}, {"key": 224, "data": "$1,000,000 - $2,500,000"}, {"key": 225, "data": "$2,500,000 +"}]}, {"key": "q_21", "question_id": "21", "group_id": "3", "question_order": "10", "searchable": "1", "type": "char", "value_min": "0", "value_max": "80", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My Job Title", "question_name_profile": "Job Title", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": []}], "completed": true}, {"group_id": "4", "site_id": "0", "group_order": "10", "group_searchable": "1", "group_points": "5", "group_visibility": "1", "group_name": "My Leisure & Fun", "group_name_profile": "Leisure & Fun", "group_name_code": "", "group_prompt": "", "group_description": "What do you like?", "group_text": "What are your music preferences, what types of movies you like, do you watch TV, basically describe your idea of great fun.", "niche_only": "0", "x_group_id": "0", "tags": [], "skip_niches": [], "questions": [{"key": "q_24", "question_id": "24", "group_id": "4", "question_order": "0", "searchable": "1", "type": "check", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "1", "question_name": "What do you watch on TV?", "question_name_profile": "TV preferences", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 235, "data": "News"}, {"key": 236, "data": "Cartoons"}, {"key": 237, "data": "Documentaries"}, {"key": 238, "data": "Dramas"}, {"key": 239, "data": "Situation comedies"}, {"key": 240, "data": "Instructional"}, {"key": 241, "data": "Movies"}, {"key": 242, "data": "I don't like TV"}, {"key": 243, "data": "I wish I had a TV"}, {"key": 244, "data": "Sports"}, {"key": 245, "data": "Reality shows"}, {"key": 246, "data": "Soaps"}, {"key": 247, "data": "Re-runs"}]}, {"key": "q_25", "question_id": "25", "group_id": "4", "question_order": "0", "searchable": "0", "type": "char", "value_min": "0", "value_max": "80", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My favorite TV shows are:", "question_name_profile": "Favorite TV shows", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": []}, {"key": "q_26", "question_id": "26", "group_id": "4", "question_order": "0", "searchable": "1", "type": "check", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "1", "question_name": "Movie types I like:", "question_name_profile": "Favorite movies types", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 248, "data": "Action"}, {"key": 249, "data": "Science fiction"}, {"key": 250, "data": "Comedy"}, {"key": 251, "data": "Romance"}, {"key": 252, "data": "Drama"}, {"key": 253, "data": "Documentary"}, {"key": 254, "data": "Family"}, {"key": 255, "data": "Animation"}, {"key": 256, "data": "Horror"}, {"key": 257, "data": "Thriller"}, {"key": 258, "data": "I don't like movies"}, {"key": 259, "data": "I'm a movie actor"}, {"key": 260, "data": "Adult"}]}, {"key": "q_27", "question_id": "27", "group_id": "4", "question_order": "0", "searchable": "0", "type": "char", "value_min": "0", "value_max": "80", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My favorite movies are:", "question_name_profile": "Favorite movies", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": []}, {"key": "q_28", "question_id": "28", "group_id": "4", "question_order": "0", "searchable": "1", "type": "check", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "1", "question_name": "My favorite music types are:", "question_name_profile": "Music types", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 261, "data": "Country"}, {"key": 262, "data": "Rap"}, {"key": 263, "data": "Rock"}, {"key": 264, "data": "Metal"}, {"key": 265, "data": "Electronic"}, {"key": 266, "data": "Pop"}, {"key": 267, "data": "Classical"}, {"key": 268, "data": "Vacuum cleaner noises"}, {"key": 269, "data": "I don't like music"}, {"key": 270, "data": "I'm a recording artist"}, {"key": 271, "data": "Blues"}, {"key": 272, "data": "Jazz"}, {"key": 273, "data": "Industrial"}, {"key": 274, "data": "Latin"}, {"key": 275, "data": "New age"}, {"key": 276, "data": "Ambient"}, {"key": 277, "data": "Dance"}, {"key": 278, "data": "Soul"}, {"key": 279, "data": "Reggae"}, {"key": 280, "data": "Gospel"}, {"key": 281, "data": "Folk"}, {"key": 282, "data": "Punk"}]}, {"key": "q_29", "question_id": "29", "group_id": "4", "question_order": "0", "searchable": "0", "type": "char", "value_min": "0", "value_max": "80", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My favorite artists are:", "question_name_profile": "Favorite artists", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": []}, {"key": "q_30", "question_id": "30", "group_id": "4", "question_order": "0", "searchable": "1", "type": "check", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "1", "question_name": "My favorite book types are:", "question_name_profile": "Reading selection", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 283, "data": "News"}, {"key": 284, "data": "Ancient"}, {"key": 285, "data": "Anthology"}, {"key": 286, "data": "Auto-biography"}, {"key": 287, "data": "Biography"}, {"key": 288, "data": "Business"}, {"key": 289, "data": "Classic"}, {"key": 290, "data": "Comic"}, {"key": 291, "data": "Computers"}, {"key": 292, "data": "Fantasy"}, {"key": 293, "data": "Fiction"}, {"key": 294, "data": "Health"}, {"key": 295, "data": "History"}, {"key": 296, "data": "Home & Garden"}, {"key": 297, "data": "Horror"}, {"key": 298, "data": "<PERSON><PERSON>"}, {"key": 299, "data": "Instructional"}, {"key": 300, "data": "Mathematics"}, {"key": 301, "data": "Music"}, {"key": 302, "data": "Mystery"}, {"key": 303, "data": "Nature"}, {"key": 304, "data": "Philosophy"}, {"key": 305, "data": "Political"}, {"key": 306, "data": "Reference"}, {"key": 307, "data": "Poetry"}, {"key": 308, "data": "Religious"}, {"key": 734, "data": "Romance"}, {"key": 309, "data": "Satire"}, {"key": 310, "data": "Science"}, {"key": 311, "data": "Science fiction"}, {"key": 312, "data": "Sports"}, {"key": 313, "data": "Technical"}, {"key": 314, "data": "I don't like reading"}, {"key": 315, "data": "I'm an author"}]}, {"key": "q_31", "question_id": "31", "group_id": "4", "question_order": "0", "searchable": "0", "type": "char", "value_min": "0", "value_max": "80", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My favorite books are:", "question_name_profile": "Favorite books", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": []}, {"key": "q_32", "question_id": "32", "group_id": "4", "question_order": "0", "searchable": "0", "type": "text", "value_min": "0", "value_max": "1000", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My idea of fun is:", "question_name_profile": "Idea of fun", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": []}, {"key": "q_33", "question_id": "33", "group_id": "4", "question_order": "0", "searchable": "1", "type": "check", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "1", "question_name": "My hobbies are:", "question_name_profile": "Hobbies", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 317, "data": "Arts & Crafts"}, {"key": 337, "data": "Camping"}, {"key": 336, "data": "Cars"}, {"key": 326, "data": "Clubbing, bar Hopping"}, {"key": 342, "data": "Computers"}, {"key": 334, "data": "Cooking"}, {"key": 327, "data": "Dancing"}, {"key": 329, "data": "Dining"}, {"key": 328, "data": "Family"}, {"key": 338, "data": "Fishing"}, {"key": 340, "data": "Gambling"}, {"key": 325, "data": "Games"}, {"key": 335, "data": "Gardening"}, {"key": 316, "data": "Exercising"}, {"key": 974, "data": "Hunting"}, {"key": 324, "data": "Internet"}, {"key": 321, "data": "Music"}, {"key": 344, "data": "Motorcycles"}, {"key": 323, "data": "Movies"}, {"key": 320, "data": "Learning"}, {"key": 330, "data": "Photography"}, {"key": 339, "data": "Playing cards"}, {"key": 319, "data": "Reading"}, {"key": 331, "data": "Religion, spirituality"}, {"key": 343, "data": "Shopping"}, {"key": 318, "data": "Sports"}, {"key": 332, "data": "Theater"}, {"key": 333, "data": "Travel"}, {"key": 322, "data": "TV"}, {"key": 341, "data": "Volunteering"}]}], "completed": true}, {"group_id": "5", "site_id": "0", "group_order": "10", "group_searchable": "1", "group_points": "5", "group_visibility": "1", "group_name": "My Personality", "group_name_profile": "Personality", "group_name_code": "", "group_prompt": "", "group_description": "What is your personality like?", "group_text": "Do opposites attract? Let the members decide! Tell them more about your personality, they way you and others see it.", "niche_only": "0", "x_group_id": "0", "tags": [], "skip_niches": [], "questions": [{"key": "q_34", "question_id": "34", "group_id": "5", "question_order": "0", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "Do you smoke?", "question_name_profile": "Smoker", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 345, "data": "Don't smoke"}, {"key": 346, "data": "Quitting smoking"}, {"key": 347, "data": "Smoke socially"}, {"key": 348, "data": "Smoke regularly (5-10 a day)"}, {"key": 349, "data": "Smoke heavily (10+ a day)"}]}, {"key": "q_35", "question_id": "35", "group_id": "5", "question_order": "0", "searchable": "1", "type": "select", "value_min": "20", "value_max": "20", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My drinking habit is:", "question_name_profile": "Drinking", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 350, "data": "Don't drink"}, {"key": 351, "data": "Light / social drinker"}, {"key": 352, "data": "Regular drinker"}, {"key": 353, "data": "Heavy drinker"}, {"key": 354, "data": "One or two"}]}, {"key": "q_36", "question_id": "36", "group_id": "5", "question_order": "0", "searchable": "0", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "1", "multiselect": "0", "question_name": "My social behavior is", "question_name_profile": "Social behavior", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 355, "data": "Reserved"}, {"key": 356, "data": "<PERSON>hy"}, {"key": 357, "data": "Boisterous"}, {"key": 358, "data": "Loud"}, {"key": 359, "data": "Observant"}, {"key": 360, "data": "Anti-social"}, {"key": 361, "data": "Friendly"}, {"key": 362, "data": "Funny"}, {"key": 363, "data": "Party starter"}, {"key": 364, "data": "Fire starter"}, {"key": 365, "data": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": 366, "data": "Outgoing"}, {"key": 367, "data": "Dark"}, {"key": 368, "data": "<PERSON>"}]}, {"key": "q_37", "question_id": "37", "group_id": "5", "question_order": "0", "searchable": "0", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My friends describe me as", "question_name_profile": "Friends describe as", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 369, "data": "Friendly"}, {"key": 370, "data": "Troublemaker"}, {"key": 371, "data": "Someone they want to be"}, {"key": 372, "data": "A troll"}, {"key": 373, "data": "Cool"}, {"key": 374, "data": "Perfect"}, {"key": 375, "data": "Raunchy"}, {"key": 376, "data": "Obscure"}, {"key": 377, "data": "Goofy"}, {"key": 378, "data": "I don't have any friends"}, {"key": 379, "data": "A flirt"}]}, {"key": "q_38", "question_id": "38", "group_id": "5", "question_order": "0", "searchable": "0", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "In High School I was a(n)", "question_name_profile": "In High School I was a(n)", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 380, "data": "Class clown"}, {"key": 381, "data": "Brain"}, {"key": 382, "data": "Bully"}, {"key": 383, "data": "Quiet one"}, {"key": 384, "data": "Cool dude"}, {"key": 385, "data": "Teachers pet"}, {"key": 386, "data": "Average Joe"}, {"key": 387, "data": "<PERSON><PERSON>"}, {"key": 388, "data": "<PERSON><PERSON>"}, {"key": 389, "data": "Outcast"}]}, {"key": "q_39", "question_id": "39", "group_id": "5", "question_order": "0", "searchable": "0", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My sense of humor is", "question_name_profile": "Sense of humor", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 390, "data": "Campy"}, {"key": 391, "data": "<PERSON><PERSON><PERSON>"}, {"key": 392, "data": "Dry / sarcastic"}, {"key": 393, "data": "Friendly"}, {"key": 394, "data": "Goofy"}, {"key": 395, "data": "Obscure"}, {"key": 396, "data": "Slapstick"}, {"key": 397, "data": "Raunchy"}, {"key": 398, "data": "Sadistic"}, {"key": 399, "data": "I never laugh"}]}, {"key": "q_40", "question_id": "40", "group_id": "5", "question_order": "0", "searchable": "0", "type": "check", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "1", "question_name": "My idea of a great time is:", "question_name_profile": "My great time", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 400, "data": "Hanging out with friends"}, {"key": 401, "data": "Partying"}, {"key": 402, "data": "Going shopping"}, {"key": 403, "data": "Staying at home"}, {"key": 404, "data": "Trying new things"}, {"key": 405, "data": "The movies"}, {"key": 406, "data": "Relaxing"}, {"key": 407, "data": "Sleeping"}, {"key": 408, "data": "Clubbing / bars"}, {"key": 409, "data": "Drinking"}, {"key": 410, "data": "Extreme sports"}, {"key": 411, "data": "Reading a book"}, {"key": 412, "data": "Going to a casino"}, {"key": 413, "data": "Playing dress-up"}, {"key": 414, "data": "Playing video games"}, {"key": 415, "data": "TV"}, {"key": 416, "data": "Going to a concert"}, {"key": 417, "data": "Going to a museum"}]}, {"key": "q_41", "question_id": "41", "group_id": "5", "question_order": "0", "searchable": "0", "type": "text", "value_min": "0", "value_max": "1000", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "I've always wanted to try:", "question_name_profile": "Always wanted to try", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": []}], "completed": true}, {"group_id": "6", "site_id": "0", "group_order": "10", "group_searchable": "1", "group_points": "5", "group_visibility": "1", "group_name": "My Views", "group_name_profile": "Views", "group_name_code": "", "group_prompt": "", "group_description": "Your views on life", "group_text": "Please share this information so that we can find better matches for you.", "niche_only": "0", "x_group_id": "0", "tags": [], "skip_niches": [], "questions": [{"key": "q_42", "question_id": "42", "group_id": "6", "question_order": "0", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My political views are:", "question_name_profile": "Political views", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 418, "data": "Very conservative"}, {"key": 419, "data": "Conservative"}, {"key": 420, "data": "Middle of the road"}, {"key": 421, "data": "Liberal"}, {"key": 422, "data": "Very liberal"}, {"key": 423, "data": "Anarchist"}, {"key": 424, "data": "I don't like politics"}, {"key": 425, "data": "Not sure"}, {"key": 426, "data": "Socialist"}, {"key": 427, "data": "Republican"}, {"key": 428, "data": "Green party"}, {"key": 429, "data": "Democrat"}, {"key": 430, "data": "Libertarian"}, {"key": 1011, "data": "Expatriate"}]}, {"key": "q_43", "question_id": "43", "group_id": "6", "question_order": "0", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My Religion is:", "question_name_profile": "Religion", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 431, "data": "Adventist"}, {"key": 432, "data": "Agnostic"}, {"key": 433, "data": "Anglicanist"}, {"key": 434, "data": "Atheist"}, {"key": 435, "data": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": 436, "data": "Baptist"}, {"key": 437, "data": "<PERSON><PERSON><PERSON>"}, {"key": 438, "data": "Buddhist"}, {"key": 439, "data": "Calvinist"}, {"key": 440, "data": "Catholic"}, {"key": 441, "data": "<PERSON>"}, {"key": 442, "data": "Confucianist"}, {"key": 443, "data": "Congregationalist"}, {"key": 444, "data": "<PERSON><PERSON><PERSON>"}, {"key": 445, "data": "Druze"}, {"key": 446, "data": "Evangelist"}, {"key": 447, "data": "Hindu"}, {"key": 448, "data": "Islam"}, {"key": 449, "data": "<PERSON><PERSON><PERSON>'s witness"}, {"key": 450, "data": "Jewish"}, {"key": 451, "data": "Lamaist"}, {"key": 452, "data": "Lutheran"}, {"key": 453, "data": "Mennonite"}, {"key": 454, "data": "Methodist"}, {"key": 455, "data": "Mormon"}, {"key": 456, "data": "Orthodox"}, {"key": 457, "data": "<PERSON><PERSON>"}, {"key": 458, "data": "Pentecostal"}, {"key": 459, "data": "Presbyterianist"}, {"key": 460, "data": "Protestant"}, {"key": 461, "data": "<PERSON>"}, {"key": 462, "data": "Santeria"}, {"key": 463, "data": "Scientologist"}, {"key": 464, "data": "Secular"}, {"key": 465, "data": "<PERSON><PERSON>"}, {"key": 466, "data": "Shin<PERSON>"}, {"key": 467, "data": "Sikh"}, {"key": 468, "data": "Spiritualist"}, {"key": 469, "data": "Sunni"}, {"key": 1014, "data": "Unitarian"}, {"key": 1015, "data": "Unitarian Universalist"}, {"key": 470, "data": "Other christian"}, {"key": 471, "data": "Other"}, {"key": 1006, "data": "Wicca"}, {"key": 472, "data": "None"}, {"key": 473, "data": "Not religious"}]}, {"key": "q_44", "question_id": "44", "group_id": "6", "question_order": "0", "searchable": "1", "type": "select", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "Do you attend religious services?", "question_name_profile": "Attend religious services", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 474, "data": "Every day"}, {"key": 475, "data": "Once a week"}, {"key": 476, "data": "Once a month"}, {"key": 477, "data": "On holidays"}, {"key": 478, "data": "Once a year"}, {"key": 479, "data": "Once"}, {"key": 480, "data": "Never"}]}, {"key": "q_45", "question_id": "45", "group_id": "6", "question_order": "0", "searchable": "0", "type": "text", "value_min": "0", "value_max": "1000", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "My goal in life is:", "question_name_profile": "Goal in life", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": []}], "completed": true}, {"group_id": "7", "site_id": "0", "group_order": "10", "group_searchable": "1", "group_points": "5", "group_visibility": "1", "group_name": "I Am Looking For", "group_name_profile": "Looking For", "group_name_code": "", "group_prompt": "", "group_description": "What are you looking for in a match?", "group_text": "Please let us know what kind of person you are looking for and what values they should have!", "niche_only": "0", "x_group_id": "0", "tags": [], "skip_niches": [], "questions": [{"key": "q_46", "question_id": "46", "group_id": "7", "question_order": "0", "searchable": "0", "type": "check", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "1", "question_name": "What do you find attractive?", "question_name_profile": "Must have", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": [], "answers": [{"key": 481, "data": "Intelligence"}, {"key": 482, "data": "Good Looks"}, {"key": 483, "data": "<PERSON><PERSON>"}, {"key": 484, "data": "Great skills"}, {"key": 485, "data": "Empathy"}, {"key": 486, "data": "Sensitivity"}, {"key": 487, "data": "Boldness"}, {"key": 488, "data": "Oddities"}, {"key": 489, "data": "Spontaneity"}, {"key": 490, "data": "Money"}, {"key": 491, "data": "Power"}, {"key": 492, "data": "Flirtatiousness"}, {"key": 493, "data": "Wit"}, {"key": 494, "data": "Thoughtfullness"}, {"key": 495, "data": "Passiveness"}]}, {"key": "q_47", "question_id": "47", "group_id": "7", "question_order": "0", "searchable": "0", "type": "text", "value_min": "0", "value_max": "1000", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "What do you look for in a partner?", "question_name_profile": "Look for in a partner", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": []}, {"key": "q_48", "question_id": "48", "group_id": "7", "question_order": "0", "searchable": "0", "type": "text", "value_min": "0", "value_max": "1000", "required": "0", "sortable": "0", "multiselect": "0", "question_name": "Describe what would you do for a first date.", "question_name_profile": "On the first date", "question_name_code": "", "callback": "", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "0", "skip_niches": []}, {"key": "q_64", "question_id": "64", "group_id": "7", "question_order": "0", "searchable": "0", "type": "slider", "value_min": "45", "value_max": "99", "required": "0", "sortable": "0", "multiselect": "1", "question_name": "What age should your perfect match be?", "question_name_profile": "Age", "question_name_code": "", "callback": "age_range", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "464", "skip_niches": [], "answers": []}, {"key": "q_65", "question_id": "65", "group_id": "7", "question_order": "0", "searchable": "0", "type": "slider", "value_min": "0", "value_max": "0", "required": "0", "sortable": "0", "multiselect": "1", "question_name": "My perfect match's age:", "question_name_profile": "Age", "question_name_code": "", "callback": "age_range", "hidden": "0", "hide_from": "", "description": "", "x_question_id": "474", "skip_niches": [], "answers": [{"key": 1022, "data": "18"}, {"key": 1023, "data": "35"}, {"key": 1024, "data": "55"}, {"key": 1025, "data": "75"}, {"key": 1026, "data": "99"}]}], "completed": true}, {"group_id": "description", "site_id": "", "group_order": 0, "group_searchable": 1, "group_points": 5, "group_name": "My Profile Description", "group_name_profile": "My Profile Description", "group_name_code": "", "group_prompt": "", "group_description": "Who you are and who are you looking for, where are you from, etc", "group_text": "<h5>My Profile Information</h5><p>Please tell us a little about yourself, who you are looking for, and where you are from.</p><p>Please provide as much information as possible. <strong>Profiles with photos and more details get 90% more views</strong>.</p>", "tags": [], "questions": [], "completed": true}, {"group_id": "interests", "site_id": "", "group_order": 0, "group_searchable": 1, "group_points": 5, "group_name": "Interests", "group_name_profile": "Interests", "group_name_code": "", "group_prompt": "", "group_description": "What are you looking for?", "group_text": "Please select one or more things that you are looking for here.", "tags": [], "questions": [], "completed": true}]