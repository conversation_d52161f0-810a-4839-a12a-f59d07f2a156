import { Injectable } from "@angular/core";
// get mock data
import _accountData from "./data/account.json";
import _profileData from "./data/profile.json";
import _browseProfiles from "./data/browse.nearby.json";
import _profileSuggestions from "./data/profile.suggestions.json";
import _inboxThreads from "./data/inbox.json";
import _inboxMessages from "./data/messages.json";
import _accountInterestOptions from "./data/account.interestOptions.json";
import _accountMatchPref from "./data/account.matchPref.json";
import _accountSettings from "./data/account.settings.json";
import _accountDetailsForm from "./data/account.detailsForm.json";
import _profileLikesUs from "./data/profile.likedUs.json";
import _profileMatches from "./data/profile.matches.json";
import _profileWeLiked from "./data/profile.weLiked.json";
import _restrictionAll from "./data/restriction.all.json";
import _usersUpdatesBrowse from "./data/usersUpdates.browse.json";
import _welcomeProfileStatus from "./data/welcomeProfile.status.json";
import _locationCountry from "./data/location.json";
import _inboxChatMessage from "./data/chat-message.json";
import _galleryImage from "./data/gallery-image.json";
import _photoComment from "./data/photo-comment.json";
import _products from "./data/products.json";
import _messages from "./data/messages.json";

@Injectable({
  providedIn: "root",
})
export class DataMockerService {
  public AccountData: any = _accountData;
  public ProfileData: any = _profileData;
  public BrowseProfiles: any = _browseProfiles;
  public FastMatchProfiles: any = _profileSuggestions;
  public InboxThreads: any = _inboxThreads;
  public InboxMessages: any = _inboxMessages;
  public AccountInterestOptions: any = _accountInterestOptions;
  public AccountMatchPref: any = _accountMatchPref;
  public AccountSettings: any = _accountSettings;
  public AccountDetailsForm: any = _accountDetailsForm;
  public ProfileLikesUs: any = _profileLikesUs;
  public ProfileMatches: any = _profileMatches;
  public ProfileWeLiked: any = _profileWeLiked;
  public RestrictionAll: any = _restrictionAll;
  public UsersUpdatesBrowse: any = _usersUpdatesBrowse;
  public WelcomeProfileStatus: any = _welcomeProfileStatus;
  public Location: any = _locationCountry;
  public LocationCountry: any = _locationCountry.country_list;
  public LocationCity: any = _locationCountry.city_list;
  public ChatMessage: any = _inboxChatMessage;
  public GalleryImage: any = _galleryImage;
  public PhotoComment: any = _photoComment;
  public Products: any = _products;
  public Messages: any = _messages;

  constructor() {}
}
