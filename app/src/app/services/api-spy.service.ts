import { Injectable, NgZone } from "@angular/core";
import { Debug } from "../helpers/debug";
import { ApiCommands } from "./api/api.commands";
import { ApiService } from "./api/api.service";
import { PaginatorHelperService } from "./paginator-helper.service";
import { ProfileSuggestionsService } from "./profile-suggestions.service";
import { ProfileService } from "./profile.service";
import { filter } from "rxjs/operators";
import { AdsService } from "./ads.service";

/**
 * A service to place all the stuff that has anything to do with
 * spying on an api call and reacting to it, like refres something
 * if settings is updated, or similar
 */

@Injectable({
  providedIn: "root",
})
export class ApiSpyService {
  constructor(
    private api: ApiService,
    private profileSuggestions: ProfileSuggestionsService,
    private profileService: ProfileService,
    private ngZone: NgZone,
    private debug: Debug,
    private paginatorHelper: PaginatorHelperService,
    private ads: AdsService
  ) {}

  public init() {
    this.initRefreshOnProfileChange();
    this.initForceReRenderUIAfterSuccessfulApiCall();
  }

  /**
   * this is a helper function that makes sure that we
   * re-render the UI when some data is changed
   */
  private initForceReRenderUIAfterSuccessfulApiCall() {
    this.api.eventStream$
      .pipe(
        filter((data) => {
          // on profile update or match pref update refresh lists

          return (
            data.method == "get" &&
            data.event == "complete" &&
            [
              ApiCommands.BrowseAll.name,
              ApiCommands.BrowseFeatured.name,
              ApiCommands.BrowseNearby.name,
              ApiCommands.BrowseNew.name,
              ApiCommands.BrowseRandomNearby.name,
              ApiCommands.BrowseRecentActivity.name,
              ApiCommands.ProfileLikedUs.name,
              ApiCommands.ProfileWeLiked.name,
            ].indexOf(data.command) >= 0
          );
        })
      )
      .subscribe({
        next: (data) => {
          setTimeout(() => {
            this.ngZone.run(() => {
              this.debug.l("api spy refresh");
            });
          }, 50);
        },
      });
  }

  /**
   * we need to reload some lists if user changes key params
   * like location, age, match options, etc
   */
  private initRefreshOnProfileChange() {
    // refresh conditions

    this.api.eventStream$
      .pipe(
        filter((data) => {
          // on profile update or match pref update refresh lists

          return (
            (data.method == "post" || data.method == "patch") &&
            data.event == "finalize" &&
            (data.command == ApiCommands.AccountPatch.name ||
              data.command == ApiCommands.AccountPostMatchPref.name ||
              data.command == ApiCommands.AccountGeolocation.name)
          );
        })
      )
      .subscribe({
        next: (data) => {
          this.profileSuggestions.refresh();
          this.paginatorHelper.browsePaginator.refresh();
          this.ads.boot();
        },
      });
  }
}
