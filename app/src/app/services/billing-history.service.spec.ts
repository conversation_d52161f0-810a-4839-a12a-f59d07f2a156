import { TestBed } from "@angular/core/testing";

import { BillingHistoryService } from "./billing-history.service";
import { ApiService } from "./api/api.service";
import { Platform } from "@ionic/angular";

describe("BillingHistoryService", () => {
  let service: BillingHistoryService;
  let apiSpy: ApiService;

  beforeEach(() => {
    apiSpy = jasmine.createSpyObj("ApiService", ["call"]);

    TestBed.configureTestingModule({
      providers: [{ provide: ApiService, useValue: apiSpy }, Platform],
    });
    service = TestBed.inject(BillingHistoryService);
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });
});
