import { Injectable } from "@angular/core";
import { ApiService } from "./api/api.service";
import { ApiParams } from "./api/api.params";
import { ApiResponses } from "./api/api.responses";
import { ApiCommands } from "./api/api.commands";
import { SearchServiceItemList } from "./lists/search.service.item-list";
import { Debug } from "../helpers/debug";

@Injectable({
  providedIn: "root",
})
export class SearchService {
  // Search list.
  public searchItems: SearchServiceItemList = new SearchServiceItemList(
    this.api,
    ApiCommands.SearchBasic,
    this.dbg
  );

  constructor(private api: ApiService, private dbg: Debug) {}

  /**
   * Search basic.
   *
   * @param {ApiParams.SearchBasic.type} params
   * @returns {Promise<ApiResponses.ProfileListResponse.type>}
   */
  public basic(params: ApiParams.SearchBasic.type) {
    return this.api
      .call<ApiResponses.ProfileListResponse.type>({
        command: ApiCommands.SearchBasic.name,
        method: "get",
        useToken: true,
        params,
        commandVersion: "2",
      })
      .toPromise();
  }
}
