
ion-toolbar {
    --background: transparent;
    --border-width: 0 !important;
    align-items: flex-start !important;
}

.ios .toolbar-header {
    --padding-start: 14px;
    --padding-end: 14px;
}

.md .toolbar-header {
    --padding-start: 8px;
    --padding-end: 8px;
}

.toolbar-footer {
    --padding-start: 14px;
    --padding-end: 14px;
}

.toolbar-slot {
    width: 32px;
    display: flex;
    
    &.slot-start {
        justify-content: flex-start;
    }

    &.slot-end {
        justify-content: flex-end;
    }
}

.toolbar-footer {
    --background: transparent;
    --border-width: 0 !important;
    --padding-start: 14px;
    --padding-end: 14px;
}

.header-md:after,
.footer-md::before {
    display: none;
}

.debug {
    font-size: 26px;
    padding: 4px;
}

.profile-card {
    display: flex;
    flex-flow: column wrap;
    align-items: center;
    justify-content: center;

    &__avatar {
        width: 110px;
        height: 110px;
        border: 3px solid var(--ion-color-white);
        margin: auto;
    
        &:focus {
            outline: none;
        }
    }

    &__username {
        font-size: 20px;
        font-weight: 600;
        margin: 10px 0 2px;
    }

    &__user-type {
        color: var(--ion-color-white);
        opacity: .6;
        margin-bottom: 8px;
    }

    &__edit {
        --border-radius: 6px;
        --padding-start: 1.2em;
        --padding-end: 1.2em;
        height: 2.2em;
        font-size: 16px;
    }
}

.md .profile-card__edit {
    font-size: 14px;
}

.menu-title {
    margin-top: 24px;
}

.md .menu-title {
    padding-left: 16px;
}

.ios .menu-title {
    padding-left: 20px;
}

.menu-active-link ion-icon {
    color: var(--ion-color-primary);
}