import { Injectable } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { Debug } from "./helpers/debug";
import { AccountService } from "./services/account.service";
import { ApiParams } from "./services/api/api.params";
import { CameraService } from "./services/camera.service";
import { Camera, CameraSource } from "@capacitor/camera";
import { DialogHelperService } from "./services/dialog-helper.service";
import { environment as Env } from "src/environments/environment";
import { MiscService } from "./services/misc.service";
import { ActionSheetController } from "@ionic/angular";
import { Router } from "@angular/router";
import { GalleryImage } from "./schemas/gallery-image";

@Injectable({
  providedIn: "root",
})
export class PictureUploadHelperService {
  public attachImages: GalleryImage.type[] = [];

  constructor(
    private debug: Debug,
    private misc: MiscService,
    private camera: CameraService,
    private translate: TranslateService,
    private ownProfileService: AccountService,
    private dialog: DialogHelperService,
    private as: ActionSheetController,
    private router: Router,
  ) {}

  private _uploadPhotoCallback: Function = (data = null, url = null) => {};
  private _deletePhotoCallback: Function = (data = null, url = null) => {};

  /**
   * Upload Photo function
   */
  public fetchPhoto(sourceType, uploadCallback) {
    this._uploadPhotoCallback = uploadCallback;

    // this.analytics.gtmScreen("photo_fetch_" + sourceType);

    return new Promise<any>((resolve, reject) => {
      this.camera
        .getPicture(sourceType)
        .then((data) => {
          // this.analytics.gtmScreen("photo_fetch_success");
          this._uploadPhotoCallback(data["base64String"]);
          resolve(data);
        })
        .catch((e) => {
          // this.analytics.gtmScreen("photo_fetch_failed");
          reject("photo_upload_failed");
        });
    });
  }

  /**
   * Get image data for display
   */
  public getPictureData(data): string {
    if (data) {
      return "data:image/jpeg;base64," + data;
    }

    return "";
  }

  /**
   * Display Photo Menu
   */
  public async displayUploadMenu(
    uplaodCallback: Function,
    attach: boolean = false,
    deleteCallback: Function = null,
  ) {
    this._uploadPhotoCallback = uplaodCallback;
    this._deletePhotoCallback = deleteCallback;

    let aso = {
      title: this.translate.instant("upload_photo"),
      buttons: [
        {
          text: this.translate.instant("take_picture"),
          handler: () => {
            this.fetchPhoto(CameraSource.Camera, uplaodCallback);
          },
        },
        {
          text: this.translate.instant("choose_library"),
          handler: () => {
            this.fetchPhoto(CameraSource.Photos, uplaodCallback);
          },
        },
      ],
    };

    if (attach) {
      aso.buttons.unshift({
        text: this.translate.instant("set_attachments"),
        handler: () => {
          this.attachImage();
        },
      });
    }

    // add cancel

    aso.buttons.push({
      text: this.translate.instant("cancel"),
      handler: () => {},
    });

    // add remove if has sense

    if (this._deletePhotoCallback) {
      aso.buttons.push({
        text: this.translate.instant("delete"),
        handler: () => {
          // delete image data
          this._deletePhotoCallback();
        },
      });
    }

    // if we're in browser and we're debuggin add a button that can set
    // image for debugging and testing purposes

    if (!this.misc.isNativePlatform() && Env.IS_DEBUG) {
      aso.buttons.push({
        text: "DEBUG ADD IMAGE",
        handler: () => {
          this._uploadPhotoCallback(this.debug.testPhoto);
        },
      });
    }

    // this.analytics.gtmScreen("upload_photo");

    let actionSheet = await this.as.create(aso);

    return await actionSheet.present();
  }

  /**
   * open image attach page
   */
  public attachImage(defaultOpen: string = "") {
    this.router.navigate(["/app/pictures"]);
  }
}
