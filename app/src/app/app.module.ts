import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Mod<PERSON>, CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { BrowserModule } from "@angular/platform-browser";
import { RouteReuseStrategy } from "@angular/router";

import { IonicModule, IonicRouteStrategy } from "@ionic/angular";
import { AppComponent } from "./app.component";
import { AppRoutingModule } from "./app-routing.module";

import { HttpClientModule } from "@angular/common/http";
import { FormsModule } from "@angular/forms";

import { IonicStorageModule } from "@ionic/storage-angular";
import { Storage, Drivers } from "@ionic/storage";

import { Utils } from "./helpers/utils";
import { AppErrorHandler } from "./helpers/error-handler";
import { DataMockerService } from "./services/data-mocker/data-mocker.service";
import { SessionService } from "./services/session.service";
import { StorageService } from "./services/storage.service";
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from "./services/error-helper";

import { TranslateModule, provideTranslateService } from "@ngx-translate/core";
import {
  provideTranslateHttpLoader,
  TranslateHttpLoaderConfig,
} from "@ngx-translate/http-loader";

import { GoogleTagManagerModule } from "angular-google-tag-manager";
import { environment as Env } from "../environments/environment";

let storageConfigObject = {
  name: Utils.getAppId("db_"),
  driverOrder: [Drivers.IndexedDB, Drivers.LocalStorage],
  storeName: Utils.getAppId("db_"),
};
@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    HttpClientModule,
    IonicModule.forRoot(),
    AppRoutingModule,
    TranslateModule,
    FormsModule,
    IonicStorageModule.forRoot(storageConfigObject),
    GoogleTagManagerModule.forRoot({ id: Env.GOOGLE_TAG_MANAGER_ID }),
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [
    provideTranslateService({ lang: "sr", fallbackLang: "en" }),
    provideTranslateHttpLoader(<TranslateHttpLoaderConfig>{
      prefix: "./assets/i18n/",
      suffix: ".json",
    }),

    Storage,
    StorageService,
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    DataMockerService,
    SessionService,
    { provide: ErrorHandler, useClass: AppErrorHandler },
    ErrorHelper,
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
