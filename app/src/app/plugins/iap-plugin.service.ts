import { Injectable } from "@angular/core";
import { AccountService } from "../services/account.service";
import { firstValueFrom } from "rxjs";
import { DeviceInfoService } from "../services/device-info.service";
import "cordova-plugin-purchase";
import _ from "lodash";
import { PaymentService } from "../services/payment.service";
import { Debug } from "../helpers/debug";
import { environment as Env } from "src/environments/environment";
import { AnalyticsService } from "../services/analytics.service";
import {
  TME_abandon_upgrade,
  TME_upgrade_error,
} from "../classes/gtm-event.class";
import { TranslateService } from "@ngx-translate/core";
import {
  IapPluginInterface,
  ProductCategory,
  PurchaseErrorResult,
  StringToArrayOfNumbers,
  StringToNumber,
  StringToObject,
} from "./iap-plugin.interface";
import { MiscService } from "../services/misc.service";

export const PURCHASE_PROMISE_TIMEOUT = 60000;

@Injectable({
  providedIn: "root",
})
export class IapPluginService implements IapPluginInterface {
  private store: CdvPurchase.Store = null;
  private serverProducts: any = {};
  // helper to find product index by id
  private serverProductsIdMap: StringToNumber = {};
  // helper to finc product id's by category
  private serverProductsCategoryMap: StringToArrayOfNumbers = {};
  private storeProducts: any = {};
  private formattedProducts: StringToObject = {};

  /**
   * Decryot iso period details from new format
   *
   * @param iso
   * @returns
   */
  private decryptDuration(iso?: string): any {
    if (!iso) return {};

    const l = iso.length;
    const n = iso.slice(1, l - 1);

    let period = { D: "Day", W: "Week", M: "Month", Y: "Year" }[iso[l - 1]];
    let length = iso[1];

    return {
      length: parseInt(length),
      period: period,
    };
  }

  /**
   * Convert new to old in app purchase cordova plugin 11 -> 13 product data
   * @param server_data
   * @param store_data
   * @returns
   */
  private convertProducts(server_data, store_data) {
    var trial: any = {};
    var regular: any = {};

    // find order with most elements

    var offer =
      _.maxBy(store_data?.offers, (obj) => obj["pricingPhases"]?.length) || {};

    this.debug.l("iap offer data", offer);

    trial =
      _.find(offer["pricingPhases"], { recurrenceMode: "FINITE_RECURRING" }) ||
      {};
    regular =
      _.find(offer["pricingPhases"], {
        recurrenceMode: "INFINITE_RECURRING",
      }) || {};

    this.debug.l("iap offer trial data", trial);
    this.debug.l("iap offer data", regular);

    let productId = store_data?.id || server_data?.id;

    var result = {
      id: productId,
      type: store_data?.type || "paid subscription",
      group: "default",
      state: "valid", // @todo see where to get this
      title: store_data?.title,
      description: store_data?.description,
      priceMicros: regular?.priceMicros,
      price: regular?.price,
      currency: regular?.currency,
      countryCode: null,
      loaded: true,
      canPurchase: offer["canPurchase"],
      owned: this.store.owned(productId),
      introPrice: trial?.price,
      introPriceMicros: trial?.priceMicros,
      introPricePeriod: this.decryptDuration(trial?.billingPeriod)?.length,
      introPriceNumberOfPeriods: trial?.billingCycles,
      introPricePeriodUnit: this.decryptDuration(trial?.billingPeriod)?.period,
      introPriceSubscriptionPeriod: this.decryptDuration(trial?.billingPeriod)
        ?.period,
      introPricePaymentMode: trial?.paymentMode,
      ineligibleForIntroPrice: null,
      discounts: [],
      downloading: false,
      downloaded: false,
      additionalData: null,
      transaction: null,
      trialPeriod: null,
      trialPeriodUnit: null,
      billingPeriod: this.decryptDuration(regular?.billingPeriod)?.length,
      billingPeriodUnit: this.decryptDuration(regular?.billingPeriod)?.period,
      valid: true, // @todo read this valid data
    };

    let _merged = { ...server_data, ...result };

    this.debug.l("iap merged data", _merged);

    return _merged;
  }

  /**
   * Get current store platform
   * @returns
   */
  public getPlatform(): CdvPurchase.Platform {
    let _p: CdvPurchase.Platform =
      this.deviceInfo.platform == "android"
        ? window.CdvPurchase?.Platform?.GOOGLE_PLAY
        : window.CdvPurchase?.Platform?.APPLE_APPSTORE;

    if (this.misc.isNativePlatform()) {
      _p = this?.store?.defaultPlatform();
    }

    return _p;
  }

  constructor(
    private accountService: AccountService,
    private deviceInfo: DeviceInfoService,
    private payment: PaymentService,
    private debug: Debug,
    private analytics: AnalyticsService,
    private translate: TranslateService,
    private misc: MiscService,
  ) {
    this.store = window?.CdvPurchase?.store;
  }

  private _registerProductsList: CdvPurchase.IRegisterProduct[] = [];

  /**
   * Initialize store
   * @returns promise that resolves when store initialized
   */
  public async initialize(): Promise<any> {
    return this._initialize();
  }

  /**
   * This method retrieves a server product based on its ID.
   * The IDs are mapped to the product data in the `serverProductsIdMap` object.
   *
   * @param {number|string} id - The ID of the product to be returned.
   *
   * @returns {object} Returns the product associated with the given ID.
   * If no product is found for the given ID, an empty object is returned.
   *
   * @private
   */
  private getServerProductById(id: number | string): object {
    return this.serverProducts[this.serverProductsIdMap[id]] || {};
  }

  /**
   * Get all products
   */
  public getAllProducts(): Array<any> {
    return _.values(this.formattedProducts);
  }

  /**
   * Retrieves an array of products that belong to a specified category.
   * @param category - the category of the products to retrieve.
   * @returns an array of products under the specified category, or an empty array if no product is available.
   */
  public getProductsByCategory(category: ProductCategory): Array<any> {
    const productsByCategory = []; // Use meaningful name for clearer intent.

    const productIndexes: number[] =
      this.serverProductsCategoryMap[category] || []; // Get array of product indexes for a specific category, or an empty array if no array exists for a category.
    productIndexes.forEach((index) => {
      // Iterate through the list of product indexes.
      const product =
        this.formattedProducts[this.serverProducts[index]?.id] || null; // Retrieve the corresponding product that matches the product index, if product doesn't exist return null.

      if (product !== null) {
        // Check if product exists and is not null.
        productsByCategory.push(product); // Add the product to the product array.
      }
    });

    return productsByCategory; // Return the product array.
  }

  // variable to store wether the last transaction was a sandbox transaction (we get this from our server)
  private _wasSandboxPurchase: boolean = false;

  /**
   * Update a single product data in formattedProducts
   * @param productId
   * @param data
   */
  private updateProductData(productId: string, data: any) {
    this.storeProducts[productId] = data;

    this.formattedProducts[productId] = this.convertProducts(
      this.getServerProductById(productId),
      data,
    );

    this.debug.l("iap product update", data);
  }

  /**
   * Callback function that runs when product is updated ( app store triggers this )
   * @param product variable that holds the products itself
   */
  private _iap_handler_product_updated(product: CdvPurchase.Product) {
    // @data_example_3 products

    this.debug.l("iap product update handler", product);

    product.offers.forEach((offer, index) => {
      this.updateProductData(product.id, product);
    });
  }

  /**
   * Callback triggered by app store when receipt updated
   * @param receipt receipt object
   */
  private _iap_handler_receipt_updated(receipt: CdvPurchase.Receipt) {
    // @data_example_2 receipt

    this.debug.l("iap receipt update handler", receipt);
  }

  /**
   * Callback triggered by app store when purchase finished in full - meaning user bought the product, and we verified it
   * @param transaction
   */
  private _iap_handler_finished(transaction: CdvPurchase.Transaction) {
    // @data_example_1 transaction

    this.debug.l("IAP Finished transaction param", transaction);

    if (this.wasPurchasePromiseFinished) {
      this.debug.l(
        "IAP transaction was already finished in this session! (duplicate callback from plugin) skipping",
      );

      return;
    }

    if (
      this.selectedProductId == "" ||
      transaction?.transactionId === "appstore.application"
    ) {
      // doing this because of
      // https://github.com/j3k0/cordova-plugin-purchase/issues/1428#issuecomment-**********

      this.debug.l("IAP not a product ( first run ) or no product selected");

      return;
    }

    if (this.accountService?.account?.is_premium) {
      // user is already a premium, no need to do anything
      // probably just finished the transaciton because of that
      this.debug.l("IAP finish - user already premium");

      this.rejectPurchse({
        code: 900055,
        message:
          this.translate.instant("purchase_failed") + " ( already premium ) ",
        doDisplay: false,
        errors: [],
      });

      return;
    }

    if (!this._wasVerified) {
      this.debug.l("IAP was not verified reject");

      this.rejectPurchse({
        code: 900013,
        message:
          this.translate.instant("purchase_failed") + " ( not verified ) ",
        doDisplay: true,
        errors: [],
      });

      return;
    }

    // for some reason this is never triggered
    this.debug.l("IAP finished");
    this.debug.l(
      "Products owned: " + transaction.products.map((p) => p.id).join(","),
    );

    let result = this.formattedProducts[this.selectedProductId];
    result["transaction"] = transaction;

    this.resolvePurchase(result);
  }

  /**
   * Callback triggered for unverified receipt
   * @param receipt
   */
  private _iap_handler_unverified(res: CdvPurchase.UnverifiedReceipt) {
    //example code @unverified_res_1

    this.debug.l("IAP unverified", res);

    // get status response from our server if it's an error then finalize

    // @todo maybe check server response

    // finish receipt for ios
    res.receipt.finish();
    this.debug.l("IAP unverified FINISH");
  }

  private _wasVerified = false;

  /**
   * Callback triggered when receipt is verified - we need to finalize it here
   * @param receipt
   */
  private _iap_handler_verified(receipt: CdvPurchase.VerifiedReceipt) {
    // @data_example_2 receipt

    this.debug.l("IAP RECEIPT verified");

    this._wasVerified = true;

    receipt
      .finish()
      .then((data) => {
        this.debug.l("IAP finish done", data);
      })
      .catch((e) => {
        this.debug.le("IAP finish error", e);

        this.rejectPurchse({
          code: 900007,
          message:
            this.translate.instant("purchase_failed") +
            " ( receipt validation failed - " +
            e?.message +
            ") ",
          doDisplay: true,
          errors: e,
        });
      });

    this._registerProductsList.forEach((sp) => {
      this.debug.l(
        "IAP owned? " + sp.id + " " + (this.store.owned(sp) ? "YES" : "NO"),
      );
    });
  }

  /**
   * Callback runs every time product is approved ( can happen anywhere some params needs checking )
   * @param transaction
   * @returns
   */
  private _iap_handler_approved(transaction: CdvPurchase.Transaction) {
    // @data_example_1 transaction

    this.debug.l("IAP STORE approved", transaction);

    // sometimes approved is triggered when we register products
    // even if it should not
    // https://github.com/j3k0/cordova-plugin-purchase/issues/1428#issuecomment-**********
    // we prevent this by only verifying product if it's not init cycle

    // maybe verify only if the user is not premium
    // but what if user is banned???
    // maybe set a flag when user started payment process, and only verify then, then send back the flag
    // but then it will not work if paid outside of app ...

    if (this.accountService?.account?.is_premium) {
      // we don't need to process this if user is already premium
      // @todo log this somewhere
      this.debug.l("IAP run finish transation - already premium");
      transaction.finish();
      return;
    }

    /*
    if (transaction?.isAcknowledged) {
      console.log(
        "If the transaction is already acknowledged we don't need to verify / process it - we just ignore it"
      );
      // do we need this, or this causing the approve to be fired?
      // @todo maybe send a notification that this happened if user does not have premium yet? think about this

      // receipt state is "approved" here

      transaction.finish();

      // when we call this receipt is updated and it will be "finished"

      return;
    }
    */

    /*

    here is an example when user 1st open the application on ios
    it registers as a transaction because user "bought" the free app, we ignore this

    [Log] IAP STORE approved (vendor.js, line 41850)
    [Log] {
      "className": "Transaction",
      "transactionId": "appstore.application",
      "state": "approved",
      "products": [
          {
              "id": "com.onlineconnections.seniornextDev"
          }
      ],
      "platform": "ios-appstore"
    }

    */

    if (
      transaction.products &&
      transaction.products[0] &&
      transaction.products[0].id &&
      transaction.products[0].id === Env.APP_STORE_ID
    ) {
      // doing this because of
      // https://github.com/j3k0/cordova-plugin-purchase/issues/1428#issuecomment-**********

      this.debug.l("IAP run finish transation - default case");
      transaction.finish();
      return;
    }

    /*

    When user really buys something on ios we get the followign data

    [Log] IAP STORE approved (vendor.js, line 41850)
    [Log] {"className":"Transaction","transactionId":"2000000427039701","state":"approved","products":[{"id":"m1_14_199_premium","offerId":""}],"platform":"ios-appstore","purchaseDate":"2023-10-03T13:39:24.000Z"} (vendor.js, line 41850)

    state is approved, we need to verify

    */

    // we run verification
    if (transaction.state == CdvPurchase.TransactionState.APPROVED) {
      this.debug.l("IAP RUN VERIFY");
      transaction.verify();
      return;
    }

    this.debug.l("IAP approved nothing triggered");
  }

  /**
   * Callback happens when error happens during purchase chain on store / device
   * @param error
   */
  private _iap_handler_error(error) {
    // @todo log maybe display ( filter out obvious like cancel ) or just use a callback and handle this outside in caller class?

    this.debug.le("IAP ERROR", error);

    this.rejectPurchse({
      code: 900004,
      message: error?.message || "default_error_message",
      doDisplay: true,
    });
  }

  /**
   * Implementation of a validator, called when plugin wants to vaidate the purchase
   * @param receipt receipt data for validation
   * @param callback callback function used for accept/reject receipt
   */
  private async _iap_handler_validator(
    receipt: CdvPurchase.Validator.Request.Body,
    callback: CdvPurchase.Callback<CdvPurchase.Validator.Response.Payload>,
  ) {
    // on android, and normally
    // @data_example_2 receipt

    // sometimes we get weird data
    // @data_example_weird_data_from_apple

    // when user really buys subscription we get the followign data
    // @data_example_ios_first_buy

    this.debug.l("VALIDATOR function", receipt);

    if (this.wasPurchasePromiseFinished) {
      this.debug.l(
        "IAP transaction was already finished (VALIDATOR) in this session! (duplicate callback from plugin) skipping",
      );

      callback({
        ok: false,
        code: CdvPurchase.ErrorCode.UNKNOWN,
        status: 400,
        // data: null,
        message: "Purchase already finished",
      });

      return;
    }

    // let receiptOffer = receipt?.offers?.[0] ?? null;

    // selected product either from user's form or receipt
    // @todo see if this is ok? if this will restore purchased and failed stuff
    let selectedProductId = this.selectedProductId || null; //receipt?.["id"] || receiptOffer?.["productId"] || null;

    if (
      !selectedProductId
      // this.wasPurchasePromiseFinished ||
      // @todo do we need this? or we want the validation to run even without user initialization
      //       if purchase was interrupted for some reason?

      // !receiptOffer ||
      // receiptOffer?.["productType"] != CdvPurchase.ProductType.PAID_SUBSCRIPTION
    ) {
      // doing this because of
      // https://github.com/j3k0/cordova-plugin-purchase/issues/1428#issuecomment-**********

      this.debug.l("Skip receipt is not for a paid subscription", receipt);

      callback({
        ok: false,
        code: CdvPurchase.ErrorCode.UNKNOWN,
        status: 400,
        // data: null,
        message: "No selected product ID",
      });

      this.rejectPurchse({
        code: 900010,
        doDisplay: false,
        message: "validator_triggered_bad_trigger",
      });

      return;
    }

    // do server side registration here
    // and call callback only when server returns success

    let product = <any>this.formattedProducts[selectedProductId];

    let wasError = false;
    let errorObject = {};
    let serverResponse = {};
    let serverReceipt = {};

    // android

    if (this.deviceInfo.platform == "android") {
      try {
        let result = await firstValueFrom(
          this.payment.android({
            amount:
              "" +
              (
                (receipt?.["priceMicros"] || product?.priceMicros) / 1000000
              ).toFixed(2),
            currency: receipt?.["currency"] || product?.currency,
            merchant_order_number: product["id"],
            package_name: Env.APP_STORE_ID,
            purchase_id: this.selectedProductId, //receipt?.transaction["id"],
            purchase_token: receipt.transaction["purchaseToken"],
            purchase_type: "0",
          }),
        );

        serverResponse = result;

        // see if the purchase was made in sandbox env
        // based on reault from api call
        if (result.data?.environment == "sandbox") {
          this._wasSandboxPurchase = true;
        }

        /*
        serverReceipt =
          result.data?.receipt["response"] || result.data?.receipt;
        */

        serverReceipt = receipt?.transaction;

        this.debug.l("android payment call", result);
      } catch (error) {
        wasError = true;
        this.debug.le("android purchase error", error);
        errorObject = error;
        //this.callErrorHandler(error);
        this.rejectPurchse({
          code: 900005,
          message: "purchase_failed",
          doDisplay: true,
          errors: error?.errors,
          originalError: error,
        });
      }
    }

    // ios
    else if (this.deviceInfo.platform == "ios") {
      // @receipt_example_ios_buy
      // for receipt value

      try {
        let result = await firstValueFrom(
          this.payment.ios({
            "receipt-data": receipt?.transaction?.["appStoreReceipt"] || "",
            // amount: "" + (receipt?.priceMicros / 1000000).toFixed(2),
            amount: "" + (product?.priceMicros / 1000000).toFixed(2),
            // currency: receipt.currency,
            currency: product?.currency,
            purchase_id: selectedProductId, //receipt?.transaction["id"],
          }),
        );

        serverResponse = result;

        // see if the purchase was made in sandbox env
        // based on reault from api call
        if (result.data?.environment == "sandbox") {
          this._wasSandboxPurchase = true;
        }

        // @todo get this from client side - no server side shit
        /*
        serverReceipt =
          result?.data?.receipt["response"] || result?.data?.receipt;
        */

        serverReceipt = receipt?.transaction;

        this.debug.l("ios payment call", result);
      } catch (error) {
        this.debug.le("IAP ios error", error);

        wasError = true;
        errorObject = error;
        this.debug.le("ios purchase error", error);

        this.rejectPurchse({
          code: 900006,
          message: "purchase_failed",
          doDisplay: true,
          errors: error?.errors,
          originalError: error,
        });
      }
    } else {
      wasError = true;
      errorObject = "no os detected";
    }

    this.debug.l("IAP Server response", serverReceipt);
    this.debug.l("IAP Server receipt", serverReceipt);

    if (!wasError) {
      let transactionObject = {
        type: receipt?.transaction?.type,
      };

      /*
      if (this.deviceInfo.platform == "android") {
        try {
          transactionObject["data"] = serverReceipt; //JSON.parse(receipt?.transaction['receipt'])
        } catch (e) {
          this.debug.le("IAP JSON parse error google receipt", receipt);
        }
      }

      if (this.deviceInfo.platform == "ios") {
        // @todo we need the receipt back from the server and use that
        transactionObject["data"] = serverReceipt;
      }
      */

      transactionObject["data"] = serverReceipt;

      callback({
        ok: true,
        data: {
          id: selectedProductId,
          latest_receipt: true,
          transaction: <CdvPurchase.Validator.Response.NativeTransaction>(
            transactionObject
          ),
        },
      });
    } else {
      callback({
        ok: false,
        code: CdvPurchase.ErrorCode.UNKNOWN,
        status: 400,
        message:
          errorObject["message"] ?? "Default error message purchase failed",
        // data: errorObject,
      });
    }

    /*

   we need to simply call our validator functions, and then finalize product based on what we get back
   we only need to validate if we have a purchase event which needs validation, nothing else - the rest is unnecesary

   as soon as https://phab.dvipdev.com/T26610 and https://hs-phab.dvipdev.com/T20151 done, I hope I'll be able to finish this

   */
  }

  /**
   * Initialize and format store products
   * @returns promise
   */
  private async _initialize(): Promise<any> {
    return new Promise(async (resolve, reject) => {
      try {
        this.serverProducts = await firstValueFrom(
          this.accountService.getProducts({
            category: "",
            is_active: 1,
            store_type: this.deviceInfo.platform,
          }),
        );
      } catch (e) {
        this.rejectPurchse({
          code: 900002,
          message: "default_error_message",
          doDisplay: true,
        });
      }

      this.serverProductsIdMap = {};
      this.serverProductsCategoryMap = {};

      for (var i = 0; i < this.serverProducts.length; i++) {
        // map id to index
        this.serverProductsIdMap[this.serverProducts[i]?.id] = i;

        this.serverProducts[i]?.categories.forEach((c) => {
          this.serverProductsCategoryMap[c] =
            this.serverProductsCategoryMap[c] ?? [];
          if (!_.includes(this.serverProductsCategoryMap[c], i)) {
            this.serverProductsCategoryMap[c].push(i);
          }
        });
      }

      this.debug.l("products", [
        this.serverProducts,
        this.serverProductsIdMap,
        this.serverProductsCategoryMap,
      ]);

      this._registerProductsList = [];

      this.serverProducts.forEach((p) => {
        this._registerProductsList.push({
          id: p.id,
          platform: this.getPlatform(),
          type: CdvPurchase.ProductType.PAID_SUBSCRIPTION,
        });
      });

      this.store.verbosity = CdvPurchase.LogLevel.DEBUG;

      // @todo try to make this work, and validate transaction with the server here
      //       so when we get success only then we finalize the transaction and charge the card?

      // if we don't define this then the product approves

      // test with iaptic to see responses
      //this.store.validator = "https://validator.iaptic.com/v3/validate?appName=com.onlineconnections.seniornext&apiKey=ea9982a3-d466-4228-b87a-a88e3c2db984";

      this.store.validator = (receipt, callback) =>
        this._iap_handler_validator(receipt, callback);

      this.store.error = (error) => this._iap_handler_error(error);

      this.store
        .when()
        .approved((transaction) => this._iap_handler_approved(transaction))
        .verified((receipt) => this._iap_handler_verified(receipt))
        .unverified((unverifiedReceipt) =>
          this._iap_handler_unverified(unverifiedReceipt),
        )
        .finished((transaction) => this._iap_handler_finished(transaction))
        .receiptUpdated((receipt) => this._iap_handler_receipt_updated(receipt))
        // @todo add periodical product update and test
        //       in case we change setup and the app is not restarted
        //       we need this
        .productUpdated((product) =>
          this._iap_handler_product_updated(product),
        );

      // @todo set real username
      this.store.applicationUsername = this.accountService?.account?.username;

      this.debug.l("reg products", this._registerProductsList);

      await this.store.register(this._registerProductsList);

      try {
        let res = await this.store.initialize();

        this.debug.l("store ready", [
          res,
          this.storeProducts,
          this.store.products,
        ]);

        // pre initialize store products before update
        this.store.products.forEach((p) => {
          if (!this.storeProducts[p?.id]) {
            this.storeProducts[p?.id] = p;
          }
        });

        this.formattedProducts = {};

        // build final array of product data and format it

        _.forEach(this.storeProducts, (p, k) => {
          this.formattedProducts[k] = this.convertProducts(
            this.getServerProductById(p?.id),
            p,
          );
        });

        this.debug.l("formatted products", this.formattedProducts);
        this.debug.l("catget", [
          this.getProductsByCategory("upgrade"),
          this.getProductsByCategory("offer"),
          this.getProductsByCategory("campaign"),
        ]);

        /*
        this._registerProductsList.forEach((sp) => {
          console.log(
            "IAP owned? " + sp.id + " " + (this.store.owned(sp) ? "YES" : "NO")
          );
        });
        */

        resolve(this.formattedProducts);
      } catch (e) {
        console.error(e);
        reject(e);
      }

      this.debug.l("products", this.store.products);

      // @data_example_3 products
    });
  }

  /**
   * Get owned product id list
   * @returns array of id's
   */
  public getOwnedProducts(): Array<string> {
    let res = [];

    this._registerProductsList.forEach((sp) => {
      if (this.store.owned(sp)) {
        res.push(sp.id);
      }
    });

    return res;
  }

  // holds the product id that was last used for purchase
  private selectedProductId: string = "";

  // globally usable callbacks set during purchase process
  private purchaseCallbackResolve: Function = null;
  private purchaseCallbackReject: Function = null;

  // flag to see if we finished the purchase process
  // used mainly for timeout check
  private wasPurchasePromiseFinished = false;

  // timeout handler to clear timeout process
  private _promiseTimeoutHandle = null;

  /**
   * Tells if the last purchase was done in a sandbox environment
   */
  public get wasSandboxPurchase() {
    return this._wasSandboxPurchase;
  }

  /**
   * Whenever purchase is successfull we call this functon to resolve it
   * @param data
   */
  private resolvePurchase(data) {
    this.wasPurchasePromiseFinished = true;
    clearTimeout(this._promiseTimeoutHandle);

    this.debug.l("IAP CALL RESOLVE?");
    if (typeof this.purchaseCallbackResolve === "function") {
      this.debug.l("IAP CALL RESOLVE", data);

      this.purchaseCallbackResolve(data);
    }
  }

  /**
   * When purchase fails at any point we use this functin to reject it
   * @param error
   */
  private rejectPurchse(error: PurchaseErrorResult) {
    // @todo add statistics!!!
    this.wasPurchasePromiseFinished = true;
    this.selectedProductId = null;

    clearTimeout(this._promiseTimeoutHandle);
    this.debug.l("IAP CALL REJECT?");

    // compatibility
    error.errors = [{ error: error.message }];

    if (error.doDisplay) {
      this.analytics.logEvent("upgrade_error", <TME_upgrade_error>{
        message: error.message,
      });
    }

    if (typeof this.purchaseCallbackReject === "function") {
      this.debug.l("IAP CALL REJECT!", error);
      this.purchaseCallbackReject(error);
    }
  }

  /**
   * Initialize purchase of a product
   * @param product_id id of the product that we want to purchase
   * @returns
   */
  public purchase(product_id): Promise<any> {
    // @todo user and isPurchaseInProgress: boolean flag and only react if it's true

    // also take a look at what will happen if user bought outside of app ... when it's triggered? how should we use that

    this.selectedProductId = product_id;
    this._wasSandboxPurchase = false;
    this._wasVerified = false;

    return new Promise((resolve, reject) => {
      this.wasPurchasePromiseFinished = false;

      // @todo do we need this? why would we need this?
      //       i think it's on ios, that finish is never called, and it can stuck in this state
      //       find a proper solution
      this._promiseTimeoutHandle = setTimeout(() => {
        /* commenting this out - we don't need this probably, if we need I'll restore
           but in case we need this, we need to find a much better solution
           so I'll comment out @todo remove this in final version

        if (!this.wasPurchasePromiseFinished) {
          this.wasPurchasePromiseFinished = true;

          // @todo try to refresh user object and see if user got premium
          // if yes, then finalize the purchase, no the throw error
          // this to prevent finished not firing problem

          this.rejectPurchse({
            code: 900001,
            doDisplay: true,
            message: "purchase_timeout",
          });
        }
        */
      }, PURCHASE_PROMISE_TIMEOUT);

      this.purchaseCallbackReject = reject;
      this.purchaseCallbackResolve = resolve;

      this.debug.l("IAP product_id ?", product_id);

      this.store
        .get(product_id)
        .getOffer()
        .order({ applicationUsername: this.accountService?.account?.username })
        .then((result) => {
          if (result?.isError) {
            /*
            android

              { "isError": true, "code": 6777006, "message": "USER_CANCELED" }
              {isError: true, code: 6777003, message: 'BILLING_UNAVAILABLE'}

            ios

              {isError: true, code: 6777006, message: "The user cancelled the order."}
            */

            let isCancel = result?.code == 6777006;

            if (isCancel) {
              this.analytics.logEvent(
                "abandon_upgrade",
                <TME_abandon_upgrade>{},
              );
            }

            let msg: string = result?.message;
            if (msg == "BILLING_UNAVAILABLE") {
              msg = this.translate.instant(
                "purchase_failed_BILLING_UNAVAILABLE",
              );
            }

            this.rejectPurchse({
              code: result?.code || 0,
              doDisplay: isCancel ? false : true,
              message:
                this.translate.instant("purchase_failed") + " (" + msg + ")",
            });
          } else {
            this.debug.l("IAP order code end");
          }
        })
        .catch((e) => {
          this.debug.le("IAP Exception", e);

          this.rejectPurchse({
            code: 900003,
            doDisplay: true,
            message: "default_error_message",
          });
        });
    });
  }

  /**
   * Open subscription management page ( system page on application )
   */
  public manageSubscription() {
    this.store.manageSubscriptions();
  }
}

/*

DATA EXAMPLES:

@data_example_1 transaction

    "className": "Transaction",
    "transactionId": "GPA.3327-7455-9225-45725",
    "state": "finished",
    "products": [
        {
            "id": "m1_14_200_premium"
        }
    ],
    "platform": "android-playstore",
    "nativePurchase": {
        "orderId": "GPA.3327-7455-9225-45725",
        "packageName": "com.onlineconnections.seniornextDev",
        "productId": "m1_14_200_premium",
        "purchaseTime": *************,
        "purchaseState": 0,
        "purchaseToken": "ocklioocbmkidopmcnjcljka.AO-J1OwDuvJNCr-kqyK0HuXliiTqhBfZtm2DqgMpg5u8E-P1PRwiodDKAbbI3bKL7Z_nRgQhJ3KPLou_dhWrH2IkGe3bAaYr84gs0rlWSyDsB3Ze777HJXM",
        "obfuscatedAccountId": "8febc4779d4b348f8198d111c775213b",
        "quantity": 1,
        "autoRenewing": true,
        "acknowledged": false,
        "productIds": [
            "m1_14_200_premium"
        ],
        "getPurchaseState": 1,
        "developerPayload": "",
        "accountId": "8febc4779d4b348f8198d111c775213b",
        "profileId": "",
        "signature": "...",
        "receipt": "{\"orderId\":\"GPA.3327-7455-9225-45725\",\"packageName\":\"com.onlineconnections.seniornextDev\",\"productId\":\"m1_14_200_premium\",\"purchaseTime\":*************,\"purchaseState\":0,\"purchaseToken\":\"ocklioocbmkidopmcnjcljka.AO-J1OwDuvJNCr-kqyK0HuXliiTqhBfZtm2DqgMpg5u8E-P1PRwiodDKAbbI3bKL7Z_nRgQhJ3KPLou_dhWrH2IkGe3bAaYr84gs0rlWSyDsB3Ze777HJXM\",\"obfuscatedAccountId\":\"8febc4779d4b348f8198d111c775213b\",\"quantity\":1,\"autoRenewing\":true,\"acknowledged\":false}"
    },
    "purchaseId": "ocklioocbmkidopmcnjcljka.AO-J1OwDuvJNCr-kqyK0HuXliiTqhBfZtm2DqgMpg5u8E-P1PRwiodDKAbbI3bKL7Z_nRgQhJ3KPLou_dhWrH2IkGe3bAaYr84gs0rlWSyDsB3Ze777HJXM",
    "purchaseDate": "2023-09-19T15:00:17.091Z",
    "isPending": false,
    "isAcknowledged": false,
    "renewalIntent": "Renew"
}

    -- transaction when user buys the product / subscription

{
    "className": "Transaction",
    "transactionId": "GPA.3347-3171-3728-83661",
    "state": "finished",
    "products": [
        {
            "id": "m1_14_195_premium"
        }
    ],
    "platform": "android-playstore",
    "nativePurchase": {
        "orderId": "GPA.3347-3171-3728-83661",
        "packageName": "com.onlineconnections.seniornextDev",
        "productId": "m1_14_195_premium",
        "purchaseTime": *************,
        "purchaseState": 0,
        "purchaseToken": "ickngajgfgendligmmgfnhkk.AO-J1OzVjLL6pxrlhvXP2Z3P51PMpy1j2NU0AReh7k5qm-4jgMCXkYPKbsl6ZC0HOPRTtkY0B-gSZhihTwy0ijKhe0rUTFMmqr68cgnZioUhNBtoscuTq5s",
        "obfuscatedAccountId": "84e62391aafc4afcb397d27ed4b0e981",
        "quantity": 1,
        "autoRenewing": true,
        "acknowledged": false,
        "productIds": [
            "m1_14_195_premium"
        ],
        "getPurchaseState": 1,
        "developerPayload": "",
        "accountId": "84e62391aafc4afcb397d27ed4b0e981",
        "profileId": "",
        "signature": "...",
        "receipt": "{\"orderId\":\"GPA.3347-3171-3728-83661\",\"packageName\":\"com.onlineconnections.seniornextDev\",\"productId\":\"m1_14_195_premium\",\"purchaseTime\":*************,\"purchaseState\":0,\"purchaseToken\":\"ickngajgfgendligmmgfnhkk.AO-J1OzVjLL6pxrlhvXP2Z3P51PMpy1j2NU0AReh7k5qm-4jgMCXkYPKbsl6ZC0HOPRTtkY0B-gSZhihTwy0ijKhe0rUTFMmqr68cgnZioUhNBtoscuTq5s\",\"obfuscatedAccountId\":\"84e62391aafc4afcb397d27ed4b0e981\",\"quantity\":1,\"autoRenewing\":true,\"acknowledged\":false}"
    },
    "purchaseId": "ickngajgfgendligmmgfnhkk.AO-J1OzVjLL6pxrlhvXP2Z3P51PMpy1j2NU0AReh7k5qm-4jgMCXkYPKbsl6ZC0HOPRTtkY0B-gSZhihTwy0ijKhe0rUTFMmqr68cgnZioUhNBtoscuTq5s",
    "purchaseDate": "2023-09-22T10:37:43.488Z",
    "isPending": false,
    "isAcknowledged": false,
    "renewalIntent": "Renew"
}

    -- transaction when application restarts

{
    "className": "Transaction",
    "transactionId": "GPA.3347-3171-3728-83661",
    "state": "approved",
    "products": [
        {
            "id": "m1_14_195_premium"
        }
    ],
    "platform": "android-playstore",
    "nativePurchase": {
        "orderId": "GPA.3347-3171-3728-83661",
        "packageName": "com.onlineconnections.seniornextDev",
        "productId": "m1_14_195_premium",
        "purchaseTime": *************,
        "purchaseState": 0,
        "purchaseToken": "ickngajgfgendligmmgfnhkk.AO-J1OzVjLL6pxrlhvXP2Z3P51PMpy1j2NU0AReh7k5qm-4jgMCXkYPKbsl6ZC0HOPRTtkY0B-gSZhihTwy0ijKhe0rUTFMmqr68cgnZioUhNBtoscuTq5s",
        "obfuscatedAccountId": "84e62391aafc4afcb397d27ed4b0e981",
        "quantity": 1,
        "autoRenewing": true,
        "acknowledged": true,
        "productIds": [
            "m1_14_195_premium"
        ],
        "getPurchaseState": 1,
        "developerPayload": "",
        "accountId": "84e62391aafc4afcb397d27ed4b0e981",
        "profileId": "",
        "signature": "...",
        "receipt": "{\"orderId\":\"GPA.3347-3171-3728-83661\",\"packageName\":\"com.onlineconnections.seniornextDev\",\"productId\":\"m1_14_195_premium\",\"purchaseTime\":*************,\"purchaseState\":0,\"purchaseToken\":\"ickngajgfgendligmmgfnhkk.AO-J1OzVjLL6pxrlhvXP2Z3P51PMpy1j2NU0AReh7k5qm-4jgMCXkYPKbsl6ZC0HOPRTtkY0B-gSZhihTwy0ijKhe0rUTFMmqr68cgnZioUhNBtoscuTq5s\",\"obfuscatedAccountId\":\"84e62391aafc4afcb397d27ed4b0e981\",\"quantity\":1,\"autoRenewing\":true,\"acknowledged\":true}"
    },
    "purchaseId": "ickngajgfgendligmmgfnhkk.AO-J1OzVjLL6pxrlhvXP2Z3P51PMpy1j2NU0AReh7k5qm-4jgMCXkYPKbsl6ZC0HOPRTtkY0B-gSZhihTwy0ijKhe0rUTFMmqr68cgnZioUhNBtoscuTq5s",
    "purchaseDate": "2023-09-22T10:37:43.488Z",
    "isPending": false,
    "isAcknowledged": true,
    "renewalIntent": "Renew"
}

    -- DIFFERENCE

--- <unnamed>
+++ <unnamed>
@@ -1,7 +1,7 @@
 {
     "className": "Transaction",
     "transactionId": "GPA.3347-3171-3728-83661",
-    "state": "finished",
+    "state": "approved",
     "products": [
         {
             "id": "m1_14_195_premium"
@@ -18,7 +18,7 @@
         "obfuscatedAccountId": "84e62391aafc4afcb397d27ed4b0e981",
         "quantity": 1,
         "autoRenewing": true,
-        "acknowledged": false,
+        "acknowledged": true,
         "productIds": [
             "m1_14_195_premium"
         ],
@@ -26,12 +26,12 @@
         "developerPayload": "",
         "accountId": "84e62391aafc4afcb397d27ed4b0e981",
         "profileId": "",
-        "signature": "aABFbrsg3wUs2x+1Mr0q//OmWhlW6WyYF/4xbddo/0X2dGIm86yIxso4xLSj4TgJJib0xJ9QdDwbxvOB50Q0bevQn9+i7EcnXu2TlMRmRm0VbsWK5e4wYpJiYcYbbC9NKqvMApgEN8v6mnvxz22IDIHZQAoyT/pvvJGU3CyqqUViiY126wG3lgrsMuCaBNNT+CCqazYwYohL01XxAQTbC9fkGQBgJ3usfephcvI2rl9w/uvZ44mxPNjUvncu0jqcoXGjhsbCek5RkM6ix7Sqz3Bs+T1wdvlaMv+gC8/fdoYa4dTo7K4jjzp2z/U6mxn05HimBkzn4gK2iVYBymyZYw==",
-        "receipt": "{\"orderId\":\"GPA.3347-3171-3728-83661\",\"packageName\":\"com.onlineconnections.seniornextDev\",\"productId\":\"m1_14_195_premium\",\"purchaseTime\":*************,\"purchaseState\":0,\"purchaseToken\":\"ickngajgfgendligmmgfnhkk.AO-J1OzVjLL6pxrlhvXP2Z3P51PMpy1j2NU0AReh7k5qm-4jgMCXkYPKbsl6ZC0HOPRTtkY0B-gSZhihTwy0ijKhe0rUTFMmqr68cgnZioUhNBtoscuTq5s\",\"obfuscatedAccountId\":\"84e62391aafc4afcb397d27ed4b0e981\",\"quantity\":1,\"autoRenewing\":true,\"acknowledged\":false}"
+        "signature": "TUg7jZh6UBPFglDAS2X3FfCDzXGAAuv3TDJPr90KX/ODAlnCq3b0VTS0zQaSsl7Pky4sz9wYtg5d+qUzxzSdrQaTpIJutDAOSFmHjEg39ZvdS1/fop0a20x9VXqWnYooPaAAMRpRtRnHP7tvaBeJokQGYh18uC3VtW1kpLZpiSewvRURby2yoN7PjK9fb01owozKo8NyF4GHjEm2S6YvHvD8Zg8+IQpgHAqcNWwQmbNKN+XD2QQFUDWnd2Wku63lRb5+BMl1lrCJkWypwIqlZpk/YCJpNRRCsczGUaTiF3+sfXA7GdiMMEQdCV73UzUpHu2UMsV+clPPdJUkwxMwrg==",
+        "receipt": "{\"orderId\":\"GPA.3347-3171-3728-83661\",\"packageName\":\"com.onlineconnections.seniornextDev\",\"productId\":\"m1_14_195_premium\",\"purchaseTime\":*************,\"purchaseState\":0,\"purchaseToken\":\"ickngajgfgendligmmgfnhkk.AO-J1OzVjLL6pxrlhvXP2Z3P51PMpy1j2NU0AReh7k5qm-4jgMCXkYPKbsl6ZC0HOPRTtkY0B-gSZhihTwy0ijKhe0rUTFMmqr68cgnZioUhNBtoscuTq5s\",\"obfuscatedAccountId\":\"84e62391aafc4afcb397d27ed4b0e981\",\"quantity\":1,\"autoRenewing\":true,\"acknowledged\":true}"
     },
     "purchaseId": "ickngajgfgendligmmgfnhkk.AO-J1OzVjLL6pxrlhvXP2Z3P51PMpy1j2NU0AReh7k5qm-4jgMCXkYPKbsl6ZC0HOPRTtkY0B-gSZhihTwy0ijKhe0rUTFMmqr68cgnZioUhNBtoscuTq5s",
     "purchaseDate": "2023-09-22T10:37:43.488Z",
     "isPending": false,
-    "isAcknowledged": false,
+    "isAcknowledged": true,
     "renewalIntent": "Renew"
 }


@data_example_2 receipt

    /*
{
    "id": "m1_14_200_premium",
    "type": "paid subscription",
    "offers": [
        {
            "className": "Offer",
            "id": "m1_14_200_premium@p3m",
            "pricingPhases": [
                {
                    "price": "RSD 4,999",
                    "priceMicros": **********,
                    "currency": "RSD",
                    "billingPeriod": "P3M",
                    "billingCycles": 0,
                    "recurrenceMode": "INFINITE_RECURRING",
                    "paymentMode": "PayAsYouGo"
                }
            ],
            "productId": "m1_14_200_premium",
            "productType": "paid subscription",
            "platform": "android-playstore",
            "type": "subs",
            "tags": [],
            "token": "AUj/YhguQ3DBnvtCB1cMvzrA7h2xFCsG9gUJkWZUFY1l7DlIiNlqqdevi8FfMnW+w7mzPjwk/tvdIE4LMOeyf7R5eeOKtVzZZXKxedbr8A=="
        }
    ],
    "products": [
        {
            "className": "Product",
            "title": "7 days trial, rebills monthly (DEV Senior Next)",
            "description": "Premium Membership Subscription",
            "platform": "android-playstore",
            "type": "paid subscription",
            "id": "m1_14_202_premium",
            "offers": [
                {
                    "className": "Offer",
                    "id": "m1_14_202_premium@p1m",
                    "pricingPhases": [
                        {
                            "price": "RSD 3,699",
                            "priceMicros": 3699000000,
                            "currency": "RSD",
                            "billingPeriod": "P1M",
                            "billingCycles": 0,
                            "recurrenceMode": "INFINITE_RECURRING",
                            "paymentMode": "PayAsYouGo"
                        }
                    ],
                    "productId": "m1_14_202_premium",
                    "productType": "paid subscription",
                    "platform": "android-playstore",
                    "type": "subs",
                    "tags": [],
                    "token": "AUj/YhjYrQ+X3zz/vG5GyLqumwUmntHIvSdB6nqgd9JFUz86UJzBkrTs8UKUzEKZQ3yr2nui9fNs7DeHMW7CWVcoBRbcb0DkxLhvKE3y9g=="
                }
            ]
        },
        {
            "className": "Product",
            "title": "[test] 3 day trial / monthly subscription (DEV Senior Next)",
            "description": "[test] 3 day trial / monthly subscription",
            "platform": "android-playstore",
            "type": "paid subscription",
            "id": "m1_14_195_premium",
            "offers": [
                {
                    "className": "Offer",
                    "id": "m1_14_195_premium@p1m",
                    "pricingPhases": [
                        {
                            "price": "RSD 3,099",
                            "priceMicros": 3099000000,
                            "currency": "RSD",
                            "billingPeriod": "P1M",
                            "billingCycles": 0,
                            "recurrenceMode": "INFINITE_RECURRING",
                            "paymentMode": "PayAsYouGo"
                        }
                    ],
                    "productId": "m1_14_195_premium",
                    "productType": "paid subscription",
                    "platform": "android-playstore",
                    "type": "subs",
                    "tags": [],
                    "token": "AUj/YhhimjpGvJONP2K8wdf1G0nBPursn2fvW4csXml1Mdow2eyesSX/MhmIsG2kD3pdmWuFZzoZHNRcXKENpNEmMN+c9nh/F7vcmIt08Q=="
                }
            ]
        },
        {
            "className": "Product",
            "title": "Quarterly Subscription (DEV Senior Next)",
            "description": "Premium Membership Subscription",
            "platform": "android-playstore",
            "type": "paid subscription",
            "id": "m1_14_200_premium",
            "offers": [
                {
                    "className": "Offer",
                    "id": "m1_14_200_premium@p3m",
                    "pricingPhases": [
                        {
                            "price": "RSD 4,999",
                            "priceMicros": **********,
                            "currency": "RSD",
                            "billingPeriod": "P3M",
                            "billingCycles": 0,
                            "recurrenceMode": "INFINITE_RECURRING",
                            "paymentMode": "PayAsYouGo"
                        }
                    ],
                    "productId": "m1_14_200_premium",
                    "productType": "paid subscription",
                    "platform": "android-playstore",
                    "type": "subs",
                    "tags": [],
                    "token": "AUj/YhguQ3DBnvtCB1cMvzrA7h2xFCsG9gUJkWZUFY1l7DlIiNlqqdevi8FfMnW+w7mzPjwk/tvdIE4LMOeyf7R5eeOKtVzZZXKxedbr8A=="
                }
            ]
        },
        {
            "className": "Product",
            "title": "[test] montly subscription (DEV Senior Next)",
            "description": "[test] montly subscription descriptionh",
            "platform": "android-playstore",
            "type": "paid subscription",
            "id": "m1_14_172_premium",
            "offers": [
                {
                    "className": "Offer",
                    "id": "m1_14_172_premium@p1m",
                    "pricingPhases": [
                        {
                            "price": "RSD 149",
                            "priceMicros": 149000000,
                            "currency": "RSD",
                            "billingPeriod": "P1M",
                            "billingCycles": 0,
                            "recurrenceMode": "INFINITE_RECURRING",
                            "paymentMode": "PayAsYouGo"
                        }
                    ],
                    "productId": "m1_14_172_premium",
                    "productType": "paid subscription",
                    "platform": "android-playstore",
                    "type": "subs",
                    "tags": [],
                    "token": "AUj/YhiGUsBbUZ6edTNubUNPSfv1wT//t+H/eBj2RKEPZGg6IAQdbzbxoGq3+9flyU355uGxsUGnPH8KnirfPrTt/aSR4mwGJmewXoSMVQ=="
                }
            ]
        }
    ],
    "transaction": {
        "type": "android-playstore",
        "id": "GPA.3327-7455-9225-45725",
        "purchaseToken": "ocklioocbmkidopmcnjcljka.AO-J1OwDuvJNCr-kqyK0HuXliiTqhBfZtm2DqgMpg5u8E-P1PRwiodDKAbbI3bKL7Z_nRgQhJ3KPLou_dhWrH2IkGe3bAaYr84gs0rlWSyDsB3Ze777HJXM",
        "signature": "H2oWqwvJ1SsrsIPPbA0Wc4F7tt0V5l29FDjYKxxL2shdg3rt/gHPWFxctYqbK6J4/HPkJAG1pZvS50Fa/yXl3B9gx3+/YT1xrBLIhUJOcmYrcchcChbPCqxUXkXJrz2FcvWfGzzW3wh1e4Xo6VyHWhAyuxuRTSApozYVqfvlxZJ+NZfHMWP9G0dd3ikK/CT8Iz+Dd9SztpwB3lefJI5nHOCytnCGfk53LcYV2E3wOOUPrg7kLlCkgy+WMF9m6qILKcPfHa+uYqPCI9M4QHazy63viUmalwwm5LZ6vy/vURKvpqFFFlCiXpAiwd5hqLbq064um8TL9KQCJHrUFxoVmw==",
        "receipt": "{\"orderId\":\"GPA.3327-7455-9225-45725\",\"packageName\":\"com.onlineconnections.seniornextDev\",\"productId\":\"m1_14_200_premium\",\"purchaseTime\":*************,\"purchaseState\":0,\"purchaseToken\":\"ocklioocbmkidopmcnjcljka.AO-J1OwDuvJNCr-kqyK0HuXliiTqhBfZtm2DqgMpg5u8E-P1PRwiodDKAbbI3bKL7Z_nRgQhJ3KPLou_dhWrH2IkGe3bAaYr84gs0rlWSyDsB3Ze777HJXM\",\"obfuscatedAccountId\":\"8febc4779d4b348f8198d111c775213b\",\"quantity\":1,\"autoRenewing\":true,\"acknowledged\":false}"
    },
    "additionalData": {
        "applicationUsername": "bbt9999"
    },
    "device": {
        "plugin": "cordova-plugin-purchase/13.8.0"
    },
    "currency": "RSD",
    "priceMicros": **********
}


@data_example_3 products

  [
    {
        "className": "Product",
        "title": "[test] 3 day trial / monthly subscription (DEV Senior Next)",
        "description": "[test] 3 day trial / monthly subscription",
        "platform": "android-playstore",
        "type": "paid subscription",
        "id": "m1_14_195_premium",
        "offers": [
            {
                "className": "Offer",
                "id": "m1_14_195_premium@AUj/YhhZzrwFDj2beMfMjbjasQLo4tmND2kGVtEhpDNJwaFCTD0Ew2qxg2dl/irwC4NUSrDaGOqrZR7dVN0B5iL9au6V+/zzTHwcGCQe3A==",
                "pricingPhases": [
                    {
                        "price": "RSD 3,099",
                        "priceMicros": 3099000000,
                        "currency": "RSD",
                        "billingPeriod": "P1M",
                        "billingCycles": 0,
                        "recurrenceMode": "INFINITE_RECURRING",
                        "paymentMode": "PayAsYouGo"
                    }
                ],
                "productId": "m1_14_195_premium",
                "productType": "paid subscription",
                "platform": "android-playstore",
                "type": "subs",
                "tags": [],
                "token": "AUj/YhhZzrwFDj2beMfMjbjasQLo4tmND2kGVtEhpDNJwaFCTD0Ew2qxg2dl/irwC4NUSrDaGOqrZR7dVN0B5iL9au6V+/zzTHwcGCQe3A=="
            }
        ]
    },
    {
        "className": "Product",
        "title": "Quarterly Subscription (DEV Senior Next)",
        "description": "Premium Membership Subscription",
        "platform": "android-playstore",
        "type": "paid subscription",
        "id": "m1_14_200_premium",
        "offers": [
            {
                "className": "Offer",
                "id": "m1_14_200_premium@AUj/YhiR1jVIxaEfMHEyUlJ5RXkUZfmivOxcgsaGHDIV4fFXPqb9F12DBsZzuxQbC/O1mzxs8V1lnVe32/5e+9VXgGTIqR6leWgDNu+YHw==",
                "pricingPhases": [
                    {
                        "price": "RSD 4,999",
                        "priceMicros": **********,
                        "currency": "RSD",
                        "billingPeriod": "P3M",
                        "billingCycles": 0,
                        "recurrenceMode": "INFINITE_RECURRING",
                        "paymentMode": "PayAsYouGo"
                    }
                ],
                "productId": "m1_14_200_premium",
                "productType": "paid subscription",
                "platform": "android-playstore",
                "type": "subs",
                "tags": [],
                "token": "AUj/YhiR1jVIxaEfMHEyUlJ5RXkUZfmivOxcgsaGHDIV4fFXPqb9F12DBsZzuxQbC/O1mzxs8V1lnVe32/5e+9VXgGTIqR6leWgDNu+YHw=="
            }
        ]
    },
    {
        "className": "Product",
        "title": "[test] montly subscription (DEV Senior Next)",
        "description": "[test] montly subscription descriptionh",
        "platform": "android-playstore",
        "type": "paid subscription",
        "id": "m1_14_172_premium",
        "offers": [
            {
                "className": "Offer",
                "id": "m1_14_172_premium@AUj/YhgivPcy+/50xiMeI9vwN7NVxARmAF6izNzwx4UCdgYVCfuG75KLg0mp2a6L1P5SJWucO2sfkmfnJ3hN3sjQB/ZylmVvr9lQpu/uzw==",
                "pricingPhases": [
                    {
                        "price": "RSD 149",
                        "priceMicros": 149000000,
                        "currency": "RSD",
                        "billingPeriod": "P1M",
                        "billingCycles": 0,
                        "recurrenceMode": "INFINITE_RECURRING",
                        "paymentMode": "PayAsYouGo"
                    }
                ],
                "productId": "m1_14_172_premium",
                "productType": "paid subscription",
                "platform": "android-playstore",
                "type": "subs",
                "tags": [],
                "token": "AUj/YhgivPcy+/50xiMeI9vwN7NVxARmAF6izNzwx4UCdgYVCfuG75KLg0mp2a6L1P5SJWucO2sfkmfnJ3hN3sjQB/ZylmVvr9lQpu/uzw=="
            }
        ]
    }
]

multi offer product example

{
    "className": "Product",
    "title": "1 Month Trial (TS Date)",
    "description": "Premium subscription",
    "platform": "android-playstore",
    "type": "paid subscription",
    "id": "m44_15_200_premium",
    "offers": [
        {
            "className": "Offer",
            "id": "m44_15_200_premium@1morm@1mnt-offer",
            "pricingPhases": [
                {
                    "price": "RSD 149",
                    "priceMicros": 149000000,
                    "currency": "RSD",
                    "billingPeriod": "P1M",
                    "billingCycles": 1,
                    "recurrenceMode": "FINITE_RECURRING",
                    "paymentMode": "PayAsYouGo"
                },
                {
                    "price": "RSD 4,599",
                    "priceMicros": 4599000000,
                    "currency": "RSD",
                    "billingPeriod": "P1M",
                    "billingCycles": 0,
                    "recurrenceMode": "INFINITE_RECURRING",
                    "paymentMode": "PayAsYouGo"
                }
            ],
            "productId": "m44_15_200_premium",
            "productType": "paid subscription",
            "platform": "android-playstore",
            "type": "subs",
            "tags": [],
            "token": "AUj/YhjdQ6WPd3LkQ9gOo9liO/Bf+wGkCjudWE436nMolodswsvTluxtyx/+OOcU3lsLuAY7hkq3MR51Xh/g9oK6eNxXbPyPEtJgPaEdFI3l1CE0yJeKo2AwpSk+YJ1Hxg=="
        },
        {
            "className": "Offer",
            "id": "m44_15_200_premium@1morm",
            "pricingPhases": [
                {
                    "price": "RSD 4,599",
                    "priceMicros": 4599000000,
                    "currency": "RSD",
                    "billingPeriod": "P1M",
                    "billingCycles": 0,
                    "recurrenceMode": "INFINITE_RECURRING",
                    "paymentMode": "PayAsYouGo"
                }
            ],
            "productId": "m44_15_200_premium",
            "productType": "paid subscription",
            "platform": "android-playstore",
            "type": "subs",
            "tags": [],
            "token": "AUj/Yhhy1EAUC6S4xxhZqAcFqWjjWPBXRytI7zPDPy0WlL7WnmKRjSqQbv0U9XIJ/iPQR4jgpo5mU71+BDzm7ebnL2fAxBqHQ4OonAYKWNMCeas="
        }
    ]
}

@data_example_weird_data_from_apple

[Log] Object (vendor.js, line 41850)

additionalData: {applicationUsername: "bbtest2"}

device: {plugin: "cordova-plugin-purchase/13.8.2"}

id: "com.onlineconnections.seniornextDev"

products: Array (3)
0 SKProduct

canPurchase:

className: "Product"

countryCode: "US"

description: "Premium subscription"

group: "20503357"

id: "m1_14_199_premium"

offers: [SKOffer] (1)

owned:

platform: "ios-appstore"

pricing:

raw: {id: "m1_14_199_premium", description: "Premium subscription", introPrice: null, introPricePaymentMode: null, billingPeriodUnit: "Month", …}

title: "Monthly rebill"

type: "paid subscription"

SKProduct Prototype
1 SKProduct {className: "Product", title: "3 month rebill", description: "Premium subscription", platform: "ios-appstore", type: "paid subscription", …}
2 SKProduct {className: "Product", title: "6 months subscription", description: "premium subscription", platform: "ios-appstore", type: "paid subscription", …}

Array Prototype

transaction: {type: "ios-appstore", id: "2000000423517359", appStoreReceipt: "MIJE6AYJKoZIhvcNAQcCoIJE2TCCRNUCAQExCzAJBgUrDgMCGg…RUsfZfK43jklNKpyMJuaTS9w+n7kxHfKMuivV0BHhnXgOMA=="}

type: "application"

Object Prototype

@data_example_ios_first_buy

[Log] VALIDATOR function (vendor.js, line 41850)
{
   "id":"com.onlineconnections.seniornextDev",
   "type":"application",
   "products":[
      {
         "className":"Product",
         "title":"Monthly rebill",
         "description":"Premium subscription",
         "platform":"ios-appstore",
         "type":"paid subscription",
         "id":"m1_14_199_premium",
         "group":"20503357",
         "offers":[
            {
               "className":"Offer",
               "id":"$",
               "pricingPhases":[
                  {
                     "price":"$29.99",
                     "priceMicros":29990000,
                     "currency":"USD",
                     "billingPeriod":"P1M",
                     "paymentMode":"PayAsYouGo",
                     "recurrenceMode":"INFINITE_RECURRING"
                  }
               ],
               "productId":"m1_14_199_premium",
               "productType":"paid subscription",
               "productGroup":"20503357",
               "platform":"ios-appstore",
               "offerType":"Default"
            }
         ],
         "raw":{
            "id":"m1_14_199_premium",
            "description":"Premium subscription",
            "introPrice":null,
            "introPricePaymentMode":null,
            "billingPeriodUnit":"Month",
            "countryCode":"US",
            "introPricePeriodUnit":null,
            "discounts":[

            ],
            "title":"Monthly rebill",
            "price":"$29.99",
            "billingPeriod":1,
            "group":"20503357",
            "priceMicros":29990000,
            "currency":"USD",
            "introPricePeriod":null,
            "introPriceMicros":null,
            "platform":"ios-appstore",
            "type":"paid subscription"
         },
         "countryCode":"US"
      },
      {
         "className":"Product",
         "title":"3 month rebill",
         "description":"Premium subscription",
         "platform":"ios-appstore",
         "type":"paid subscription",
         "id":"m1_14_200_premium",
         "group":"20503357",
         "offers":[
            {
               "className":"Offer",
               "id":"$",
               "pricingPhases":[
                  {
                     "price":"$39.99",
                     "priceMicros":39990000,
                     "currency":"USD",
                     "billingPeriod":"P3M",
                     "paymentMode":"PayAsYouGo",
                     "recurrenceMode":"INFINITE_RECURRING"
                  }
               ],
               "productId":"m1_14_200_premium",
               "productType":"paid subscription",
               "productGroup":"20503357",
               "platform":"ios-appstore",
               "offerType":"Default"
            }
         ],
         "raw":{
            "id":"m1_14_200_premium",
            "description":"Premium subscription",
            "introPrice":null,
            "introPricePaymentMode":null,
            "billingPeriodUnit":"Month",
            "countryCode":"US",
            "introPricePeriodUnit":null,
            "discounts":[

            ],
            "title":"3 month rebill",
            "price":"$39.99",
            "billingPeriod":3,
            "group":"20503357",
            "priceMicros":39990000,
            "currency":"USD",
            "introPricePeriod":null,
            "introPriceMicros":null,
            "platform":"ios-appstore",
            "type":"paid subscription"
         },
         "countryCode":"US"
      },
      {
         "className":"Product",
         "title":"6 months subscription",
         "description":"premium subscription",
         "platform":"ios-appstore",
         "type":"paid subscription",
         "id":"m1_14_203_premium",
         "group":"20503357",
         "offers":[
            {
               "className":"Offer",
               "id":"$",
               "pricingPhases":[
                  {
                     "price":"$49.99",
                     "priceMicros":49990000,
                     "currency":"USD",
                     "billingPeriod":"P6M",
                     "paymentMode":"PayAsYouGo",
                     "recurrenceMode":"INFINITE_RECURRING"
                  }
               ],
               "productId":"m1_14_203_premium",
               "productType":"paid subscription",
               "productGroup":"20503357",
               "platform":"ios-appstore",
               "offerType":"Default"
            }
         ],
         "raw":{
            "id":"m1_14_203_premium",
            "description":"premium subscription",
            "introPrice":null,
            "introPricePaymentMode":null,
            "billingPeriodUnit":"Month",
            "countryCode":"US",
            "introPricePeriodUnit":null,
            "discounts":[

            ],
            "title":"6 months subscription",
            "price":"$49.99",
            "billingPeriod":6,
            "group":"20503357",
            "priceMicros":49990000,
            "currency":"USD",
            "introPricePeriod":null,
            "introPriceMicros":null,
            "platform":"ios-appstore",
            "type":"paid subscription"
         },
         "countryCode":"US"
      }
   ],
   "transaction":{
      "type":"ios-appstore",
      "id":"2000000427039701",
      "appStoreReceipt":"..."
   },
   "additionalData":{
      "applicationUsername":"bbtest2"
   },
   "device":{
      "plugin":"cordova-plugin-purchase/13.8.2"
   }
}

@receipt_example_ios_buy

{
    "id": "com.onlineconnections.seniornextDev",
    "type": "application",
    "products": [
        {
            "className": "Product",
            "title": "Monthly rebill",
            "description": "Premium subscription",
            "platform": "ios-appstore",
            "type": "paid subscription",
            "id": "m1_14_199_premium",
            "group": "20503357",
            "offers": [
                {
                    "className": "Offer",
                    "id": "$",
                    "pricingPhases": [
                        {
                            "price": "$29.99",
                            "priceMicros": 29990000,
                            "currency": "USD",
                            "billingPeriod": "P1M",
                            "paymentMode": "PayAsYouGo",
                            "recurrenceMode": "INFINITE_RECURRING"
                        }
                    ],
                    "productId": "m1_14_199_premium",
                    "productType": "paid subscription",
                    "productGroup": "20503357",
                    "platform": "ios-appstore",
                    "offerType": "Default"
                }
            ],
            "raw": {
                "id": "m1_14_199_premium",
                "description": "Premium subscription",
                "introPrice": null,
                "introPricePaymentMode": null,
                "billingPeriodUnit": "Month",
                "countryCode": "US",
                "introPricePeriodUnit": null,
                "discounts": [],
                "title": "Monthly rebill",
                "price": "$29.99",
                "billingPeriod": 1,
                "group": "20503357",
                "priceMicros": 29990000,
                "currency": "USD",
                "introPricePeriod": null,
                "introPriceMicros": null,
                "platform": "ios-appstore",
                "type": "paid subscription"
            },
            "countryCode": "US"
        },
        {
            "className": "Product",
            "title": "3 month rebill",
            "description": "Premium subscription",
            "platform": "ios-appstore",
            "type": "paid subscription",
            "id": "m1_14_200_premium",
            "group": "20503357",
            "offers": [
                {
                    "className": "Offer",
                    "id": "$",
                    "pricingPhases": [
                        {
                            "price": "$39.99",
                            "priceMicros": 39990000,
                            "currency": "USD",
                            "billingPeriod": "P3M",
                            "paymentMode": "PayAsYouGo",
                            "recurrenceMode": "INFINITE_RECURRING"
                        }
                    ],
                    "productId": "m1_14_200_premium",
                    "productType": "paid subscription",
                    "productGroup": "20503357",
                    "platform": "ios-appstore",
                    "offerType": "Default"
                }
            ],
            "raw": {
                "id": "m1_14_200_premium",
                "description": "Premium subscription",
                "introPrice": null,
                "introPricePaymentMode": null,
                "billingPeriodUnit": "Month",
                "countryCode": "US",
                "introPricePeriodUnit": null,
                "discounts": [],
                "title": "3 month rebill",
                "price": "$39.99",
                "billingPeriod": 3,
                "group": "20503357",
                "priceMicros": 39990000,
                "currency": "USD",
                "introPricePeriod": null,
                "introPriceMicros": null,
                "platform": "ios-appstore",
                "type": "paid subscription"
            },
            "countryCode": "US"
        },
        {
            "className": "Product",
            "title": "7 days free trial, monthly",
            "description": "premium subscription",
            "platform": "ios-appstore",
            "type": "paid subscription",
            "id": "m1_14_201_premium",
            "group": "20503357",
            "offers": [
                {
                    "className": "Offer",
                    "id": "$",
                    "pricingPhases": [
                        {
                            "price": "$0.00",
                            "priceMicros": 0,
                            "currency": "USD",
                            "billingPeriod": "P7D",
                            "paymentMode": "FreeTrial",
                            "recurrenceMode": "FINITE_RECURRING",
                            "billingCycles": 1
                        },
                        {
                            "price": "$29.99",
                            "priceMicros": 29990000,
                            "currency": "USD",
                            "billingPeriod": "P1M",
                            "paymentMode": "PayAsYouGo",
                            "recurrenceMode": "INFINITE_RECURRING"
                        }
                    ],
                    "productId": "m1_14_201_premium",
                    "productType": "paid subscription",
                    "productGroup": "20503357",
                    "platform": "ios-appstore",
                    "offerType": "Default"
                }
            ],
            "raw": {
                "id": "m1_14_201_premium",
                "description": "premium subscription",
                "introPrice": "$0.00",
                "introPricePaymentMode": "FreeTrial",
                "billingPeriodUnit": "Month",
                "countryCode": "US",
                "introPricePeriodUnit": "Day",
                "discounts": [],
                "title": "7 days free trial, monthly",
                "price": "$29.99",
                "billingPeriod": 1,
                "group": "20503357",
                "priceMicros": 29990000,
                "currency": "USD",
                "introPricePeriod": 7,
                "introPriceMicros": 0,
                "platform": "ios-appstore",
                "type": "paid subscription"
            },
            "countryCode": "US"
        },
        {
            "className": "Product",
            "title": "6 months subscription",
            "description": "premium subscription",
            "platform": "ios-appstore",
            "type": "paid subscription",
            "id": "m1_14_203_premium",
            "group": "20503357",
            "offers": [
                {
                    "className": "Offer",
                    "id": "$",
                    "pricingPhases": [
                        {
                            "price": "$49.99",
                            "priceMicros": 49990000,
                            "currency": "USD",
                            "billingPeriod": "P6M",
                            "paymentMode": "PayAsYouGo",
                            "recurrenceMode": "INFINITE_RECURRING"
                        }
                    ],
                    "productId": "m1_14_203_premium",
                    "productType": "paid subscription",
                    "productGroup": "20503357",
                    "platform": "ios-appstore",
                    "offerType": "Default"
                }
            ],
            "raw": {
                "id": "m1_14_203_premium",
                "description": "premium subscription",
                "introPrice": null,
                "introPricePaymentMode": null,
                "billingPeriodUnit": "Month",
                "countryCode": "US",
                "introPricePeriodUnit": null,
                "discounts": [],
                "title": "6 months subscription",
                "price": "$49.99",
                "billingPeriod": 6,
                "group": "20503357",
                "priceMicros": 49990000,
                "currency": "USD",
                "introPricePeriod": null,
                "introPriceMicros": null,
                "platform": "ios-appstore",
                "type": "paid subscription"
            },
            "countryCode": "US"
        }
    ],
    "transaction": {
        "type": "ios-appstore",
        "id": "2000000444700224",
        "appStoreReceipt": ".."
    },
    "additionalData": {
        "applicationUsername": "bboldi36"
    },
    "device": {
        "plugin": "cordova-plugin-purchase/13.8.6"
    }
}

@unverified_res_1

{
    "receipt": {
        "className": "Receipt",
        "transactions": [
            {
                "className": "Transaction",
                "transactionId": "appstore.application",
                "state": "finished",
                "products": [
                    {
                        "id": "com.onlineconnections.seniornextDev"
                    }
                ],
                "platform": "ios-appstore"
            },
            {
                "className": "Transaction",
                "transactionId": "2000000447254223",
                "state": "approved",
                "products": [
                    {
                        "id": "m1_14_201_premium",
                        "offerId": ""
                    }
                ],
                "platform": "ios-appstore",
                "originalTransactionId": "2000000445404275",
                "purchaseDate": "2023-10-27T10:40:16.000Z"
            }
        ],
        "platform": "ios-appstore",
        "nativeData": {
            "appStoreReceipt": "",
            "bundleIdentifier": "com.onlineconnections.seniornextDev",
            "bundleShortVersion": "2.2.8",
            "bundleNumericVersion": 16809984,
            "bundleSignature": null
        }
    },
    "payload": {
        "ok": false,
        "data": {
            "headers": {
                "normalizedNames": {},
                "lazyUpdate": null,
                "lazyInit": null,
                "headers": {}
            },
            "status": 400,
            "statusText": "OK",
            "url": "https://staging.hellosrv.com/api/v2/payment.ios",
            "ok": false,
            "name": "HttpErrorResponse",
            "message": "Http failure response for https://staging.hellosrv.com/api/v2/payment.ios: 400 OK",
            "error": {
                "data": false,
                "errors": [
                    {
                        "key": "Subscription expired based on api response.",
                        "error": "Subscription expired based on api response."
                    }
                ],
                "messages": [],
                "timestamp": 1698775262,
                "duration": 3215.8
            }
        }
    }
}

iaptic validator response


{
    "ok": true,
    "data": {
        "latest_receipt": true,
        "id": "com.onlineconnections.seniornextDev",
        "ineligible_for_intro_price": [
            "m1_14_201_premium"
        ],
        "transaction": {
            "type": "ios-appstore",
            "receipt_type": "ProductionSandbox",
            "adam_id": 0,
            "app_item_id": 0,
            "bundle_id": "com.onlineconnections.seniornextDev",
            "application_version": "1",
            "download_id": 0,
            "version_external_identifier": 0,
            "receipt_creation_date": "2023-11-01 15:00:19 Etc/GMT",
            "receipt_creation_date_ms": "1698850819000",
            "receipt_creation_date_pst": "2023-11-01 08:00:19 America/Los_Angeles",
            "request_date": "2023-11-01 15:01:09 Etc/GMT",
            "request_date_ms": "1698850869742",
            "request_date_pst": "2023-11-01 08:01:09 America/Los_Angeles",
            "original_purchase_date": "2013-08-01 07:00:00 Etc/GMT",
            "original_purchase_date_ms": "1375340400000",
            "original_purchase_date_pst": "2013-08-01 00:00:00 America/Los_Angeles",
            "original_application_version": "1.0",
            "in_app": "<<< use latest_receipt_info >>>"
        },
        "collection": [
            {
                "id": "m1_14_201_premium",
                "isBillingRetryPeriod": false,
                "renewalIntent": "Lapse",
                "purchaseDate": 1698401842000,
                "expiryDate": 1698781069000,
                "cancelationReason": "Customer",
                "lastRenewalDate": 1698780769000,
                "isExpired": true
            }
        ]
    }
}

iaptic error code response from validation server

{
    "ok": false,
    "route": "/validate",
    "status": 404,
    "code": "AppNotFound",
    "message": "Failed to fetch the app: missing.",
    "data": {
        "latest_receipt": true,
        "code": 7691001,
        "error": {
            "message": "Failed to fetch the app: missing."
        }
    },
    "source": {
        "isHttpError": true,
        "status": 404,
        "code": 7691001,
        "route": "/validate"
    }
}

 */
