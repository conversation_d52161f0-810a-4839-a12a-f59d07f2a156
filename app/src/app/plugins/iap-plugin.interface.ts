export type ProductCategory = "upgrade" | "offer" | "campaign" | string;

export type StringToArrayOfNumbers = {
  [key: string]: number[];
};

export type StringToNumber = {
  [key: string]: number;
};

export type StringToObject = {
  [key: string]: object;
};

export type PurchaseErrorResult = {
  code: number;
  message: string;
  doDisplay: boolean;
  errors?: any;
  originalError?: any;
};

export interface IapPluginInterface {
  /*
   * Define a function to initialize the class
   */
  initialize(): Promise<void>;

  /**
   * return all products formatted
   */
  getAllProducts(): Array<any>;

  /**
   * Retrieves an array of products that belong to a specified category.
   * @param category - the category of the products to retrieve.
   * @returns an array of products under the specified category, or an empty array if no product is available.
   */
  getProductsByCategory(category: ProductCategory): Array<any>;

  /**
   * Getter - Tells if the last purchase was done in a sandbox environment
   */
  wasSandboxPurchase: boolean;

  /**
   * Initialize purchase of a product
   * @param product_id id of the product that we want to purchase
   * @returns
   */
  purchase(product_id): Promise<any>;

  /**
   * Open subscription management page ( system page on application )
   */
  manageSubscription();
}
