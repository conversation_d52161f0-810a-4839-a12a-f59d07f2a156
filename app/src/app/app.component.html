<ion-app>
  <ion-split-pane contentId="main">
    <ion-menu menuId="main" contentId="main" type="overlay">
      <ion-header>
        <ion-toolbar
          color="primary"
          class="toolbar-header ion-align-items-start"
          >
          <ion-row class="ion-padding-vertical">
            <ion-col class="toolbar-slot slot-start">
              <ion-icon
                id="close-menu"
                (click)="closeMenu()"
                size="large"
                name="close-outline"
                class="ion-hide-lg-up"
              ></ion-icon>
            </ion-col>
            <ion-col size="auto" class="profile-card flex">
              <ion-avatar
                [routerLink]="'/app/tabs/my-profile'"
                (click)="closeMenu()"
                class="profile-card__avatar"
                >
                <ion-img
                  [src]="
                    accountService.account?.avatar_url ||
                    '/assets/img/avatar.svg'
                  "
                ></ion-img>
              </ion-avatar>
              <span class="profile-card__username"
                >{{
                accountService?.account?.first_name ||
                accountService?.account?.username
                }}, {{ accountService?.account?.age }}</span
                >
                <small class="profile-card__user-type">{{
                  !accountService?.account?.is_premium
                  ? ("free_user" | translate)
                  : ("premium_user" | translate)
                }}</small>
                <ion-button
                  [routerLink]="'/app/my-profile-edit'"
                  (click)="closeMenu()"
                  color="white"
                  class="profile-card__edit"
                  >
                  {{ "title_edit_profile" | translate }}
                </ion-button>
              </ion-col>
              <ion-col class="toolbar-slot slot-end debug-button">
                @if (showDebugButton) {
                  <ion-icon
                    [routerLink]="'/app/debug'"
                    (click)="closeMenu()"
                    name="bug-outline"
                    class="debug"
                  ></ion-icon>
                }
              </ion-col>
            </ion-row>
          </ion-toolbar>
        </ion-header>
        <ion-content>
          <ion-list>
            <h2 class="menu-title">{{ "menu_title_features" | translate }}</h2>

            <ion-item
              [routerLink]="'/app/inbox'"
              [routerLinkActive]="'menu-active-link'"
              (click)="closeMenu()"
              detail="true"
              >
              <ion-icon name="mail" slot="start"></ion-icon>
              <ion-label>{{ "title_inbox" | translate }}</ion-label>
              <ion-badge
                style="pointer-events: none"
                [class.hidden]="!this.unreadCountService.hasInbox"
                slot="end"
                >
                {{ this.unreadCountService.countInbox }}
              </ion-badge>
            </ion-item>
            <ion-item
              [routerLink]="'/app/tabs/browse'"
              [routerLinkActive]="'menu-active-link'"
              (click)="closeMenu()"
              detail="true"
              >
              <ion-icon name="grid" slot="start"></ion-icon>
              <ion-label>{{ "title_browse" | translate }}</ion-label>
            </ion-item>
            <ion-item
              [routerLink]="'/app/tabs/search'"
              [routerLinkActive]="'menu-active-link'"
              (click)="closeMenu()"
              detail="true"
              >
              <ion-icon src="assets/icons/ico-search.svg" slot="start"></ion-icon>
              <ion-label>{{ "tabs_search" | translate }}</ion-label>
            </ion-item>
            <ion-item
              [routerLink]="'/app/tabs/meet/fast-match'"
              [routerLinkActive]="'menu-active-link'"
              (click)="closeMenu()"
              detail="true"
              >
              <ion-icon src="assets/icons/ico-meet.svg" slot="start"></ion-icon>
              <ion-label>{{ "title-fast-match" | translate }}</ion-label>
            </ion-item>
            <ion-item
            [class.menu-active-link]="
              this.menuHelper.currentURL.includes('/app/my-lists')
            "
              (click)="openMyLists()"
              detail="true"
              >
              <ion-icon src="assets/icons/ico-view.svg" slot="start"></ion-icon>
              <ion-label>{{ "title-viewed-my-profile" | translate }}</ion-label>
              <ion-badge
                style="pointer-events: none"
                [class.hidden]="!profileService?.viewedMeBadge"
                slot="end"
                >
                {{ profileService?.viewedMeBadge }}
              </ion-badge>
            </ion-item>
            <!--

            -------------------------------------------------------------------------------
            Updates: Commenting out till a proper UI/UX user notification system is applied
            -------------------------------------------------------------------------------

            <ion-item
              [routerLink]="'/app/updates'"
              [routerLinkActive]="'menu-active-link'"
              (click)="closeMenu()"
              >
              <ion-icon name="notifications" slot="start"></ion-icon>
              <ion-label>{{ "title_updates" | translate }}</ion-label>
              <ion-badge
                style="pointer-events: none"
                [class.hidden]="!this.unreadCountService.hasUpdates"
                slot="end"
                >
                {{ this.unreadCountService.countUpdates }}
              </ion-badge>
            </ion-item>
            -->

            <h2 class="menu-title">{{ "menu_title_preferences" | translate }}</h2>

            <ion-item
              [routerLink]="'/app/match-options-menu'"
              [routerLinkActive]="'menu-active-link'"
              (click)="closeMenu()"
              detail="true"
              >
              <ion-icon name="options" slot="start"></ion-icon>
              <ion-label>{{ "match_options" | translate }}</ion-label>
            </ion-item>
            <ion-item
              [routerLink]="'/app/settings'"
              [routerLinkActive]="'menu-active-link'"
              (click)="closeMenu()"
              detail="true"
              >
              <ion-icon name="settings" slot="start"></ion-icon>
              <ion-label>{{ "title_settings" | translate }}</ion-label>
            </ion-item>
          </ion-list>
        </ion-content>
        <ion-footer>
          <ion-toolbar [class.hidden]="this.isPremium()" class="toolbar-footer">
            <ion-button expand="block" (click)="openUpgrade()">
              {{ "title_go_premium" | translate }}
            </ion-button>
          </ion-toolbar>
        </ion-footer>
      </ion-menu>

      <ion-router-outlet id="main"></ion-router-outlet>
    </ion-split-pane>
  </ion-app>
