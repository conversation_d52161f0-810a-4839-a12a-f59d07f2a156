import { NgModule } from "@angular/core";
import { PreloadAllModules, RouterModule, Routes } from "@angular/router";
import { SignupWizardGuardService } from "./services/signup-wizard.guard.service";
import { environment as Env } from "../environments/environment";
import { UpgradeRedirectService } from "./services/upgrade-redirect.service";

export interface RouteSettings {
  isMenuVisible?: boolean; // hidden by default
  doEnableBackNavigation?: boolean; // enabled by default ( back button for android, back swipe for ios )
}

const routes: Routes = [
  {
    path: "app/debug",
    loadChildren: () =>
      import("./pages/debug/debug.module").then((m) => m.DebugPageModule),
    data: <RouteSettings>{ isMenuVisible: true },
  },
  {
    path: "debug",
    loadChildren: () =>
      import("./pages/debug/debug.module").then((m) => m.DebugPageModule),
    data: <RouteSettings>{ isMenuVisible: false },
  },
  {
    path: "error",
    loadChildren: () =>
      import("./pages/error/error.module").then((m) => m.ErrorPageModule),
    data: <RouteSettings>{
      doEnableBackNavigation: false,
    },
  },
  {
    path: "app",
    children: [
      {
        path: "",
        redirectTo: Env.HOME_PATH,
        pathMatch: "full",
        data: <RouteSettings>{
          isMenuVisible: true,
          doEnableBackNavigation: false,
        },
      },
      {
        path: "tabs",
        loadChildren: () =>
          import("./pages/tabs/tabs.module").then((m) => m.TabsPageModule),
        data: <RouteSettings>{
          isMenuVisible: true,
          doEnableBackNavigation: false,
        },
      },
      {
        path: "matches",
        loadChildren: () =>
          import("./pages/matches/matches.module").then(
            (m) => m.MatchesPageModule,
          ),
      },
      {
        path: "error",
        loadChildren: () =>
          import("./pages/error/error.module").then((m) => m.ErrorPageModule),
        data: <RouteSettings>{
          doEnableBackNavigation: false,
        },
      },
      {
        path: "my-profile",
        loadChildren: () =>
          import("./pages/my-profile/my-profile.module").then(
            (m) => m.MyProfilePageModule,
          ),
      },
      {
        path: "my-profile-edit",
        loadChildren: () =>
          import("./pages/my-profile-edit/my-profile-edit.module").then(
            (m) => m.MyProfileEditPageModule,
          ),
        data: <RouteSettings>{
          doEnableBackNavigation: false,
        },
      },
      {
        path: "settings",
        loadChildren: () =>
          import("./pages/settings/settings.module").then(
            (m) => m.SettingsPageModule,
          ),
        data: <RouteSettings>{
          doEnableBackNavigation: false,
        },
      },
      {
        path: "faq",
        loadChildren: () =>
          import("./pages/faq/faq.module").then((m) => m.FaqPageModule),
      },
      {
        path: "inbox",
        loadChildren: () =>
          import("./pages/inbox/inbox.module").then((m) => m.InboxPageModule),
      },
      {
        path: "location-city",
        loadChildren: () =>
          import("./pages/location-city/location-city.module").then(
            (m) => m.LocationCityPageModule,
          ),
      },
      {
        path: "location-country",
        loadChildren: () =>
          import("./pages/location-country/location-country.module").then(
            (m) => m.LocationCountryPageModule,
          ),
      },
      {
        path: "location-zip",
        loadChildren: () =>
          import("./pages/location-zip/location-zip.module").then(
            (m) => m.LocationZipPageModule,
          ),
      },
      {
        path: "photo-editor",
        loadChildren: () =>
          import("./pages/photo-editor/photo-editor.module").then(
            (m) => m.PhotoEditorPageModule,
          ),
        data: <RouteSettings>{
          doEnableBackNavigation: false,
        },
      },
      {
        path: "photo-full-view/:type/:username/:index",
        loadChildren: () =>
          import("./pages/photo-full-view/photo-full-view.module").then(
            (m) => m.PhotoFullViewPageModule,
          ),
      },
      {
        path: "photo-gallery/:username",
        loadChildren: () =>
          import("./pages/photo-gallery/photo-gallery.module").then(
            (m) => m.PhotoGalleryPageModule,
          ),
      },
      {
        path: "photo-comments/:username/:galleryId/:imageId",
        loadChildren: () =>
          import("./pages/photo-comments/photo-comments.module").then(
            (m) => m.PhotoCommentsPageModule,
          ),
      },
      {
        path: "pictures",
        loadChildren: () =>
          import("./pages/pictures/pictures.module").then(
            (m) => m.PicturesPageModule,
          ),
      },
      {
        path: "chat-messages/:username",
        canActivate: [UpgradeRedirectService],
        loadChildren: () =>
          import("./pages/chat-messages/chat-messages.module").then(
            (m) => m.ChatMessagesPageModule,
          ),
      },
      {
        path: "offers",
        loadChildren: () =>
          import("./pages/offers/offers.module").then(
            (m) => m.OffersPageModule,
          ),
      },
      {
        path: "my-lists",
        loadChildren: () =>
          import("./pages/my-lists/my-lists.module").then(
            (m) => m.MyListsPageModule,
          ),
      },
      {
        path: "profile/:username",
        loadChildren: () =>
          import("./pages/profile/profile.module").then(
            (m) => m.ProfilePageModule,
          ),
      },
      {
        path: "profile-tabs",
        loadChildren: () =>
          import("./pages/profile-tabs/profile-tabs.module").then(
            (m) => m.ProfileTabsPageModule,
          ),
      },
      {
        path: "profile-tab",
        loadChildren: () =>
          import("./pages/profile-tab/profile-tab.module").then(
            (m) => m.ProfileTabPageModule,
          ),
      },
      {
        path: "deactivation",
        loadChildren: () =>
          import("./pages/deactivation/deactivation.module").then(
            (m) => m.DeactivationPageModule,
          ),
      },
      {
        path: "delete-account",
        loadChildren: () =>
          import("./pages/delete-account/delete-account.module").then(
            (m) => m.DeleteAccountPageModule,
          ),
      },
      {
        path: "modals/flirt/:username",
        loadChildren: () =>
          import("./pages/modals/flirt/flirt.module").then(
            (m) => m.FlirtPageModule,
          ),
      },
      {
        path: "modals/free-countdown",
        loadChildren: () =>
          import("./pages/modals/free-countdown/free-countdown.module").then(
            (m) => m.FreeCountdownPageModule,
          ),
      },
      {
        path: "modals/offer",
        loadChildren: () =>
          import("./pages/modals/offer/offer.module").then(
            (m) => m.OfferPageModule,
          ),
      },
      {
        path: "modals/undo-offer",
        loadChildren: () =>
          import("./pages/modals/undo-offer/undo-offer.module").then(
            (m) => m.UndoOfferPageModule,
          ),
      },
      {
        path: "modals/countdown",
        loadChildren: () =>
          import("./pages/modals/countdown/countdown.module").then(
            (m) => m.CountdownPageModule,
          ),
      },
      {
        path: "modals/thanks",
        loadChildren: () =>
          import("./pages/modals/thanks/thanks.module").then(
            (m) => m.ThanksPageModule,
          ),
      },
      {
        path: "modals/go-premium",
        loadChildren: () =>
          import("./pages/modals/go-premium/go-premium.module").then(
            (m) => m.GoPremiumPageModule,
          ),
      },
      {
        path: "modals/go-premium-discounted-24h",
        loadChildren: () =>
          import(
            "./pages/modals/go-premium-discounted-24h/go-premium-discounted-24h.module"
          ).then((m) => m.GoPremiumDiscounted24hPageModule),
      },
      {
        path: "modals/go-premium-promo",
        loadChildren: () =>
          import(
            "./pages/modals/go-premium-promo/go-premium-promo.module"
          ).then((m) => m.GoPremiumPromoPageModule),
      },
      {
        path: "modals/go-premium-discounted",
        loadChildren: () =>
          import(
            "./pages/modals/go-premium-discounted/go-premium-discounted.module"
          ).then((m) => m.GoPremiumDiscountedPageModule),
      },
      {
        path: "modals/match/:username",
        loadChildren: () =>
          import("./pages/modals/match/match.module").then(
            (m) => m.MatchPageModule,
          ),
      },
      {
        path: "modals/try-premium",
        loadChildren: () =>
          import("./pages/modals/try-premium/try-premium.module").then(
            (m) => m.TryPremiumPageModule,
          ),
      },
      {
        path: "modals/go-premium-downloaded",
        loadChildren: () =>
          import(
            "./pages/modals/go-premium-downloaded/go-premium-downloaded.module"
          ).then((m) => m.GoPremiumDownloadedPageModule),
      },
      {
        path: "app-tour",
        loadChildren: () =>
          import("./pages/app-tour/app-tour.module").then(
            (m) => m.AppTourPageModule,
          ),
      },
      {
        path: "updates",
        loadChildren: () =>
          import("./pages/updates/updates.module").then(
            (m) => m.UpdatesPageModule,
          ),
      },
      /*
      {
        path: "welcome",
        loadChildren: () =>
          import("./pages/welcome/welcome.module").then(
            (m) => m.WelcomePageModule
          ),
      },
      */
      {
        path: "customer-support-menu",
        loadChildren: () =>
          import(
            "./pages/customer-support-menu/customer-support-menu.module"
          ).then((m) => m.CustomerSupportMenuPageModule),
      },
      {
        path: "manage-menu",
        loadChildren: () =>
          import("./pages/manage-menu/manage-menu.module").then(
            (m) => m.ManageMenuPageModule,
          ),
      },
      {
        path: "profile-verification",
        loadChildren: () =>
          import(
            "./pages/profile-verification/profile-verification.module"
          ).then((m) => m.ProfileVerificationPageModule),
      },
      {
        path: "match-options-menu",
        loadChildren: () =>
          import("./pages/match-options-menu/match-options-menu.module").then(
            (m) => m.MatchOptionsMenuPageModule,
          ),
        data: <RouteSettings>{
          doEnableBackNavigation: false,
        },
      },
      {
        path: "settings",
        loadChildren: () =>
          import("./pages/settings/settings.module").then(
            (m) => m.SettingsPageModule,
          ),
      },
      {
        path: "wpg-data/:id/:name",
        loadChildren: () =>
          import("./pages/wpg-data/wpg-data.module").then(
            (m) => m.WpgDataPageModule,
          ),
        data: <RouteSettings>{
          isMenuVisible: false,
          doEnableBackNavigation: false,
        },
      },
      {
        path: "wpg-info",
        loadChildren: () =>
          import("./pages/wpg-info/wpg-info.module").then(
            (m) => m.WpgInfoPageModule,
          ),
        data: <RouteSettings>{
          isMenuVisible: false,
          doEnableBackNavigation: false,
        },
      },
      {
        path: "wpg-photo",
        loadChildren: () =>
          import("./pages/wpg-photo/wpg-photo.module").then(
            (m) => m.WpgPhotoPageModule,
          ),
        data: <RouteSettings>{
          isMenuVisible: false,
          doEnableBackNavigation: false,
        },
      },
      {
        path: "search-results",
        loadChildren: () =>
          import("./pages/search-results/search-results.module").then(
            (m) => m.SearchResultsPageModule,
          ),
      },
      {
        path: "billing-history",
        loadChildren: () =>
          import("./pages/billing-history/billing-history.module").then(
            (m) => m.BillingHistoryPageModule,
          ),
      },
    ],
  },
  {
    path: Env.APPLE_SIGNUP_URL_PREFIX,
    redirectTo: Env.APPLE_SIGNUP_URL_PREFIX + "/gender",
    pathMatch: "full",
  },
  {
    path: Env.APPLE_SIGNUP_URL_PREFIX,
    canActivateChild: [SignupWizardGuardService],
    children: [
      {
        path: "age",
        loadChildren: () =>
          import("./pages/signup/age/age.module").then((m) => m.AgePageModule),
      },
      {
        path: "consent",
        loadChildren: () =>
          import("./pages/signup/consent/consent.module").then(
            (m) => m.ConsentPageModule,
          ),
      },
      {
        path: "email",
        loadChildren: () =>
          import("./pages/signup/email/email.module").then(
            (m) => m.EmailPageModule,
          ),
      },
      {
        path: "gender",
        loadChildren: () =>
          import("./pages/signup/gender/gender.module").then(
            (m) => m.GenderPageModule,
          ),
      },
      {
        path: "username",
        loadChildren: () =>
          import("./pages/signup/username/username.module").then(
            (m) => m.UsernamePageModule,
          ),
      },
    ],
  },
  {
    path: Env.SIGNUP_URL_PREFIX,
    redirectTo: Env.SIGNUP_URL_PREFIX + "/gender",
    pathMatch: "full",
  },
  {
    path: Env.SIGNUP_URL_PREFIX,
    canActivateChild: [SignupWizardGuardService],
    children: [
      {
        path: "age",
        loadChildren: () =>
          import("./pages/signup/age/age.module").then((m) => m.AgePageModule),
      },
      {
        path: "consent",
        loadChildren: () =>
          import("./pages/signup/consent/consent.module").then(
            (m) => m.ConsentPageModule,
          ),
      },
      {
        path: "email",
        loadChildren: () =>
          import("./pages/signup/email/email.module").then(
            (m) => m.EmailPageModule,
          ),
      },
      {
        path: "gender",
        loadChildren: () =>
          import("./pages/signup/gender/gender.module").then(
            (m) => m.GenderPageModule,
          ),
      },
      {
        path: "username",
        loadChildren: () =>
          import("./pages/signup/username/username.module").then(
            (m) => m.UsernamePageModule,
          ),
      },
    ],
  },
  {
    path: "login",
    loadChildren: () =>
      import("./pages/login/login.module").then((m) => m.LoginPageModule),
  },
  {
    path: "login-direct",
    loadChildren: () =>
      import("./pages/login-direct/login-direct.module").then(
        (m) => m.LoginDirectPageModule,
      ),
  },
  {
    path: "forgot-password",
    loadChildren: () =>
      import("./pages/forgot-password/forgot-password.module").then(
        (m) => m.ForgotPasswordPageModule,
      ),
  },
  {
    path: "welcome",
    loadChildren: () =>
      import("./pages/welcome/welcome.module").then((m) => m.WelcomePageModule),
  },
  {
    path: "page/:pageName",
    loadChildren: () =>
      import("./pages/static-content/static-content.module").then(
        (m) => m.StaticContentPageModule,
      ),
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      preloadingStrategy: PreloadAllModules,
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
