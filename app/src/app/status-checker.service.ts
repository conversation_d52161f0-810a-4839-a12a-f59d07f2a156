import { Injectable } from "@angular/core";
import { Platform } from "@ionic/angular";
import _ from "lodash";
import { firstValueFrom } from "rxjs";
import { Debug } from "./helpers/debug";
import { AccountService } from "./services/account.service";
import { DialogHelperService } from "./services/dialog-helper.service";
import { MiscService } from "./services/misc.service";
import { UnreadCountService } from "./services/unread-count.service";
import { Router } from "@angular/router";
import { StateService } from "./services/state.service";
import { RefresherService } from "./services/refresher.service";

/**
 * Service to check if user have been updated while app was in background.
 */

@Injectable({
  providedIn: "root",
})
export class StatusCheckerService {
  constructor(
    private platform: Platform,
    private account: AccountService,
    private dialog: DialogHelperService,
    private misc: MiscService,
    private dbg: Debug,
    private unreadCountService: UnreadCountService,
    private router: Router,
    private stateService: StateService,
    private refresherService: RefresherService,
  ) {}

  private _statusList: {
    execute: Function;
    // how often will it be executed on app reasotre
    // always - on every restore
    // high - every minute
    // medium - every 5 minutes
    // low - every 15 minutes

    frequency: "always" | "high" | "medium" | "low";
  }[] = [];

  private activationStorage = {
    high: 0,
    medium: 0,
    low: 0,
    duration: {
      high: 60, //1*60,
      medium: 300, // 5*60,
      low: 900, // 15*60
    },
  };

  public init() {
    this._statusList.push({
      execute: this.checkUserStatus.bind(this),
      frequency: "always",
    });

    this._statusList.push({
      execute: this.fetchUnreadCount.bind(this),
      frequency: "high",
    });

    this.platform.resume.subscribe(() => {
      this.checkAndExecute();
    });
  }

  // calculate wether the time to execute have arrived
  private hasTimeCome(frequency: "always" | "high" | "medium" | "low") {
    // always execute
    if (frequency == "always") {
      return true;
    }

    let now = Math.round(_.now() / 1000);

    // calculate if it needs to be executed

    if (
      this.activationStorage[frequency] <
      now - this.activationStorage.duration[frequency]
    ) {
      this.activationStorage[frequency] = now;
      return true;
    }

    return false;
  }

  // execute functions
  public checkAndExecute() {
    this._statusList.forEach((i) => {
      if (this.hasTimeCome(i.frequency)) {
        this.dbg.l("status checker execute", i.execute.name);
        i.execute();
      }
    });
  }

  // fetch / refresh unread count
  private fetchUnreadCount() {
    this.unreadCountService.get();
  }

  // check if user become premium
  private checkUserStatus() {
    if (this.stateService.get("blockStatusChecker", "")) return;

    // store previous state
    let _previousStatus = this.account.account.is_premium;

    if (!_previousStatus) {
      // if the user was free, check if he is premium now
      firstValueFrom(this.account.get())
        .then((data) => {
          this.dbg.l("[StatusCheckerService] checkUserStatus", data);

          // if user becaome premium ( for example upgraded from email, or from website, or some other promotion )
          if (data.data.is_premium) {
            this.dbg.l("[StatusCheckerService] checkUserStatus CHANGED");

            // restart the app with a message
            this.dialog
              .showAlert(
                "thanks",
                "upgrade_thanks_background",
                "ok",
                () => this.refresherService.refresh(),
                "️",
                "☺️",
              )
              .catch((err) => {
                this.dbg.le("app restore upgrade error", err);
              });
          }
        })
        .catch((e) => this.dbg.le("failed to get account data", e));
    }
  }
}
