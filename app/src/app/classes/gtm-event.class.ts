import _ from "lodash";

// event definitions

// Signup steps

export type TME_sign_up_interaction = {
  step_type: string; // 'INSERT_STEP_TITLE'
  step_number: any; // 'INSERT_STEP_NUMBER'
  total_steps: any; // 'INSERT_TOTAL_SIGNUP_STEPS'
};

// Successful signup

export type TME_sign_up = {
  method: any; // 'INSERT_SIGNUP_METHOD',
  user_id: any; // 'INSERT_HASHED_USER_ID'
};

// Co-registration

export type TME_co_registration = {
  sponsor_website: any; // 'INSERT_SPONSOR_WEBSITE'
};

// Cross-registration

export type TME_cross_registration = {
  sponsor_website: any; // 'INSERT_SPONSOR_WEBSITE'
};

// Welcome form steps

export type TME_welcome_form_interaction = {
  step_type: any; // 'INSERT_STEP_TITLE',
  step_number: any; // 'INSERT_STEP_NUMBER',
  total_steps: any; // 'INSERT_TOTAL_WELCOM_FLOW_STEPS'
};

// Complete welcome flow

export type TME_welcome_form_completed = {};

// Welcome offer CTA

export type TME_welcome_offer_click = {
  offer_type: any; // 'INSERT_OFFER_TYPE'
};

// Navigate to upgrade page

export type TME_initiate_upgrade = {
  initiate_upgrade_category: any; // 'INSERT_UPGRADE_CATEGORY',
  initiate_upgrade_title: any; // 'INSERT_UPGRADE_TITLE'
};

// Abandon upgrade form

export type TME_abandon_upgrade = {};

// Submit upgrade form

export type TME_submit_upgrade_form = {};

// Error during signup process

export type TME_signup_error = {
  message: any; // 'INSERT_ERROR_MESSAGE'
};

// Error during login process

export type TME_login_error = {
  message: any; // 'INSERT_ERROR_MESSAGE'
};

// Error during welcome flow

export type TME_welcome_error = {
  message: any; // 'INSERT_ERROR_MESSAGE'
};

// Error during upgrade process

export type TME_upgrade_error = {
  message: any; // 'INSERT_ERROR_MESSAGE'
};

// Spam control issues

export type TME_malicious_issue = {
  message: any; // 'INSERT_ERROR_MESSAGE'
};

// Login

export type TME_login = {
  method: any; // 'INSERT_LOGIN_METHOD',
  user_id: any; // 'INSERT_HASHED_USER_ID'
};

// Login first initial

export type TME_login_initial_app = {
  method: any; // 'INSERT_LOGIN_METHOD',
  user_id: any; // 'INSERT_HASHED_USER_ID'
};

// Logout

export type TME_logout = {};

// Reset password

export type TME_reset_password = {};

// Display of rules popup

export type TME_rules_popup_display = {
  popup_type: any; // 'INSERT_TYPE_OF_POPUP'
};

// Interaction with rules popup

export type TME_rules_popup_interaction = {
  popup_type: any; // 'INSERT_TYPE_OF_POPUP',
  interaction_type: any; // 'INSERT_TYPE_OF_INTERACTION'
};

// Complete rules popup

export type TME_rules_popup_complete = {
  popup_type: any; // 'INSERT_TYPE_OF_POPUP'
};

// Display of happy hour popup

export type TME_happy_hour_displa = {};

// Upload photo

export type TME_photo_upload = {
  upload_type: any; // 'INSERT_UPLOAD_INSTANCE'
};

// Upload video

export type TME_video_upload = {
  upload_type: any; // 'INSERT_UPLOAD_INSTANCE'
};

// Use of search filter

export type TME_search_filter = {
  filter_looking_for: any; // 'INSERT_LOOKING_FOR_SELECTION',
  filter_age: any; // 'INSERT_AGE_RANGE',
  filter_distance: any; // 'INSERT_SELECTED_DISTANCE',
  filter_photos: any; // 'INSERT_PHOTO_SELECTION'
};

// Clicks on ads

export type TME_ad_click = {
  page_name: any; // 'INSERT_PAGE_NAME',
  location_on_page: any; // 'INSERT_AD_POSITION'
};

// Count number of likes

export type TME_like_count = {
  count: any; // 'INSERT_NUMBER_OF_PROFILE_LIKES_OF_SESSION'
};

// Start using rapid match

export type TME_swipe = {
  swipe_type: any; // 'Web Rapid Match'
};

// Count number of rapid match interactions

export type TME_swipe_count = {
  count: any; // 'INSERT_NUMBER_OF_CLICKS_ON_YES_OR_PASS_RAPID_MATCH_OF_LAST_SESSION'
};

// Mutual meet

export type TME_mutual_meet = {};

// Add a friend

export type TME_add_frien = {};

// Count private chat messages

export type TME_message_count = {
  count: any; // 'INSERT_NUMBER_OF_MESSAGE_SENT_DURING_LAST_SESSION'
};

// Count number of flirts

export type TME_flirt_count = {
  count: any; // 'INSERT_NUMBER_OF_FLIRTS_OF_LAST_SESSION'
};

// Count number of viewed profiles

export type TME_view_profile_count = {
  count: any; // 'INSERT_NUMBER_OF_VIEWED_PROFILES_OF_LAST_SESSION'
};

// Open chat

export type TME_open_chat = {
  page_name: any; // 'INSERT_PAGE_NAME'
};

// Count number of requested photos

export type TME_request_photo_count = {
  count: any; // 'INSERT_NUMBER_OF_REQUESTED_PHOTOS_OF_LAST_SESSION'
};

//Create chat room

export type TME_create_room = {
  room_type: any; // 'INSERT_ROOM_TYPE'
};

// Delete chat room

export type TME_delete_roo = {};

// Enter chat room

export type TME_enter_room = {
  room_name: any; // 'INSERT_ROOM_NAME',
  page_name: any; // 'INSERT_PAGE_NAME'
};

// Browse chat room

export type TME_browse_room = {
  room_type: any; // 'INSERT_ROOM_TYPE',
  page_name: any; // 'INSERT_PAGE_NAME'
};

// Count number of help clicks in the chat

export type TME_help_click_count = {
  count: any; // 'INSERT_NUMBER_OF_HELP_CLICKS_OF_SESSION'
};

// View a cam in a chat

export type TME_view_ca = {};

// User starts the cam in a chat

export type TME_start_cam = {
  chat_type: any; // 'INSERT_CHAT_TYPE'
};

// User hides webcam in a chat

export type TME_hide_ca = {};

// User shows webcam in a chat

export type TME_show_ca = {};

// Count number of requested private chats

export type TME_request_private_chat_count = {
  count: any; // 'INSERT_NUMBER_OF_REQUESTED_PRIVATE_CHATS_OF_LAST_SESSION'
};

// Count number of requested encrypted chats

export type TME_request_encrypted_chat_count = {
  count: any; // 'INSERT_NUMBER_OF_REQUESTED_ENCRYPTED_CHATS_OF_LAST_SESSION'
};

// Count number of accepted encrypted chats

export type TME_accept_encrypted_chat_count = {
  count: any; // 'INSERT_NUMBER_OF_ACCEPTED_ENCRYPTED_CHATS_OF_LAST_SESSION'
};

// Count number of declined encrypted chats

export type TME_decline_encrypted_chat_count = {
  count: any; // 'INSERT_NUMBER_OF_DECLINED_ENCRYPTED_CHATS_OF_LAST_SESSION'
};

// Session restore failed for app

export type TME_session_restore_failed = {};

// Push notificaiton event for mobile

export type TME_push_notification_open = {
  message_title: any; // 'INSERT_NOTIFICATION_MESSAGE'
};

// APP RELATED

// App and User information type

export type TM_app_and_user_info = {
  database_id: any; // 'INSERT_DATABASE_ID';
  app_id: any; // 'App id';
  app_version: any; // Application version
  os: any; // app os
  user_id: any; // 'INSERT_HASHED_USER_ID',
  membership: any; // 'INSERT_MEMBERSHIP_TYPE',
  gender: any; // 'INSERT_USER_GENDER'
  device_id: any; // device uuid hash
  email_domain: any; // email domain
  website_niche: any; // email domain
  //device_info: any; // device info object
};

// purchase_info event

export type TME_purchase = {
  transaction_id: any;
  affiliation: any; // @todo add affiliate link or campaign link here
  currency: any;
  value: any; // Total Revenue
  tax?: any;
  shipping?: any;
  coupon: any; // @todo add afiiliate link or campaign link?
  initiate_upgrade_category?: any; // campaign_offer | upgrade_page
  initiate_upgrade_title?: any; // wanted feature if it's upgrade_page, campaign name if it's a campaign
};

// error message event

export type TME_error_message = {
  message: any;
  type: any;
};

// test

export type TME_test_event = {
  message: any;
};

// password saved event
// - when user saves password with manager

export type TME_password_saved = {
  action: any;
};

// class implementation

export class GTMEvent<TGTMEventType> {
  private eventName: string;
  private params: TGTMEventType;

  public constructor(name: string, params: TGTMEventType) {
    this.eventName = name;
    this.params = params;
  }

  /**
   * Get GTM event conpatible object
   * @returns object
   */
  public get() {
    return _.merge({ event: this.eventName }, this.params);
  }
}
