/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@use "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@use "@ionic/angular/css/normalize.css";
@use "@ionic/angular/css/structure.css";
@use "@ionic/angular/css/typography.css";
@use '@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@use "@ionic/angular/css/padding.css";
@use "@ionic/angular/css/float-elements.css";
@use "@ionic/angular/css/text-alignment.css";
@use "@ionic/angular/css/text-transformation.css";
@use "@ionic/angular/css/flex-utils.css";

@use '../node_modules/swiper/swiper-bundle.min.css';
@use '../node_modules/@ionic/angular/css/ionic-swiper.css';

/* import custom stuff */
@use "./custom.scss";

/* GlobaL Css */

/* Browse: Distance popover: Fixed width override */

.distance-popover {
  --width: auto;
  --min-width: 200px;
}

.hidden {
  display: none !important;
}

hot-toast-container > div {
  margin-top: constant(safe-area-inset-top) !important;
  margin-top: env(safe-area-inset-top) !important;
}

[hidden] {
  display: none !important;
}

.swiper-pagination {
  bottom: 20px !important;
  pointer-events: none;
}

swiper-container {
  --swiper-pagination-bullet-inactive-color: var(--ion-color-step-200, #cccccc);
  --swiper-pagination-color: var(--ion-color-primary, #3880ff);
  --swiper-pagination-progressbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.25);
  --swiper-scrollbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.1);
  --swiper-scrollbar-drag-bg-color: rgba(var(--ion-text-color-rgb, 255, 255, 255), 1);
}

swiper-slide {
  display: flex;
  position: relative;

  flex-direction: column;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;

  width: 100%;
  height: 100%;

  font-size: 18px;

  text-align: center;
  box-sizing: border-box;
}

swiper-slide img {
  width: auto;
  max-width: 100%;
  height: auto;
  max-height: 100%;
}

app-profile,
app-my-profile {
  .swiper-scrollbar-horizontal {
      display: flex;
      padding: 0 16px;
      top: 12px;
  }
}

/* !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!   */

/*  IMPORTANT: Please use custom.scss , don't modify this file
    for per application CSS mods, ONLY global stuff             */

/* !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!   */
