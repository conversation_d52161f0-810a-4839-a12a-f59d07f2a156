# Specifies intentionally untracked files to ignore when using Git
# http://git-scm.com/docs/gitignore

*~
*.sw[mnpcod]
.tmp
*.tmp
*.tmp.*
*.sublime-project
*.sublime-workspace
.DS_Store
Thumbs.db
UserInterfaceState.xcuserstate
$RECYCLE.BIN/

*.log
log.txt
npm-debug.log*

/.idea
/.ionic
/.sass-cache
/.sourcemaps
/.versions
/coverage
/dist
/node_modules
/platforms
/plugins
/www
.nx

../.idea

android/.idea

# mpm .gitignore entries ...

/src/custom.scss
/src/theme/variables.scss
/src/assets/icon
/src/assets/icons
/src/assets/img
/src/assets/json
/android/app/src/main/res/values/strings.xml
/android/app/src/main/AndroidManifest.xml
/android/app/build.gradle
/ios/App/App/Info.plist
/android/app/google-services.json
/ios/App/App/GoogleService-Info.plist
/ios/App/App.xcodeproj/project.pbxproj
/android/app/src/main/res
/android/app/src/main/ic_launcher-playstore.png
/ios/App/App/Assets.xcassets/AppIcon.appiconset
/ios/App/Podfile.lock

# ... end of mpm .gitignore entries

# custom files

/android/app/src/main/java/com
/src/environments/config.json
/src/environments/common.config.json
/android/app/src/main/assets/capacitor.config.json
/ios/App/App/capacitor.config.json
/ios/App/App/App.entitlements
android/app/src/main/AndroidManifest.xml.orig
.angular
