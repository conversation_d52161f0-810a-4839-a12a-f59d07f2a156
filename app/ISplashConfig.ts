export interface ISplashConfig {
  /**
   * How long to show the launch splash screen when autoHide is enabled (in ms)
   *
   * @since 1.0.0
   * @default 0
   * @example 3000
   */
  launchShowDuration?: number;
  /**
   * Whether to auto hide the splash after launchShowDuration.
   *
   * @since 1.0.0
   * @default true
   * @example true
   */
  launchAutoHide?: boolean;
  /**
   * Color of the background of the Splash Screen in hex format, #RRGGBB or #RRGGBBAA.
   * Doesn't work if `useDialog` is true.
   *
   * @since 1.0.0
   * @example "#ffffffff"
   */
  backgroundColor?: string;
  /**
   * Name of the resource to be used as Splash Screen.
   *
   * Only available on Android.
   *
   * @since 1.0.0
   * @default splash
   * @example "splash"
   */
  androidSplashResourceName?: string;
  /**
   * The [ImageView.ScaleType](https://developer.android.com/reference/android/widget/ImageView.ScaleType) used to scale
   * the Splash Screen image.
   * Doesn't work if `useDialog` is true.
   *
   * Only available on Android.
   *
   * @since 1.0.0
   * @default FIT_XY
   * @example "CENTER_CROP"
   */
  androidScaleType?:
    | "CENTER"
    | "CENTER_CROP"
    | "CENTER_INSIDE"
    | "FIT_CENTER"
    | "FIT_END"
    | "FIT_START"
    | "FIT_XY"
    | "MATRIX";
  /**
   * Show a loading spinner on the Splash Screen.
   * Doesn't work if `useDialog` is true.
   *
   * @since 1.0.0
   * @example true
   */
  showSpinner?: boolean;
  /**
   * Style of the Android spinner.
   * Doesn't work if `useDialog` is true.
   *
   * @since 1.0.0
   * @default large
   * @example "large"
   */
  androidSpinnerStyle?:
    | "horizontal"
    | "small"
    | "large"
    | "inverse"
    | "smallInverse"
    | "largeInverse";
  /**
   * Style of the iOS spinner.
   * Doesn't work if `useDialog` is true.
   *
   * Only available on iOS.
   *
   * @since 1.0.0
   * @default large
   * @example "small"
   */
  iosSpinnerStyle?: "large" | "small";
  /**
   * Color of the spinner in hex format, #RRGGBB or #RRGGBBAA.
   * Doesn't work if `useDialog` is true.
   *
   * @since 1.0.0
   * @example "#999999"
   */
  spinnerColor?: string;
  /**
   * Hide the status bar on the Splash Screen.
   *
   * Only available on Android.
   *
   * @since 1.0.0
   * @example true
   */
  splashFullScreen?: boolean;
  /**
   * Hide the status bar and the software navigation buttons on the Splash Screen.
   *
   * Only available on Android.
   *
   * @since 1.0.0
   * @example true
   */
  splashImmersive?: boolean;
  /**
   * If `useDialog` is set to true, configure the Dialog layout.
   * If `useDialog` is not set or false, use a layout instead of the ImageView.
   *
   * Only available on Android.
   *
   * @since 1.1.0
   * @example "launch_screen"
   */
  layoutName?: string;
  /**
   * Use a Dialog instead of an ImageView.
   * If `layoutName` is not configured, it will use
   * a layout that uses the splash image as background.
   *
   * Only available on Android.
   *
   * @since 1.1.0
   * @example true
   */
  useDialog?: boolean;
}
