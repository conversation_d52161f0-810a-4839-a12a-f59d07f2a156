import {
  url,
  getUrl,
  pause,
  restartApp,
  switchToWeb,
  waitForLoad,
} from "../helpers";
import Welcome from "../pageobjects/welcome.page";
import Login from "../pageobjects/login.page";
import * as Env from "../config/e2eConfig";

describe("Welcome", () => {
  it("Should navigate to forgot password page", async () => {
    const button = await Welcome.loginButton;
    await expect(await button.$).toBeDisplayed();
    await button.tap();
    await expect((await getUrl()).pathname).toBe("/login");
    await pause(Env.SHORT_SLEEP);
    const forgotPassword = await Login.forgotPassword;
    await expect(await forgotPassword.$).toBeDisplayed();
    await forgotPassword.tap();
    await expect((await getUrl()).pathname).toBe("/forgot-password");
  });
});
