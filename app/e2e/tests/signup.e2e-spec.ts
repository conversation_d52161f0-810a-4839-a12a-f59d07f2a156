import {
  backButton,
  url,
  getUrl,
  pause,
  restartApp,
  splashScreen,
  switchToWeb,
  waitForLoad,
  waitUntilUrl,
} from "../helpers";
import Signup from "../pageobjects/signup.page";
import Welcome from "../pageobjects/welcome.page";
import * as Env from "../config/e2eConfig";

describe("Signup", () => {
  const randomString = Math.random().toString(36).slice(2, 10);

  before(async () => {
    await waitForLoad();
    await switchToWeb();
    await url("/welcome");
    await restartApp("/welcome");
    await splashScreen();
  });

  it("Should open signup page", async () => {
    const button = await Welcome.signupButton;
    await button.tap();
    await waitUntilUrl("/signup/gender");
    await expect((await getUrl()).pathname).toBe("/signup/gender");
    await pause(Env.SHORT_SLEEP);
  });

  describe("Gender", async () => {
    it("Should see gender and looking fields", async () => {
      const gender = await Signup.gender;
      const looking = await Signup.looking;
      await expect(await gender.$).toBeDisplayed();
      await expect(await looking.$).toBeDisplayed();
      await expect(await (await gender.$).getText()).toBe("I'm a");
      await expect(await (await looking.$).getText()).toContain("Looking for");
      const genderVal = await (await Signup.genderValue()).getText();
      const lookingVal = await (await Signup.lookingValue()).getText();
      await expect(genderVal).toContain("Male");
      await expect(lookingVal).toContain("Female");
    });

    it("Should change gender from male to female and see looking id is male", async () => {
      const selectGender = await Signup.selectGender;
      await expect(await selectGender.$).toBeDisplayed();
      await selectGender.open();
      await selectGender.select(1);
      const gender = await Signup.gender;
      const looking = await Signup.looking;
      await expect(await (await gender.$).getText()).toContain("I'm a");
      await expect(await (await looking.$).getText()).toContain("Looking for");
      const genderVal = await (await Signup.genderValue()).getText();
      const lookingVal = await (await Signup.lookingValue()).getText();
      await expect(genderVal).toContain("Female");
      await expect(lookingVal).toContain("Male");
    });

    it("Should change looking from male to female and see gender id is female", async () => {
      const gender = await Signup.gender;
      const looking = await Signup.looking;
      const selectLooking = await Signup.selectLooking;
      await expect(await selectLooking.$).toBeDisplayed();
      await selectLooking.open();
      await selectLooking.select(1);
      await expect(await (await gender.$).getText()).toContain("I'm a");
      await expect(await (await looking.$).getText()).toContain("Looking for");
      const genderVal = await (await Signup.genderValue()).getText();
      const lookingVal = await (await Signup.lookingValue()).getText();
      await expect(genderVal).toContain("Female");
      await expect(lookingVal).toContain("Female");
    });

    it("Should click on next button and see page signup age", async () => {
      const nextButton = await Signup.next;
      await expect(await nextButton.$).toBeClickable();
      await nextButton.tap();
      await waitUntilUrl("/signup/age");
      const url = (await getUrl()).pathname + (await getUrl()).hash;
      await expect(url).toBe("/signup/age#step_2");
      await pause(Env.SHORT_SLEEP);
    });
  });

  describe("Age", async () => {
    it("Should click on next button and see erro message age is required", async () => {
      const nextButton = Signup.next;
      await nextButton.tap();
      const errorMessage = Signup.errorMessage("Age is required");
      await expect(errorMessage).toBeDisplayed();
      await pause(Env.SHORT_SLEEP);
    });

    it("Should enter invalid age and click next and see error message", async () => {
      const ageInput = await Signup.ageInput;
      await ageInput.setValue("17");
      await pause(Env.SHORT_SLEEP);
      const nextButton = Signup.next;
      await nextButton.tap();
      const errorMessage = Signup.errorMessage(
        "You have to be at least 18 to use this service"
      );
      await expect(errorMessage).toBeDisplayed();
      await pause(Env.SHORT_SLEEP);
    });

    it("Should enter age and click next", async () => {
      const ageInput = await Signup.ageInput;
      await expect(await ageInput.$).toBeDisplayed();
      await expect(await (await ageInput.$).getText()).toBe("");
      await ageInput.setValue("33");
      await pause(Env.SHORT_SLEEP);
      const nextButton = Signup.next;
      await nextButton.tap();
      await waitUntilUrl("/signup/username");
      const url = (await getUrl()).pathname + (await getUrl()).hash;
      await expect(url).toBe("/signup/username#step_3");
      await pause(Env.SHORT_SLEEP);
    });
  });

  describe("Username and Password", async () => {
    it("Should see userame and password inputs", async () => {
      const username = await Signup.username;
      const password = await Signup.password;
      await expect(await username.$).toBeDisplayed();
      await expect(await password.$).toBeDisplayed();
      await pause(Env.SHORT_SLEEP);
    });

    it("Should click on next button and see error message", async () => {
      const nextButton = await Signup.next;
      await nextButton.tap();
      const errorMessage1 = await Signup.errorMessage("Username is required");
      const errorMessage2 = await Signup.errorMessage("Password is required");
      await expect(errorMessage1).toBeDisplayed();
      await expect(errorMessage2).toBeDisplayed();
      await pause(Env.SHORT_SLEEP);
    });

    it("Should enter invalid value in username input and see error message", async () => {
      const username = await Signup.username;
      await username.setValue("test");
      await pause(Env.SHORT_SLEEP);
      const nextButton = Signup.next;
      await nextButton.tap();
      const errorMessage = await Signup.errorMessage("Username is too short");
      await expect(errorMessage).toBeDisplayed();
      await pause(Env.SHORT_SLEEP);
    });

    it("Should enter exising username and see error message", async () => {
      const username = await Signup.username;
      const password = await Signup.password;
      username.setValue(Env.TEST_USERNAME);
      await pause(Env.SHORT_SLEEP);
      password.setValue(Env.TEST_PASSWORD);
      await pause(Env.SHORT_SLEEP);
      const nextButton = Signup.next;
      await nextButton.tap();
      const errorMessage = Signup.errorMessage(
        "This screen name is already taken, please choose another one, or if it's yours please",
        true
      );
      await expect(errorMessage).toBeDisplayed();
      await pause(Env.SHORT_SLEEP);
    });

    it("Should enter valid parameters and click next button", async () => {
      const username = await Signup.username;
      const password = await Signup.password;
      username.setValue(randomString);
      await pause(Env.SHORT_SLEEP);
      password.setValue(Env.TEST_PASSWORD);
      await pause(Env.SHORT_SLEEP);
      const nextButton = Signup.next;
      await nextButton.tap();
      await waitUntilUrl("/signup/email");
      const url = (await getUrl()).pathname + (await getUrl()).hash;
      await expect(url).toBe("/signup/email#step_4");
      await pause(Env.SHORT_SLEEP);
    });
  });

  describe("Email", async () => {
    it("Should see email input", async () => {
      const email = await Signup.email;
      await expect(await email.$).toBeDisplayed();
      await expect(await email.getValue()).toBe("");
      await pause(Env.SHORT_SLEEP);
    });

    it("Should enter invalid email and click next button and see error message", async () => {
      const email = await Signup.email;
      const nextButton = Signup.next;
      await nextButton.tap();
      const errorMessage = Signup.errorMessage("Email address is required");
      await expect(errorMessage).toBeDisplayed();
      await email.setValue("test");
      await pause(Env.SHORT_SLEEP);
      await nextButton.tap();
      const errorMessage1 = Signup.errorMessage(
        "Please enter a valid email address"
      );
      await expect(errorMessage).not.toBeDisplayed();
      await expect(errorMessage1).toBeDisplayed();
      await pause(Env.SHORT_SLEEP);
    });

    it("Should enter valid email and click next button", async () => {
      const email = await Signup.email;
      await email.setValue(randomString + "@gmail.com");
      await pause(Env.SHORT_SLEEP);
      const nextButton = Signup.next;
      await nextButton.tap();
      await waitUntilUrl("/signup/consent");
      const url = (await getUrl()).pathname + (await getUrl()).hash;
      await expect(url).toBe("/signup/consent#step_5");
      await pause(Env.SHORT_SLEEP);
    });
  });

  describe("Consent", async () => {
    it("Should see consent page", async () => {
      const special = await Signup.cardSpecial;
      const terms = await Signup.cardTerms;
      const privacy = await Signup.cardPrivacy;
      await expect(await special.$).toBeDisplayed();
      await expect(await terms.$).toBeDisplayed();
      await expect(await privacy.$).toBeDisplayed();
    });

    it("Should see finish button is disabled", async () => {
      const finishButton = await Signup.finish;
      await expect(await finishButton.$).toBeDisplayed();
      await expect(await finishButton.$).not.toBeClickable();
    });

    it("Should see toggle all checkboxes and see finish button is enabled", async () => {
      await Signup.toggleAllCheckboxes();
      const finishButton = await Signup.finish;
      await expect(await finishButton.$).toBeClickable();
    });

    it("Should open terms page", async () => {
      await Signup.openTermsPage();
      await waitUntilUrl("/page/terms");
      await expect((await getUrl()).pathname).toBe("/page/terms");
      await pause(Env.SHORT_SLEEP);
      const back = await backButton();
      await expect(back.$).toBeDisplayed();
      await back.tap();
    });

    it("Should open privacy page", async () => {
      await Signup.openPrivacyPage();
      await waitUntilUrl("/page/privacy-policy");
      await expect((await getUrl()).pathname).toBe("/page/privacy-policy");
      await pause(Env.SHORT_SLEEP);
      const back = await backButton();
      await expect(back.$).toBeDisplayed();
      await back.tap();
    });
  });
});
