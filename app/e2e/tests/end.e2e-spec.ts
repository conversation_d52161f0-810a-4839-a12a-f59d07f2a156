import {
  pause,
  restartApp,
  url,
  waitForLoad,
  switchToWeb,
  clearIndexedDB,
} from "../helpers";
import * as Env from "../config/e2eConfig";

describe("End", () => {
  after(async () => {
    await clearIndexedDB(Env.DB);
    await waitForLoad();
    await switchToWeb();
    await url("/");
    await restartApp("/");
  });

  it("Should restart the app", async () => {
    console.log(">>> RESTART APP <<<");
    await pause(100);
  });
});
