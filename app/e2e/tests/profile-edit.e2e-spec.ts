import { Ionic$ } from "../helpers";
import ProfileEditPage from "../pageobjects/profile-edit.page";
import { ProfileEditFields } from "../pageobjects/profile-edit.fields";

describe("ProfileEdit", () => {
  it("Should open profile edit page", async () => {
    await ProfileEditPage.openPage();
    const items = await $$("ion-item-group");
    expect(items.length).toEqual(7);
  });

  it("Should see profile edit groups", async () => {
    const groups = await ProfileEditPage.getGroups();
    expect(groups.length).toEqual(7);
  });

  it("Check groups headers", async () => {
    const groups = await ProfileEditPage.getGroups();
    let i: number = 0;
    for (const group of groups) {
      let text = await group.$("ion-item-divider").getText();
      expect(text).toEqual(ProfileEditPage.groups_names[i]);
      i++;
    }
  });

  it("Check first name field", async () => {
    await ProfileEditFields.checkAllFields();
    /*
    await ProfileEditFields.getField("first_name").checkField();
    await ProfileEditFields.getField("headline").checkField();
    /*
    await ProfileEditFields.getField("first_name").checkValueOld();
    await ProfileEditFields.getField("first_name").setValueNew();
    await ProfileEditFields.getField("first_name").checkValueNew();


    await ProfileEditFields.getField("headline").checkValueOld();
    await ProfileEditFields.getField("headline").setValueNew();
    await ProfileEditFields.getField("headline").checkValueNew();
*/
  });

  /*
  it("Check headline field", async () => {
    await ProfileEditFields.addField('first_name', {'old':'Jasmine Tester', 'new':'Jasmine Tester New'}, ProfileEditPage.profileHeadline);
    await ProfileEditFields.checkValueOld('first_name');
    await ProfileEditFields.setValueNew('first_name');
    await ProfileEditFields.checkValueNew('first_name');
  });
*/
});
