import {
  url,
  getUrl,
  pause,
  restartApp,
  splashScreen,
  switchToWeb,
  waitForLoad,
  waitUntilUrl,
} from "../helpers";
import Login from "../pageobjects/login.page";
import Welcome from "../pageobjects/welcome.page";
import * as Env from "../config/e2eConfig";

describe("Autologin", () => {
  before(async () => {
    await waitForLoad();
    await switchToWeb();
    await url("/welcome");
    await restartApp("/welcome");
    await splashScreen();
  });

  it("Should autologin", async () => {
    await waitUntilUrl("/welcome");
    const button = await Welcome.loginButton;
    await button.tap();
    await waitUntilUrl("/login");
    await expect((await getUrl()).pathname).toBe("/login");
    await pause(Env.SHORT_SLEEP);
    await Login.login(Env.TEST_USERNAME, Env.TEST_PASSWORD);
  });
});
