import {
  backButton,
  url,
  getUrl,
  pause,
  menuLink,
  openMenu,
  closeMenu,
  restartApp,
  switchToWeb,
  waitForLoad,
  waitUntilUrl,
  menuLinkByTitle,
} from "../helpers";
import Viewed from "../pageobjects/viewed.page";

describe("Viewed", () => {
  let profile: string = "";

  it("Should open I Viewed page", async () => {
    await Viewed.openPage(true);
    await Viewed.openIViewedTab();
  });

  it("The back button should work", async () => {
    const back = await backButton(true);
    await expect(back.$).toBeDisplayed();
    await back.tap();
  });

  it("Should see profiles", async () => {
    await Viewed.openPage();
    await Viewed.openIViewedTab();

    await Viewed.waitForProfiles();
    const profiles = await Viewed.profiles();
    expect(profiles.length).toBeGreaterThan(0);
    profile = await (await profiles[0].$).getAttribute("ng-reflect-router-link");
  });

});
