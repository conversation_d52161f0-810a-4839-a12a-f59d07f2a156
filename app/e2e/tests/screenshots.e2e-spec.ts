import {
  url,
  getUrl,
  pause,
  restartApp,
  splashScreen,
  switchToWeb,
  waitForLoad,
  waitUntilUrl,
} from "../helpers";
import Login from "../pageobjects/login.page";
import Welcome from "../pageobjects/welcome.page";
import * as Env from "../config/e2eConfig";
import Debug from "../pageobjects/debug.page";
import Purchase from "../pageobjects/purchase.page";
import { createWriteStream } from "fs";

describe("Product Screenshots", () => {
  before(async () => {
    await waitForLoad();
    await switchToWeb();
    await url("/welcome");
    await restartApp("/welcome");
    await splashScreen();
  });

  it("Should open debug page", async () => {
    await waitUntilUrl("/welcome");
    const button = await Welcome.loginButton;
    await button.tap();
    await waitUntilUrl("/login");
    await expect((await getUrl()).pathname).toBe("/login");
    await pause(Env.SHORT_SLEEP);
    await Login.login(Env.TEST_USERNAME, Env.TEST_PASSWORD);
    await Debug.openPage();
  });

  /**
   * Default.
   */

  it("Should open purchase default page", async () => {
    await Debug.open("#purchase-default");
  });

  it("Should take screenshots", async () => {
    const items = await Purchase.getProductItems();
    expect(items.length).toBe(3);
    await items[0].click();
    Purchase.takeScreenshot("default1");
    await pause(Env.LONG_SLEEP);
    await items[1].click();
    Purchase.takeScreenshot("default2");
    await pause(Env.LONG_SLEEP);
    await items[2].click();
    Purchase.takeScreenshot("default3");
    await pause(Env.LONG_SLEEP);
  });

  it("Should close purchase default page", async () => {
    await Purchase.closePage();
    await pause(Env.SHORT_SLEEP);
  });

  /**
   * New member special offer.
   */

  it("Should open new memeber special offer page", async () => {
    await Debug.open("#purchase-new-member");
  });

  it("Should take screenshots", async () => {
    const items = await Purchase.getProductItems();
    expect(items.length).toBe(2);
    await items[0].click();
    Purchase.takeScreenshot("new_member1");
    await pause(Env.LONG_SLEEP);
    await items[1].click();
    Purchase.takeScreenshot("new_member2");
    await pause(Env.LONG_SLEEP);
  });

  it("Should close new memeber special offer page", async () => {
    await Purchase.closePage();
    await pause(Env.SHORT_SLEEP);
  });

  /**
   * App Downloaded.
   */

  it("Should open purchase app downloaded page", async () => {
    await Debug.open("#purchase-app-downloaded");
  });

  it("Should take screenshots", async () => {
    const items = await Purchase.getProductItems();
    expect(items.length).toBe(2);
    await items[0].click();
    Purchase.takeScreenshot("app_downloaded1");
    await pause(Env.LONG_SLEEP);
    await items[1].click();
    Purchase.takeScreenshot("app_downloaded2");
    await pause(Env.LONG_SLEEP);
  });

  it("Should close purchase app downloaded page", async () => {
    await Purchase.closePage();
    await pause(Env.SHORT_SLEEP);
  });

  /**
   * App Downloaded 24h.
   */

  it("Should open purchase app downloaded 24h page", async () => {
    await Debug.open("#purchase-app-downloaded-24h");
  });

  it("Should take screenshots", async () => {
    const items = await Purchase.getProductItems();
    expect(items.length).toBe(3);
    await items[0].click();
    Purchase.takeScreenshot("app_downloaded_24h1");
    await pause(Env.LONG_SLEEP);
    await items[1].click();
    Purchase.takeScreenshot("app_downloaded_24h2");
    await pause(Env.LONG_SLEEP);
    await items[2].click();
    Purchase.takeScreenshot("app_downloaded_24h3");
    await pause(Env.LONG_SLEEP);
  });

  it("Should close purchase app downloaded 24h page", async () => {
    await Purchase.closePage();
    await pause(Env.SHORT_SLEEP);
  });

  /**
   * Try 1 month.
   */

  it("Should open purchase try 1 month page", async () => {
    await Debug.open("#purchase-try-1-month");
  });

  it("Should take screenshots", async () => {
    const items = await Purchase.getProductItems();
    expect(items.length).toBe(1);
    Purchase.takeScreenshot("try_1_month");
    await pause(Env.LONG_SLEEP);
  });

  it("Should close purchase try 1 month page", async () => {
    await Purchase.closePage();
    await pause(Env.SHORT_SLEEP);
  });
});
