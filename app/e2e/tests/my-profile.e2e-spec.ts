import {
  backButton,
  url,
  getUrl,
  pause,
  menuLink,
  openMenu,
  closeMenu,
  restartApp,
  switchToWeb,
  waitForLoad,
  waitUntilUrl,
  menuLinkByTitle,
  menuAvatarLink,
} from "../helpers";
import myProfilePage from "../pageobjects/my-profile.page";

describe("MyProfile", () => {
  let img1: string =
    "https://dev-files.dvipcdn.com/data/dating/m1/thumbnails3/11/8/4153118_1_1_0_fabddd13.jpg";
  let img2: string =
    "https://dev-files.dvipcdn.com/data/dating/m1/thumbnails3/11/8/4153118_1_654_0_1ff149aa.jpg";

  it("Should open My Profile page", async () => {
    await waitUntilUrl("/app/tabs/browse");
    await menuAvatarLink();
    await waitUntilUrl("/app/tabs/my-profile");
    await myProfilePage.waitForProfile();
  });

  it("Should open Menu", async () => {
    await openMenu();
  });

  it("Should close Menu", async () => {
    await closeMenu();
  });

  it("Should load swiper", async () => {
    await expect(await myProfilePage.slides.$).toBeDisplayed();
  });

  it("Swiper length should be 2", async () => {
    await expect(await myProfilePage.slides.length()).toEqual(2);
  });

  it("Check profile active photo (source 1)", async () => {
    await expect(await myProfilePage.activeProfilePhoto()).toEqual(img1);
  });

  it("Should swipe left", async () => {
    await myProfilePage.slides.swipeLeft();
  });

  it("Check profile active photo (source 2)", async () => {
    await expect(await myProfilePage.activeProfilePhoto()).toEqual(img2);
  });

  it("Should swipe left but no slide", async () => {
    await myProfilePage.slides.swipeLeft();
  });

  it("Check profile active photo (source 2, no swipe left)", async () => {
    await expect(await myProfilePage.activeProfilePhoto()).toEqual(img2);
  });

  it("Should swipe right", async () => {
    await myProfilePage.slides.swipeRight();
  });

  it("Check profile active photo (source 1), swipe right", async () => {
    await expect(await myProfilePage.activeProfilePhoto()).toEqual(img1);
  });

  it("Should swipe right", async () => {
    await myProfilePage.slides.swipeRight();
  });

  it("Check profile active photo (source 1, no swipe right)", async () => {
    await expect(await myProfilePage.activeProfilePhoto()).toEqual(img1);
  });

  it("Check h1..h6 content sections", async () => {
    const h2_el = await myProfilePage.contentHeader(2);
    await expect((await h2_el).length).toEqual(1);
    await expect(await h2_el[0].getText()).toContain("Jasmine Tester, 20");

    const h6_el = await myProfilePage.contentHeader(6);
    await expect((await h6_el).length).toEqual(2);
    await expect(await h6_el[0].getText()).toContain("My Interests");
    await expect(await h6_el[1].getText()).toContain("My Profile Description");
  });
  /*
  it('pause', async () => {
    await driver.pause(5000);
  });
*/
});
