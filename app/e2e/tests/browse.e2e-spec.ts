import {
  backButton,
  url,
  getUrl,
  pause,
  menuLink,
  openMenu,
  closeMenu,
  restartApp,
  switchToWeb,
  waitForLoad,
  waitUntilUrl,
} from "../helpers";
import Browse from "../pageobjects/browse.page";

describe("Browse", () => {
  let profile: string = "";

  it("Should see the browse page", async () => {
    await waitUntilUrl("/app/tabs/browse");
    await expect((await getUrl()).pathname).toBe("/app/tabs/browse");
  });

  it("Should open and close menu", async () => {
    await openMenu();
    await closeMenu();
  });

  it("Should open inbox page from menu", async () => {
    await menuLink("/app/inbox");
    await waitUntilUrl("/app/inbox");
    await (await backButton()).tap();
    await waitUntilUrl("/app/tabs/browse");
  });

  it("Should see profiles", async () => {
    await Browse.waitForProfiles();
    const profiles = await Browse.profiles();
    expect(profiles.length).toBeGreaterThan(0);
    profile = await (
      await profiles[0].$
    ).getAttribute("ng-reflect-router-link");
  });

  it("Should click on browse by button and see dropdown menu and change selection", async () => {
    const select = await Browse.selectButton;
    await select.open();
    await select.select(1);

    await Browse.waitForProfiles();
    const profiles = await Browse.profiles();
    await expect(
      await (await profiles[0].$).getAttribute("ng-reflect-router-link")
    ).not.toBe(profile);
    expect(profiles.length).toBeGreaterThan(0);
  });
});
