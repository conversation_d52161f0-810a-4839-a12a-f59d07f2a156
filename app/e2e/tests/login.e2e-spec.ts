import {
  backButton,
  url,
  getUrl,
  pause,
  restartApp,
  switchToWeb,
  waitForLoad,
  waitUntilUrl,
} from "../helpers";
import Login from "../pageobjects/login.page";
import * as Env from "../config/e2eConfig";

describe("Login", () => {
  afterEach(async () => {
    await pause(Env.SHORT_SLEEP);
  });

  it("Should see the login page", async () => {
    await expect((await getUrl()).pathname).toBe("/login");
  });

  it("Should see username, password and login button", async () => {
    const username = await Login.username;
    const password = await Login.password;
    const login = await Login.loginButton;
    const forgotPassword = await Login.forgotPassword;
    // const eyeIconOff = await Login.eyeIconOff;
    const errorMessage = await Login.errorMessage("msg");
    await expect(await username.$).toBeDisplayed();
    await expect(await password.$).toBeDisplayed();
    await expect(await login.$).toBeDisplayed();
    await expect(await forgotPassword.$).toBeDisplayed();
    // await expect(await eyeIconOff.$).toBeDisplayed();
    await expect(errorMessage).not.toBeDisplayed();
  });

  it("Should click on eye icon to see password", async () => {
    // Ionic update - changed ion-icon to ion-input-password-toggle
    // @todo - update test!
    // const eyeIconOff = await Login.eyeIconOff;
    // await eyeIconOff.tap();
    // const eyeIconOn = await Login.eyeIconOn;
    // await expect(await eyeIconOff.$).not.toBeDisplayed();
    // await expect(await eyeIconOn.$).toBeDisplayed();
    // await eyeIconOn.tap();
    // await expect(await eyeIconOn.$).not.toBeDisplayed();
  });

  it("Should failed to login", async () => {
    await Login.login("", "");
    let errorMessage1 = await Login.errorMessage(
      "Please enter a valid email address or username",
    );
    let errorMessage2 = await Login.errorMessage(
      "Email address or username is required",
    );
    let errorMessage3 = await Login.errorMessage("Password is required");
    await expect(errorMessage1).not.toBeDisplayed();
    await expect(errorMessage2).toBeDisplayed();
    await expect(errorMessage3).toBeDisplayed();
    await pause(100);
    await Login.login("test", "test");
    errorMessage1 = await Login.errorMessage(
      "Please enter a valid email address or username",
    );
    errorMessage2 = await Login.errorMessage(
      "Email address or username is required",
    );
    errorMessage3 = await Login.errorMessage("Password is required");
    await expect(errorMessage1).toBeDisplayed();
    await expect(errorMessage2).not.toBeDisplayed();
    await expect(errorMessage3).not.toBeDisplayed();
    await expect((await getUrl()).pathname).toBe("/login");
    const password = await Login.password;
    await expect(await password.getValue()).toBe("test");
  });

  it("Should login", async () => {
    await Login.login(Env.TEST_USERNAME, Env.TEST_PASSWORD);
    await waitUntilUrl("/app/tabs/browse");
    await expect((await getUrl()).pathname).toBe("/app/tabs/browse");
  });
});
