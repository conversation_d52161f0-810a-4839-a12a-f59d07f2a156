import {
  backButton,
  url,
  getUrl,
  pause,
  restartApp,
  switchToWeb,
  waitForLoad,
} from "../helpers";
import ForgotPassword from "../pageobjects/forgot-password.page";
import * as Env from "../config/e2eConfig";

describe("ForgotPassword", () => {
  afterEach(async () => {
    await pause(Env.SHORT_SLEEP);
  });

  it("Should see ForgotPassword page", async () => {
    await expect((await getUrl()).pathname).toBe("/forgot-password");
  });

  it("Should click on reset password button and see toast with message", async () => {
    const button = await ForgotPassword.resetPasswordButton;
    await expect(await button.$).toBeDisplayed();
    await button.tap();
    await pause(Env.SHORT_SLEEP);
    const toast = await ForgotPassword.toast;
    await expect(await toast.getText()).toContain(
      "The email field is required."
    );
    await expect(await toast.$).toBeDisplayed();
    await toast.close();
    await pause(Env.SHORT_SLEEP);
  });

  it("Should enter invalid email", async () => {
    const input = await ForgotPassword.email;
    await expect(input.$).toBeDisplayed();
    await input.setValue("test");
    await pause(Env.SHORT_SLEEP);
    const button = await ForgotPassword.resetPasswordButton;
    await expect(await button.$).toBeDisplayed();
    await button.tap();
    await pause(Env.SHORT_SLEEP);
    const toast = await ForgotPassword.toast;
    await expect(await toast.getText()).toContain(
      "The email must be a valid email address."
    );
    await expect(await toast.$).toBeDisplayed();
    await toast.close();
    await pause(Env.SHORT_SLEEP);
  });

  it("Should enter walid password and see toast", async () => {
    const input = await ForgotPassword.email;
    await input.setValue("<EMAIL>");
    await pause(Env.SHORT_SLEEP);
    const button = await ForgotPassword.resetPasswordButton;
    await button.tap();
    await pause(Env.SHORT_SLEEP);
    const toast = await ForgotPassword.toast;
    await expect(await toast.getText()).toContain("Request successfully sent");
    await toast.close();
    await pause(Env.SHORT_SLEEP);
  });

  it("Should go back to login page", async () => {
    const back = await backButton();
    await expect(back.$).toBeDisplayed();
    await back.tap();
  });
});
