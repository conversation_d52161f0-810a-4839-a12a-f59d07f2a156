{"extends": "../tsconfig.json", "compilerOptions": {"outDir": "../.tsbuild/", "sourceMap": false, "target": "es2019", "module": "commonjs", "removeComments": true, "noImplicitAny": true, "esModuleInterop": true, "strictPropertyInitialization": true, "strictNullChecks": true, "types": ["node", "webdriverio/async", "@wdio/globals/types", "@wdio/mocha-framework", "@wdio/devtools-service", "expect-webdriverio"]}}