export class IonicComponent {
  constructor(
    public selector: string | WebdriverIO.Element,
    public outsideIonRoot: boolean = false
  ) {}

  get $() {
    if (this.outsideIonRoot) {
      return $(this.selector);
    }

    const page = import("./page");
    return page.then(async ({ IonicPage }) => {
      if (typeof this.selector === "string") {
        const activePage = await IonicPage.active();
        return activePage.$(this.selector);
      }

      return this.selector;
    });
  }
}
