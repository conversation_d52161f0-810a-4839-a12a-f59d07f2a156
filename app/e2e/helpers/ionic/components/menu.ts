import { debug } from "webdriverio/build/commands/browser";

import { Ionic$ } from "..";
import { ElementActionOptions } from "../..";
import { IonicComponent } from "./component";

export interface OpenMenuOptions extends ElementActionOptions {
  delayForAnimation?: boolean;
}

export class IonicMenu extends IonicComponent {
  constructor(selector?: string) {
    super(selector || "ion-menu", true);
  }
  /*
  async getMenuButton() {
    /**
     * NOT WORKIN ON ALL pages bcs not selector
    return $("//*[contains(@class, 'ion-page') and not(contains(@class, 'ion-page-hidden'))]//ion-menu-button");

    //return $(".ion-page:not(.ion-page-hidden) ion-menu-button");
    //return await $(".ion-page:not(.ion-page-hidden) ion-menu-button");

    const menus = await $$(".ion-page ion-menu-button");
    for (const value of menus) {
      if (await value.isDisplayed()) {
        return value;
      }
    }

    return await $(".ion-page:not(.ion-page-hidden) ion-menu-button");
  }
    */
  get menuButton() {
    return Ionic$.$(".ion-page:not(.ion-page-hidden) ion-menu-button");
  }

  get menuButtonClose() {
    return $("#close-menu");
  }

  async open({
    delayForAnimation = true,
    visibilityTimeout = 5000,
  }: OpenMenuOptions = {}) {
    //if (await this.isOpened()) { return Promise.resolve(); }

    //const btn = await this.getMenuButton();
    const btn = await this.menuButton;
    await btn.waitForClickable({ timeout: visibilityTimeout });
    await btn.click();
    // Let the menu animate open/closed
    if (delayForAnimation) {
      return driver.pause(500);
    }
  }

  async isOpened() {
    let elem = await $("menu");
    let isExisting = await elem.isExisting();
    if (!isExisting) {
      return false;
    }
    return await elem.isDisplayed();
  }

  async close({
    delayForAnimation = true,
    visibilityTimeout = 5000,
  }: OpenMenuOptions = {}) {
    if ((await this.isOpened()) == false) {
      return Promise.resolve();
    }
    const closeButton = await this.menuButtonClose;
    await closeButton.waitForDisplayed({ timeout: visibilityTimeout });
    await closeButton.waitForClickable();
    await (await closeButton).click();

    // Let the menu animate open/closed
    if (delayForAnimation) {
      return driver.pause(500);
    }
  }

  async closeIfOpened() {
    const closeButton = await this.menuButtonClose;
    if (await closeButton.isDisplayed()) {
      this.close();
    }
  }

  async link(link: string) {
    await this.open();
    const button = await (await this.$).$(`[ng-reflect-router-link="${link}"]`);
    await button.waitForDisplayed();
    await button.click();
    return driver.pause(500);
  }

  async linkByTitle(title: string) {
    await this.open();
    const button = await (await this.$).$(`ion-label=${title}`);
    await button.waitForDisplayed();
    await button.click();
    return driver.pause(500);
  }

  async menuLinkAvatarMyProfile() {
    //return await this.link("/app/tabs/my-profile");
    ///app/tabs/my-profile
    let link: string = "/app/inbox";
    await this.open();
    const button = await (await this.$).$(`[ng-reflect-router-link="${link}"]`);
    await button.waitForDisplayed();
    await button.click();
    return driver.pause(500);
  }

  async debugButton(name: string = "bug-outline") {
    await this.open();
    const button = await await (await this.$).$(".debug-button").$("ion-icon");
    await button.click();
    return driver.pause(500);
  }
}
