import { Ionic$ } from "..";
import { ElementActionOptions } from "../..";
import { IonicComponent } from "./component";

export interface BackButtonOptions extends ElementActionOptions {
  delayForAnimation?: boolean;
}

export class IonicBackButton extends IonicComponent {
  constructor(
    public selector: string | WebdriverIO.Element,
    public outsideIonRoot: boolean = false
  ) {
    super(selector ?? "ion-back-button", outsideIonRoot);
  }

  async tap({
    delayForAnimation = true,
    visibilityTimeout = 5000,
  }: BackButtonOptions = {}) {
    await (await this.$).waitForDisplayed({ timeout: visibilityTimeout });
    await (await this.$).waitForClickable({ timeout: visibilityTimeout });
    await (await this.$).click();

    if (delayForAnimation) {
      return driver.pause(500);
    }
  }
}
