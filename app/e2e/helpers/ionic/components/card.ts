import { IonicComponent } from "./component";
import { Ionic$ } from "../..";

export class IonicCard extends IonicComponent {
  constructor(selector?: string | WebdriverIO.Element) {
    super(selector ?? "ion-card");
  }

  static async withClass(selector: string): Promise<IonicCard> {
    return new IonicCard(`ion-card.${selector}`);
  }

  static async withClassAll(selector: string): Promise<IonicCard[]> {
    const data = await $$(`ion-card.${selector}`);
    let results = [];
    for (let value of data) {
      results.push(new IonicCard(value));
    }
    return Promise.resolve(results);
  }

  static async withHeader(title: string) {
    const container = await new IonicCard();
    return (await container.$).$(`h4=${title}`);
  }
}
