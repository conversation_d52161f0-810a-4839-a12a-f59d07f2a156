import { Ionic$ } from "..";
import { IonicComponent } from "./component";

export class IonicToast extends IonicComponent {
  constructor() {
    super("ion-toast", true);
  }

  getText() {
    return $(this.selector).shadow$('.toast-message').getText();
  }

  async close() {
    const button = await (
      await $(this.selector as string)
    ).shadow$("button.toast-button");
    return button.click();
  }
}
