import { TapButtonOptions } from ".";
import { Ionic$ } from "..";
import { IonicComponent } from "./component";

export class IonicIcon extends IonicComponent {
  constructor(selector: string) {
    super(selector);
  }

  static withName(name: string) {
    return new IonicIcon(`ion-icon[name="${name}"]`);
  }

  async tap({
    visibilityTimeout = 5000,
    scroll = true,
  }: TapButtonOptions = {}) {
    const button = await Ionic$.$(this.selector as string);
    await button.waitForDisplayed({ timeout: visibilityTimeout });
    if (scroll) {
      await button.scrollIntoView();
    }
    return button.click();
  }
}
