import { Ionic$ } from "..";
import { IonicComponent } from "./component";

export class IonicImage extends IonicComponent {

  constructor(public selector: string) {
    super(selector);
  }
/*
  static withTitle(title: string): IonicImage {
    return new IonicImage(`a=${title}`);
  }

  static async withClass(selector: string): Promise<IonicImage> {
    return new IonicImage(`img.${selector}`);
  }
*/
  async source() {
    //const form = await $('form')
    const src = (await Ionic$.$(this.selector)).getAttribute("src");
    return src;
  }

}
