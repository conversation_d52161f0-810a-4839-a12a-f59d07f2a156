import { IonicComponent } from "./component";

export class IonicFormError extends IonicComponent {
  constructor(selector?: string) {
    super(selector || "<app-form-error-list />");
  }

  static async withTitle(message: string, partial: boolean = false) {
    const container = await new IonicFormError();
    if (partial) {
      return (await container.$).$(`span*=${message}`);
    }
    return (await container.$).$(`span=${message}`);
  }
}
