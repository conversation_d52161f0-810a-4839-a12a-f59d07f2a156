import { browser, driver } from "@wdio/globals";
export async function getUrl(): Promise<URL> {
  return new URL(await browser.getUrl());
}

export async function waitUntilUrl(url: string): Promise<any> {
  return await driver.waitUntil(
    async () => {
      const pathname = (await getUrl()).pathname;
      return pathname === url;
    },
    {
      timeout: 20000, // 20s
      timeoutMsg: "Page not loaded",
      interval: 1000,
    }
  );
}
