import { hasUncaughtExceptionCaptureCallback } from "node:process";
import { IonicInput } from "./ionic";

/**
 * Value type with structure new value and old value
 */
export type fieldValueType = {
  old: string;
  new: string;
};

/**
 * Single field object
 */
class EditField {
  private values: fieldValueType[] = [];
  private field_obj: IonicInput;

  public constructor(field_object: IonicInput) {
    this.field_obj = field_object;
    return this;
  }

  /**
   * Put the test value (new and old) of the field
   *
   * @param value
   * @returns
   */
  public putValue(value: fieldValueType | fieldValueType[]): boolean {
    if (Array.isArray(value) && this.isMultivalue()) {
      this.values = [...this.values, ...value];
      return true;
    }
    if (!Array.isArray(value) && !this.isMultivalue()) {
      this.values = [value];
      return true;
    }

    return false;
  }

  /**
   * Get the test value (new and old) of the field
   *
   * @returns
   */
  public getValue(): fieldValueType | fieldValueType[] {
    if (!this.isMultivalue() && this.lenght() == 1) return this.values[0];
    else if (this.isMultivalue() && this.lenght() > 1) return this.values;
    else return [];
  }

  /**
   * Get the current value of the field
   *
   * @returns
   */
  public async getValueCurrent() {
    if (!this.isMultivalue()) {
      return await (await this.field_obj).getValue();
    }

    return null;
  }

  /**
   * Set the new test value in the field
   *
   * @returns
   */
  public async setValueNew() {
    if (!this.isMultivalue()) {
      return await (await this.field_obj).setValue(await this.getValueNew());
    }

    return null;
  }

  /**
   * Set the old test value in the field
   *
   * @returns
   */
  public async setValueOld() {
    if (!this.isMultivalue()) {
      return await (await this.field_obj).setValue(await this.getValueOld());
    }

    return null;
  }

  /**
   * Get the new test value
   * @returns
   */
  public getValueNew() {
    if (!this.isMultivalue() && this.values[0].new) {
      return this.values[0].new;
    } else if (this.isMultivalue()) {
      var v: any = [];
      for (var val of this.values) {
        v.push(val.new);
      }
      return v;
    }

    return null;
  }

  /**
   * Get the old (default) test value
   * @returns
   */
  public getValueOld() {
    if (!this.isMultivalue() && this.values[0].old !== null) {
      return this.values[0].old;
    } else if (this.isMultivalue()) {
      var v: any = [];
      for (var val of this.values) {
        v.push(val.old);
      }
      return v;
    }

    return null;
  }

  /**
   * Check if the current value is equal to the old test value
   */
  public async checkValueOld() {
    expect(await this.getValueCurrent()).toEqual(await this.getValueOld());
  }

  /**
   * Check if the current value is equal to the new test value
   */
  public async checkValueNew() {
    expect(await this.getValueCurrent()).toEqual(await this.getValueNew());
  }

  /**
   * @description Check old value, set new value and check new value, keep new value
   */
  public async checkField() {
    await this.checkValueOld();
    await this.setValueNew();
    await this.checkValueNew();
  }

  /**
   * Is value multivalue (has more then one values (select box) )
   * @returns {boolean}
   */
  public isMultivalue(): boolean {
    if (this.field_obj.constructor.name == IonicInput.name) {
      return false;
    }
    return true;
  }

  /**
   * Get count of fields
   * @returns number
   */
  public lenght(): number {
    return this.values.length;
  }
}

/**
 * Array of EditField and maniuplation
 */
export class EditFields {
  /**
   * Array of EditField object
   */
  private fields: { [key: string]: EditField } = {};

  /**
   *
   * @param json - list of fields with selector, new and default value
   */
  constructor(json: any = {}) {
    for (const key in json) {
      if (!this.fields[key]) {
        let value: fieldValueType = {
          new: json[key].new_value,
          old: json[key].old_value,
        };
        let field: IonicInput;
        switch (json[key].object_type) {
          case "input":
            field = new IonicInput(json[key].selector);
            break;
          default:
            field = new IonicInput(json[key].selector);
            break;
        }
        this.addField(key, value, field);
      }
    }
  }

  /**
   * Add field to the fields list
   *
   * @param {string} field_name
   * @param {fieldValueType | fieldValueType[]} field_value - single fieldValueType or array of fieldValueType
   * @param {IonicInput} field_obj_type - type of field object
   */
  public async addField(
    field_name: string,
    field_value: fieldValueType | fieldValueType[],
    field_obj_type: IonicInput,
  ) {
    let field = new EditField(field_obj_type);
    field.putValue(field_value);
    this.fields[field_name] = field;
  }

  /**
   * Get field object
   *
   * @param {string} field_name
   * @returns EditField object
   */
  public getField(field_name: string): EditField {
    if (!this.fields[field_name])
      throw new Error(
        "EditFields class. No field with name '" + field_name + "'",
      );
    return this.fields[field_name];
  }

  public async checkAllFields() {
    for (const key in this.fields) {
      await this.fields[key].checkField();
    }
  }
}
