import { Ionic$, IonicButton, IonicInput, IonicToast } from "../helpers";

import Page from "./page";

class ForgotPassword extends Page {
  get title() {
    return Ionic$.$("h1");
  }
  get resetPasswordButton() {
    return IonicButton.withTitle("Reset password");
  }

  get toast() {
    return new IonicToast();
  }

  get email() {
    return new IonicInput('ion-input [placeholder="Email"]');
  }
}

export default new ForgotPassword();
