import { Ionic$, IonicCard, IonicSelect } from "../helpers";

import Page from "./page";

class Browse extends Page {
  async profiles() {
    return await IonicCard.withClassAll("browse-profile");
  }

  async waitForProfiles() {
    const loading = await Ionic$.$(".browse-profiles-wrapper .browse-profile");
    return await loading.waitForDisplayed({
      timeout: 30000,
      interval: 1000,
      timeoutMsg: "Browse profiles not found after 30s",
    });
  }

  get selectButton() {
    return new IonicSelect("ion-item.distance-sort");
  }
}

export default new Browse();
