import {
  IonicFormError,
  IonicButton,
  IonicInput,
  IonicItem,
  IonicIcon,
  IonicText,
  Link,
} from "../helpers";
import Page from "./page";
import * as Env from "../config/e2eConfig";

class Login extends Page {
  get username() {
    return new IonicInput('ion-input [placeholder="Username or email"]');
  }
  get password() {
    return new IonicInput('ion-input [placeholder="Password"]');
  }
  get loginButton() {
    return IonicButton.withTitle("Sign in");
  }

  get signIn() {
    return IonicButton.withTitle("Sign in");
  }

  get forgotPassword() {
    return Link.withTitle("Forgot password?");
  }

  get eyeIconOn() {
    return IonicIcon.withName("eye-outline");
  }

  get eyeIconOff() {
    return IonicIcon.withName("eye-off-outline");
  }

  async errorMessage(message: string) {
    return IonicFormError.withTitle(message);
  }

  async login(username: string, password: string) {
    await this.username.setValue(username);
    await browser.pause(Env.SHORT_SLEEP);
    await this.password.setValue(password);
    await browser.pause(Env.SHORT_SLEEP);
    return this.signIn.tap();
  }
}

export default new Login();
