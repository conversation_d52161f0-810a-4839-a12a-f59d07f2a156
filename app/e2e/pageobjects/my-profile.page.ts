import {
  Ionic$,
  IonicCard,
  IonicPage,
  IonicSelect,
  menuAvatarLink,
  openMenu,
  IonicSlides,
  IonicImage,
  closeMenu,
} from "../helpers";
import { getUrl, waitUntilUrl } from "../helpers/browser";
import { $, $$ } from "@wdio/globals";

import Page from "./page";

class MyProfile extends Page {
  async openPage() {
    await openMenu();
    await waitUntilUrl("/app/tabs/browse");
    await menuAvatarLink();
    await waitUntilUrl("/app/tabs/my-profile");
    await IonicPage.active();
    await this.waitForProfile();

    return Promise.resolve();
  }

  async waitForProfile() {
    const loading = await $("app-my-profile");
    return await loading.waitForDisplayed({
      timeout: 10000,
      interval: 1000,
      timeoutMsg: "My Profile not found after 10s",
    });
  }

  get slides() {
    return new IonicSlides("swiper-container#profile-photos");
  }

  async contentHeader(h_num: number) {
    return Ionic$.$$(
      "app-my-profile ion-content[role='main'] div:not(.hidden) ion-grid h" +
        h_num,
    );
  }

  async activeProfilePhoto() {
    return (
      await $(
        "swiper-container#profile-photos swiper-slide.swiper-slide-active ion-img",
      ).shadow$("img")
    ).getAttribute("src");
  }

  async swipeLeft() {
    return this.slides.swipeLeft();
  }

  async swipeRight() {
    return this.slides.swipeRight();
  }
}

export default new MyProfile();
