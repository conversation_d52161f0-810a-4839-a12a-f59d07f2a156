import {
  pause,
  Ionic$,
  IonicCard,
  IonicIcon,
  IonicButton,
  IonicSelect,
  IonicTabButton,
  IonicToast,
  menuLinkByTitle,
} from "../helpers";
import * as Env from "../config/e2eConfig";
import Page from "./page";
import { getUrl, waitUntilUrl } from "../helpers/browser";
import { menuDebugLink } from "../helpers/index";
import { createWriteStream } from "fs";
const im = require("imagemagick");

class Purchase extends Page {
  public path: string = `${__dirname}/../product_screenshots/`;

  async getProductItems() {
    return await $("ion-row.product-items").$$("ion-col");
  }

  async takeScreenshot(name: string) {
    browser.takeScreenshot().then((png) => {
      let stream = createWriteStream(this.path + name + ".png");
      stream.write(new Buffer(png, "base64"));
      stream.end;
    });

    await pause(Env.SHORT_SLEEP);

    im.resize(
      {
        srcPath: this.path + name + ".png",
        dstPath: this.path + name + ".png",
        width: Env.SCREENSHOT_WIDTH,
        height: Env.SCREENSHOT_HEIGHT,
      },
      () => {
        console.log("resized > " + name + ".png");
      },
    );

    await pause(Env.SHORT_SLEEP);
  }

  async closePage() {
    const button = IonicIcon.withName("close-outline");
    await button.tap();
  }
}

export default new Purchase();
