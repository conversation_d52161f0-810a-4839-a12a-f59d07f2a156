import {
  pause,
  Ionic$,
  IonicCard,
  IonicButton,
  IonicSelect,
  IonicTabButton,
  IonicToast,
  menuLinkByTitle,
} from "../helpers";
import * as Env from "../config/e2eConfig";
import Page from "./page";
import { getUrl, waitUntilUrl } from "../helpers/browser";
import { menuDebugLink } from "../helpers/index";

class Debug extends Page {
  async openPage() {
    await waitUntilUrl("/app/tabs/browse");
    await menuDebugLink();
    await waitUntilUrl("/app/debug");
    return Promise.resolve();
  }

  async open(id: string) {
    await pause(Env.SHORT_SLEEP);
    const button = new IonicButton(id);
    await button.tap();
    await pause(Env.SHORT_SLEEP);
    const toast = await new IonicToast();
    if (toast) toast.close();
    await pause(Env.SHORT_SLEEP);
  }
}

export default new Debug();
