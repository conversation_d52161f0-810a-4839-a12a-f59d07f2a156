import {
  Ionic$,
  IonicCard,
  IonicSelect,
  IonicTabButton,
  IonicToast, menuLinkByTitle } from "../helpers";

import Page from "./page";
import { getUrl, waitUntilUrl } from '../helpers/browser';

class Viewed extends Page {
  get IViewed() {
    return IonicTabButton.withTitle("I Viewed");
  }

  get ViewedMe() {
    return IonicTabButton.withTitle("Viewed Me");
  }

  get toast() {
    return new IonicToast();
  }

  async openPage(closeToast: boolean = false) {
    await waitUntilUrl("/app/tabs/browse");
    await menuLinkByTitle("Viewed My Profile");
    await waitUntilUrl("/app/my-lists/viewed-me");
    if (closeToast) {
      const toast = await this.toast;
      await toast.close();
    }
    return Promise.resolve();
  }

  async openIViewedTab() {
    const tab = await this.IViewed;
    await expect(await tab.$).toBeDisplayed();
    await (await tab.$).click();
    await waitUntilUrl("/app/my-lists/i-viewed");
    await expect((await getUrl()).pathname).toBe("/app/my-lists/i-viewed");
    return Promise.resolve();
  }

  async openViewedMeTab() {
    const tab = await this.ViewedMe;
    await expect(await tab.$).toBeDisplayed();
    await (await tab.$).click();
    await waitUntilUrl("/app/my-lists/viewed-me");
    await expect((await getUrl()).pathname).toBe("/app/my-lists/viewed-me");
    return Promise.resolve();
  }

  async profiles() {
    return await IonicCard.withClassAll("browse-profile");
  }

  async waitForProfiles() {
    const loading = await Ionic$.$(".browse-profiles-wrapper .browse-profile");
    return await loading.waitForDisplayed({
      timeout: 30000,
      interval: 1000,
      timeoutMsg: "Browse profiles not found after 30s",
    });
  }
}

export default new Viewed();
