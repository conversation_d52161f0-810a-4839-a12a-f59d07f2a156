import { menu, IonicInput } from "../helpers";

import { waitUntilUrl } from "../helpers/browser";
import { $, $$ } from "@wdio/globals";

import Page from "./page";

class ProfileEdit extends Page {
  public page_selector: string = "app-my-profile-edit ion-content";
  private groups: any = false;
  public groups_names = new Array(
    "Distance",
    "MY PHOTOS",
    "MY PROFILE HEADLINE IS",
    "ABOUT ME",
    "MY LOCATION",
    "MY AGE",
    "GENDER",
  );

  async openPage() {
    (await menu()).link("/app/my-profile-edit");
    await waitUntilUrl("/app/my-profile-edit");
    await this.waitForPage();
    return Promise.resolve();
  }

  async waitForPage() {
    const loading = await $("app-my-profile-edit");
    return await loading.waitForDisplayed({
      timeout: 10000,
      interval: 1000,
      timeoutMsg: "My Profile Edit not found after 10s",
    });
  }

  async getGroups(): Promise<WebdriverIO.ElementArray> {
    if (this.groups === false) {
      this.groups = await $$("ion-item-group");
    }
    return this.groups;
  }

  get firstName() {
    return new IonicInput("ion-input.test-first-name");
  }

  get profileHeadline() {
    return new IonicInput("ion-input /parent::*");
  }
}

export default new ProfileEdit();
