import {
  Ionic$,
  IonicCard,
  IonicFormError,
  IonicButton,
  IonicInput,
  IonicItem,
  IonicSelect,
} from "../helpers";

import Page from "./page";
import { Link } from "../helpers/ionic";

class Signup extends Page {
  get gender() {
    return new IonicItem("#gender-id");
  }

  async genderValue() {
    return await (await this.gender.$).shadow$('.select-text');
  }

  get looking() {
    return new IonicItem("#looking-id");
  }

  async lookingValue() {
    return await (await this.looking.$).shadow$('.select-text');
  }

  get selectGender() {
    return new IonicSelect('[name="genderId"]');
  }

  get selectLooking() {
    return new IonicSelect('[name="lookingId"]');
  }

  get next() {
    return IonicButton.withTitle("Next");
  }

  get finish() {
    return IonicButton.withTitle("Finish");
  }

  get ageInput() {
    return new IonicInput('ion-input [type="number"]');
  }

  async errorMessage(message: string, partial: boolean = false) {
    return IonicFormError.withTitle(message, partial);
  }

  get username() {
    return new IonicInput('ion-input [placeholder="Username"]');
  }

  get password() {
    return new IonicInput('ion-input [placeholder="Password"]');
  }

  get email() {
    return new IonicInput('ion-input [placeholder="Email"]');
  }

  get cardSpecial() {
    return IonicCard.withClass("consent-special");
  }

  get cardTerms() {
    return IonicCard.withClass("consent-terms");
  }

  get cardPrivacy() {
    return IonicCard.withClass("consent-privacy");
  }

  async specialCheckbox() {
    const card = await this.cardSpecial;
    return await (await card.$).$("ion-checkbox");
  }

  async termsCheckbox() {
    const card = await this.cardTerms;
    return await (await card.$).$("ion-checkbox");
  }

  async privacyCheckbox() {
    const card = await this.cardPrivacy;
    return await (await card.$).$("ion-checkbox");
  }

  async toggleAllCheckboxes() {
    const special = await this.specialCheckbox();
    const terms = await this.termsCheckbox();
    const privacy = await this.privacyCheckbox();
    await special.click();
    await terms.click();
    await privacy.click();
    return await browser.pause(500);
  }

  get termsLink() {
    return Link.withTitle("Read Terms and Conditions");
  }

  get privacyLink() {
    return Link.withTitle("Read Privacy Policy");
  }

  async openTermsPage() {
    const termsLink = await this.termsLink;
    return await (await termsLink.$).click();
  }

  async openPrivacyPage() {
    const privacyLink = await this.privacyLink;
    return await (await privacyLink.$).click();
  }
}

export default new Signup();
