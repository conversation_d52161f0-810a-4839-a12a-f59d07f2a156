{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "esModuleInterop": true, "experimentalDecorators": true, "moduleResolution": "bundler", "importHelpers": true, "lib": ["es2022", "es2021", "es2020", "es2018", "dom"], "resolveJsonModule": true, "emitDecoratorMetadata": true, "target": "es2022", "module": "es2022", "useDefineForClassFields": false, "skipLibCheck": true}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true}}