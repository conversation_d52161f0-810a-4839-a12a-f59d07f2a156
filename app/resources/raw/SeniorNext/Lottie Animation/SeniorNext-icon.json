{"v": "4.8.0", "meta": {"g": "LottieFiles AE 1.0.0", "a": "", "k": "", "d": "", "tc": "none"}, "fr": 30, "ip": 0, "op": 150, "w": 512, "h": 512, "nm": "SeniorNext-json", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "heart5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [100]}, {"t": 149, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [316.791, 124.671, 0], "ix": 2}, "a": {"a": 0, "k": [14.563, 12.522, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 113, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 121, "s": [400, 400, 100]}, {"t": 123, "s": [380, 380, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.65, -0.057], [-0.107, -0.076], [-0.219, -1.16], [0.066, -0.095], [1.832, -0.22], [0.766, 4.749], [-1.189, 1.392], [-0.167, -0.006], [-0.051, -0.199], [0, 0], [0, 0]], "o": [[2.067, 0.071], [1.195, 0.772], [0.453, 2.413], [-5.191, 6.381], [-1.286, -1.459], [-0.537, -2.763], [1.748, -2.049], [4.682, 0.279], [0, 0], [0, 0], [1.297, -1.324]], "v": [[20.603, 5.78], [24.403, 7.065], [26.532, 9.977], [25.155, 15.048], [10.16, 22.957], [2.635, 10.317], [3.617, 4.056], [8.682, 2.084], [14.67, 7.281], [15.047, 8.826], [16.161, 7.69]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.603, 3.233], [1.602, 1.041], [2.562, 0.088], [1.42, -1.084], [4.257, 0.252], [2.267, -2.656], [-0.643, -3.298], [-0.297, -0.323], [0, 0], [0, 0], [-6.417, 7.909]], "o": [[-0.311, -1.655], [-0.214, -0.152], [-1.739, -0.059], [-0.88, -1.962], [-0.423, -0.015], [-1.558, 1.823], [0.947, 5.882], [0, 0], [0, 0], [0.418, -0.027], [0.094, -0.129]], "v": [[28.273, 9.658], [25.388, 5.593], [20.665, 4.01], [15.909, 5.553], [8.738, 0.314], [2.271, 2.906], [0.893, 10.627], [9.15, 24.483], [9.436, 24.794], [9.857, 24.766], [26.573, 16.108]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 2", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "heart4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [100]}, {"t": 149, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [347.075, 205.538, 0], "ix": 2}, "a": {"a": 0, "k": [8.999, 7.882, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 83, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 90, "s": [400, 400, 100]}, {"t": 93, "s": [380, 380, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.103, -0.053], [0.255, 2.916], [-0.698, 0.785], [-0.698, 0], [0, 0], [-0.021, -0.122], [0, 0], [0, 0], [-0.847, 0], [-0.128, -0.102], [-0.082, -0.696], [0.127, -0.159]], "o": [[-0.705, -0.929], [-0.194, -1.617], [0.835, -0.942], [0.237, 0], [2.825, 0.374], [0, 0], [0, 0], [0.731, -0.645], [1.366, 0], [0.676, 0.512], [0.161, 1.42], [-3.448, 3.666]], "v": [[5.76, 14.238], [1.75, 6.221], [2.508, 2.6], [5.339, 1.517], [5.729, 1.535], [9.098, 4.92], [9.288, 6.043], [10.143, 5.289], [12.519, 4.317], [15.119, 5.277], [16.261, 7.098], [15.217, 10.075]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.23, 2.034], [0.949, 0.716], [1.908, 0], [0.759, -0.471], [2.734, 0.363], [0.322, 0], [1.165, -1.312], [-0.239, -1.978], [-0.169, -0.213], [0, 0], [0, 0], [-4.275, 4.551]], "o": [[-0.118, -1.057], [-0.059, -0.051], [-0.858, 0], [-0.334, -0.874], [-0.023, -0.003], [-0.912, 0], [-0.947, 1.069], [0.319, 3.665], [0, 0], [0, 0], [0.26, 0], [0.065, -0.078]], "v": [[17.519, 6.961], [15.91, 4.288], [12.519, 3.049], [10.081, 3.757], [5.89, 0.277], [5.339, 0.25], [1.558, 1.758], [0.489, 6.352], [4.958, 15.276], [5.148, 15.512], [5.465, 15.515], [16.168, 10.913]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 2", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "heart3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [100]}, {"t": 149, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [210.721, 158.034, 0], "ix": 2}, "a": {"a": 0, "k": [11.254, 9.96, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 93, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 101, "s": [400, 400, 100]}, {"t": 103, "s": [380, 380, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.196, 0], [1.438, 0.677], [0.37, 1.401], [-0.015, 0.02], [-1.667, 0], [-0.009, -0.003], [0, 0], [0, 0], [-0.421, 0.153], [-0.5, 0], [-0.133, -0.343], [0.642, -1.227]], "o": [[-2.815, 0], [-2.036, -0.879], [-0.549, -2.087], [0.979, -1.265], [1.155, 0], [0, 0], [0, 0], [-0.8, -4.03], [0.555, -0.233], [2.55, 0], [1.845, 6.198], [-0.804, 0.1]], "v": [[12.31, 18.401], [5.811, 17.368], [2.185, 13.932], [3.158, 9.792], [7.145, 7.887], [9.185, 8.211], [10.252, 8.612], [10.03, 7.494], [13.278, 1.868], [14.87, 1.518], [18.801, 4.908], [17.284, 18.088]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[2.252, 7.56], [3.574, 0], [0.687, -0.289], [-0.267, -4.399], [0.502, 0], [1.537, -1.984], [-0.706, -2.7], [-2.403, -1.036], [-3.045, 0], [-0.103, 0.015], [0, 0], [0, 0]], "o": [[-0.061, -0.175], [-0.671, 0], [-0.183, 0.067], [-0.504, -0.097], [-1.429, 0], [-0.078, 0.102], [0.472, 1.801], [1.612, 0.76], [2.925, 0], [0, 0], [0, 0], [0.166, -0.279]], "v": [[20.005, 4.514], [14.87, 0.25], [12.821, 0.686], [8.659, 6.766], [7.145, 6.619], [2.152, 9.019], [0.956, 14.246], [5.29, 18.524], [12.31, 19.669], [17.778, 19.301], [18.077, 19.257], [18.231, 18.997]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 2", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "heart2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [100]}, {"t": 149, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [137.172, 262.988, 0], "ix": 2}, "a": {"a": 0, "k": [8.877, 8.16, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 103, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 111, "s": [400, 400, 100]}, {"t": 113, "s": [380, 380, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.6, 0], [0.578, 0.165], [0.439, 0.986], [-0.008, 0.017], [-1.126, 0], [-0.039, -0.007], [0, 0], [0, 0], [-0.293, 0.167], [-0.484, 0], [-0.125, -0.212], [0, 0], [0.335, -1.036]], "o": [[-0.819, 0], [-1.627, -0.411], [-0.657, -1.476], [0.877, -1.611], [0.411, 0], [0, 0], [0, 0], [-1.066, -2.862], [0.507, -0.316], [1.649, 0], [0, 0], [2.125, 4.406], [-0.97, 0.28]], "v": [[7.534, 14.803], [5.43, 14.555], [2.316, 12.45], [2.541, 9.279], [6.07, 7.33], [6.808, 7.386], [7.932, 7.612], [7.534, 6.544], [9.268, 1.994], [10.761, 1.518], [13.681, 3.584], [13.682, 3.585], [14.173, 13.7]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[2.699, 5.581], [2.487, 0], [0.689, -0.43], [-0.503, -2.701], [0, 0], [1.237, -2.276], [-0.904, -2.043], [-2.013, -0.507], [-0.941, 0], [-0.161, 0.051], [0, 0], [0, 0]], "o": [[-0.062, -0.112], [-0.725, 0], [-1.111, 0.609], [0, 0], [-1.202, 0], [-0.048, 0.089], [0.605, 1.365], [0.674, 0.193], [3.345, 0], [0, 0], [0, 0], [0.093, -0.237]], "v": [[14.805, 2.998], [10.761, 0.25], [8.628, 0.9], [6.087, 6.061], [6.065, 6.061], [1.425, 8.678], [1.154, 12.958], [5.1, 15.779], [7.534, 16.071], [14.856, 14.82], [15.144, 14.728], [15.255, 14.447]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 2", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "heart1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [100]}, {"t": 149, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [186.969, 220.203, 0], "ix": 2}, "a": {"a": 0, "k": [5.816, 5.351, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 75, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 83, "s": [400, 400, 100]}, {"t": 85, "s": [380, 380, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.63, 0], [0.368, 0.105], [0.272, 0.612], [-0.006, 0.01], [-0.703, 0], [-0.031, -0.005], [0, 0], [0, 0], [-0.077, 0.043], [-0.298, 0], [-0.081, -0.137], [0.222, -0.695]], "o": [[-0.521, 0], [-1.02, -0.259], [-0.401, -0.904], [0.546, -1.003], [0.253, 0], [0, 0], [0, 0], [-0.679, -1.828], [0.314, -0.195], [1.018, 0], [1.328, 2.761], [-0.636, 0.182]], "v": [[4.962, 9.532], [3.625, 9.374], [1.677, 8.061], [1.821, 6.088], [4.021, 4.874], [4.481, 4.909], [5.299, 5.075], [5.012, 4.3], [6.098, 1.465], [7.02, 1.171], [8.834, 2.461], [9.145, 8.843]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[1.734, 3.598], [1.628, 0], [0.447, -0.278], [-0.283, -1.72], [0.785, -1.438], [-0.583, -1.32], [-1.3, -0.328], [-0.607, 0], [-0.264, 0.082], [0, 0], [0, 0]], "o": [[-0.04, -0.073], [-0.471, 0], [-0.712, 0.388], [-0.794, 0.013], [-0.031, 0.058], [0.391, 0.888], [0.44, 0.126], [2.056, 0], [0, 0], [0, 0], [0.06, -0.152]], "v": [[9.649, 2.034], [7.02, 0.25], [5.634, 0.67], [3.97, 3.954], [1.01, 5.649], [0.833, 8.429], [3.384, 10.262], [4.962, 10.452], [9.641, 9.657], [9.85, 9.591], [9.931, 9.387]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 2", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "tree", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [100]}, {"t": 149, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [243.578, 327.1, 0], "ix": 2}, "a": {"a": 0, "k": [26.113, 47.551, 0], "ix": 1}, "s": {"a": 0, "k": [376.921, 376.921, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, -35.898], [35.898, 0], [0, 35.899], [-35.898, 0]], "o": [[0, 35.899], [-35.898, 0], [0, -35.898], [35.898, 0]], "v": [[94.884, 28.541], [29.884, 93.541], [-35.116, 28.541], [29.884, -36.459]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [-2.807, 1.312], [0, 0], [0, 0], [-0.209, 0.008], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.239, -0.579], [0.756, -0.008], [0.279, -0.016], [0.231, 0.008], [0.191, -0.013], [0.172, -0.01], [0.172, -0.021], [0.162, -0.018], [0.141, -0.017], [0, 0], [0.112, -0.018], [0.227, -0.016], [0.28, 0.037], [0.151, 0.004], [0.157, 0.002], [0.112, 0.001], [0.086, 0.009], [6.45, 0.463], [0, 0]], "v": [[17.435, 97.24], [22.126, 95.046], [24.64, 94.484], [26.212, 94.484], [27.263, 94.46], [29.077, 94.455], [29.743, 94.469], [30.332, 94.463], [30.822, 94.477], [31.325, 94.468], [31.753, 94.47], [32.141, 94.469], [32.565, 94.451], [33.109, 94.472], [33.719, 94.53], [34.334, 94.532], [34.976, 94.541], [35.774, 94.557], [36.5, 94.603], [45.555, 97.24]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [-2.807, 1.312], [0, 0], [0, 0], [-0.209, 0.008], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.239, -0.579], [0.756, -0.008], [0.279, -0.016], [0.231, 0.008], [0.191, -0.013], [0.172, -0.01], [0.172, -0.021], [0.162, -0.018], [0.141, -0.017], [0, 0], [0.112, -0.018], [0.227, -0.016], [0.28, 0.037], [0.151, 0.004], [0.157, 0.002], [0.112, 0.001], [0.086, 0.009], [6.45, 0.463], [0, 0]], "v": [[17.435, 94.852], [22.126, 92.659], [24.64, 92.096], [26.212, 92.096], [27.263, 92.072], [29.077, 92.067], [29.743, 92.081], [30.332, 92.075], [30.822, 92.09], [31.325, 92.08], [31.753, 92.083], [32.141, 92.081], [32.565, 92.063], [33.109, 92.084], [33.719, 92.142], [34.334, 92.144], [34.976, 92.153], [35.774, 92.17], [36.5, 92.215], [45.555, 94.852]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [{"i": [[0, 0], [-2.807, 1.312], [0, 0], [0, 0], [-0.209, 0.008], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.239, -0.579], [0.756, -0.008], [0.279, -0.016], [0.231, 0.008], [0.191, -0.013], [0.172, -0.01], [0.172, -0.021], [0.162, -0.018], [0.141, -0.017], [0, 0], [0.112, -0.018], [0.227, -0.016], [0.28, 0.037], [0.151, 0.004], [0.157, 0.002], [0.112, 0.001], [0.086, 0.009], [6.45, 0.463], [0, 0]], "v": [[17.435, 94.852], [22.126, 92.659], [24.64, 92.096], [26.212, 92.096], [27.263, 92.072], [29.077, 92.067], [29.743, 92.081], [30.332, 92.075], [30.822, 92.09], [31.325, 92.08], [31.753, 92.083], [32.141, 92.081], [32.565, 92.063], [33.109, 92.084], [33.719, 92.142], [34.334, 92.144], [34.976, 92.153], [35.774, 92.17], [36.5, 92.215], [45.555, 94.852]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [-3.76, 8.78], [0, 0], [0, 0], [-0.068, 0.82], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [3.762, -8.78], [0.325, -1.725], [0.213, -0.679], [0.193, -1.448], [-0.025, -0.717], [0.081, -0.839], [0.114, -0.195], [0.112, -0.176], [0.265, -0.316], [0, 0], [0.187, -0.167], [0.84, 0.166], [0.844, 1.173], [0.698, 1.347], [0.514, 1.246], [0.005, 12.778], [-1.103, 5.351], [6.45, 0.463], [0, 0]], "v": [[17.435, 94.852], [28.195, 82.975], [32.915, 65.532], [33.906, 61.536], [34.733, 58.328], [35.47, 55.422], [36.359, 53.172], [36.832, 51.765], [37.19, 51.166], [37.452, 50.809], [37.888, 50.396], [38.243, 50.096], [38.924, 49.655], [40.272, 50.166], [41.479, 51.948], [42.559, 54.603], [43.068, 56.9], [42.083, 74.021], [36.5, 92.248], [45.555, 94.852]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [{"i": [[0, 0], [-3.76, 8.78], [0, 0], [0, 0], [-0.068, 0.82], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [3.762, -8.78], [0.325, -1.725], [0.213, -0.679], [0.193, -1.448], [-0.025, -0.717], [0.354, -1.884], [-0.657, -2.549], [-0.766, -3.476], [1.261, -4.859], [0, 0], [1.588, 1.963], [1.255, 1.907], [3.084, 3.905], [0.698, 1.347], [0.514, 1.246], [1.599, 13.067], [-1.103, 5.351], [6.45, 0.463], [0, 0]], "v": [[17.435, 94.852], [28.195, 82.975], [33.578, 59.961], [34.038, 57.821], [34.6, 55.41], [34.806, 53.299], [34.9, 51.448], [34.444, 42.778], [33.492, 39.592], [32.361, 35.487], [20.386, 7.715], [31.668, 21.949], [33.593, 23.995], [34.966, 26.288], [42.142, 39.877], [43.222, 42.134], [44.129, 44.696], [42.083, 74.021], [36.5, 92.248], [45.555, 94.852]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[0, 0], [-3.76, 8.78], [0, 0], [0, 0], [2.218, 2.133], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [3.762, -8.78], [0, -3.239], [-18.527, -8.071], [-2.219, -2.134], [15.407, 13.279], [1.348, -2.082], [-0.657, -2.549], [-0.766, -3.476], [1.261, -4.859], [0, 0], [1.588, 1.963], [1.255, 1.907], [3.084, 3.905], [0.698, 1.347], [0.514, 1.246], [1.599, 13.067], [-1.103, 5.351], [6.45, 0.463], [0, 0]], "v": [[17.435, 94.852], [28.195, 82.975], [33.578, 59.961], [29.064, 55.102], [5.284, 41.216], [0.25, 34.446], [34.444, 51.456], [34.444, 42.778], [33.492, 39.592], [32.361, 35.487], [20.386, 7.715], [31.668, 21.949], [33.593, 23.995], [34.966, 26.288], [42.142, 39.877], [43.222, 42.134], [44.129, 44.696], [42.083, 74.021], [36.5, 92.248], [45.555, 94.852]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [{"i": [[0, 0], [-3.76, 8.78], [0, 0], [0, 0], [2.218, 2.133], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [3.762, -8.78], [0, -3.239], [-18.527, -8.071], [-2.219, -2.134], [15.407, 13.279], [1.348, -2.082], [-0.657, -2.549], [-0.766, -3.476], [1.261, -4.859], [0, 0], [1.588, 1.963], [1.255, 1.907], [3.084, 3.905], [3.72, -2.112], [-0.91, 16.056], [1.599, 13.067], [-1.103, 5.351], [6.45, 0.463], [0, 0]], "v": [[17.435, 94.852], [28.195, 82.975], [33.578, 59.961], [29.064, 55.102], [5.284, 41.216], [0.25, 34.446], [34.444, 51.456], [34.444, 42.778], [33.492, 39.592], [32.361, 35.487], [20.386, 7.715], [31.668, 21.949], [33.593, 23.995], [34.966, 26.288], [41.446, 39.943], [51.977, 17.261], [44.129, 44.696], [42.083, 74.021], [36.5, 92.248], [45.555, 94.852]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [{"i": [[0, 0], [-3.76, 8.78], [0, 0], [0, 0], [2.218, 2.133], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [3.762, -8.78], [0, -3.239], [-18.527, -8.071], [-2.219, -2.134], [15.407, 13.279], [1.348, -2.082], [-15.489, -8.852], [6.507, 7.323], [1.261, -4.859], [0, 0], [1.588, 1.963], [1.255, 1.907], [3.084, 3.905], [3.72, -2.112], [-0.91, 16.056], [1.599, 13.067], [-1.103, 5.351], [6.45, 0.463], [0, 0]], "v": [[17.435, 94.852], [28.195, 82.975], [33.578, 59.961], [29.064, 55.102], [5.284, 41.216], [0.25, 34.446], [34.444, 51.456], [34.444, 42.778], [12.4, 21.949], [32.361, 35.487], [20.386, 7.715], [31.668, 21.949], [33.593, 23.995], [34.966, 26.288], [41.446, 39.943], [51.977, 17.261], [44.129, 44.696], [42.083, 74.021], [36.5, 92.248], [45.555, 94.852]], "c": true}]}, {"t": 75, "s": [{"i": [[0, 0], [-3.76, 8.78], [0, 0], [0, 0], [2.218, 2.133], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [3.762, -8.78], [0, -3.239], [-18.527, -8.071], [-2.219, -2.134], [15.407, 13.279], [1.348, -2.082], [-15.489, -8.852], [6.507, 7.323], [1.261, -4.859], [0, 0], [5.338, -2.257], [0.566, 9.114], [3.084, 3.905], [3.72, -2.112], [-0.91, 16.056], [1.599, 13.067], [-1.103, 5.351], [6.45, 0.463], [0, 0]], "v": [[17.435, 94.852], [28.195, 82.975], [33.578, 59.961], [29.064, 55.102], [5.284, 41.216], [0.25, 34.446], [34.444, 51.456], [34.444, 42.778], [12.4, 21.949], [32.361, 35.487], [20.386, 7.715], [31.668, 21.949], [42.083, 0.25], [34.966, 26.288], [41.446, 39.943], [51.977, 17.261], [44.129, 44.696], [42.083, 74.021], [36.5, 92.248], [45.555, 94.852]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "shadow", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 130, "s": [10]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [10]}, {"t": 149, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [309.8, 290.73, 0], "ix": 2}, "a": {"a": 0, "k": [53.95, 55.77, 0], "ix": 1}, "s": {"a": 0, "k": [376.921, 376.921, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, -35.898], [35.898, 0], [0, 35.899], [-35.898, 0]], "o": [[0, 35.899], [-35.898, 0], [0, -35.898], [35.898, 0]], "v": [[105.546, 46.117], [40.546, 111.117], [-24.454, 46.117], [40.546, -18.883]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.381, -1.738], [-3.928, 0], [0, 35.899], [0.006, 0.378], [0.127, 0.116], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.178, 0.161], [0.584, 0.38], [2.563, 0.088], [1.272, -0.81], [0.121, 0.11], [0.177, 0.162], [0.177, 0.162], [0.177, 0.162], [0.177, 0.162], [2.493, 0.147], [2.267, -2.656], [-0.643, -3.298], [-0.297, -0.322], [0, 0], [0.467, -1.218], [0.073, 0.066], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [0.18, 0.161], [1.603, 0], [0.687, -0.288], [-0.267, -4.399], [0.501, 0], [1.537, -1.984], [-0.706, -2.7], [-0.854, -0.767], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.18, -0.162], [-0.095, -0.093], [0.001, -0.008], [0.182, 0.156], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [0.195, 0.167], [1.028, 0], [0.447, -0.278], [-0.283, -1.72], [0.785, -1.438], [-0.583, -1.32], [-0.364, -0.289], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.192, -0.152], [-0.085, -0.085], [3.056, 1.099], [0.045, 0.04], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [0.186, 0.167], [1.449, 0], [0.688, -0.431], [-0.502, -2], [0, 0], [1.237, -2.276], [-0.905, -2.043], [-0.476, -0.412], [-0.182, -0.158], [-0.182, -0.157], [-0.182, -0.157], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.182, -0.158], [-0.179, -0.156], [0.695, -1.623]], "o": [[3.736, 0.668], [35.899, 0], [0, -0.379], [-0.116, -0.121], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.416, -0.464], [-0.214, -0.152], [-1.507, -0.052], [-0.113, -0.115], [-0.158, -0.171], [-0.158, -0.171], [-0.158, -0.171], [-0.158, -0.171], [-1.184, -1.279], [-0.423, -0.016], [-1.558, 1.823], [0.947, 5.882], [0, 0], [-0.387, 1.141], [-0.069, -0.068], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.154, -0.172], [-0.826, -0.923], [-0.671, 0], [-0.183, 0.068], [-0.504, -0.098], [-1.43, 0], [-0.077, 0.102], [0.26, 0.995], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.154, 0.172], [0.086, 0.096], [-0.001, 0.007], [-0.128, -0.152], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.133, -0.161], [-0.432, -0.521], [-0.471, 0], [-0.711, 0.388], [-0.794, 0.012], [-0.031, 0.058], [0.177, 0.4], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.14, 0.182], [0.071, 0.093], [-1.576, -0.404], [-0.043, -0.04], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.147, -0.164], [-0.688, -0.768], [-0.725, 0], [-1.112, 0.608], [0, 0], [-1.202, 0], [-0.049, 0.09], [0.243, 0.549], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.178], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.148, 0.177], [0.147, 0.176], [-0.552, 1.871], [-2.292, 5.354]], "v": [[31.395, 110.504], [42.9, 111.54], [107.9, 46.54], [107.872, 45.409], [107.514, 45.051], [107.014, 44.551], [106.514, 44.051], [106.014, 43.551], [105.514, 43.051], [105.014, 42.551], [104.514, 42.051], [104.014, 41.551], [103.514, 41.051], [103.014, 40.551], [102.514, 40.051], [102.014, 39.551], [101.514, 39.051], [101.014, 38.551], [100.514, 38.051], [100.014, 37.551], [99.514, 37.051], [99.014, 36.551], [98.514, 36.051], [98.014, 35.551], [97.514, 35.051], [97.014, 34.551], [96.514, 34.051], [96.014, 33.551], [95.514, 33.051], [95.014, 32.551], [94.514, 32.051], [94.014, 31.551], [93.514, 31.051], [93.014, 30.551], [92.514, 30.051], [92.014, 29.551], [91.514, 29.051], [91.014, 28.551], [90.514, 28.051], [90.014, 27.551], [89.514, 27.051], [89.014, 26.551], [88.514, 26.051], [88.014, 25.551], [87.514, 25.051], [87.014, 24.551], [86.514, 24.051], [86.014, 23.551], [85.514, 23.051], [85.014, 22.551], [84.514, 22.051], [84.014, 21.551], [83.514, 21.051], [83.014, 20.551], [82.514, 20.051], [82.014, 19.551], [81.514, 19.051], [81.014, 18.551], [80.514, 18.051], [80.014, 17.551], [79.514, 17.051], [79.014, 16.551], [78.514, 16.051], [78.014, 15.551], [77.514, 15.051], [77.014, 14.551], [76.514, 14.051], [76.014, 13.551], [75.514, 13.051], [75.014, 12.551], [74.514, 12.051], [74.014, 11.551], [73.514, 11.051], [73.014, 10.551], [72.514, 10.051], [72.014, 9.551], [71.514, 9.051], [71.014, 8.551], [70.514, 8.051], [70.014, 7.551], [69.514, 7.051], [69.014, 6.551], [67.516, 5.282], [62.792, 3.7], [56.895, 5.2], [56.544, 4.862], [56.044, 4.362], [55.544, 3.862], [55.044, 3.362], [54.544, 2.862], [50.866, 0.004], [44.399, 2.595], [43.021, 10.316], [51.278, 24.172], [51.346, 24.246], [50.062, 27.812], [49.85, 27.611], [49.35, 27.111], [48.85, 26.611], [48.35, 26.111], [47.85, 25.611], [47.35, 25.111], [46.85, 24.611], [46.35, 24.111], [45.85, 23.611], [45.35, 23.111], [44.85, 22.611], [44.35, 22.111], [43.85, 21.611], [43.35, 21.111], [42.85, 20.611], [42.35, 20.111], [41.85, 19.611], [41.35, 19.111], [40.85, 18.611], [40.35, 18.111], [39.85, 17.611], [39.35, 17.111], [38.85, 16.611], [38.35, 16.111], [37.85, 15.611], [37.35, 15.111], [36.85, 14.611], [36.35, 14.111], [35.85, 13.611], [35.35, 13.111], [34.85, 12.611], [31.239, 10.865], [29.189, 11.3], [25.027, 17.381], [23.514, 17.234], [18.52, 19.634], [17.325, 24.86], [19.011, 27.501], [19.511, 28.001], [20.011, 28.501], [20.511, 29.001], [21.011, 29.501], [21.511, 30.001], [22.011, 30.501], [22.511, 31.001], [23.011, 31.501], [23.511, 32.001], [24.011, 32.501], [24.511, 33.001], [25.011, 33.501], [25.511, 34.001], [26.011, 34.501], [26.511, 35.001], [27.011, 35.501], [27.511, 36.001], [28.011, 36.501], [28.511, 37.001], [29.011, 37.501], [29.511, 38.001], [30.011, 38.501], [30.511, 39.001], [31.011, 39.501], [31.511, 40.001], [32.011, 40.501], [32.511, 41.001], [33.011, 41.501], [33.511, 42.001], [34.011, 42.501], [34.511, 43.001], [35.011, 43.501], [35.511, 44.001], [36.011, 44.501], [36.511, 45.001], [37.011, 45.501], [37.511, 46.001], [38.011, 46.501], [38.511, 47.001], [39.011, 47.501], [39.511, 48.001], [40.011, 48.501], [40.511, 49.001], [41.011, 49.501], [41.511, 50.001], [42.011, 50.501], [42.511, 51.001], [43.011, 51.501], [43.511, 52.001], [43.77, 52.289], [43.766, 52.312], [43.293, 51.844], [42.793, 51.344], [42.293, 50.844], [41.793, 50.344], [41.293, 49.844], [40.793, 49.344], [40.293, 48.844], [39.793, 48.344], [39.293, 47.844], [38.793, 47.344], [38.293, 46.844], [37.793, 46.344], [37.293, 45.844], [36.793, 45.344], [36.293, 44.844], [35.793, 44.344], [35.293, 43.844], [34.793, 43.344], [34.293, 42.844], [33.793, 42.344], [33.293, 41.844], [32.793, 41.344], [32.293, 40.844], [31.793, 40.344], [31.293, 39.844], [30.793, 39.344], [30.293, 38.844], [29.793, 38.344], [29.293, 37.844], [28.793, 37.344], [28.293, 36.844], [27.793, 36.344], [27.293, 35.844], [26.793, 35.344], [26.293, 34.844], [25.793, 34.344], [22.844, 32.412], [21.458, 32.832], [19.794, 36.116], [16.834, 37.811], [16.657, 40.591], [18.212, 42.347], [18.712, 42.847], [19.212, 43.347], [19.712, 43.847], [20.212, 44.347], [20.712, 44.847], [21.212, 45.347], [21.712, 45.847], [22.212, 46.347], [22.712, 46.847], [23.212, 47.347], [23.712, 47.847], [24.212, 48.347], [24.712, 48.847], [25.212, 49.347], [25.712, 49.847], [26.212, 50.347], [26.712, 50.847], [27.212, 51.347], [27.712, 51.847], [28.212, 52.347], [28.712, 52.847], [29.212, 53.347], [29.712, 53.847], [30.212, 54.347], [30.712, 54.847], [31.212, 55.347], [31.712, 55.847], [32.212, 56.347], [32.712, 56.847], [33.212, 57.347], [33.712, 57.847], [34.212, 58.347], [34.712, 58.847], [35.212, 59.347], [35.712, 59.847], [36.212, 60.347], [36.712, 60.847], [37.212, 61.347], [37.712, 61.847], [38.212, 62.347], [38.712, 62.847], [39.212, 63.347], [39.712, 63.847], [40.212, 64.347], [40.712, 64.847], [41.212, 65.347], [41.712, 65.847], [42.212, 66.347], [42.712, 66.847], [43.212, 67.347], [43.712, 67.847], [43.954, 68.108], [36.834, 65.899], [36.703, 65.779], [36.203, 65.279], [35.703, 64.779], [35.203, 64.279], [34.703, 63.779], [34.203, 63.279], [33.703, 62.779], [33.203, 62.279], [32.703, 61.779], [32.203, 61.279], [31.703, 60.779], [31.203, 60.279], [30.703, 59.779], [30.203, 59.279], [29.703, 58.779], [29.203, 58.279], [28.703, 57.779], [28.203, 57.279], [27.703, 56.779], [27.203, 56.279], [26.703, 55.779], [26.203, 55.279], [25.703, 54.779], [25.203, 54.279], [24.703, 53.779], [24.203, 53.279], [23.703, 52.779], [23.203, 52.279], [22.703, 51.779], [22.203, 51.279], [21.703, 50.779], [21.203, 50.279], [20.703, 49.779], [20.203, 49.279], [19.703, 48.779], [19.203, 48.279], [18.703, 47.779], [18.203, 47.279], [17.703, 46.779], [17.203, 46.279], [16.703, 45.779], [16.203, 45.279], [15.703, 44.779], [15.203, 44.279], [14.703, 43.779], [14.203, 43.279], [13.703, 42.779], [13.203, 42.279], [9.957, 40.617], [7.824, 41.108], [5.282, 46.109], [5.26, 46.109], [0.621, 48.885], [0.35, 53.246], [1.435, 54.726], [1.935, 55.246], [2.435, 55.756], [2.935, 56.261], [3.435, 56.764], [3.935, 57.265], [4.435, 57.766], [4.935, 58.266], [5.435, 58.766], [5.935, 59.266], [6.435, 59.766], [6.935, 60.266], [7.435, 60.766], [7.935, 61.266], [8.435, 61.766], [8.935, 62.266], [9.435, 62.766], [9.935, 63.266], [10.435, 63.766], [10.935, 64.266], [11.435, 64.766], [11.935, 65.266], [12.435, 65.766], [12.935, 66.266], [13.435, 66.766], [13.935, 67.266], [14.435, 67.766], [14.935, 68.266], [15.435, 68.766], [15.935, 69.266], [16.435, 69.766], [16.935, 70.266], [17.435, 70.766], [17.935, 71.266], [18.435, 71.766], [18.935, 72.266], [19.435, 72.766], [19.935, 73.266], [20.435, 73.766], [20.935, 74.266], [21.435, 74.766], [21.935, 75.266], [22.435, 75.766], [22.935, 76.266], [23.435, 76.766], [23.935, 77.266], [24.435, 77.766], [24.935, 78.266], [25.435, 78.766], [25.935, 79.266], [26.435, 79.766], [26.935, 80.266], [27.435, 80.766], [27.935, 81.266], [28.435, 81.766], [28.935, 82.266], [29.435, 82.766], [29.935, 83.266], [30.435, 83.766], [30.935, 84.266], [31.435, 84.766], [31.935, 85.266], [32.435, 85.766], [32.935, 86.266], [33.435, 86.766], [33.935, 87.266], [34.435, 87.766], [34.935, 88.266], [35.435, 88.766], [35.935, 89.266], [36.435, 89.766], [36.935, 90.266], [37.435, 90.766], [37.935, 91.266], [38.435, 91.766], [38.935, 92.266], [39.435, 92.766], [39.935, 93.266], [40.435, 93.766], [40.935, 94.266], [41.429, 94.761], [39.56, 100.149]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "background-circle", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [100]}, {"t": 149, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [257, 253, 0], "ix": 2}, "a": {"a": 0, "k": [65.25, 65.25, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 10, "s": [390, 390, 100]}, {"t": 15, "s": [380, 380, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -35.898], [35.898, 0], [0, 35.899], [-35.898, 0]], "o": [[0, 35.899], [-35.898, 0], [0, -35.898], [35.898, 0]], "v": [[65, 0], [0, 65], [-65, 0], [0, -65]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.937254901961, 0.38431372549, 0.313725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [65.25, 65.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}], "markers": []}