module.exports = function getConfig() {
  return {
    // which one is the max depth the script should have access to
    // this will prevent the script to delete, modify files outside of it's scope
    // if you're running the script from a sub directory of the project, then you can
    // set this to for example "../" relative path. "/" is not a valid value (empty means current path)
    secure_base_path: "../",

    // where are the separate project directories
    // relative to mpm.config.json file
    project_root: "../projects/apps",

    // config that will be used to find/replace stuff in certain files
    // json file with specific format
    project_config_file: "[project_path]/config[env].json",

    // where to copy project config file when generating
    // json file with specific format
    project_config_destination:
      "[destination_root]/src/environments/config.json",

    // where is the destination root
    // relative to mpm.config.json file
    destination_root: ".",

    // where are common files stored
    // relative to mpm.config.json file
    common_root: "../projects/common",

    // a common config file we'll be using with every project
    common_config_file: "[common_root]/common.config.json",

    // file and directory list that will be deleted and then softlinked
    // these files will also be used to create new projects
    // simple file copy / link only, no modifications - mostly per project files
    file_list_simple: [
      // ["[project_path]/", "[destination_root]/"],

      // assets

      {
        src: "[project_path]/src/custom.scss",
        dest: "[destination_root]/src/custom.scss",
        type: "link",
      },
      {
        src: "[project_path]/src/theme/variables.scss",
        dest: "[destination_root]/src/theme/variables.scss",
        type: "link",
      },
      {
        src: "[project_path]/src/assets/json",
        dest: "[destination_root]/src/assets/json",
        type: "link",
      },
      {
        src: "[project_path]/src/assets/icon",
        dest: "[destination_root]/src/assets/icon",
        type: "link",
      },
      {
        src: "[project_path]/src/assets/icons",
        dest: "[destination_root]/src/assets/icons",
        type: "link",
      },
      {
        src: "[project_path]/src/assets/img",
        dest: "[destination_root]/src/assets/img",
        type: "link",
      },
      {
        src: "[common_root]/common.config.json",
        dest: "[destination_root]/src/environments/common.config.json",
        type: "link",
      },

      // google service json - per application stuff

      {
        src: "[project_path]/android/app/google-services[env].json",
        dest: "[destination_root]/android/app/google-services.json",
        type: "copy",
      },

      {
        src: "[project_path]/ios/App/App/GoogleService-Info[env].plist",
        dest: "[destination_root]/ios/App/App/GoogleService-Info.plist",
        type: "copy",
      },

      // android res folder per application

      {
        src: "[project_path]/android/app/src/main/res",
        dest: "[destination_root]/android/app/src/main/res",
        type: "copy",
      },

      {
        src: "[project_path]/android/app/src/main/ic_launcher-playstore.png",
        dest: "[destination_root]/android/app/src/main/ic_launcher-playstore.png",
        type: "copy",
      },

      // ios res folder per application

      {
        src: "[project_path]/ios/App/App/Assets.xcassets/AppIcon.appiconset",
        dest: "[destination_root]/ios/App/App/Assets.xcassets/AppIcon.appiconset",
        type: "copy",
      },
    ],

    // file liest used for cleanup, all files here will be delted during checkout
    file_list_delete: ["[destination_root]/android/app/src/main/java/com"],

    // copy and modify files
    // this is used to copy files that have variables in them
    // these files are usually kept in a common place, and copied to project when
    // changed, also if we change the content of them, we can update them in common forlder or wherever
    // ... mostly files that will be modded for each project, and used from a common place - not per project stuff
    // generated files so to speak, but it can do copy only if there's not replace_rules defined
    file_list_mod: [
      {
        src: "[common_root]/android/app/src/main/res/values/strings.xml",
        dest: "[destination_root]/android/app/src/main/res/values/strings.xml",

        replace_rules: (config) => {
          return [
            {
              find: /(<string.*name="app_name"[^>]*>)[^>]*</gm, // find what to replace
              replace_variable: "$1" + config["APP_NAME"] + "<", // used when applied
              replace_placeholder: "$1[app_name]" + "<", // used for placeholder in global app
            },
            {
              find: /(<string.*name="title_activity_main"[^>]*>)[^>]*</gm,
              replace_variable: "$1" + config["APP_NAME"] + "<",
              replace_placeholder: "$1[title_main_activity]" + "<",
            },
            {
              find: /(<string.*name="package_name"[^>]*>)[^>]*</gm,
              replace_variable: "$1" + config["APP_STORE_ID"] + "<",
              replace_placeholder: "$1[app_store_id]" + "<",
            },
            {
              find: /(<string.*name="uri_scheme"[^>]*>)[^>]*</gm,
              replace_variable: "$1" + config["URI_SCHEME"] + "<",
              replace_placeholder: "$1[uri_scheme]" + "<",
            },
            {
              find: /(<string.*name="deeplink_host"[^>]*>)[^>]*</gm,
              replace_variable: "$1*." + config["DEEPLINK_HOST"] + "<",
              replace_placeholder: "$1[deeplink_host]" + "<",
            },
            {
              find: /(<string.*name="app_store_id"[^>]*>)[^>]*</gm,
              replace_variable: "$1" + config["APP_STORE_ID"] + "<",
              replace_placeholder: "$1[app_store_id]" + "<",
            },
            {
              find: /(<string.*name="app_activity_id"[^>]*>)[^>]*</gm,
              replace_variable:
                "$1" + config["APP_STORE_ID"] + ".MainActivity<",
              replace_placeholder: "$1[app_activity_id]" + "<",
            },
          ];
        },
      },

      {
        src: "[common_root]/android/app/build.gradle",
        dest: "[destination_root]/android/app/build.gradle",

        replace_rules: (config) => {
          return [
            {
              find: /(^.*applicationId\ ")(.*)"/gm,
              replace_variable: "$1" + config["APP_STORE_ID"] + '"',
              replace_placeholder: '$1[app_store_id]"',
            },
            {
              find: /(^.*namespace\ ")(.*)"/gm,
              replace_variable: "$1" + config["APP_STORE_ID"] + '"',
              replace_placeholder: '$1[namespace]"',
            },
            {
              find: /(^.*versionCode\ )(.*)$/gm,
              replace_variable: "$1" + config["APP_ANDROID_VERSION"],
              replace_placeholder: "$1[app_android_version]",
            },
            {
              find: /(^.*versionName\ ")(.*)"$/gm,
              replace_variable: "$1" + config["APP_VERSION"] + '"',
              replace_placeholder: "$1[app_version]" + '"',
            },
          ];
        },
      },

      {
        src: "[common_root]/android/app/src/main/AndroidManifest.xml",
        dest: "[destination_root]/android/app/src/main/AndroidManifest.xml",

        replace_rules: (config) => {
          return [
            {
              find: /(<manifest.*package=")[^"]*"/ms,
              replace_variable: "$1" + config["APP_STORE_ID"] + '"',
              replace_placeholder: "$1[app_store_id]" + '"',
            },
            {
              find: /(<activity[^<]*android:name=")[^"]*"/ms,
              replace_variable:
                "$1" + config["APP_STORE_ID"] + '.MainActivity"',
              replace_placeholder: '$1[app_activity_id]"',
            },
          ];
        },
      },

      {
        src: "[common_root]/ios/App/App/App.entitlements",
        dest: "[destination_root]/ios/App/App/App.entitlements",

        replace_rules: (config) => {
          return [
            {
              find: /(com\.apple\.developer\.associated-domains<\/key>[^<]*<array>)(.*)(<\/array>)/gms,
              replace_variable:
                "$1" +
                `
              <string>applinks:${config["APP_STORE_APP_NAME"]}.onelink.me</string>
              <string>applinks:${config["URI_SCHEME"]}.onelink.me</string>
              <string>applinks:${config["URI_SCHEME"]}</string>
              <string>applinks:${config["DEEPLINK_HOST"]}</string>
              <string>applinks:*.${config["DEEPLINK_HOST"]}</string>
              <string>webcredentials:${config["DEEPLINK_HOST"]}</string>
              ` +
                "$3",
              replace_placeholder: "$1[deeplink_config]$3",
            },
          ];
        },
      },

      {
        src: "[common_root]/android/MainActivity.java",
        dest: "[destination_root]/android/app/src/main/java/com/[_config:APP_STORE_COMPANY]/[_config:APP_STORE_APP_NAME]/MainActivity.java",

        replace_rules: (config) => {
          return [
            {
              find: /(package )[^;\n]*/ms,
              replace_variable: "$1" + config["APP_STORE_ID"],
              replace_placeholder: "$1[app_store_id]",
            },
          ];
        },
      },

      {
        src: "[common_root]/ios/project.pbxproj",
        dest: "[destination_root]/ios/App/App.xcodeproj/project.pbxproj",

        replace_rules: (config) => {
          return [
            {
              find: /(PRODUCT_BUNDLE_IDENTIFIER =)[^;]*/gms,
              replace_variable: "$1 " + config["APP_STORE_ID"],
              replace_placeholder: "$1 [app_store_id]",
            },
            {
              find: /(IPHONEOS_DEPLOYMENT_TARGET =)[^;]*/gms,
              replace_variable: '$1 "' + config["IOS_BUILD_TARGET"] + '"',
              replace_placeholder: "$1 [ios_sdk_version]",
            },
          ];
        },
      },

      {
        src: "[common_root]/ios/App/App/Info.plist",
        dest: "[destination_root]/ios/App/App/Info.plist",

        replace_rules: (config) => {
          return [
            {
              find: /(CFBundleDisplayName[^>]*>[^<]*<string>)[^<]*/gms,
              replace_variable: "$1" + config["APP_NAME"],
              replace_placeholder: "$1[app_name]",
            },
            {
              find: /(CFBundleShortVersionString[^>]*>[^<]*<string>)[^<]*/gms,
              replace_variable: "$1" + config["APP_VERSION"],
              replace_placeholder: "$1[app_version]",
            },
            {
              find: /(CFBundleVersion[^>]*>[^<]*<string>)[^<]*/gms,
              replace_variable: "$1" + config["APP_BUILD_NUMBER"],
              replace_placeholder: "$1[app_build_number]",
            },
            {
              find: /(CFBundleURLSchemes[^>][^>]*>[^>]*>[^>]*>)[^<]*/gms,
              replace_variable: "$1" + config["URI_SCHEME"],
              replace_placeholder: "$1[deeplink_uri_array]",
            },
          ];
        },
      },
    ],

    // list of commands to execute before checkout proccess
    execute_before: ["echo *execute before placeholder*\n"],

    // list of commands to execute after checkout proccess
    execute_after: ["echo *execute after placeholder*\n"],
    //execute_after: ["ionic build ", "npx cap sync", "npx cap copy"],
  };
};
