[*]
charset=utf-8
end_of_line=lf
insert_final_newline=true
indent_style=space
indent_size=2
trim_trailing_whitespace = true

[{.eslintrc,.babelrc,.stylelintrc,jest.config,*.json,*.jsb3,*.jsb2,*.bowerrc}]
indent_style=space
indent_size=2

[{jshint.json,*.jshintrc}]
indent_style=space
indent_size=2

[*.scss]
indent_style=space
indent_size=2

[*.coffee]
indent_style=space
indent_size=2

[{.analysis_options,*.yml,*.yaml}]
indent_style=space
indent_size=2

[tslint.json]
indent_style=space
indent_size=2

[*.md]
trim_trailing_whitespace = false
